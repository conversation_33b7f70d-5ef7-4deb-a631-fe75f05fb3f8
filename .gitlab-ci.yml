image: node:latest

variables:
    GIT_DEPTH: 10
#     CocosCreator: '\ProgramData\cocos\editors\Creator\3.8.4\CocosCreator.exe'
# S3 配置 (需要在GitLab CI/CD设置中配置以下变量)
# S3_ACCESS_KEY_ID: S3的访问密钥ID
# S3_SECRET_ACCESS_KEY: S3的秘密访问密钥
# S3_ENDPOINT: S3的终端节点URL，例如 https://wh.s3down.com
# S3_REGION: S3的区域，例如 wh
# S3_BUCKET: S3的存储桶名称（联调、测试、预发布环境共用）
# S3_BUCKET_PRODUCTION: S3的线上环境存储桶名称
# 通知服务端配置 (需要在GitLab CI/CD设置中配置以下变量)
# NOTIFY_API_URL_DEV: 联调环境通知服务端的API URL
# NOTIFY_API_URL_TEST: 测试环境通知服务端的API URL
# NOTIFY_API_URL_PRE: 预发布环境通知服务端的API URL
# NOTIFY_API_URL_PROD: 线上环境通知服务端的API URL
# NOTIFY_API_TOKEN: 通知服务端的API Token (可选)

# 构建步骤
stages:
    # 依赖安装
    - install

    # 生成语音房需要的zip包
    - build_zip

    # 上传构建包到S3 （各环境）
    - deploy_dev # 联调环境
    - deploy_test # 测试环境
    - deploy_pre # 预发布环境
    - deploy_prod # 线上环境

   


# 安装依赖
install_dependency:
    stage: install
    # 把文件传递到下一个job expire_in 过期时间 建议加上
    # artifacts:
    #   # 把没版本跟踪的产物发送到 下一个任务
    #   untracked: true
    #   expire_in: 1 week
    before_script:
        - whoami
        - pwd
        - source ~/.nvm/nvm.sh
        - echo ${PATH}
        - cat ~/.bashrc
        - nvm ls
        - node -v
    script:
        # - nvm use 16
        #    # 开始安装
        # - echo "begin nvm use"
        # - nvm use 20.16.0

        # - nvm ls
        # - nvm use 22.11.0
        # - node -v

        # - yarn config set cache-folder /mnt/d/YarnCache
        - echo "begin installing..."
        #    # 安装模块依赖：包含deps和devDeps
        #    # - npm config set cache /cache/npm-cache --global
        - yarn install
    cache:
        key: ${CI_COMMIT_REF_NAME}_${CI_PROJECT_ID}
        paths: # 缓存路径
            - node_modules/
        policy: push
    # only:
    #   - develop
    #   - testing
    #   - release
    #   - master
    only:
        changes:
            - .gitlab-ci.yml
            - package.json
            - yarn-lock.json

    tags:
        - liucl-macos


.job_build_zip:
    script:
        - ls
        - cp -r ${BUILD_PATH}/* ./

    stage: build_zip
    artifacts:
        name: '${CI_PROJECT_NAME}_${CI_BUILD_REF_NAME}_${CI_BUILD_ID}_zip'
        paths:
            - web-mobile/*
    tags:
        - liucl-macos

build_zip_shark:
    variables:
        BUILD_PATH: 'packages/sf_care_shark/build'
    extends:
        - .job_build_zip
    only:
        changes:
            - .gitlab-ci.yml
            - package-lock.json
            - packages/sf_care_shark/**/*



# S3上传和通知服务端的基础模板（通用部分）
.job_deploy_base:
    variables:
        S3_REGION: ${S3_REGION}
        S3_ACCESS_KEY_ID: ${S3_ACCESS_KEY_ID}
        S3_SECRET_ACCESS_KEY: ${S3_SECRET_ACCESS_KEY}
        S3_ENDPOINT: ${S3_ENDPOINT}
        NOTIFY_API_TOKEN: ${NOTIFY_API_TOKEN}
    before_script:
        - source ~/.nvm/nvm.sh
    script:
        - echo "开始打包构建文件..."
        - zip -r ./${GAME_CODE}-${CI_COMMIT_SHORT_SHA}.zip ./web-mobile/
        - echo "开始上传构建包到S3..."
        - node scripts/upload-to-s3.js ${GAME_CODE} ${CI_COMMIT_SHORT_SHA}
        - echo "开始通知服务端..."
        - node scripts/notify-server.js
    cache:
        key: ${CI_COMMIT_REF_NAME}_${CI_PROJECT_ID}
        paths:
            - node_modules/
        policy: pull
    artifacts:
        name: '${CI_PROJECT_NAME}_${CI_BUILD_REF_NAME}_${CI_BUILD_ID}_s3_upload'
        paths:
            - upload-result.json
            - '*.zip'
    tags:
        - liucl-macos
    # 需要手动触发
    when: manual

# 联调环境上传S3基础模板
.job_deploy_dev:
    stage: deploy_dev
    variables:
        S3_BUCKET: ${S3_BUCKET}
        NOTIFY_API_URL: ${NOTIFY_API_URL_DEV}
    extends:
        - .job_deploy_base

# 测试环境上传S3基础模板
.job_deploy_test:
    stage: deploy_test
    variables:
        S3_BUCKET: ${S3_BUCKET}
        NOTIFY_API_URL: ${NOTIFY_API_URL_TEST}
    extends:
        - .job_deploy_base

# 预发布环境上传S3基础模板
.job_deploy_pre:
    stage: deploy_pre
    variables:
        S3_BUCKET: ${S3_BUCKET}
        NOTIFY_API_URL: ${NOTIFY_API_URL_PRE}
    extends:
        - .job_deploy_base

# 线上环境上传S3基础模板
.job_deploy_prod:
    stage: deploy_prod
    variables:
        S3_BUCKET: ${S3_BUCKET_PRODUCTION}
        NOTIFY_API_URL: ${NOTIFY_API_URL_PROD}
    extends:
        - .job_deploy_base

# 鲨鱼游戏上传S3 - 联调环境
deploy_dev_sf_care_shark:
    variables:
        GAME_CODE: 'sf_care_shark'
    dependencies:
        - build_zip_shark
    extends:
        - .job_deploy_dev
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/sf_care_shark/**/*


# 鲨鱼游戏上传S3 - 测试环境
deploy_test_sf_care_shark:
    variables:
        GAME_CODE: 'sf_care_shark'
    dependencies:
        - build_zip_shark
    extends:
        - .job_deploy_test
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/sf_care_shark/**/*


# 鲨鱼游戏上传S3 - 预发布环境
deploy_pre_sf_care_shark:
    variables:
        GAME_CODE: 'sf_care_shark'
    dependencies:
        - build_zip_shark
    extends:
        - .job_deploy_pre
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/sf_care_shark/**/*


# 鲨鱼游戏上传S3 - 线上环境
deploy_prod_sf_care_shark:
    variables:
        GAME_CODE: 'sf_care_shark'
    dependencies:
        - build_zip_shark
    extends:
        - .job_deploy_prod
    only:
        changes:
            - .gitlab-ci.yml
            - scripts/upload-to-s3.js
            - scripts/notify-server.js
            - packages/sf_care_shark/**/*


