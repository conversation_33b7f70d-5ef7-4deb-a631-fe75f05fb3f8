System.register([],(function(r){"use strict";return{execute:function(){var n;r("default",(n="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(r={}){var e,i,f;e||(e=void 0!==r?r:{}),e.ready=new Promise(((r,n)=>{i=r,f=n}));var t,u=Object.assign({},e),o="";function a(r){return e.locateFile?e.locateFile(r,o):o+r}"undefined"!=typeof document&&document.currentScript&&(o=document.currentScript.src),n&&(o=n),o=0!==o.indexOf("blob:")?o.substr(0,o.replace(/[?#].*/,"").lastIndexOf("/")+1):"",t=(r,n)=>{var e=new XMLHttpRequest;e.open("GET",J,!0),e.responseType="arraybuffer",e.onload=()=>{200==e.status||0==e.status&&e.response?r(e.response):n()},e.onerror=n,e.send(null)};var c,b=e.printErr||console.error.bind(console);function k(r){this.exports=function(r){var n=new ArrayBuffer(16),e=new Int32Array(n),i=new Float32Array(n),f=new Float64Array(n);function t(r){return e[r]}function u(r,n){e[r]=n}function o(){return f[0]}function a(r){f[0]=r}function c(){throw new Error("abort")}function b(r){i[2]=r}function k(){return i[2]}return function(r){var n=r.a,e=n.a,i=e.buffer;e.grow=function(r){r|=0;var n=0|pa(),t=n+r|0;if(n<t&&t<65536){var u=new ArrayBuffer(m(t,65536));new Int8Array(u).set(f),f=new Int8Array(u),s=new Int16Array(u),v=new Int32Array(u),l=new Uint8Array(u),h=new Uint16Array(u),d=new Uint32Array(u),p=new Float32Array(u),y=new Float64Array(u),i=u,e.buffer=i}return n};var f=new Int8Array(i),s=new Int16Array(i),v=new Int32Array(i),l=new Uint8Array(i),h=new Uint16Array(i),d=new Uint32Array(i),p=new Float32Array(i),y=new Float64Array(i),m=Math.imul,w=Math.fround,g=Math.abs,F=Math.clz32,A=Math.floor,T=Math.sqrt,$=n.b,I=n.c,C=n.d,P=n.e,E=n.f,O=n.g,R=n.h,S=n.i,W=n.j,G=n.k,U=n.l,j=n.m,H=n.n,L=n.o,M=n.p,_=n.q,z=n.r,x=n.s,J=n.t,K=n.u,B=n.v,N=n.w,q=n.x,D=87568,V=0;function Z(){var r=0,n=0;x(21272,4826),E(21273,5254,4,0,-1),E(21274,3949,4,0,-1),E(21275,6101,4,0,-1),E(21276,5202,4,0,-1),E(21277,6044,4,0,-1),E(21278,5940,4,0,-1),E(21279,5906,4,0,-1),E(21280,5986,4,0,-1),E(21281,6016,4,0,-1),E(21282,3545,4,0,-1),E(21283,3761,4,0,-1),E(21284,5219,4,0,-1),C(21285,21286,21287,0,11232,400,11235,0,11235,0,5274,11237,401),P(21285,1,11240,11232,402,403),r=Tt(8),v[r+4>>2]=0,v[r>>2]=404,$(21285,4900,4,11248,11264,405,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=406,$(21285,4902,2,11272,11280,407,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=408,$(21285,2232,3,11284,11296,409,0|r,0,0),C(21291,21292,21293,0,11232,410,11235,0,11235,0,2360,11237,411),P(21291,1,11304,11232,412,413),r=Tt(8),v[r+4>>2]=0,v[r>>2]=414,$(21291,4900,4,11312,11328,415,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=416,$(21291,4902,2,11336,11280,417,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=418,$(21291,2232,3,11344,11356,419,0|r,0,0),r=Tt(4),v[r>>2]=420,$(21291,2225,4,11376,11328,1609,0|r,0,0),C(21296,21297,21298,0,11232,421,11235,0,11235,0,2340,11237,422),P(21296,1,11392,11232,423,424),r=Tt(8),v[r+4>>2]=0,v[r>>2]=425,$(21296,4900,4,11408,11264,426,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=427,$(21296,4902,2,11424,11280,428,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=429,$(21296,2232,3,11432,11296,430,0|r,0,0),r=Tt(4),v[r>>2]=431,$(21296,2225,4,11456,11264,1610,0|r,0,0),C(21299,21300,21301,0,11232,432,11235,0,11235,0,2126,11237,433),P(21299,1,11472,11232,434,435),r=Tt(8),v[r+4>>2]=0,v[r>>2]=436,$(21299,4900,4,11488,11264,437,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=438,$(21299,4902,2,11504,11280,439,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=440,$(21299,2232,3,11512,11296,441,0|r,0,0),r=Tt(4),v[r>>2]=442,$(21299,2225,4,11536,11264,1611,0|r,0,0),C(21302,21303,21304,0,11232,443,11235,0,11235,0,1743,11237,444),P(21302,1,11552,11232,445,446),r=Tt(8),v[r+4>>2]=0,v[r>>2]=447,$(21302,4900,4,11568,11264,448,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=449,$(21302,4902,2,11584,11280,450,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=451,$(21302,2232,3,11592,11296,452,0|r,0,0),r=Tt(4),v[r>>2]=453,$(21302,2225,4,11616,11264,1612,0|r,0,0),C(21306,21307,21308,0,11232,454,11235,0,11235,0,2108,11237,455),P(21306,1,11632,11232,456,457),r=Tt(8),v[r+4>>2]=0,v[r>>2]=458,$(21306,4900,4,11648,11264,459,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=460,$(21306,4902,2,11664,11280,461,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=462,$(21306,2232,3,11672,11296,463,0|r,0,0),r=Tt(4),v[r>>2]=464,$(21306,2225,4,11696,11264,1613,0|r,0,0),C(21309,21310,21311,0,11232,465,11235,0,11235,0,2374,11237,466),P(21309,1,11712,11232,467,468),r=Tt(8),v[r+4>>2]=0,v[r>>2]=469,$(21309,4900,4,11728,11264,470,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=471,$(21309,4902,2,11744,11280,472,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=473,$(21309,2232,3,11752,11296,474,0|r,0,0),r=Tt(4),v[r>>2]=475,$(21309,2225,4,11776,11264,1614,0|r,0,0),C(21312,21313,21314,0,11232,476,11235,0,11235,0,1481,11237,477),P(21312,1,11792,11232,478,479),r=Tt(8),v[r+4>>2]=0,v[r>>2]=480,$(21312,4900,4,11808,11264,481,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=482,$(21312,4902,2,11824,11280,483,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=484,$(21312,2232,3,11832,11296,485,0|r,0,0),r=Tt(4),v[r>>2]=486,$(21312,2225,4,11856,11264,1615,0|r,0,0),C(21316,21317,21318,0,11232,487,11235,0,11235,0,4838,11237,488),P(21316,1,11872,11232,489,490),r=Tt(8),v[r+4>>2]=0,v[r>>2]=491,$(21316,4900,4,11888,11264,492,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=493,$(21316,4902,2,11904,11280,494,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=495,$(21316,2232,3,11912,11296,496,0|r,0,0),r=Tt(4),v[r>>2]=497,$(21316,2225,4,11936,11264,1616,0|r,0,0),C(21319,21320,21321,0,11232,498,11235,0,11235,0,3254,11237,499),P(21319,1,11952,11232,500,501),r=Tt(8),v[r+4>>2]=0,v[r>>2]=502,$(21319,4900,4,11968,11264,503,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=504,$(21319,4902,2,11984,11280,505,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=506,$(21319,2232,3,11992,11296,507,0|r,0,0),C(21322,21323,21324,0,11232,508,11235,0,11235,0,3471,11237,509),P(21322,1,12004,11232,510,511),r=Tt(8),v[r+4>>2]=0,v[r>>2]=512,$(21322,4900,4,12016,11264,513,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=514,$(21322,4902,2,12032,11280,515,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=516,$(21322,2232,3,12040,11296,517,0|r,0,0),C(21326,21327,21328,0,11232,518,11235,0,11235,0,3311,11237,519),P(21326,1,12052,11232,520,521),r=Tt(8),v[r+4>>2]=0,v[r>>2]=522,$(21326,4900,4,12064,11264,523,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=524,$(21326,4902,2,12080,11280,525,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=526,$(21326,2232,3,12088,11296,527,0|r,0,0),C(21330,21331,21332,0,11232,528,11235,0,11235,0,3357,11237,529),P(21330,1,12100,11232,530,531),r=Tt(8),v[r+4>>2]=0,v[r>>2]=532,$(21330,4900,4,12112,11264,533,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=534,$(21330,4902,2,12128,11280,535,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=536,$(21330,2232,3,12136,11296,537,0|r,0,0),C(21333,21334,21335,0,11232,538,11235,0,11235,0,3420,11237,539),P(21333,1,12148,11232,540,541),r=Tt(8),v[r+4>>2]=0,v[r>>2]=542,$(21333,4900,4,12160,11264,543,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=544,$(21333,4902,2,12176,11280,545,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=546,$(21333,2232,3,12184,11296,547,0|r,0,0),C(21336,21337,21338,0,11232,548,11235,0,11235,0,3331,11237,549),P(21336,1,12196,11232,550,551),r=Tt(8),v[r+4>>2]=0,v[r>>2]=552,$(21336,4900,4,12208,11264,553,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=554,$(21336,4902,2,12224,11280,555,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=556,$(21336,2232,3,12232,11296,557,0|r,0,0),C(21339,21340,21341,0,11232,558,11235,0,11235,0,3098,11237,559),P(21339,1,12244,11232,560,561),r=Tt(8),v[r+4>>2]=0,v[r>>2]=562,$(21339,4900,4,12256,11264,563,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=564,$(21339,4902,2,12272,11280,565,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=566,$(21339,2232,3,12280,11296,567,0|r,0,0),C(21343,21344,21345,0,11232,568,11235,0,11235,0,3233,11237,569),P(21343,1,12292,11232,570,571),r=Tt(8),v[r+4>>2]=0,v[r>>2]=572,$(21343,4900,4,12304,11264,573,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=574,$(21343,4902,2,12320,11280,575,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=576,$(21343,2232,3,12328,11296,577,0|r,0,0),C(21347,21348,21349,0,11232,578,11235,0,11235,0,3450,11237,579),P(21347,1,12340,11232,580,581),r=Tt(8),v[r+4>>2]=0,v[r>>2]=582,$(21347,4900,4,12352,11264,583,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=584,$(21347,4902,2,12368,11280,585,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=586,$(21347,2232,3,12376,11296,587,0|r,0,0),C(21351,21352,21353,0,11232,588,11235,0,11235,0,3195,11237,589),P(21351,1,12388,11232,590,591),r=Tt(8),v[r+4>>2]=0,v[r>>2]=592,$(21351,4900,4,12400,11264,593,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=594,$(21351,4902,2,12416,11280,595,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=596,$(21351,2232,3,12424,11296,597,0|r,0,0),C(21355,21356,21357,0,11232,598,11235,0,11235,0,3212,11237,599),P(21355,1,12436,11232,600,601),r=Tt(8),v[r+4>>2]=0,v[r>>2]=602,$(21355,4900,4,12448,11264,603,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=604,$(21355,4902,2,12464,11280,605,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=606,$(21355,2232,3,12472,11296,607,0|r,0,0),C(21359,21360,21361,0,11232,608,11235,0,11235,0,3145,11237,609),P(21359,1,12484,11232,610,611),r=Tt(8),v[r+4>>2]=0,v[r>>2]=612,$(21359,4900,4,12496,11264,613,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=614,$(21359,4902,2,12512,11280,615,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=616,$(21359,2232,3,12520,11296,617,0|r,0,0),C(21362,21363,21364,0,11232,618,11235,0,11235,0,3392,11237,619),P(21362,1,12532,11232,620,621),r=Tt(8),v[r+4>>2]=0,v[r>>2]=622,$(21362,4900,4,12544,11264,623,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=624,$(21362,4902,2,12560,11280,625,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=626,$(21362,2232,3,12568,11296,627,0|r,0,0),C(21365,21366,21367,0,11232,628,11235,0,11235,0,3114,11237,629),P(21365,1,12580,11232,630,631),r=Tt(8),v[r+4>>2]=0,v[r>>2]=632,$(21365,4900,4,12592,11264,633,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=634,$(21365,4902,2,12608,11280,635,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=636,$(21365,2232,3,12616,11296,637,0|r,0,0),C(21368,21369,21370,0,11232,638,11235,0,11235,0,3169,11237,639),P(21368,1,12628,11232,640,641),r=Tt(8),v[r+4>>2]=0,v[r>>2]=642,$(21368,4900,4,12640,11264,643,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=644,$(21368,4902,2,12656,11280,645,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=646,$(21368,2232,3,12664,11296,647,0|r,0,0),C(21371,21372,21373,0,11232,648,11235,0,11235,0,3270,11237,649),P(21371,1,12676,11232,650,651),r=Tt(8),v[r+4>>2]=0,v[r>>2]=652,$(21371,4900,4,12688,11264,653,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=654,$(21371,4902,2,12704,11280,655,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=656,$(21371,2232,3,12712,11296,657,0|r,0,0),r=Tt(4),v[r>>2]=658,$(21371,2225,4,12736,11264,1617,0|r,0,0),C(21374,21375,21376,0,11232,659,11235,0,11235,0,3071,11237,660),P(21374,1,12752,11232,661,662),r=Tt(8),v[r+4>>2]=0,v[r>>2]=663,$(21374,4900,4,12768,11264,664,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=665,$(21374,4902,2,12784,11280,666,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=667,$(21374,2232,3,12792,11296,668,0|r,0,0),C(21378,21379,21380,0,11232,669,11235,0,11235,0,3290,11237,670),P(21378,1,12804,11232,671,672),r=Tt(8),v[r+4>>2]=0,v[r>>2]=673,$(21378,4900,4,12816,11264,674,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=675,$(21378,4902,2,12832,11280,676,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=677,$(21378,2232,3,12840,11296,678,0|r,0,0),C(21381,21382,21383,0,11232,679,11235,0,11235,0,3050,11237,680),P(21381,1,12852,11232,681,682),r=Tt(8),v[r+4>>2]=0,v[r>>2]=683,$(21381,4900,4,12880,11264,684,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=685,$(21381,4902,2,12896,11280,686,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=687,$(21381,2232,3,12904,11296,688,0|r,0,0),C(21385,21386,21387,0,11232,689,11235,0,11235,0,3023,11237,690),P(21385,1,12916,11232,691,692),r=Tt(8),v[r+4>>2]=0,v[r>>2]=693,$(21385,4900,4,12944,11264,694,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=695,$(21385,4902,2,12960,11280,696,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=697,$(21385,2232,3,12968,11296,698,0|r,0,0),C(21388,21389,21390,0,11232,699,11235,0,11235,0,6924,11237,700),P(21388,1,12980,11232,701,702),P(21388,3,12984,12996,703,704),r=Tt(4),v[r>>2]=0,n=Tt(4),v[n>>2]=0,I(21388,1374,21294,13001,705,0|r,21294,13005,706,0|n),r=Tt(4),v[r>>2]=4,n=Tt(4),v[n>>2]=4,I(21388,1200,21294,13001,705,0|r,21294,13005,706,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=707,$(21388,2225,4,13024,13040,708,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=709,$(21388,4667,2,13048,13001,710,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=711,$(21388,4907,2,13056,11280,712,0|r,0,0),C(21391,21392,21393,0,11232,713,11235,0,11235,0,3519,11237,714),P(21391,1,13064,11232,715,716),P(21391,5,13072,13092,717,718),r=Tt(8),v[r+4>>2]=0,v[r>>2]=719,$(21391,2225,6,13104,13128,720,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=721,$(21391,6303,6,13104,13128,720,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=722,$(21391,3743,2,13136,11280,723,0|r,0,0),r=Tt(4),v[r>>2]=4,n=Tt(4),v[n>>2]=4,I(21391,3736,21294,13001,724,0|r,21294,13005,725,0|n),r=Tt(4),v[r>>2]=8,n=Tt(4),v[n>>2]=8,I(21391,4898,21294,13001,724,0|r,21294,13005,725,0|n),r=Tt(4),v[r>>2]=12,n=Tt(4),v[n>>2]=12,I(21391,6323,21294,13001,724,0|r,21294,13005,725,0|n),r=Tt(4),v[r>>2]=16,n=Tt(4),v[n>>2]=16,I(21391,6538,21294,13001,724,0|r,21294,13005,725,0|n),C(4276,7452,7446,0,11232,726,11235,0,11235,0,4276,11237,727),r=Tt(8),v[r+4>>2]=1,v[r>>2]=4,$(4276,1181,3,13144,13156,728,0|r,1,0),C(21394,21395,21396,0,11232,729,11235,0,11235,0,2236,11237,730),P(21394,1,13164,11232,731,732),C(6384,7982,7976,0,11232,733,11235,0,11235,0,6384,11237,734),P(6384,2,13168,11280,735,736),r=Tt(8),v[r+4>>2]=0,v[r>>2]=737,I(6384,5744,21272,11280,738,0|r,0,0,0,0),r=Tt(4),v[r>>2]=16,n=Tt(4),v[n>>2]=16,I(6384,3604,21289,11280,739,0|r,21289,13176,740,0|n),r=Tt(4),v[r>>2]=20,n=Tt(4),v[n>>2]=20,I(6384,6257,21397,11280,741,0|r,21397,13176,742,0|n),C(6363,7932,7926,6384,11232,743,11232,744,11232,745,6363,11237,746),P(6363,2,13184,11280,747,748),r=Tt(4),v[r>>2]=749,$(6363,2751,2,13192,11280,1618,0|r,0,0),r=Tt(4),v[r>>2]=40,n=Tt(4),v[n>>2]=40,I(6363,2229,21398,11280,750,0|r,21398,13176,751,0|n),r=Tt(4),v[r>>2]=44,n=Tt(4),v[n>>2]=44,I(6363,3969,21295,11280,752,0|r,21295,13176,753,0|n),r=Tt(4),v[r>>2]=48,n=Tt(4),v[n>>2]=48,I(6363,2564,21397,11280,754,0|r,21397,13176,755,0|n),r=Tt(4),v[r>>2]=49,n=Tt(4),v[n>>2]=49,I(6363,4804,21397,11280,754,0|r,21397,13176,755,0|n),r=Tt(4),v[r>>2]=50,n=Tt(4),v[n>>2]=50,I(6363,4465,21397,11280,754,0|r,21397,13176,755,0|n),r=Tt(4),v[r>>2]=52,n=Tt(4),v[n>>2]=52,I(6363,1214,21294,13001,756,0|r,21294,13005,757,0|n),r=Tt(4),v[r>>2]=56,n=Tt(4),v[n>>2]=56,I(6363,2573,21294,13001,756,0|r,21294,13005,757,0|n),C(6380,7956,7950,6384,11232,758,11232,759,11232,760,6380,11237,761),P(6380,2,13200,11280,762,763),r=Tt(4),v[r>>2]=764,$(6380,2751,2,13208,11280,1619,0|r,0,0),r=Tt(4),v[r>>2]=40,n=Tt(4),v[n>>2]=40,I(6380,2229,21399,11280,765,0|r,21399,13176,766,0|n),r=Tt(4),v[r>>2]=44,n=Tt(4),v[n>>2]=44,I(6380,5893,21279,11280,767,0|r,21279,13176,768,0|n),r=Tt(4),v[r>>2]=48,n=Tt(4),v[n>>2]=48,I(6380,5974,21280,11280,769,0|r,21280,13176,770,0|n),r=Tt(4),v[r>>2]=52,n=Tt(4),v[n>>2]=52,I(6380,6005,21281,11280,771,0|r,21281,13176,772,0|n),r=Tt(4),v[r>>2]=56,n=Tt(4),v[n>>2]=56,I(6380,4036,21294,13001,773,0|r,21294,13005,774,0|n),r=Tt(4),v[r>>2]=60,n=Tt(4),v[n>>2]=60,I(6380,3919,21294,13001,773,0|r,21294,13005,774,0|n),r=Tt(4),v[r>>2]=64,n=Tt(4),v[n>>2]=64,I(6380,4892,21294,13001,773,0|r,21294,13005,774,0|n),r=Tt(4),v[r>>2]=68,n=Tt(4),v[n>>2]=68,I(6380,1252,21294,13001,773,0|r,21294,13005,774,0|n),r=Tt(4),v[r>>2]=72,n=Tt(4),v[n>>2]=72,I(6380,1262,21294,13001,773,0|r,21294,13005,774,0|n),C(21400,21401,21402,0,11232,775,11235,0,11235,0,2947,11237,776),P(21400,1,13216,11232,777,778),r=Tt(8),v[r+4>>2]=0,v[r>>2]=779,$(21400,5087,4,13232,11264,780,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=781,$(21400,1665,4,13248,13040,782,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=783,$(21400,2054,6,13264,13128,784,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=785,$(21400,3843,3,13288,11296,786,0|r,0,0),r=Tt(4),v[r>>2]=787,$(21400,1651,4,13312,13040,1620,0|r,0,0),r=Tt(4),v[r>>2]=788,$(21400,2036,6,13328,13128,1621,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=789,$(21400,4711,2,13352,13001,790,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=791,$(21400,2173,2,13352,13001,790,0|r,0,0),C(21404,21354,21405,0,11232,792,11235,0,11235,0,1800,11237,793),P(21404,3,13360,13372,794,795),r=Tt(4),v[r>>2]=796,I(21404,6325,21406,11280,797,0|r,0,0,0,0),r=Tt(4),v[r>>2]=12,n=Tt(4),v[n>>2]=12,I(21404,4984,21295,11280,798,0|r,21295,13176,799,0|n),r=Tt(4),v[r>>2]=16,n=Tt(4),v[n>>2]=16,I(21404,4993,21294,13001,800,0|r,21294,13005,801,0|n),r=Tt(4),v[r>>2]=20,n=Tt(4),v[n>>2]=20,I(21404,5004,21272,11280,802,0|r,21272,13176,803,0|n),r=Tt(4),v[r>>2]=8,I(21404,5659,21294,13001,800,0|r,0,0,0,0),r=Tt(4),v[r>>2]=32,n=Tt(4),v[n>>2]=32,I(21404,5652,21294,13001,800,0|r,21294,13005,801,0|n),r=Tt(4),v[r>>2]=36,n=Tt(4),v[n>>2]=36,I(21404,6078,21294,13001,800,0|r,21294,13005,801,0|n),C(21406,21350,21407,0,11232,804,11235,0,11235,0,6399,11237,805),P(21406,2,13380,11280,806,807),r=Tt(8),v[r+4>>2]=0,v[r>>2]=808,I(21406,5744,21272,11280,809,0|r,0,0,0,0),r=Tt(4),v[r>>2]=16,n=Tt(4),v[n>>2]=16,I(21406,4984,21295,11280,810,0|r,21295,13176,811,0|n),r=Tt(4),v[r>>2]=20,n=Tt(4),v[n>>2]=20,I(21406,4993,21294,13001,812,0|r,21294,13005,813,0|n),r=Tt(4),v[r>>2]=24,n=Tt(4),v[n>>2]=24,I(21406,5004,21272,11280,814,0|r,21272,13176,815,0|n),r=Tt(4),v[r>>2]=36,n=Tt(4),v[n>>2]=36,I(21406,4736,21272,11280,814,0|r,21272,13176,815,0|n),r=Tt(4),v[r>>2]=48,n=Tt(4),v[n>>2]=48,I(21406,5652,21294,13001,812,0|r,21294,13005,813,0|n),r=Tt(4),v[r>>2]=52,n=Tt(4),v[n>>2]=52,I(21406,6078,21294,13001,812,0|r,21294,13005,813,0|n),C(2025,7259,7253,0,11232,816,11235,0,11235,0,2025,11237,817),r=Tt(8),v[r+4>>2]=0,v[r>>2]=818,I(2025,5744,21272,11280,819,0|r,0,0,0,0),C(1860,7118,7112,2025,11232,820,11232,821,11232,822,1860,11237,823),r=Tt(8),v[r+4>>2]=0,v[r>>2]=824,I(1860,6241,21295,11280,825,0|r,0,0,0,0),r=Tt(4),v[r>>2]=826,$(1860,2751,2,13388,11280,1622,0|r,0,0),r=Tt(4),v[r>>2]=827,$(1860,2881,2,13396,11280,1623,0|r,0,0),r=Tt(4),v[r>>2]=52,n=Tt(4),v[n>>2]=52,I(1860,4674,21289,11280,828,0|r,21289,13176,829,0|n),r=Tt(4),v[r>>2]=56,n=Tt(4),v[n>>2]=56,I(1860,1944,1860,11280,830,0|r,1860,13176,831,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=832,$(1860,2910,8,13408,13440,833,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=834,$(1860,3793,3,13452,13176,835,0|r,0,0),C(1838,7089,7083,1860,11232,836,11232,837,11232,838,1838,11237,839),P(1838,2,13464,11280,840,841),r=Tt(8),v[r+4>>2]=0,v[r>>2]=818,I(1838,5744,21272,11280,842,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(1838,1151,2,13472,11280,843,0|r,0,0),C(2e3,7233,7227,1860,11232,844,11232,845,11232,846,2e3,11237,847),P(2e3,2,13480,11280,848,849),r=Tt(8),v[r+4>>2]=0,v[r>>2]=850,n=Tt(8),v[n+4>>2]=0,v[n>>2]=851,I(2e3,1597,21399,11280,852,0|r,21399,13176,853,0|n),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(2e3,1151,2,13488,11280,854,0|r,0,0),C(1982,7211,7205,1860,11232,855,11232,856,11232,857,1982,11237,858),P(1982,2,13496,11280,859,860),r=Tt(4),v[r>>2]=168,n=Tt(4),v[n>>2]=168,I(1982,4720,21272,11280,861,0|r,21272,13176,862,0|n),Nf(2989,863),Nf(2982,864),qf(2812,865),r=Tt(4),v[r>>2]=866,I(1982,3491,21391,11280,867,0|r,0,0,0,0),r=Tt(4),v[r>>2]=196,n=Tt(4),v[n>>2]=196,I(1982,4705,21294,13001,868,0|r,21294,13005,869,0|n),r=Tt(4),v[r>>2]=200,n=Tt(4),v[n>>2]=200,I(1982,2166,21294,13001,868,0|r,21294,13005,869,0|n);r=Tt(4),v[r>>2]=224,n=Tt(4),v[n>>2]=224,I(1982,4694,21295,11280,870,0|r,21295,13176,871,0|n),qf(2848,872),r=Tt(8),v[r+4>>2]=0,v[r>>2]=873,$(1982,3002,2,13520,13528,874,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=875,$(1982,4771,2,13532,11280,876,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=877,$(1982,4757,3,13540,13176,878,0|r,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(1982,1151,2,13552,11280,879,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=880,$(1982,4785,2,13532,11280,876,0|r,0,0),C(1964,7189,7183,1860,11232,881,11232,882,11232,883,1964,11237,884),P(1964,2,13560,11280,885,886),r=Tt(4),v[r>>2]=887,$(1964,2713,2,13568,11280,1626,0|r,0,0),r=Tt(4),v[r>>2]=80,n=Tt(4),v[n>>2]=80,I(1964,6250,21397,11280,888,0|r,21397,13176,889,0|n),r=Tt(4),v[r>>2]=81,n=Tt(4),v[n>>2]=81,I(1964,6289,21397,11280,888,0|r,21397,13176,889,0|n),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(1964,1151,2,13576,11280,890,0|r,0,0),C(1880,7142,7136,2025,11232,891,11232,892,11232,893,1880,11237,894),P(1880,2,13584,11280,895,896),r=Tt(4),v[r>>2]=20,n=Tt(4),v[n>>2]=20,I(1880,1374,21294,13001,897,0|r,21294,13005,898,0|n),r=Tt(4),v[r>>2]=24,n=Tt(4),v[n>>2]=24,I(1880,1200,21294,13001,897,0|r,21294,13005,898,0|n),r=Tt(4),v[r>>2]=28,n=Tt(4),v[n>>2]=28,I(1880,4027,21294,13001,897,0|r,21294,13005,898,0|n),r=Tt(4),v[r>>2]=899,$(1880,3928,5,13600,13620,1627,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=900,$(1880,4093,3,13628,11356,901,0|r,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(1880,1151,2,13640,11280,902,0|r,0,0),C(1927,7165,7159,2025,11232,903,11232,904,11232,905,1927,11237,906),P(1927,2,13648,11280,907,908),r=Tt(4),v[r>>2]=32,n=Tt(4),v[n>>2]=32,I(1927,1374,21294,13001,909,0|r,21294,13005,910,0|n),r=Tt(4),v[r>>2]=36,n=Tt(4),v[n>>2]=36,I(1927,1200,21294,13001,909,0|r,21294,13005,910,0|n),r=Tt(4),v[r>>2]=44,n=Tt(4),v[n>>2]=44,I(1927,6697,21294,13001,909,0|r,21294,13005,910,0|n),r=Tt(4),v[r>>2]=48,n=Tt(4),v[n>>2]=48,I(1927,6604,21294,13001,909,0|r,21294,13005,910,0|n),r=Tt(4),v[r>>2]=40,n=Tt(4),v[n>>2]=40,I(1927,4027,21294,13001,909,0|r,21294,13005,910,0|n),r=Tt(4),v[r>>2]=52,n=Tt(4),v[n>>2]=52,I(1927,4705,21294,13001,909,0|r,21294,13005,910,0|n),r=Tt(4),v[r>>2]=56,n=Tt(4),v[n>>2]=56,I(1927,2166,21294,13001,909,0|r,21294,13005,910,0|n),r=Tt(4),v[r>>2]=911,I(1927,3491,21391,11280,912,0|r,0,0,0,0),r=Tt(4),v[r>>2]=116,n=Tt(4),v[n>>2]=116,I(1927,4720,21272,11280,913,0|r,21272,13176,914,0|n),Df(2198,915),r=Tt(8),v[r+4>>2]=0,v[r>>2]=916,$(1927,2975,7,13664,13692,917,0|r,0,0),Df(2982,918),r=Tt(8),v[r+4>>2]=0,v[r>>2]=919,$(1927,2216,2,13704,13528,920,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=921,$(1927,2910,6,13712,13736,922,0|r,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(1927,1151,2,13744,11280,923,0|r,0,0),C(3638,7377,7371,0,11232,924,11235,0,11235,0,3638,11237,925),r=Tt(8),v[r+4>>2]=1,v[r>>2]=32,$(3638,1997,4,13760,13776,926,0|r,1,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=28,$(3638,1877,4,13792,13776,927,0|r,1,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=24,$(3638,1961,4,13808,13776,928,0|r,1,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=20,$(3638,1835,4,13824,13776,929,0|r,1,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=16,$(3638,1979,5,13840,13860,930,0|r,1,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(3638,1924,5,13872,13860,931,0|r,1,0),C(3633,7348,7342,3638,11232,932,11232,933,11232,934,3633,11237,935),P(3633,2,13892,11280,936,937),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(3633,1924,5,13904,13860,938,0|r,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=16,$(3633,1979,5,13936,13860,939,0|r,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=20,$(3633,1835,4,13968,13776,940,0|r,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=24,$(3633,1961,4,13984,13776,941,0|r,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=28,$(3633,1877,4,14e3,13776,942,0|r,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=32,$(3633,1997,4,14016,13776,943,0|r,0,0),C(21411,21412,21413,0,11232,944,11235,0,11235,0,5876,11237,945),P(21411,2,14032,11280,946,947),r=Tt(4),v[r>>2]=948,$(21411,5783,2,14040,11280,1629,0|r,0,0),r=Tt(4),v[r>>2]=44,n=Tt(4),v[n>>2]=44,I(21411,3525,21282,11280,949,0|r,21282,13176,950,0|n),r=Tt(4),v[r>>2]=48,n=Tt(4),v[n>>2]=48,I(21411,3535,21282,11280,949,0|r,21282,13176,950,0|n),r=Tt(4),v[r>>2]=52,n=Tt(4),v[n>>2]=52,I(21411,3755,21283,11280,951,0|r,21283,13176,952,0|n),r=Tt(4),v[r>>2]=56,n=Tt(4),v[n>>2]=56,I(21411,3749,21283,11280,951,0|r,21283,13176,952,0|n),r=Tt(4),v[r>>2]=60,n=Tt(4),v[n>>2]=60,I(21411,4705,21295,11280,953,0|r,21295,13176,954,0|n),r=Tt(4),v[r>>2]=64,n=Tt(4),v[n>>2]=64,I(21411,2166,21295,11280,953,0|r,21295,13176,954,0|n),C(21414,21415,21416,0,11232,955,11235,0,11235,0,4322,11237,956),r=Tt(4),v[r>>2]=957,$(21414,5783,2,14048,11280,1630,0|r,0,0),r=Tt(4),v[r>>2]=20,n=Tt(4),v[n>>2]=20,I(21414,1374,21295,11280,958,0|r,21295,13176,959,0|n),r=Tt(4),v[r>>2]=24,n=Tt(4),v[n>>2]=24,I(21414,1200,21295,11280,958,0|r,21295,13176,959,0|n),r=Tt(4),v[r>>2]=68,n=Tt(4),v[n>>2]=68,I(21414,1284,21295,11280,958,0|r,21295,13176,959,0|n),r=Tt(4),v[r>>2]=72,n=Tt(4),v[n>>2]=72,I(21414,5052,21397,11280,960,0|r,21397,13176,961,0|n),r=Tt(4),v[r>>2]=76,n=Tt(4),v[n>>2]=76,I(21414,2864,21295,11280,958,0|r,21295,13176,959,0|n),C(21417,21418,21419,0,11232,962,11235,0,11235,0,3655,11237,963),C(21420,21410,21421,0,11232,964,11235,0,11235,0,2962,11237,965),P(21420,4,14064,13776,966,967),r=Tt(8),v[r+4>>2]=0,v[r>>2]=968,$(21420,4341,3,14080,11296,969,0|r,0,0),C(4253,7401,7395,4276,11232,970,11232,971,11232,972,1376,11237,973),P(4253,2,14092,11280,974,975),C(4270,7425,7419,4276,11232,976,11232,977,11232,978,1385,11237,979),P(4270,2,14100,11280,980,981),C(21399,21329,21422,0,11232,982,11235,0,11235,0,6330,11237,983),P(21399,4,14112,13776,984,985),r=Tt(8),v[r+4>>2]=0,v[r>>2]=986,I(21399,1284,21295,11280,987,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=988,I(21399,5744,21272,11280,989,0|r,0,0,0,0),r=Tt(4),v[r>>2]=990,I(21399,6479,21398,11280,991,0|r,0,0,0,0),r=Tt(4),v[r>>2]=992,I(21399,3491,21391,11280,993,0|r,0,0,0,0),r=Tt(4),v[r>>2]=994,I(21399,3515,21391,11280,993,0|r,0,0,0,0),r=Tt(4),v[r>>2]=80,n=Tt(4),v[n>>2]=80,I(21399,6034,21277,11280,995,0|r,21277,13176,996,0|n),C(5804,7884,7878,0,11232,997,11235,0,11235,0,5804,11237,998),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(5804,5087,2,14128,13528,999,0|r,1,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=16,$(5804,4961,2,14136,11280,1e3,0|r,1,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=16,n=Tt(8),v[n+4>>2]=1,v[n>>2]=20,I(5804,4954,21397,11280,1001,0|r,21397,13176,1002,0|n),C(1711,7047,7041,5804,11232,1003,11232,1004,11232,1005,1711,11237,1006),P(1711,3,14144,11296,1007,1008),r=Tt(4),v[r>>2]=1009,I(1711,6325,6363,11280,1010,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1011,$(1711,2751,2,14156,11280,1631,0|r,0,0),r=Tt(4),v[r>>2]=40,n=Tt(4),v[n>>2]=40,I(1711,2229,5335,11280,1012,0|r,5335,13176,1013,0|n),r=Tt(4),v[r>>2]=24,n=Tt(4),v[n>>2]=24,I(1711,3969,21295,11280,1014,0|r,21295,13176,1015,0|n),r=Tt(4),v[r>>2]=28,n=Tt(4),v[n>>2]=28,I(1711,2564,21397,11280,1016,0|r,21397,13176,1017,0|n),r=Tt(4),v[r>>2]=29,n=Tt(4),v[n>>2]=29,I(1711,4804,21397,11280,1016,0|r,21397,13176,1017,0|n),r=Tt(4),v[r>>2]=32,n=Tt(4),v[n>>2]=32,I(1711,1214,21294,13001,1018,0|r,21294,13005,1019,0|n),r=Tt(4),v[r>>2]=36,n=Tt(4),v[n>>2]=36,I(1711,2573,21294,13001,1018,0|r,21294,13005,1019,0|n),O(1711,6935,8,14176,14208,1020,1021,0),O(1711,6917,9,14224,14260,1022,1023,0),C(1728,7067,7061,5804,11232,1024,11232,1025,11232,1026,1728,11237,1027),P(1728,3,14272,11296,1028,1029),r=Tt(4),v[r>>2]=1030,I(1728,6325,6380,11280,1031,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1032,$(1728,2751,2,14284,11280,1632,0|r,0,0),r=Tt(4),v[r>>2]=24,n=Tt(4),v[n>>2]=24,I(1728,2229,21408,11280,1033,0|r,21408,13176,1034,0|n),r=Tt(4),v[r>>2]=28,n=Tt(4),v[n>>2]=28,I(1728,3919,21294,13001,1035,0|r,21294,13005,1036,0|n),r=Tt(4),v[r>>2]=32,n=Tt(4),v[n>>2]=32,I(1728,4892,21294,13001,1035,0|r,21294,13005,1036,0|n),r=Tt(4),v[r>>2]=36,n=Tt(4),v[n>>2]=36,I(1728,1252,21294,13001,1035,0|r,21294,13005,1036,0|n),r=Tt(4),v[r>>2]=40,n=Tt(4),v[n>>2]=40,I(1728,1262,21294,13001,1035,0|r,21294,13005,1036,0|n),C(6339,7901,7895,6384,11232,1037,11232,1038,11232,1039,6339,11237,1040),P(6339,2,14292,11280,1041,1042),r=Tt(4),v[r>>2]=1043,$(6339,2751,2,14300,11280,1633,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1044,I(6339,2229,21398,11280,1045,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1046,I(6339,1252,21294,13001,1047,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1048,I(6339,1262,21294,13001,1047,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1049,I(6339,1275,21294,13001,1047,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1050,I(6339,1243,21294,13001,1047,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1051,I(6339,4036,21294,13001,1047,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1052,I(6339,6646,21294,13001,1047,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1053,I(6339,6540,21294,13001,1047,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1054,I(6339,6704,21294,13001,1047,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1055,I(6339,6611,21294,13001,1047,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1056,I(6339,6572,21294,13001,1047,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1057,I(6339,4970,21397,11280,1058,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1059,I(6339,4559,21397,11280,1058,0|r,0,0,0,0),C(1687,7020,7014,5804,11232,1060,11232,1061,11232,1062,1687,11237,1063),P(1687,3,14308,11296,1064,1065),r=Tt(4),v[r>>2]=1066,I(1687,6325,6339,11280,1067,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1068,$(1687,2751,2,14320,11280,1634,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1069,I(1687,2229,5335,11280,1070,0|r,0,0,0,0),r=Tt(4),v[r>>2]=28,n=Tt(4),v[n>>2]=28,I(1687,1252,21294,13001,1071,0|r,21294,13005,1072,0|n),r=Tt(4),v[r>>2]=32,n=Tt(4),v[n>>2]=32,I(1687,1262,21294,13001,1071,0|r,21294,13005,1072,0|n),r=Tt(4),v[r>>2]=36,n=Tt(4),v[n>>2]=36,I(1687,1275,21294,13001,1071,0|r,21294,13005,1072,0|n),r=Tt(4),v[r>>2]=40,n=Tt(4),v[n>>2]=40,I(1687,1243,21294,13001,1071,0|r,21294,13005,1072,0|n),C(5335,7473,7467,5804,11232,1073,11232,1074,11232,1075,5335,11237,1076),P(5335,4,14336,13776,1077,1078),r=Tt(4),v[r>>2]=1079,I(5335,6325,21398,11280,1080,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1081,I(5335,3809,21403,11280,1082,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1083,I(5335,1817,5335,11280,1084,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1085,$(5335,4415,2,14352,11280,1635,0|r,0,0),r=Tt(4),v[r>>2]=32,n=Tt(4),v[n>>2]=32,I(5335,1374,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=36,n=Tt(4),v[n>>2]=36,I(5335,1200,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=40,n=Tt(4),v[n>>2]=40,I(5335,4027,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=44,n=Tt(4),v[n>>2]=44,I(5335,6697,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=48,n=Tt(4),v[n>>2]=48,I(5335,6604,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=52,n=Tt(4),v[n>>2]=52,I(5335,6671,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=56,n=Tt(4),v[n>>2]=56,I(5335,6565,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=60,n=Tt(4),v[n>>2]=60,I(5335,1373,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=64,n=Tt(4),v[n>>2]=64,I(5335,1199,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=68,n=Tt(4),v[n>>2]=68,I(5335,4026,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=72,n=Tt(4),v[n>>2]=72,I(5335,6696,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=76,n=Tt(4),v[n>>2]=76,I(5335,6603,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=80,n=Tt(4),v[n>>2]=80,I(5335,6670,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=84,n=Tt(4),v[n>>2]=84,I(5335,6564,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=88,n=Tt(4),v[n>>2]=88,I(5335,6231,21397,11280,1088,0|r,21397,13176,1089,0|n),r=Tt(4),v[r>>2]=92,n=Tt(4),v[n>>2]=92,I(5335,6538,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=96,n=Tt(4),v[n>>2]=96,I(5335,6323,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=104,n=Tt(4),v[n>>2]=104,I(5335,6321,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=108,n=Tt(4),v[n>>2]=108,I(5335,6319,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=100,n=Tt(4),v[n>>2]=100,I(5335,6732,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(4),v[r>>2]=112,n=Tt(4),v[n>>2]=112,I(5335,6639,21294,13001,1086,0|r,21294,13005,1087,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1090,$(5335,4444,2,14360,13528,1091,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1092,$(5335,4642,9,14368,14404,1093,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1094,$(5335,5101,2,14360,13528,1091,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1095,$(5335,6678,2,14416,13001,1096,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1097,$(5335,6585,2,14416,13001,1096,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1098,$(5335,6717,2,14416,13001,1096,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1099,$(5335,6624,2,14416,13001,1096,0|r,0,0),Vf(4565,1100),Vf(6147,1101),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1102,$(5335,4051,3,14436,13156,1103,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1104,$(5335,4072,3,14436,13156,1103,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1105,$(5335,6160,3,14448,13005,1106,0|r,0,0),C(21398,21325,21424,0,11232,1107,11235,0,11235,0,6488,11237,1108),P(21398,4,14464,13776,1109,1110),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1111,I(21398,1284,21295,11280,1112,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1113,I(21398,5744,21272,11280,1114,0|r,0,0,0,0),r=Tt(4),v[r>>2]=20,n=Tt(4),v[n>>2]=20,I(21398,1817,21398,11280,1115,0|r,21398,13176,1116,0|n),r=Tt(4),v[r>>2]=24,n=Tt(4),v[n>>2]=24,I(21398,4667,21294,13001,1117,0|r,21294,13005,1118,0|n),r=Tt(4),v[r>>2]=28,n=Tt(4),v[n>>2]=28,I(21398,1374,21294,13001,1117,0|r,21294,13005,1118,0|n),r=Tt(4),v[r>>2]=32,n=Tt(4),v[n>>2]=32,I(21398,1200,21294,13001,1117,0|r,21294,13005,1118,0|n),r=Tt(4),v[r>>2]=36,n=Tt(4),v[n>>2]=36,I(21398,4027,21294,13001,1117,0|r,21294,13005,1118,0|n),r=Tt(4),v[r>>2]=40,n=Tt(4),v[n>>2]=40,I(21398,6697,21294,13001,1117,0|r,21294,13005,1118,0|n),r=Tt(4),v[r>>2]=44,n=Tt(4),v[n>>2]=44,I(21398,6604,21294,13001,1117,0|r,21294,13005,1118,0|n),r=Tt(4),v[r>>2]=48,n=Tt(4),v[n>>2]=48,I(21398,6671,21294,13001,1117,0|r,21294,13005,1118,0|n),r=Tt(4),v[r>>2]=52,n=Tt(4),v[n>>2]=52,I(21398,6565,21294,13001,1117,0|r,21294,13005,1118,0|n),r=Tt(4),v[r>>2]=56,n=Tt(4),v[n>>2]=56,I(21398,5926,21278,11280,1119,0|r,21278,13176,1120,0|n),r=Tt(4),v[r>>2]=60,n=Tt(4),v[n>>2]=60,I(21398,6257,21397,11280,1121,0|r,21397,13176,1122,0|n),C(21408,21342,21425,0,11232,1123,11235,0,11235,0,1600,11237,1124),P(21408,3,14480,11296,1125,1126),r=Tt(4),v[r>>2]=1127,I(21408,6325,21399,11280,1128,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1129,I(21408,5314,5335,11280,1130,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1131,I(21408,3491,21391,11280,1132,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1133,I(21408,3515,21391,11280,1132,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1134,$(21408,4480,2,14492,11280,1637,0|r,0,0),r=Tt(4),v[r>>2]=1135,$(21408,3831,2,14500,11280,1638,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1136,$(21408,1910,2,14508,11280,1137,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1138,$(21408,1896,3,14516,13176,1139,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1140,$(21408,5672,3,14528,13005,1141,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1142,$(21408,5690,2,14540,13001,1143,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1144,$(21408,5101,2,14548,13528,1145,0|r,0,0),C(21409,21346,21426,0,11232,1146,11235,0,11235,0,4398,11237,1147),P(21409,2,14556,11280,1148,1149),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1150,I(21409,5744,21272,11280,1151,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1152,$(21409,2751,2,14564,11280,1639,0|r,0,0),r=Tt(4),v[r>>2]=1153,$(21409,2437,2,14572,11280,1640,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1154,$(21409,1896,5,14592,14612,1155,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1156,$(21409,4395,3,14620,13176,1157,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1158,$(21409,4357,3,14620,13176,1157,0|r,0,0),r=Tt(4),v[r>>2]=1159,$(21409,1555,3,14632,11296,1641,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1160,$(21409,1910,4,14656,13776,1161,0|r,0,0),r=Tt(4),v[r>>2]=1162,$(21409,2541,2,14672,11280,1642,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1163,$(21409,2019,4,14688,11264,1164,0|r,0,0),r=Tt(4),v[r>>2]=1165,$(21409,1533,3,14704,11296,1643,0|r,0,0),C(21427,21384,21428,0,11232,1166,11235,0,11235,0,1069,11237,1167),P(21427,4,14720,13776,1168,1169),r=Tt(4),v[r>>2]=0,n=Tt(4),v[n>>2]=0,I(21427,1290,21289,11280,1170,0|r,21289,13176,1171,0|n),r=Tt(4),v[r>>2]=1172,$(21427,5783,2,14736,11280,1644,0|r,0,0),r=Tt(4),v[r>>2]=1173,$(21427,1910,2,14744,11280,1645,0|r,0,0),C(21430,21431,21432,0,11232,1174,11235,0,11235,0,4875,11237,1175),P(21430,1,14752,11232,1176,1177),r=Tt(4),v[r>>2]=1178,I(21430,2931,21291,11280,1179,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1180,I(21430,2825,21312,11280,1181,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1182,I(21430,3012,21291,11280,1179,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1183,$(21430,1503,4,14768,13776,1184,0|r,0,0);r=Tt(8),v[r+4>>2]=0,v[r>>2]=1185,$(21430,1572,3,14784,13176,1186,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1187,$(21430,6117,2,14796,13528,1188,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1189,$(21430,4864,2,14804,11280,1190,0|r,0,0),C(21433,21434,21435,0,11232,1191,11235,0,11235,0,6447,11237,1192),P(21433,1,14812,11232,1193,1194),r=Tt(4),v[r>>2]=4,n=Tt(4),v[n>>2]=4,I(21433,5744,21272,11280,1195,0|r,21272,13176,1196,0|n),r=Tt(4),v[r>>2]=1197,$(21433,2751,2,14816,11280,1646,0|r,0,0),r=Tt(4),v[r>>2]=1198,$(21433,2428,2,14824,11280,1647,0|r,0,0),r=Tt(4),v[r>>2]=1199,$(21433,2674,2,14832,11280,1648,0|r,0,0),r=Tt(4),v[r>>2]=64,n=Tt(4),v[n>>2]=64,I(21433,4366,21409,11280,1200,0|r,21409,13176,1201,0|n),r=Tt(4),v[r>>2]=1202,$(21433,2519,2,14840,11280,1649,0|r,0,0),r=Tt(4),v[r>>2]=1203,$(21433,2654,2,14848,11280,1650,0|r,0,0),r=Tt(4),v[r>>2]=1204,$(21433,2476,2,14856,11280,1651,0|r,0,0),r=Tt(4),v[r>>2]=1205,$(21433,2452,2,14864,11280,1652,0|r,0,0),r=Tt(4),v[r>>2]=1206,$(21433,2493,2,14872,11280,1653,0|r,0,0),r=Tt(4),v[r>>2]=148,n=Tt(4),v[n>>2]=148,I(21433,1374,21294,13001,1207,0|r,21294,13005,1208,0|n),r=Tt(4),v[r>>2]=152,n=Tt(4),v[n>>2]=152,I(21433,1200,21294,13001,1207,0|r,21294,13005,1208,0|n),r=Tt(4),v[r>>2]=156,n=Tt(4),v[n>>2]=156,I(21433,4705,21294,13001,1207,0|r,21294,13005,1208,0|n),r=Tt(4),v[r>>2]=160,n=Tt(4),v[n>>2]=160,I(21433,2166,21294,13001,1207,0|r,21294,13005,1208,0|n),r=Tt(4),v[r>>2]=164,n=Tt(4),v[n>>2]=164,I(21433,4290,21272,11280,1195,0|r,21272,13176,1196,0|n),r=Tt(4),v[r>>2]=176,n=Tt(4),v[n>>2]=176,I(21433,4799,21272,11280,1195,0|r,21272,13176,1196,0|n),r=Tt(4),v[r>>2]=204,n=Tt(4),v[n>>2]=204,I(21433,2596,21294,13001,1207,0|r,21294,13005,1208,0|n),r=Tt(4),v[r>>2]=208,n=Tt(4),v[n>>2]=208,I(21433,4725,21272,11280,1195,0|r,21272,13176,1196,0|n),r=Tt(4),v[r>>2]=220,n=Tt(4),v[n>>2]=220,I(21433,4736,21272,11280,1195,0|r,21272,13176,1196,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1209,$(21433,5331,3,14880,11296,1210,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1211,$(21433,1359,3,14892,11296,1212,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1213,$(21433,1588,3,14904,11296,1214,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1215,$(21433,1300,3,14892,11296,1212,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1216,$(21433,4386,3,14916,11296,1217,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1218,$(21433,1796,3,14928,11296,1219,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1220,$(21433,4210,3,14940,11296,1221,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1222,$(21433,1707,3,14952,11296,1223,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1224,$(21433,1683,3,14964,11296,1225,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1226,$(21433,1724,3,14976,11296,1227,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1228,$(21433,1314,3,14892,11296,1212,0|r,0,0),C(21436,21358,21437,0,11232,1229,11235,0,11235,0,4227,11237,1230),P(21436,4,14992,15008,1231,1232),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1233,I(21436,5744,21272,11280,1234,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1235,$(21436,2760,2,15020,11280,1654,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1236,$(21436,5454,3,15028,11296,1237,0|r,0,0),r=Tt(4),v[r>>2]=32,n=Tt(4),v[n>>2]=32,I(21436,4114,21294,13001,1238,0|r,21294,13005,1239,0|n),C(5643,7868,7862,0,11232,1240,11235,0,11235,0,5643,11237,1241),r=Tt(8),v[r+4>>2]=1,v[r>>2]=16,$(5643,6307,2,15040,11280,1242,0|r,1,0),C(5591,7779,7773,5643,11232,1243,11232,1244,11232,1245,5591,11237,1246),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1247,$(5591,1631,2,15048,11280,1248,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1249,$(5591,3703,3,15056,13176,1250,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1251,$(5591,6278,3,15056,13176,1250,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1252,$(5591,4923,7,15072,15100,1253,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1254,$(5591,2092,4,15120,15136,1255,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1256,$(5591,5241,3,15144,11356,1257,0|r,0,0),C(5620,7822,7816,5591,11232,1258,11232,1259,11232,1260,5620,11237,1261),P(5620,2,15156,11280,1262,1263),S(5620,6739,21295,10884,11232,1264,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1265,$(5620,5735,6,15168,15192,1266,0|r,0,0),C(5638,7847,7841,5620,11232,1267,11232,1268,11232,1269,5638,11237,1270),P(5638,2,15200,11280,1271,1272),C(5501,7698,7692,5620,11232,1273,11232,1274,11232,1275,5501,11237,1276),P(5501,2,15208,11280,1277,1278),C(5605,7800,7794,5591,11232,1279,11232,1280,11232,1281,5605,11237,1282),P(5605,2,15216,11280,1283,1284),r=Tt(4),v[r>>2]=20,n=Tt(4),v[n>>2]=20,I(5605,1349,21295,11280,1285,0|r,21295,13176,1286,0|n),r=Tt(4),v[r>>2]=1287,$(5605,2773,2,15224,11280,1655,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1288,$(5605,5735,5,15232,13620,1289,0|r,0,0),C(5469,7652,7646,5591,11232,1290,11232,1291,11232,1292,5469,11237,1293),P(5469,2,15252,11280,1294,1295),S(5469,6739,21295,9268,11232,1264,0,0),r=Tt(4),v[r>>2]=20,n=Tt(4),v[n>>2]=20,I(5469,1290,21295,11280,1296,0|r,21295,13176,1297,0|n),r=Tt(4),v[r>>2]=1298,$(5469,2773,2,15260,11280,1656,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1299,$(5469,5735,8,15280,15312,1300,0|r,0,0),C(5466,7628,7622,5591,11232,1301,11232,1302,11232,1303,5466,11237,1304),P(5466,2,15324,11280,1305,1306),S(5466,6739,21295,9268,11232,1264,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1307,n=Tt(8),v[n+4>>2]=0,v[n>>2]=1308,I(5466,1290,21295,11280,1309,0|r,21295,13176,1310,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1311,$(5466,5735,11,15344,15388,1312,0|r,0,0),C(5435,7602,7596,5643,11232,1313,11232,1314,11232,1315,5435,11237,1316),P(5435,2,15404,11280,1317,1318),r=Tt(4),v[r>>2]=4,n=Tt(4),v[n>>2]=4,I(5435,1290,21289,11280,1319,0|r,21289,13176,1320,0|n),r=Tt(4),v[r>>2]=1321,$(5435,2773,2,15412,11280,1657,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1322,$(5435,2783,2,15420,11280,1323,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1324,$(5435,1631,2,15428,11280,1325,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1326,$(5435,5735,5,15440,15460,1327,0|r,0,0),C(5546,7757,7751,5591,11232,1328,11232,1329,11232,1330,5546,11237,1331),P(5546,2,15468,11280,1332,1333),r=Tt(4),v[r>>2]=20,n=Tt(4),v[n>>2]=20,I(5546,1290,21295,11280,1334,0|r,21295,13176,1335,0|n),r=Tt(4),v[r>>2]=56,n=Tt(4),v[n>>2]=56,I(5546,1824,1860,11280,1336,0|r,1860,13176,1337,0|n),r=Tt(4),v[r>>2]=1338,$(5546,2773,2,15476,11280,1658,0|r,0,0),r=Tt(4),v[r>>2]=1339,$(5546,2893,2,15484,11280,1659,0|r,0,0),r=Tt(4),v[r>>2]=1340,$(5546,5735,5,15504,15460,1660,0|r,0,0),C(5421,7581,7575,5643,11232,1341,11232,1342,11232,1343,5421,11237,1344),P(5421,2,15524,11280,1345,1346),r=Tt(4),v[r>>2]=1347,$(5421,2773,2,15532,11280,1661,0|r,0,0),r=Tt(4),v[r>>2]=1348,$(5421,2519,2,15540,11280,1662,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1349,$(5421,1631,2,15548,11280,1350,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1351,$(5421,5735,4,15568,11264,1352,0|r,0,0),C(5483,7673,7667,5643,11232,1353,11232,1354,11232,1355,5483,11237,1356),P(5483,2,15584,11280,1357,1358),r=Tt(4),v[r>>2]=1359,$(5483,2773,2,15592,11280,1663,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1360,$(5483,1631,2,15600,11280,1361,0|r,0,0),r=Tt(4),v[r>>2]=1362,$(5483,2582,2,15608,11280,1664,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1363,$(5483,5735,5,15616,15460,1364,0|r,0,0),C(5400,7553,7547,5591,11232,1365,11232,1366,11232,1367,5400,11237,1368),P(5400,2,15636,11280,1369,1370),S(5400,6739,21295,9620,11232,1264,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1371,$(5400,5735,9,15648,15684,1372,0|r,0,0),C(5372,7518,7512,5591,11232,1373,11232,1374,11232,1375,5372,11237,1376),P(5372,2,15696,11280,1377,1378),S(5372,6739,21295,10852,11232,1264,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1379,$(5372,5735,8,15712,15312,1380,0|r,0,0),C(5515,7719,7713,5591,11232,1381,11232,1382,11232,1383,5515,11237,1384),P(5515,2,15744,11280,1385,1386),S(5515,6739,21295,10852,11232,1264,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1387,$(5515,5735,5,15760,13620,1388,0|r,0,0),C(5346,7485,7479,5591,11232,1389,11232,1390,11232,1391,5346,11237,1392),P(5346,2,15780,11280,1393,1394),S(5346,6739,21295,9876,11232,1264,0,0),C(21439,21377,21440,0,11232,1395,11235,0,11235,0,1089,11237,1396),P(21439,1,15788,11232,1397,1398),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1399,I(21439,4135,21436,11280,1400,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1401,I(21439,1380,21439,11280,1402,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1403,I(21439,4490,21439,11280,1402,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1404,I(21439,3800,21439,11280,1402,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1405,I(21439,1338,21295,11280,1406,0|r,0,0,0,0),r=Tt(4),v[r>>2]=36,n=Tt(4),v[n>>2]=36,I(21439,3738,21397,11280,1407,0|r,21397,13176,1408,0|n),r=Tt(4),v[r>>2]=37,n=Tt(4),v[n>>2]=37,I(21439,2402,21397,11280,1407,0|r,21397,13176,1408,0|n),r=Tt(4),v[r>>2]=40,n=Tt(4),v[n>>2]=40,I(21439,6172,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(4),v[r>>2]=44,n=Tt(4),v[n>>2]=44,I(21439,6187,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(4),v[r>>2]=48,n=Tt(4),v[n>>2]=48,I(21439,6207,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(4),v[r>>2]=52,n=Tt(4),v[n>>2]=52,I(21439,1513,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(4),v[r>>2]=56,n=Tt(4),v[n>>2]=56,I(21439,6125,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1411,n=Tt(8),v[n+4>>2]=0,v[n>>2]=1412,I(21439,1452,21294,13001,1413,0|r,21294,13005,1414,0|n),r=Tt(4),v[r>>2]=68,n=Tt(4),v[n>>2]=68,I(21439,1196,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(4),v[r>>2]=72,n=Tt(4),v[n>>2]=72,I(21439,5725,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(4),v[r>>2]=84,n=Tt(4),v[n>>2]=84,I(21439,6138,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(4),v[r>>2]=88,n=Tt(4),v[n>>2]=88,I(21439,5846,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(4),v[r>>2]=92,n=Tt(4),v[n>>2]=92,I(21439,6497,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(4),v[r>>2]=96,n=Tt(4),v[n>>2]=96,I(21439,5664,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(4),v[r>>2]=100,n=Tt(4),v[n>>2]=100,I(21439,4123,21294,13001,1409,0|r,21294,13005,1410,0|n),r=Tt(4),v[r>>2]=112,n=Tt(4),v[n>>2]=112,I(21439,6092,21275,11280,1415,0|r,21275,13176,1416,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1417,$(21439,5708,2,15792,13001,1418,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1419,$(21439,5041,2,15800,11280,1420,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1421,$(21439,2600,2,15808,13528,1422,0|r,0,0),C(21441,21442,21443,0,11232,1423,11235,0,11235,0,6460,11237,1424),P(21441,2,15816,11280,1425,1426),r=Tt(4),v[r>>2]=8,n=Tt(4),v[n>>2]=8,I(21441,1218,21294,13001,1427,0|r,21294,13005,1428,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1429,I(21441,6434,21433,11280,1430,0|r,0,0,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1431,$(21441,1229,5,15824,15844,1432,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1433,$(21441,4597,5,15856,15844,1434,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1435,$(21441,1236,4,15888,15904,1436,0|r,0,0),C(21444,21445,21446,0,11232,1437,11235,0,11235,0,5062,11237,1438),P(21444,2,15912,11280,1439,1440),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1441,I(21444,6325,21441,11280,1442,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1443,$(21444,2683,2,15920,11280,1665,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1444,n=Tt(8),v[n+4>>2]=0,v[n>>2]=1445,I(21444,5846,21294,13001,1446,0|r,21294,13005,1447,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1448,$(21444,5087,3,15928,13005,1449,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1450,$(21444,1181,3,15940,11296,1451,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1452,$(21444,2693,2,15952,13528,1453,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1454,$(21444,4586,3,15960,13176,1455,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1456,$(21444,4181,5,15984,13860,1457,0|r,0,0),r=Tt(4),v[r>>2]=1458,$(21444,4608,5,16016,13860,1666,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1459,$(21444,4224,6,16048,16072,1460,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1461,$(21444,4625,6,16080,16072,1462,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1463,$(21444,4145,4,16112,15008,1464,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1465,$(21444,4163,5,16128,16148,1466,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1467,$(21444,2635,3,15928,13005,1449,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1468,$(21444,1806,3,16156,11296,1469,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1470,$(21444,5016,2,15952,13528,1453,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1471,$(21444,5029,2,15952,13528,1453,0|r,0,0),C(21403,21447,21448,0,11232,1472,11235,0,11235,0,3878,11237,1473),P(21403,2,16168,11280,1474,1475),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1476,I(21403,6325,21433,11280,1477,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1478,$(21403,2751,2,16176,11280,1667,0|r,0,0),Kf(2428,1479),Kf(3620,1480),r=Tt(4),v[r>>2]=1481,$(21403,2476,2,16192,11280,1669,0|r,0,0),r=Tt(4),v[r>>2]=1482,$(21403,2452,2,16200,11280,1670,0|r,0,0),r=Tt(4),v[r>>2]=1483,$(21403,2493,2,16208,11280,1671,0|r,0,0),r=Tt(4),v[r>>2]=1484,$(21403,1392,2,16216,11280,1672,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1485,I(21403,4352,21409,11280,1486,0|r,0,0,0,0),r=Tt(4),v[r>>2]=1487,I(21403,3491,21391,11280,1488,0|r,0,0,0,0),r=Tt(4),v[r>>2]=160,n=Tt(4),v[n>>2]=160,I(21403,5659,21294,13001,1489,0|r,21294,13005,1490,0|n),r=Tt(4),v[r>>2]=164,n=Tt(4),v[n>>2]=164,I(21403,6697,21294,13001,1489,0|r,21294,13005,1490,0|n),r=Tt(4),v[r>>2]=168,n=Tt(4),v[n>>2]=168,I(21403,6604,21294,13001,1489,0|r,21294,13005,1490,0|n),r=Tt(4),v[r>>2]=172,n=Tt(4),v[n>>2]=172,I(21403,1374,21294,13001,1489,0|r,21294,13005,1490,0|n),r=Tt(4),v[r>>2]=176,n=Tt(4),v[n>>2]=176,I(21403,1200,21294,13001,1489,0|r,21294,13005,1490,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1491,$(21403,5864,2,16224,13528,1492,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1493,$(21403,4444,2,16224,13528,1492,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1494,$(21403,5101,2,16224,13528,1492,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1495,$(21403,5136,2,16224,13528,1492,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1496,$(21403,5116,2,16224,13528,1492,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1497,$(21403,5319,2,16232,11280,1498,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1499,$(21403,5331,3,16240,11296,1500,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1501,$(21403,1359,3,16252,11296,1502,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1503,$(21403,1588,3,16264,11296,1504,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1505,$(21403,1300,3,16252,11296,1502,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1506,$(21403,5769,3,16276,13176,1507,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1508,$(21403,4378,3,16288,13176,1509,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1510,$(21403,5749,4,16304,13776,1511,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1512,$(21403,1910,4,16320,13776,1513,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1514,$(21403,1896,4,16336,11264,1515,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1516,$(21403,1707,3,16352,11296,1517,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1518,$(21403,1683,3,16364,11296,1519,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1520,$(21403,1724,3,16376,11296,1521,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1522,$(21403,5087,3,16388,13005,1523,0|r,0,0),C(2278,7328,7322,0,11232,1524,11235,0,11235,0,2278,11237,1525),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(2278,4403,3,16400,13176,1526,0|r,1,0),r=Tt(4),v[r>>2]=1527,$(2278,4434,4,16416,16432,1673,0|r,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=20,$(2278,6113,2,16440,13528,1528,0|r,1,0),C(2254,7277,7271,2278,11232,1529,11232,1530,11232,1531,2294,11237,1532),P(2254,3,16448,12996,1533,1534),r=Tt(4),v[r>>2]=4,n=Tt(4),v[n>>2]=4,I(2254,6654,21294,13001,1535,0|r,21294,13005,1536,0|n),r=Tt(4),v[r>>2]=8,n=Tt(4),v[n>>2]=8,I(2254,6548,21294,13001,1535,0|r,21294,13005,1536,0|n),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(2254,4403,3,16460,13176,1537,0|r,0,0),r=Tt(4),v[r>>2]=1538,$(2254,4434,4,16416,16432,1673,0|r,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=20,$(2254,6113,2,16472,13528,1539,0|r,0,0),C(2273,7303,7297,2278,11232,1540,11232,1541,11232,1542,2322,11237,1543),P(2273,3,16480,13372,1544,1545),r=Tt(8),v[r+4>>2]=1,v[r>>2]=12,$(2273,4403,3,16492,13176,1546,0|r,0,0),r=Tt(4),v[r>>2]=1547,$(2273,4434,4,16416,16432,1673,0|r,0,0),r=Tt(8),v[r+4>>2]=1,v[r>>2]=20,$(2273,6113,2,16504,13528,1548,0|r,0,0),r=Tt(4),v[r>>2]=4,n=Tt(4),v[n>>2]=4,I(2273,6662,21294,13001,1549,0|r,21294,13005,1550,0|n),r=Tt(4),v[r>>2]=8,n=Tt(4),v[n>>2]=8,I(2273,6556,21294,13001,1549,0|r,21294,13005,1550,0|n),r=Tt(4),v[r>>2]=12,n=Tt(4),v[n>>2]=12,I(2273,2415,21294,13001,1549,0|r,21294,13005,1550,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1551,n=Tt(8),v[n+4>>2]=0,v[n>>2]=1552,I(2273,5791,21294,13001,1553,0|r,21294,13005,1554,0|n),r=Tt(4),v[r>>2]=20,n=Tt(4),v[n>>2]=20,I(2273,6732,21294,13001,1549,0|r,21294,13005,1550,0|n),r=Tt(4),v[r>>2]=24,n=Tt(4),v[n>>2]=24,I(2273,6639,21294,13001,1549,0|r,21294,13005,1550,0|n),C(21449,21450,21451,0,11232,1555,11235,0,11235,0,4525,11237,1556),r=Tt(4),v[r>>2]=16,n=Tt(4),v[n>>2]=16,I(21449,1617,21305,11280,1557,0|r,21305,13176,1558,0|n),r=Tt(4),v[r>>2]=20,n=Tt(4),v[n>>2]=20,I(21449,1624,21305,11280,1557,0|r,21305,13176,1558,0|n),r=Tt(4),v[r>>2]=24,n=Tt(4),v[n>>2]=24,I(21449,3093,21305,11280,1557,0|r,21305,13176,1558,0|n),r=Tt(4),v[r>>2]=28,n=Tt(4),v[n>>2]=28,I(21449,3249,21305,11280,1557,0|r,21305,13176,1558,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1559,$(21449,6409,2,16512,11280,1560,0|r,0,0),C(21290,21452,21453,0,11232,1561,11235,0,11235,0,5293,11237,1562),r=Tt(4),v[r>>2]=0,n=Tt(4),v[n>>2]=0,I(21290,5177,21305,11280,1563,0|r,21305,13176,1564,0|n),r=Tt(4),v[r>>2]=4,n=Tt(4),v[n>>2]=4,I(21290,2190,21305,11280,1563,0|r,21305,13176,1564,0|n),r=Tt(4),v[r>>2]=8,n=Tt(4),v[n>>2]=8,I(21290,1617,21305,11280,1563,0|r,21305,13176,1564,0|n);r=Tt(4),v[r>>2]=12,n=Tt(4),v[n>>2]=12,I(21290,2208,21305,11280,1563,0|r,21305,13176,1564,0|n),r=Tt(4),v[r>>2]=16,n=Tt(4),v[n>>2]=16,I(21290,1624,21305,11280,1563,0|r,21305,13176,1564,0|n),C(21454,21455,21456,0,11232,1565,11235,0,11235,0,6061,11237,1566),P(21454,1,16520,11232,1567,1568),r=Tt(4),v[r>>2]=0,n=Tt(4),v[n>>2]=0,I(21454,5856,21397,11280,1569,0|r,21397,13176,1570,0|n),r=Tt(4),v[r>>2]=4,n=Tt(4),v[n>>2]=4,I(21454,5094,21294,13001,1571,0|r,21294,13005,1572,0|n),r=Tt(4),v[r>>2]=1,n=Tt(4),v[n>>2]=1,I(21454,5814,21397,11280,1569,0|r,21397,13176,1570,0|n),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1573,$(21454,3818,3,16524,11296,1574,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1575,$(21454,4181,5,16544,16564,1576,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1577,$(21454,4378,3,16572,13176,1578,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1579,$(21454,4194,3,16584,13005,1580,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1581,$(21454,6417,2,16596,11280,1582,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1583,$(21454,6518,3,16604,13176,1584,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1585,$(21454,1756,3,16604,13176,1584,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1586,$(21454,3497,6,16624,16648,1587,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1588,$(21454,2291,3,16656,13176,1589,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1590,$(21454,2319,3,16668,13176,1591,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1592,$(21454,2307,2,16680,13528,1593,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1594,$(21454,5059,2,16688,11280,1595,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1596,$(21454,1229,5,16704,15844,1597,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1598,$(21454,3588,3,16724,13176,1599,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1600,$(21454,3566,4,16736,11264,1601,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1602,$(21454,5961,3,16604,13176,1584,0|r,0,0),r=Tt(4),v[r>>2]=1603,$(21454,2730,2,16752,11280,1674,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1604,$(21454,4305,6,16768,13736,1605,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1606,$(21454,1156,2,16680,13528,1593,0|r,0,0),r=Tt(8),v[r+4>>2]=0,v[r>>2]=1607,$(21454,5162,4,16800,11264,1608,0|r,0,0)}function Y(r){var n,e=0,i=0,f=0,t=0,u=0,o=0,a=0,c=0,b=0,k=0,s=0,h=0;D=n=D-16|0;r:{n:{e:{i:{f:{t:{u:{o:{a:{c:{b:{k:{s:{v:{if((r|=0)>>>0<=244){if(3&(e=(o=v[5374])>>>(i=(a=r>>>0<11?16:r+11&-8)>>>3|0)|0)){e=21536+(r=(i=i+(1&~e)|0)<<3)|0,f=v[r+21544>>2],(0|e)!=(0|(r=v[f+8>>2]))?(v[r+12>>2]=e,v[e+8>>2]=r):(s=21496,h=Qf(i)&o,v[s>>2]=h),r=f+8|0,e=i<<3,v[f+4>>2]=3|e,v[4+(e=e+f|0)>>2]=1|v[e+4>>2];break r}if((k=v[5376])>>>0>=a>>>0)break v;if(e){e=21536+(r=(f=qt(0-(r=(0-(r=2<<i)|r)&e<<i)&r))<<3)|0,t=v[r+21544>>2],(0|e)!=(0|(r=v[t+8>>2]))?(v[r+12>>2]=e,v[e+8>>2]=r):(o=Qf(f)&o,v[5374]=o),v[t+4>>2]=3|a,f=(r=f<<3)-a|0,v[4+(i=t+a|0)>>2]=1|f,v[r+t>>2]=f,k&&(e=21536+(-8&k)|0,u=v[5379],(r=1<<(k>>>3))&o?r=v[e+8>>2]:(v[5374]=r|o,r=e),v[e+8>>2]=u,v[r+12>>2]=u,v[u+12>>2]=e,v[u+8>>2]=r),r=t+8|0,v[5379]=i,v[5376]=f;break r}if(!(b=v[5375]))break v;for(i=v[21800+(qt(0-b&b)<<2)>>2],u=(-8&v[i+4>>2])-a|0,e=i;(r=v[e+16>>2])||(r=v[e+20>>2]);)u=(f=(e=(-8&v[r+4>>2])-a|0)>>>0<u>>>0)?e:u,i=f?r:i,e=r;if(c=v[i+24>>2],(0|(f=v[i+12>>2]))!=(0|i)){r=v[i+8>>2],v[r+12>>2]=f,v[f+8>>2]=r;break n}if(!(r=v[(e=i+20|0)>>2])){if(!(r=v[i+16>>2]))break s;e=i+16|0}for(;t=e,f=r,(r=v[(e=r+20|0)>>2])||(e=f+16|0,r=v[f+16>>2]););v[t>>2]=0;break n}if(a=-1,!(r>>>0>4294967231)&&(a=-8&(r=r+11|0),b=v[5375])){u=0-a|0,o=0,a>>>0<256||(o=31,a>>>0>16777215||(o=62+((a>>>38-(r=F(r>>>8|0))&1)-(r<<1)|0)|0));l:{h:{if(e=v[21800+(o<<2)>>2])for(r=0,i=a<<(31!=(0|o)?25-(o>>>1|0):0);;){if(!((t=(-8&v[e+4>>2])-a|0)>>>0>=u>>>0||(f=e,u=t,t))){u=0,r=e;break h}if(t=v[e+20>>2],e=v[16+((i>>>29&4)+e|0)>>2],r=t?(0|t)==(0|e)?r:t:r,i<<=1,!e)break}else r=0;if(!(r|f)){if(f=0,!(r=(0-(r=2<<o)|r)&b))break v;r=v[21800+(qt(r&0-r)<<2)>>2]}if(!r)break l}for(;u=(i=(e=(-8&v[r+4>>2])-a|0)>>>0<u>>>0)?e:u,f=i?r:f,r=(e=v[r+16>>2])||v[r+20>>2];);}if(!(!f|v[5376]-a>>>0<=u>>>0)){if(o=v[f+24>>2],(0|f)!=(0|(i=v[f+12>>2]))){r=v[f+8>>2],v[r+12>>2]=i,v[i+8>>2]=r;break e}if(!(r=v[(e=f+20|0)>>2])){if(!(r=v[f+16>>2]))break k;e=f+16|0}for(;t=e,i=r,(r=v[(e=r+20|0)>>2])||(e=i+16|0,r=v[i+16>>2]););v[t>>2]=0;break e}}}if((r=v[5376])>>>0>=a>>>0){f=v[5379],(e=r-a|0)>>>0>=16?(v[4+(i=f+a|0)>>2]=1|e,v[r+f>>2]=e,v[f+4>>2]=3|a):(v[f+4>>2]=3|r,v[4+(r=r+f|0)>>2]=1|v[r+4>>2],i=0,e=0),v[5376]=e,v[5379]=i,r=f+8|0;break r}if((c=v[5377])>>>0>a>>>0){e=c-a|0,v[5377]=e,r=(i=v[5380])+a|0,v[5380]=r,v[r+4>>2]=1|e,v[i+4>>2]=3|a,r=i+8|0;break r}if(r=0,b=a+47|0,v[5492]?i=v[5494]:(v[5495]=-1,v[5496]=-1,v[5493]=4096,v[5494]=4096,v[5492]=n+12&-16^1431655768,v[5497]=0,v[5485]=0,i=4096),(e=(t=b+i|0)&(u=0-i|0))>>>0<=a>>>0)break r;if((f=v[5484])&&f>>>0<(o=(i=v[5482])+e|0)>>>0|i>>>0>=o>>>0)break r;v:{if(!(4&l[21940])){l:{h:{d:{p:{if(f=v[5380])for(r=21944;;){if((i=v[r>>2])>>>0<=f>>>0&f>>>0<i+v[r+4>>2]>>>0)break p;if(!(r=v[r+8>>2]))break}if(-1==(0|(i=di(0))))break l;if(o=e,(r=(f=v[5493])-1|0)&i&&(o=(e-i|0)+(r+i&0-f)|0),o>>>0<=a>>>0)break l;if((f=v[5484])&&f>>>0<(u=(r=v[5482])+o|0)>>>0|r>>>0>=u>>>0)break l;if((0|i)!=(0|(r=di(o))))break d;break v}if((0|(i=di(o=u&t-c)))==(v[r>>2]+v[r+4>>2]|0))break h;r=i}if(-1==(0|r))break l;if(a+48>>>0<=o>>>0){i=r;break v}if(-1==(0|di(i=(i=v[5494])+(b-o|0)&0-i)))break l;o=i+o|0,i=r;break v}if(-1!=(0|i))break v}v[5485]=4|v[5485]}if(-1==(0|(i=di(e)))|-1==(0|(r=di(0)))|r>>>0<=i>>>0)break a;if((o=r-i|0)>>>0<=a+40>>>0)break a}r=v[5482]+o|0,v[5482]=r,r>>>0>d[5483]&&(v[5483]=r);v:{if(t=v[5380]){for(r=21944;;){if(((f=v[r>>2])+(e=v[r+4>>2])|0)==(0|i))break v;if(!(r=v[r+8>>2]))break}break b}for((r=v[5378])>>>0<=i>>>0&&r||(v[5378]=i),r=0,v[5487]=o,v[5486]=i,v[5382]=-1,v[5383]=v[5492],v[5489]=0;e=21536+(f=r<<3)|0,v[f+21544>>2]=e,v[f+21548>>2]=e,32!=(0|(r=r+1|0)););e=(f=o-40|0)-(r=i+8&7?-8-i&7:0)|0,v[5377]=e,r=r+i|0,v[5380]=r,v[r+4>>2]=1|e,v[4+(i+f|0)>>2]=40,v[5381]=v[5496];break c}if(8&v[r+12>>2]|i>>>0<=t>>>0|f>>>0>t>>>0)break b;v[r+4>>2]=e+o,i=(r=t+8&7?-8-t&7:0)+t|0,v[5380]=i,r=(e=v[5377]+o|0)-r|0,v[5377]=r,v[i+4>>2]=1|r,v[4+(e+t|0)>>2]=40,v[5381]=v[5496];break c}f=0;break n}i=0;break e}d[5378]>i>>>0&&(v[5378]=i),e=i+o|0,r=21944;b:{k:{s:{for(;;){if((0|e)!=v[r>>2]){if(r=v[r+8>>2])continue;break s}break}if(!(8&l[r+12|0]))break k}for(r=21944;;){if((e=v[r>>2])>>>0<=t>>>0&&(u=e+v[r+4>>2]|0)>>>0>t>>>0)break b;r=v[r+8>>2]}}if(v[r>>2]=i,v[r+4>>2]=v[r+4>>2]+o,v[4+(b=(i+8&7?-8-i&7:0)+i|0)>>2]=3|a,r=(o=e+(e+8&7?-8-e&7:0)|0)-(c=a+b|0)|0,(0|t)==(0|o)){v[5380]=c,r=v[5377]+r|0,v[5377]=r,v[c+4>>2]=1|r;break i}if(v[5379]==(0|o)){v[5379]=c,r=v[5376]+r|0,v[5376]=r,v[c+4>>2]=1|r,v[r+c>>2]=r;break i}if(1!=(3&(u=v[o+4>>2])))break f;if(t=-8&u,u>>>0<=255){if((0|(i=v[o+12>>2]))==(0|(e=v[o+8>>2]))){s=21496,h=v[5374]&Qf(u>>>3|0),v[s>>2]=h;break t}v[e+12>>2]=i,v[i+8>>2]=e;break t}if(a=v[o+24>>2],(0|o)!=(0|(i=v[o+12>>2]))){e=v[o+8>>2],v[e+12>>2]=i,v[i+8>>2]=e;break u}if(!(u=v[(e=o+20|0)>>2])){if(!(u=v[o+16>>2]))break o;e=o+16|0}for(;f=e,(u=v[(e=(i=u)+20|0)>>2])||(e=i+16|0,u=v[i+16>>2]););v[f>>2]=0;break u}for(e=(f=o-40|0)-(r=i+8&7?-8-i&7:0)|0,v[5377]=e,r=r+i|0,v[5380]=r,v[r+4>>2]=1|e,v[4+(i+f|0)>>2]=40,v[5381]=v[5496],v[(f=(r=(u+(u-39&7?39-u&7:0)|0)-47|0)>>>0<t+16>>>0?t:r)+4>>2]=27,r=v[5489],v[f+16>>2]=v[5488],v[f+20>>2]=r,r=v[5487],v[f+8>>2]=v[5486],v[f+12>>2]=r,v[5488]=f+8,v[5487]=o,v[5486]=i,v[5489]=0,r=f+24|0;v[r+4>>2]=7,e=r+8|0,r=r+4|0,e>>>0<u>>>0;);if((0|f)!=(0|t))if(v[f+4>>2]=-2&v[f+4>>2],u=f-t|0,v[t+4>>2]=1|u,v[f>>2]=u,u>>>0<=255)e=21536+(-8&u)|0,(i=v[5374])&(r=1<<(u>>>3))?r=v[e+8>>2]:(v[5374]=r|i,r=e),v[e+8>>2]=t,v[r+12>>2]=t,v[t+12>>2]=e,v[t+8>>2]=r;else{r=31,u>>>0<=16777215&&(r=62+((u>>>38-(r=F(u>>>8|0))&1)-(r<<1)|0)|0),v[t+28>>2]=r,v[t+16>>2]=0,v[t+20>>2]=0,e=21800+(r<<2)|0;b:{if((f=v[5375])&(i=1<<r)){for(r=u<<(31!=(0|r)?25-(r>>>1|0):0),f=v[e>>2];;){if((0|u)==(-8&v[(e=f)+4>>2]))break b;if(i=r>>>29|0,r<<=1,!(f=v[16+(i=(4&i)+e|0)>>2]))break}v[i+16>>2]=t}else v[5375]=i|f,v[e>>2]=t;v[t+24>>2]=e,v[t+12>>2]=t,v[t+8>>2]=t;break c}r=v[e+8>>2],v[r+12>>2]=t,v[e+8>>2]=t,v[t+24>>2]=0,v[t+12>>2]=e,v[t+8>>2]=r}}if(!((r=v[5377])>>>0<=a>>>0)){e=r-a|0,v[5377]=e,r=(i=v[5380])+a|0,v[5380]=r,v[r+4>>2]=1|e,v[i+4>>2]=3|a,r=i+8|0;break r}}v[5373]=48,r=0;break r}i=0}if(a){f=v[o+28>>2];u:{if(v[(e=21800+(f<<2)|0)>>2]==(0|o)){if(v[e>>2]=i,i)break u;s=21500,h=v[5375]&Qf(f),v[s>>2]=h;break t}if(v[a+(v[a+16>>2]==(0|o)?16:20)>>2]=i,!i)break t}v[i+24>>2]=a,(e=v[o+16>>2])&&(v[i+16>>2]=e,v[e+24>>2]=i),(e=v[o+20>>2])&&(v[i+20>>2]=e,v[e+24>>2]=i)}}r=r+t|0,u=v[4+(o=t+o|0)>>2]}if(v[o+4>>2]=-2&u,v[c+4>>2]=1|r,v[r+c>>2]=r,r>>>0<=255)e=21536+(-8&r)|0,(i=v[5374])&(r=1<<(r>>>3))?r=v[e+8>>2]:(v[5374]=r|i,r=e),v[e+8>>2]=c,v[r+12>>2]=c,v[c+12>>2]=e,v[c+8>>2]=r;else{u=31,r>>>0<=16777215&&(u=62+((r>>>38-(e=F(r>>>8|0))&1)-(e<<1)|0)|0),v[c+28>>2]=u,v[c+16>>2]=0,v[c+20>>2]=0,e=21800+(u<<2)|0;f:{if((f=v[5375])&(i=1<<u)){for(u=r<<(31!=(0|u)?25-(u>>>1|0):0),i=v[e>>2];;){if(e=i,(-8&v[i+4>>2])==(0|r))break f;if(i=u>>>29|0,u<<=1,!(i=v[16+(f=(4&i)+e|0)>>2]))break}v[f+16>>2]=c}else v[5375]=i|f,v[e>>2]=c;v[c+24>>2]=e,v[c+12>>2]=c,v[c+8>>2]=c;break i}r=v[e+8>>2],v[r+12>>2]=c,v[e+8>>2]=c,v[c+24>>2]=0,v[c+12>>2]=e,v[c+8>>2]=r}}r=b+8|0;break r}e:if(o){e=v[f+28>>2];i:{if(v[(r=21800+(e<<2)|0)>>2]==(0|f)){if(v[r>>2]=i,i)break i;b=Qf(e)&b,v[5375]=b;break e}if(v[o+(v[o+16>>2]==(0|f)?16:20)>>2]=i,!i)break e}v[i+24>>2]=o,(r=v[f+16>>2])&&(v[i+16>>2]=r,v[r+24>>2]=i),(r=v[f+20>>2])&&(v[i+20>>2]=r,v[r+24>>2]=i)}e:if(u>>>0<=15)r=u+a|0,v[f+4>>2]=3|r,v[4+(r=r+f|0)>>2]=1|v[r+4>>2];else if(v[f+4>>2]=3|a,v[4+(t=f+a|0)>>2]=1|u,v[t+u>>2]=u,u>>>0<=255)e=21536+(-8&u)|0,(i=v[5374])&(r=1<<(u>>>3))?r=v[e+8>>2]:(v[5374]=r|i,r=e),v[e+8>>2]=t,v[r+12>>2]=t,v[t+12>>2]=e,v[t+8>>2]=r;else{r=31,u>>>0<=16777215&&(r=62+((u>>>38-(r=F(u>>>8|0))&1)-(r<<1)|0)|0),v[t+28>>2]=r,v[t+16>>2]=0,v[t+20>>2]=0,e=21800+(r<<2)|0;i:{if((i=1<<r)&b){for(r=u<<(31!=(0|r)?25-(r>>>1|0):0),a=v[e>>2];;){if((-8&v[(e=a)+4>>2])==(0|u))break i;if(i=r>>>29|0,r<<=1,!(a=v[16+(i=(4&i)+e|0)>>2]))break}v[i+16>>2]=t}else v[5375]=i|b,v[e>>2]=t;v[t+24>>2]=e,v[t+12>>2]=t,v[t+8>>2]=t;break e}r=v[e+8>>2],v[r+12>>2]=t,v[e+8>>2]=t,v[t+24>>2]=0,v[t+12>>2]=e,v[t+8>>2]=r}r=f+8|0;break r}n:if(c){e=v[i+28>>2];e:{if(v[(r=21800+(e<<2)|0)>>2]==(0|i)){if(v[r>>2]=f,f)break e;s=21500,h=Qf(e)&b,v[s>>2]=h;break n}if(v[c+(v[c+16>>2]==(0|i)?16:20)>>2]=f,!f)break n}v[f+24>>2]=c,(r=v[i+16>>2])&&(v[f+16>>2]=r,v[r+24>>2]=f),(r=v[i+20>>2])&&(v[f+20>>2]=r,v[r+24>>2]=f)}u>>>0<=15?(r=u+a|0,v[i+4>>2]=3|r,v[4+(r=r+i|0)>>2]=1|v[r+4>>2]):(v[i+4>>2]=3|a,v[4+(f=i+a|0)>>2]=1|u,v[f+u>>2]=u,k&&(e=21536+(-8&k)|0,t=v[5379],(r=1<<(k>>>3))&o?r=v[e+8>>2]:(v[5374]=r|o,r=e),v[e+8>>2]=t,v[r+12>>2]=t,v[t+12>>2]=e,v[t+8>>2]=r),v[5379]=f,v[5376]=u),r=i+8|0}return D=n+16|0,0|r}function X(r,n,e,i,t){var o,a=0,c=0,b=0,s=0,h=0,d=w(0),y=0,m=0,g=0,F=0,A=0,T=0,$=0,I=0,C=0,P=0,E=0,O=0,R=0,S=0,W=0,G=0,U=0,j=0,H=0,L=0,M=0,_=0,z=0,x=0,J=0,K=0,B=0,N=0,q=0,V=0,Z=0,Y=0,X=0,Q=0,rr=0,nr=0,er=0;D=o=D-32|0;r:{n:{e:{if(e){if($=Wn(n,1))break e;break r}if(y=ge(a=Xf(68),e=gu(c=o+20|0,e=(e=Wn(n,1))?v[(v[i+200>>2]+(e<<2)|0)-4>>2]:0,0)),lf(e),(0|(a=Wn(n,1)))>0)for(c=y+36|0,e=0;s=Wn(n,1),Sn(c,v[i+28>>2]+(s<<2)|0),(0|a)!=(0|(e=e+1|0)););if((0|(a=Wn(n,1)))>0)for(c=y+52|0,e=0;s=Wn(n,1),v[o+20>>2]=v[v[i+112>>2]+(s<<2)>>2],Sn(c,o+20|0),(0|a)!=(0|(e=e+1|0)););if((0|(a=Wn(n,1)))>0)for(c=y+52|0,e=0;s=Wn(n,1),v[o+20>>2]=v[v[i+128>>2]+(s<<2)>>2],Sn(c,o+20|0),(0|a)!=(0|(e=e+1|0)););if((0|(a=Wn(n,1)))>0)for(c=y+52|0,e=0;s=Wn(n,1),v[o+20>>2]=v[v[i+144>>2]+(s<<2)>>2],Sn(c,o+20|0),(0|a)!=(0|(e=e+1|0)););$=Wn(n,1);break n}y=ge(a=Xf(68),e=gu(o+20|0,2138,0)),lf(e)}if(!((0|$)<=0))for(;;){if(P=Wn(n,1),I=0,(0|(E=Wn(n,1)))>0)for(;;){a=C=gu(a=o+20|0,e=(e=Wn(n,1))?v[(v[i+200>>2]+(e<<2)|0)-4>>2]:0,0),D=s=D-80|0,m=gu(c=s+68|0,e=(e=Wn(n,1))?v[(v[i+200>>2]+(e<<2)|0)-4>>2]:0,0),v[m+4>>2]||ee(m,a),a=v[n+4>>2],v[n+4>>2]=a+1,e=0;n:{e:switch(l[0|a]){case 0:c=gu(a=s+56|0,e=(e=Wn(n,1))?v[(v[i+200>>2]+(e<<2)|0)-4>>2]:0,0),v[c+4>>2]||ee(c,m),a=v[n+4>>2],v[n+4>>2]=a+1,h=l[0|a],v[n+4>>2]=a+2,b=l[a+1|0],v[n+4>>2]=a+3,F=l[a+2|0],v[n+4>>2]=a+4,g=l[a+3|0],v[n+4>>2]=a+5,A=l[a+4|0],v[n+4>>2]=a+6,T=l[a+5|0],v[n+4>>2]=a+7,W=l[a+6|0],v[n+4>>2]=a+8,G=l[a+7|0],v[n+4>>2]=a+9,U=l[a+8|0],v[n+4>>2]=a+10,j=l[a+9|0],v[n+4>>2]=a+11,H=l[a+10|0],v[n+4>>2]=a+12,L=l[a+11|0],v[n+4>>2]=a+13,M=l[a+12|0],v[n+4>>2]=a+14,_=l[a+13|0],v[n+4>>2]=a+15,z=l[a+14|0],v[n+4>>2]=a+16,x=l[a+15|0],v[n+4>>2]=a+17,J=l[a+16|0],v[n+4>>2]=a+18,K=l[a+17|0],v[n+4>>2]=a+19,B=l[a+18|0],v[n+4>>2]=a+20,N=l[a+19|0],v[n+4>>2]=a+21,q=l[a+20|0],v[n+4>>2]=a+22,V=l[a+21|0],v[n+4>>2]=a+23,Z=l[a+22|0],v[n+4>>2]=a+24,Y=l[a+23|0],v[n+4>>2]=a+25,X=l[a+24|0],v[n+4>>2]=a+26,Q=l[a+25|0],v[n+4>>2]=a+27,rr=l[a+26|0],e=a+28|0,v[n+4>>2]=e,a=l[a+27|0],l[21076]||(v[5265]=0,v[5266]=0,v[5267]=0,v[5268]=0,v[5264]=9308,f[21076]=1,e=v[n+4>>2]),v[n+4>>2]=e+1,p[5265]=w(l[0|e])/w(255),v[n+4>>2]=e+2,p[5266]=w(l[e+1|0])/w(255),v[n+4>>2]=e+3,p[5267]=w(l[e+2|0])/w(255),v[n+4>>2]=e+4,p[5268]=w(l[e+3|0])/w(255),e=v[r+4>>2],O=Re(s+44|0,m),R=Re(s+32|0,c),e=0|da[v[v[e>>2]+12>>2]](e,y,O,R),lf(R),lf(O),e&&(ee(e+116|0,c),v[e+40>>2]=g|(F|b<<8|h<<16)<<8,d=p[r+36>>2],v[e+48>>2]=(K<<8|J<<16|B)<<8|N,v[e+44>>2]=(_<<8|M<<16|z)<<8|x,p[e+56>>2]=d*(u(2,a|(Q<<8|X<<16|rr)<<8),k()),p[e+52>>2]=d*(u(2,(V<<8|q<<16|Z)<<8|Y),k()),p[e+36>>2]=d*(u(2,(j<<8|U<<16|H)<<8|L),k()),p[e+32>>2]=d*(u(2,(T<<8|A<<16|W)<<8|G),k()),a=v[5266],v[e+148>>2]=v[5265],v[e+152>>2]=a,a=v[5268],v[e+156>>2]=v[5267],v[e+160>>2]=a,zr(e),a=v[r+4>>2],da[v[v[a>>2]+36>>2]](a,e)),lf(c);break n;case 1:a=Wn(n,1),e=v[r+4>>2],c=Re(s+56|0,m),e=0|da[v[v[e>>2]+20>>2]](e,y,c),lf(c),$r(r,n,e,a),t&&(v[n+4>>2]=v[n+4>>2]+4),a=v[r+4>>2],da[v[v[a>>2]+36>>2]](a,e);break n;case 2:h=gu(a=s+56|0,e=(e=Wn(n,1))?v[(v[i+200>>2]+(e<<2)|0)-4>>2]:0,0),v[h+4>>2]||ee(h,m),e=v[r+4>>2],c=Re(s+44|0,m),b=Re(s+32|0,h),a=0|da[v[v[e>>2]+16>>2]](e,y,c,b),lf(b),lf(c),e=a,a||(e=Mr(Xf(236),m)),ee(e+168|0,h),b=ra(e),c=v[n+4>>2],v[n+4>>2]=c+1,p[b+4>>2]=w(l[0|c])/w(255),v[n+4>>2]=c+2,p[b+8>>2]=w(l[c+1|0])/w(255),v[n+4>>2]=c+3,p[b+12>>2]=w(l[c+2|0])/w(255),v[n+4>>2]=c+4,p[b+16>>2]=w(l[c+3|0])/w(255),Nr(n,(c=Wn(n,1))<<1,w(1),e+120|0),re(n,e+136|0),$r(r,n,e,c),sr(e),nr=e,er=Wn(n,1)<<1,v[nr+224>>2]=er,t?(re(n,e+152|0),c=v[n+4>>2],v[n+4>>2]=c+1,b=l[0|c],v[n+4>>2]=c+2,F=l[c+1|0],v[n+4>>2]=c+3,g=l[c+2|0],v[n+4>>2]=c+4,d=p[r+36>>2],p[e+196>>2]=d*(u(2,l[c+3|0]|(g|F<<8|b<<16)<<8),k()),v[n+4>>2]=c+5,b=l[c+4|0],v[n+4>>2]=c+6,F=l[c+5|0],v[n+4>>2]=c+7,g=l[c+6|0],v[n+4>>2]=c+8,d=w(d*(u(2,l[c+7|0]|(g|F<<8|b<<16)<<8),k()))):(v[e+196>>2]=0,d=w(0)),p[e+200>>2]=d,a?(a=v[r+4>>2],da[v[v[a>>2]+36>>2]](a,e)):(da[v[v[e>>2]+4>>2]](e),e=0),lf(h);break n;case 3:h=gu(a=s+56|0,e=(e=Wn(n,1))?v[(v[i+200>>2]+(e<<2)|0)-4>>2]:0,0),v[h+4>>2]||ee(h,m),e=v[r+4>>2],c=Re(a=s+44|0,m),b=Re(s+32|0,h),e=0|da[v[v[e>>2]+16>>2]](e,y,c,b),lf(b),lf(c),ee(e+168|0,h),b=ra(e),c=v[n+4>>2],v[n+4>>2]=c+1,p[b+4>>2]=w(l[0|c])/w(255),v[n+4>>2]=c+2,p[b+8>>2]=w(l[c+1|0])/w(255),v[n+4>>2]=c+3,p[b+12>>2]=w(l[c+2|0])/w(255),v[n+4>>2]=c+4,p[b+16>>2]=w(l[c+3|0])/w(255),c=gu(a,c=(c=Wn(n,1))?v[(v[i+200>>2]+(c<<2)|0)-4>>2]:0,0),b=gu(b=s+32|0,a=(a=Wn(n,1))?v[(v[i+200>>2]+(a<<2)|0)-4>>2]:0,0),a=v[n+4>>2],v[n+4>>2]=a+1,F=l[0|a],t&&(v[n+4>>2]=a+2,g=l[a+1|0],v[n+4>>2]=a+3,A=l[a+2|0],v[n+4>>2]=a+4,T=l[a+3|0],v[n+4>>2]=a+5,d=p[r+36>>2],p[e+196>>2]=d*(u(2,l[a+4|0]|(T|A<<8|g<<16)<<8),k()),v[n+4>>2]=a+6,g=l[a+5|0],v[n+4>>2]=a+7,A=l[a+6|0],v[n+4>>2]=a+8,T=l[a+7|0],v[n+4>>2]=a+9,p[e+200>>2]=d*(u(2,l[a+8|0]|(T|A<<8|g<<16)<<8),k())),F=Cf(T=Xf(40),e,a=Re(s+20|0,c),P,A=Re(g=s+8|0,b),!!(0|F)),lf(A),lf(a),v[s+8>>2]=F,Sn(r+8|0,g),lf(b),lf(c),lf(h);break n;case 4:if(e=v[r+4>>2],a=Re(c=s+56|0,m),e=0|da[v[v[e>>2]+24>>2]](e,y,a),lf(a),a=v[n+4>>2],v[n+4>>2]=a+1,h=0,f[e+80|0]=0!=l[0|a],v[n+4>>2]=a+2,f[e+81|0]=0!=l[a+1|0],$r(r,n,e,a=Wn(n,1)),v[s+56>>2]=0,yn(e- -64|0,b=(0|a)/3|0,c),(0|a)>=3)for(F=v[e+76>>2],a=v[n+4>>2];v[n+4>>2]=a+1,g=l[0|a],v[n+4>>2]=a+2,A=l[a+1|0],v[n+4>>2]=a+3,T=l[a+2|0],c=a+4|0,v[n+4>>2]=c,p[F+(h<<2)>>2]=p[r+36>>2]*(u(2,l[a+3|0]|(T|A<<8|g<<16)<<8),k()),a=c,(0|b)!=(0|(h=h+1|0)););t&&(v[n+4>>2]=v[n+4>>2]+4),a=v[r+4>>2],da[v[v[a>>2]+36>>2]](a,e);break n;case 5:e=v[r+4>>2],a=Re(s+56|0,m),e=0|da[v[v[e>>2]+28>>2]](e,y,a),lf(a),a=v[n+4>>2],v[n+4>>2]=a+1,c=l[0|a],v[n+4>>2]=a+2,h=l[a+1|0],v[n+4>>2]=a+3,b=l[a+2|0],v[n+4>>2]=a+4,v[e+28>>2]=l[a+3|0]|(b|h<<8|c<<16)<<8,v[n+4>>2]=a+5,c=l[a+4|0],v[n+4>>2]=a+6,h=l[a+5|0],v[n+4>>2]=a+7,b=l[a+6|0],v[n+4>>2]=a+8,d=p[r+36>>2],p[e+20>>2]=d*(u(2,l[a+7|0]|(b|h<<8|c<<16)<<8),k()),v[n+4>>2]=a+9,c=l[a+8|0],v[n+4>>2]=a+10,h=l[a+9|0],v[n+4>>2]=a+11,b=l[a+10|0],v[n+4>>2]=a+12,p[e+24>>2]=d*(u(2,l[a+11|0]|(b|h<<8|c<<16)<<8),k()),t&&(v[n+4>>2]=a+16),a=v[r+4>>2],da[v[v[a>>2]+36>>2]](a,e);break n;case 6:break e;default:break n}a=Wn(n,1),c=Wn(n,1),e=v[r+4>>2],$r(r,n,e=0|da[v[v[e>>2]+32>>2]](e,y,m),c),v[e+64>>2]=v[v[i+44>>2]+(a<<2)>>2],t&&(v[n+4>>2]=v[n+4>>2]+4),a=v[r+4>>2],da[v[v[a>>2]+36>>2]](a,e)}if(lf(m),D=s+80|0,e&&(yt(y,P,a=Re(o+8|0,C),e),lf(a)),lf(C),(0|E)==(0|(I=I+1|0)))break}if((0|(S=S+1|0))==(0|$))break}}return D=o+32|0,y}function Q(r,n,e){r|=0,n|=0,e|=0;var i,t=0,u=0,o=0,a=0,c=0,b=0,k=0,s=0,h=0,d=w(0),y=w(0),m=0,g=0,F=w(0),A=w(0),T=w(0),$=0,I=0,C=w(0),P=w(0),E=0,O=0,R=w(0),S=w(0),W=w(0),G=w(0),U=w(0),j=w(0),H=0,L=0,M=0;if(D=i=D-16|0,!v[r+224>>2]){v[r+224>>2]=e,u=v[e+52>>2],v[i+12>>2]=0,yn(b=r+128|0,u,i+12|0),Gf(e,n,0,u,b,0,2),Qr(b),e=0,D=c=D-16|0,n=v[b+4>>2],v[40+(a=r+4|0)>>2]=0,ca(g=a+36|0,k=n>>>1|0),v[c+12>>2]=0,mn(g,k,c+12|0);r:if(n>>>0>=2){for(u=k>>>0<=1?1:k,s=v[a+48>>2];v[s+(e<<2)>>2]=e,(0|u)!=(0|(e=e+1|0)););if(Ji(u=a+52|0,k),e=0,f[c+11|0]=0,Cn(u,k,c+11|0),n>>>0<2)break r;for(u=k>>>0<=1?1:k,s=v[a- -64>>2];L=e+s|0,M=Yn(e,k,b,g),f[0|L]=M,(0|u)!=(0|(e=e+1|0)););}else Ji(e=a+52|0,k),f[c+11|0]=0,Cn(e,k,c+11|0);if(v[a+72>>2]=0,ca($=a+68|0,(((0|k)<=2?2:k)<<2)-8|0),n>>>0>=8)for(t=v[a- -64>>2];;){I=v[b+12>>2],h=v[a+48>>2],n=0,e=1,s=k=(o=k)-1|0;r:{for(;;){n:{u=n,n=e;e:if(!l[t+u|0]){if((0|s)==(0|(e=(0|(e=n+1|0))!=(0|o)?e:0)))break n;for(m=I+(v[h+(n<<2)>>2]<<3)|0,C=p[m>>2],E=I+(v[h+(u<<2)>>2]<<3)|0,T=p[E>>2],O=I+(v[h+(s<<2)>>2]<<3)|0,R=p[O>>2],y=p[m+4>>2],d=p[E+4>>2],G=w(y-d),A=p[O+4>>2],U=w(d-A),j=w(A-y);;){if(l[e+t|0]&&(m=I+(v[h+(e<<2)>>2]<<3)|0,P=p[m>>2],F=p[m+4>>2],!(!(w(w(P*j)+w(w(C*w(F-A))+w(R*w(y-F))))>=w(0))|!(w(w(P*U)+w(w(R*w(F-d))+w(T*w(A-F))))>=w(0)))&&w(w(P*G)+w(w(T*w(F-y))+w(C*w(d-F))))>=w(0)))break e;if((0|s)==(0|(e=(e+1>>>0)%(o>>>0)|0)))break}break n}if(n){e=(0|(e=n+1|0))!=(0|o)?e:0,s=u;continue}for(;;){if(!l[t+u|0])break n;if(n=0,!(u=u-1|0))break r}}break}n=u}if(Sn($,h+(((I=n+k|0)>>>0)%(o>>>0)<<2)|0),Sn($,(e=n<<2)+v[a+48>>2]|0),Sn($,v[a+48>>2]+((n+1>>>0)%(o>>>0)<<2)|0),u=v[a+40>>2]-1|0,v[a+40>>2]=u,n>>>0<u>>>0)for(s=v[a+48>>2],o=v[s+e>>2],e=n;m=s+(e<<2)|0,t=s+((e=e+1|0)<<2)|0,v[m>>2]=v[t>>2],v[t>>2]=o,(0|e)!=(0|u););if(s=v[a+56>>2]-1|0,v[a+56>>2]=s,t=v[a+64>>2],n>>>0<s>>>0)for(o=l[n+t|0],e=n;m=l[0|(h=(u=e+1|0)+t|0)],f[0|h]=o,f[e+t|0]=m,(0|s)!=(0|(e=u)););if(L=(e=(I-1>>>0)%(k>>>0)|0)+t|0,M=Yn(e,k,b,g),f[0|L]=M,L=(n=(0|n)!=(0|k)?n:0)+t|0,M=Yn(n,k,b,g),f[0|L]=M,!(k>>>0>3))break}if(3==(0|k)&&(Sn($,v[a+48>>2]+8|0),Sn($,v[a+48>>2]),Sn($,v[a+48>>2]+4|0)),e=$,n=0,D=t=(D=c+16|0)-48|0,u=v[a+8>>2])for(s=a+84|0;he(s,v[v[a+16>>2]+(n<<2)>>2]),(0|u)!=(0|(n=n+1|0)););if(n=0,v[a+8>>2]=0,u=v[a+24>>2])for(s=a+104|0;he(s,v[v[a+32>>2]+(n<<2)>>2]),(0|u)!=(0|(n=n+1|0)););if(s=a+4|0,v[a+24>>2]=0,n=Vo(k=a+104|0),v[t+44>>2]=n,v[n+4>>2]=0,n=Zo($=a+84|0),v[t+40>>2]=n,v[n+4>>2]=0,E=v[e+4>>2]){for(g=a+20|0,I=-1,u=0,n=0;o=v[e+12>>2]+(n<<2)|0,h=(c=v[o>>2])<<1,v[t+36>>2]=h,m=v[o+4>>2],v[t+32>>2]=m<<1,O=v[o+8>>2],v[t+28>>2]=O<<1,c=(o=v[b+12>>2])+(c<<3)|0,p[t+24>>2]=p[c>>2],p[t+20>>2]=p[c+4>>2],c=o+(m<<3)|0,p[t+16>>2]=p[c>>2],p[t+12>>2]=p[c+4>>2],F=p[(o=o+(O<<3)|0)>>2],p[t+8>>2]=F,y=p[o+4>>2],p[t+4>>2]=y,c=v[t+40>>2],m=v[c+4>>2],(0|h)!=(0|I)||(o=v[c+12>>2],d=p[(h=o+(m<<2)|0)-16>>2],C=p[h-12>>2],A=w(p[h-4>>2]-C),T=w(p[h-8>>2]-d),(0|(w(w(w(T*C)+w(w(F*A)-w(y*T)))-w(d*A))>=w(0)?1:-1))!=(0|u)||(d=w(p[o>>2]-F),A=w(d*y),y=w(p[o+4>>2]-y),(0|(w(w(A+w(w(p[o+8>>2]*y)-w(p[o+12>>2]*d)))-w(F*y))>=w(0)?1:-1))!=(0|u)))?(m?(Sn(s,t+40|0),Sn(g,t+44|0)):(he($,c),he(k,v[t+44>>2])),u=Zo($),v[t+40>>2]=u,v[u+4>>2]=0,Rn(u,t+24|0),Rn(v[t+40>>2],t+20|0),Rn(v[t+40>>2],t+16|0),Rn(v[t+40>>2],t+12|0),Rn(v[t+40>>2],t+8|0),Rn(v[t+40>>2],t+4|0),u=Vo(k),v[t+44>>2]=u,v[u+4>>2]=0,Sn(u,t+36|0),Sn(v[t+44>>2],t+32|0),Sn(v[t+44>>2],t+28|0),y=p[t+20>>2],F=w(p[t+12>>2]-y),d=p[t+24>>2],A=w(p[t+16>>2]-d),u=w(w(w(A*y)+w(w(p[t+8>>2]*F)-w(p[t+4>>2]*A)))-w(d*F))>=w(0)?1:-1,I=v[t+36>>2]):(Rn(c,t+8|0),Rn(v[t+40>>2],t+4|0),Sn(v[t+44>>2],t+28|0)),E>>>0>(n=n+3|0)>>>0;);v[v[t+40>>2]+4>>2]&&(Sn(s,t+40|0),Sn(g,t+44|0))}if(o=v[a+8>>2]){for(e=0;;){if(n=v[(u=e<<2)+v[a+32>>2]>>2],v[t+44>>2]=n,b=v[n+4>>2])for(n=v[n+12>>2],c=v[(n+(b<<2)|0)-4>>2],h=v[n>>2],n=v[u+v[a+16>>2]>>2],v[t+40>>2]=n,u=v[n+4>>2]<<2,n=v[n+12>>2],F=p[(u=u+n|0)-8>>2],d=p[u-4>>2],A=p[u-12>>2],y=w(d-A),C=p[u-16>>2],T=w(F-C),R=p[n>>2],P=p[n+4>>2],G=w(w(w(T*A)+w(w(R*y)-w(P*T)))-w(C*y)),U=p[n+12>>2],j=p[n+8>>2],n=0;y=d,(0|n)!=(0|e)?(u=v[(g=n<<2)+v[a+32>>2]>>2],3!=v[u+4>>2]||(b=v[u+12>>2],I=v[b+4>>2],m=v[b>>2],v[t+36>>2]=v[b+8>>2],b=v[g+v[a+16>>2]>>2],g=(v[b+4>>2]<<2)+v[b+12>>2]|0,d=p[g-8>>2],p[t+32>>2]=d,T=p[g-4>>2],p[t+28>>2]=T,(0|h)!=(0|m)|(0|c)!=(0|I)||(g=G>=w(0),S=w(F-C),W=w(y-A),(0|g)==(0|!(w(w(w(S*A)+w(w(d*W)-w(T*S)))-w(C*W))>=w(0)))||(S=w(R-d),W=w(S*T),T=w(P-T),g^w(w(W+w(w(j*T)-w(U*S)))-w(d*T))>=w(0))))?d=y:(n=0,v[b+4>>2]=0,v[u+4>>2]=0,Rn(v[t+40>>2],t+32|0),Rn(v[t+40>>2],t+28|0),Sn(v[t+44>>2],t+36|0),d=p[t+28>>2],A=y,C=F,F=p[t+32>>2])):n=e,o>>>0>(n=n+1|0)>>>0;);if((0|o)==(0|(e=e+1|0)))break}if(!((0|(u=v[a+8>>2]))<=0))for(;;){if(o=v[(c=(e=u-1|0)<<2)+v[a+16>>2]>>2],v[t+40>>2]=o,!v[o+4>>2]){if(b=v[a+8>>2]-1|0,v[a+8>>2]=b,b>>>0>(n=e)>>>0){for(;o=v[a+16>>2],g=v[(h=o+(n<<2)|0)>>2],m=o,o=(n=n+1|0)<<2,v[h>>2]=v[m+o>>2],v[o+v[a+16>>2]>>2]=g,(0|n)!=(0|b););o=v[t+40>>2]}if(he($,o),o=v[c+v[a+32>>2]>>2],v[t+44>>2]=o,b=v[a+24>>2]-1|0,v[a+24>>2]=b,b>>>0>(n=e)>>>0){for(;o=v[a+32>>2],h=v[(c=o+(n<<2)|0)>>2],m=o,o=(n=n+1|0)<<2,v[c>>2]=v[m+o>>2],v[o+v[a+32>>2]>>2]=h,(0|n)!=(0|b););o=v[t+44>>2]}he(k,o)}if(n=(0|u)>1,u=e,!n)break}}if(D=t+48|0,e=s,v[r+228>>2]=e,v[e+4>>2])for(;Qr(n=v[v[e+12>>2]+(H<<2)>>2]),Rn(n,v[n+12>>2]),Rn(n,v[n+12>>2]+4|0),e=v[r+228>>2],(u=v[e+4>>2])>>>0>(H=H+1|0)>>>0;);else u=0}return D=i+16|0,0|u}function rr(r){var n,e,i,t=0,u=0,o=0,a=0,c=0,b=0,k=0,h=0,d=0,p=0,y=0,m=0;if(v[124+(r|=0)>>2]=0,v[r+108>>2]=0,u=v[r+12>>2])for(;a=v[v[r+20>>2]+(t<<2)>>2],h=l[v[a+4>>2]+60|0],f[a+116|0]=h,f[a+117|0]=1^h,(0|u)!=(0|(t=t+1|0)););if((h=v[r+136>>2])&&(u=v[h+40>>2]))for(t=0;;){for(a=v[v[r+20>>2]+(v[v[v[h+48>>2]+(t<<2)>>2]+4>>2]<<2)>>2];s[a+116>>1]=256,a=v[a+12>>2];);if((0|u)==(0|(t=t+1|0)))break}r:if(h=(i=v[r+92>>2])+((n=v[r+76>>2])+(e=v[r+60>>2])|0)|0)for(a=0;;){n:{e:{i:if(e){if(t=0,d=1,u=v[v[r+68>>2]>>2],v[v[u+4>>2]+16>>2]!=(0|a)){for(;;){if((0|e)==(0|(t=t+1|0)))break i;if(u=v[v[r+68>>2]+(t<<2)>>2],v[v[u+4>>2]+16>>2]==(0|a))break}d=t>>>0<e>>>0}D=c=D-16|0;f:{t:if(l[v[u+40>>2]+117|0]){u:{if(l[v[u+4>>2]+20|0]){if(!(t=v[r+136>>2]))break t;if(!(o=v[t+56>>2])){f[u+44|0]=0;break f}if(k=v[u+4>>2],b=v[t- -64>>2],(0|k)!=v[b>>2]){for(t=0;(0|o)!=(0|(t=t+1|0))&(0|k)!=v[b+(t<<2)>>2];);if(t=t>>>0<o>>>0,f[u+44|0]=t,t)break u;break f}}f[u+44|0]=1}Ki(r,v[u+40>>2]),Ki(r,k=v[v[u+20>>2]>>2]);u:if(!((t=v[u+12>>2])>>>0<2)){if(o=v[(v[u+20>>2]+(t<<2)|0)-4>>2],v[c+12>>2]=o,b=v[r+108>>2]){if(p=v[r+116>>2],(0|o)==v[p>>2])break u;for(t=0;(0|b)!=(0|(t=t+1|0))&(0|o)!=v[p+(t<<2)>>2];);if(t>>>0<b>>>0)break u}Sn(r+120|0,c+12|0)}v[c+8>>2]=u,Sn(r+104|0,c+8|0),ui(k+16|0),f[v[(v[u+20>>2]+(v[u+12>>2]<<2)|0)-4>>2]+116|0]=1;break f}f[u+44|0]=0}if(D=c+16|0,a=a+1|0,d)break e}i:if(n){if(t=0,d=1,u=v[v[r+84>>2]>>2],v[v[u+4>>2]+16>>2]!=(0|a)){for(;;){if((0|n)==(0|(t=t+1|0)))break i;if(u=v[v[r+84>>2]+(t<<2)>>2],v[v[u+4>>2]+16>>2]==(0|a))break}d=t>>>0<n>>>0}D=c=D-16|0;f:{t:{u:if(l[v[u+24>>2]+117|0]){o:{if(l[v[u+4>>2]+20|0]){if(!(t=v[r+136>>2]))break u;if(!(o=v[t+56>>2])){f[u+44|0]=0;break f}if(k=v[u+4>>2],b=v[t- -64>>2],(0|k)!=v[b>>2]){for(t=0;(0|o)!=(0|(t=t+1|0))&(0|k)!=v[b+(t<<2)>>2];);if(t=t>>>0<o>>>0,f[u+44|0]=t,t)break o;break f}}f[u+44|0]=1}if(Ki(r,v[u+24>>2]),o=v[u+12>>2],l[v[u+4>>2]+85|0]){if(!o)break t;for(p=r+120|0,b=0;;){t=v[v[u+20>>2]+(b<<2)>>2],v[c+12>>2]=t,Ki(r,v[t+12>>2]);o:{if(k=v[r+108>>2]){if(t=0,y=v[c+12>>2],m=v[r+116>>2],(0|y)==v[m>>2])break o;for(;(0|k)!=(0|(t=t+1|0))&v[(t<<2)+m>>2]!=(0|y););if(t>>>0<k>>>0)break o}Sn(p,c+12|0)}if((0|o)==(0|(b=b+1|0)))break}}else{if(!o)break t;for(t=0;Ki(r,v[v[u+20>>2]+(t<<2)>>2]),(0|o)!=(0|(t=t+1|0)););}if(v[c+8>>2]=u,Sn(r+104|0,c+8|0),!o)break f;for(t=0;ui(v[v[u+20>>2]+(t<<2)>>2]+16|0),(0|o)!=(0|(t=t+1|0)););if(!o)break f;for(u=v[u+20>>2],t=0;f[v[u+(t<<2)>>2]+116|0]=1,(0|o)!=(0|(t=t+1|0)););break f}f[u+44|0]=0;break f}v[c+8>>2]=u,Sn(r+104|0,c+8|0)}if(D=c+16|0,a=a+1|0,d)break e}if(t=0,!i)break n;for(;;){if(u=v[v[r+100>>2]+(t<<2)>>2],v[v[u+4>>2]+16>>2]!=(0|a)){if((0|i)!=(0|(t=t+1|0)))continue;break n}break}D=c=D-16|0;i:{f:{t:if(l[v[v[u+24>>2]+8>>2]+117|0]){u:{if(l[v[u+4>>2]+20|0]){if(!(t=v[r+136>>2]))break t;if(!(o=v[t+56>>2])){f[u+140|0]=0;break i}if(b=v[u+4>>2],d=v[t- -64>>2],(0|b)!=v[d>>2]){for(t=0;(0|o)!=(0|(t=t+1|0))&(0|b)!=v[d+(t<<2)>>2];);if(t=t>>>0<o>>>0,f[u+140|0]=t,t)break u;break i}}f[u+140|0]=1}if(b=v[u+24>>2],d=v[v[b+4>>2]+4>>2],o=v[b+8>>2],(t=v[r+136>>2])?(Vr(r,t,d,o),k=v[r+136>>2]):k=0,t=v[r+4>>2],!(p=v[t+64>>2])|(0|k)==(0|p)||(Vr(r,p,d,o),t=v[r+4>>2]),k=v[t+52>>2])for(t=0;Vr(r,v[v[v[r+4>>2]+60>>2]+(t<<2)>>2],d,o),(0|k)!=(0|(t=t+1|0)););if((t=v[b+60>>2])&&Tf(0|da[v[v[t>>2]+8>>2]](t),20924)&&jn(r,t,o),!(o=v[u+12>>2])){v[c+12>>2]=u,Sn(r+104|0,c+12|0);break i}for(t=0;Ki(r,v[v[u+20>>2]+(t<<2)>>2]),(0|o)!=(0|(t=t+1|0)););break f}f[u+140|0]=0;break i}if(v[c+12>>2]=u,Sn(r+104|0,c+12|0),o){for(t=0;ui(v[v[u+20>>2]+(t<<2)>>2]+16|0),(0|o)!=(0|(t=t+1|0)););if(o)for(u=v[u+20>>2],t=0;f[v[u+(t<<2)>>2]+116|0]=1,(0|o)!=(0|(t=t+1|0)););}}D=c+16|0,a=a+1|0}if(h>>>0>a>>>0)continue;break r}if(!(h>>>0>(a=a+1|0)>>>0))break}if(h=v[r+12>>2])for(a=0;Ki(r,v[v[r+20>>2]+(a<<2)>>2]),(0|h)!=(0|(a=a+1|0)););}function nr(r,n,e,i,f,t){var u,o,a,c=0,b=0,k=0,l=0,d=w(0),y=0,F=w(0),A=w(0),T=0,$=0,I=0,C=w(0),P=0,E=w(0),O=0,R=0,S=w(0),W=0,G=0,U=w(0),j=0,H=w(0),L=w(0),M=w(0),_=w(0),z=w(0),x=w(0),J=0,K=w(0),B=w(0),N=w(0),q=0,V=0,Z=0,Y=w(0),X=w(0),Q=w(0),rr=w(0),nr=w(0),er=w(0),ir=w(0),fr=w(0),tr=w(0),ur=w(0),or=0,ar=w(0),cr=w(0),br=w(0),kr=0;D=u=D-16|0,o=v[r+228>>2],a=v[o+4>>2],v[r+196>>2]=0,v[r+164>>2]=0,v[r+180>>2]=0;r:if(i)for(q=r+192|0,V=r+176|0,Z=r+160|0,O=r+144|0;;){n:{if(a)for(k=m(h[(c=(G<<1)+e|0)>>1],t)<<2,Y=p[k+f>>2],l=m(h[c+4>>1],t)<<2,X=p[l+f>>2],c=m(h[c+2>>1],t)<<2,Q=p[c+f>>2],rr=p[(y=k+4|0)+f>>2],nr=p[(T=l+4|0)+f>>2],er=p[(b=c+4|0)+f>>2],K=p[n+b>>2],U=p[n+T>>2],ir=w(K-U),B=p[n+k>>2],H=p[n+l>>2],fr=w(B-H),N=p[n+c>>2],tr=w(H-N),x=p[n+y>>2],ur=w(w(1)/w(w(ir*fr)+w(tr*w(x-U)))),br=w(U-x),J=0;;){$=v[r+164>>2],j=v[v[o+12>>2]+(J<<2)>>2],I=0,D=b=D-48|0,p[b+40>>2]=x,p[b+44>>2]=B,p[b+36>>2]=N,p[b+32>>2]=K,p[b+28>>2]=H,p[b+24>>2]=U,c=r+208|0,l=2&v[j+4>>2],v[(k=l?O:c)+4>>2]=0,Rn(k,y=b+44|0),Rn(k,T=b+40|0),Rn(k,b+36|0),Rn(k,b+32|0),Rn(k,b+28|0),Rn(k,b+24|0),Rn(k,y),Rn(k,T),v[(c=l?c:O)+4>>2]=0,kr=v[j+4>>2]-4|0,y=0;e:{i:{for(;;){if(P=v[j+12>>2],F=p[(R=P+(l=y<<2)|0)>>2],p[b+20>>2]=F,d=p[P+(4|l)>>2],p[b+16>>2]=d,or=v[k+4>>2]-2|0){for(l=k,L=p[P+((T=y+2|0)<<2)>>2],ar=w(F-L),F=p[R+12>>2],cr=w(-w(d-F)),k=0;;){P=v[l+12>>2],d=p[P+(4|(R=k<<2))>>2],A=p[(R=P+R|0)>>2],E=p[P+((k=k+2|0)<<2)>>2],p[b+12>>2]=E,C=p[R+12>>2],p[b+8>>2]=C,S=w(w(ar*w(C-F))+w(w(E-L)*cr));f:if(w(w(ar*w(d-F))+w(w(A-L)*cr))>w(0)){if(S>w(0)){Rn(c,b+12|0),Rn(c,b+8|0);break f}if(S=w(C-d),C=p[b+20>>2],M=w(L-C),_=p[b+16>>2],E=w(E-A),z=w(w(S*M)-w(w(F-_)*E)),w(g(z))>w(9.999999974752427e-7)){d=w(w(w(E*w(_-d))-w(w(C-A)*S))/z),p[b+4>>2]=w(M*d)+C,Rn(c,I=b+4|0),A=p[b+16>>2],p[b+4>>2]=w(w(F-A)*d)+A,Rn(c,I),I=1;break f}Rn(c,b+20|0),Rn(c,b+16|0),I=1}else I=1,S>w(0)&&(S=w(C-d),C=p[b+20>>2],M=w(L-C),_=p[b+16>>2],E=w(E-A),z=w(w(S*M)-w(w(F-_)*E)),w(g(z))>w(9.999999974752427e-7)?(d=w(w(w(E*w(_-d))-w(w(C-A)*S))/z),p[b+4>>2]=w(M*d)+C,Rn(c,P=b+4|0),A=p[b+16>>2],p[b+4>>2]=w(w(F-A)*d)+A,Rn(c,P)):(Rn(c,b+20|0),Rn(c,b+16|0)),Rn(c,b+12|0),Rn(c,b+8|0));if(!(k>>>0<or>>>0))break}if(v[c+4>>2]){if(Rn(c,v[c+12>>2]),Rn(c,v[c+12>>2]+4|0),(0|y)==(0|kr))break i;v[l+4>>2]=0,y=T,k=c,c=l;continue}}break}v[O+4>>2]=0,I=1;break e}if((0|c)==(0|O))c=v[O+4>>2],v[b+20>>2]=0,yn(O,c-2|0,b+20|0);else{if(k=0,v[O+4>>2]=0,!(l=v[c+4>>2]-2|0))break e;for(;Rn(O,v[c+12>>2]+(k<<2)|0),(0|l)!=(0|(k=k+1|0)););}}if(D=b+48|0,!I)break n;if(k=v[r+148>>2]){for(c=0,v[u+12>>2]=0,yn(Z,l=(-2&k)+$|0,y=u+12|0),v[u+12>>2]=0,yn(q,l,y),l=v[r+204>>2],y=v[r+172>>2],T=v[r+156>>2];F=p[(I=c<<2)+T>>2],j=4+(b=$<<2)|0,d=p[T+(4|I)>>2],p[j+y>>2]=d,p[b+y>>2]=F,A=w(F-H),d=w(d-U),F=w(ur*w(w(ir*A)+w(tr*d))),d=w(ur*w(w(br*A)+w(fr*d))),A=w(w(w(1)-F)-d),p[l+j>>2]=w(nr*A)+w(w(rr*F)+w(er*d)),p[b+l>>2]=w(X*A)+w(w(Y*F)+w(Q*d)),$=$+2|0,k>>>0>(c=c+2|0)>>>0;);if($=v[r+180>>2],s[u+12>>1]=0,pn(V,(m(k=k>>>1|0,3)+$|0)-6|0,u+12|0),(y=k-1|0)>>>0>=2)for(T=v[r+188>>2],c=1;s[(l=T+($<<1)|0)>>1]=W,b=c+W|0,s[l+2>>1]=b,s[l+4>>1]=b+1,$=$+3|0,(0|y)!=(0|(c=c+1|0)););W=k+W|0}if((0|a)==(0|(J=J+1|0)))break}if((G=G+3|0)>>>0<i>>>0)continue;break r}if(v[u+12>>2]=0,yn(Z,c=$+6|0,l=u+12|0),v[u+12>>2]=0,yn(q,c,l),k=v[r+172>>2],p[k+(y=20+(c=$<<2)|0)>>2]=U,p[(T=c+16|0)+k>>2]=H,p[(b=c+12|0)+k>>2]=K,p[($=c+8|0)+k>>2]=N,p[(I=c+4|0)+k>>2]=x,p[c+k>>2]=B,k=v[r+204>>2],p[k+y>>2]=nr,p[k+T>>2]=X,p[b+k>>2]=er,p[k+$>>2]=Q,p[k+I>>2]=rr,p[c+k>>2]=Y,c=v[r+180>>2],s[u+12>>1]=0,pn(V,c+3|0,l),c=v[r+188>>2]+(c<<1)|0,s[c>>1]=W,s[c+4>>1]=W+2,s[c+2>>1]=W+1,W=W+3|0,!((G=G+3|0)>>>0<i>>>0))break}D=u+16|0}function er(r,n){var e,i=0,f=0,o=0,a=0,c=0,s=0,l=0,h=0,d=0,p=0,F=0,T=0,$=0,I=0,C=0,P=0,E=0,O=0,R=0,S=0,W=0,G=0;D=e=D-16|0,b(r);r:if((o=2147483647&(P=t(2)))>>>0<=1305022426){if(C=(h=+r)+-1.5707963109016418*(i=.6366197723675814*h+6755399441055744-6755399441055744)+-1.5893254773528196e-8*i,y[n>>3]=C,a=C<-.7853981852531433,o=g(i)<2147483648?~~i:-2147483648,a){i+=-1,y[n>>3]=h+-1.5707963109016418*i+-1.5893254773528196e-8*i,o=o-1|0;break r}if(!(C>.7853981852531433))break r;i+=1,y[n>>3]=h+-1.5707963109016418*i+-1.5893254773528196e-8*i,o=o+1|0}else if(o>>>0>=2139095040)y[n>>3]=w(r-r),o=0;else{if(f=o,o=(o>>>23|0)-150|0,y[e+8>>3]=(u(2,f-(o<<23)|0),k()),E=e+8|0,D=c=D-560|0,l=o+m($=(0|(f=(o-3|0)/24|0))>0?f:0,-24)|0,(0|(p=v[4356]))>=0)for(o=p+1|0,f=$;y[(c+320|0)+(a<<3)>>3]=(0|f)<0?0:+v[17440+(f<<2)>>2],f=f+1|0,(0|o)!=(0|(a=a+1|0)););for(F=l-24|0,o=0,a=(0|p)>0?p:0;;){for(f=0,i=0;i=y[(f<<3)+E>>3]*y[(c+320|0)+(o-f<<3)>>3]+i,1!=(0|(f=f+1|0)););if(y[(o<<3)+c>>3]=i,f=(0|o)==(0|a),o=o+1|0,f)break}W=47-l|0,O=48-l|0,R=(0|l)<25,G=l-25|0,o=p;n:{for(;;){if(i=y[(o<<3)+c>>3],f=0,a=o,!(d=(0|o)<=0))for(;T=(c+480|0)+(f<<2)|0,s=g(h=5.960464477539063e-8*i)<2147483648?~~h:-2147483648,s=g(i=-16777216*(h=+(0|s))+i)<2147483648?~~i:-2147483648,v[T>>2]=s,i=y[((a=a-1|0)<<3)+c>>3]+h,(0|(f=f+1|0))!=(0|o););i=Gn(i,F),i+=-8*A(.125*i),i-=+(0|(T=g(i)<2147483648?~~i:-2147483648));e:{i:{f:{if(R){if(F)break f;s=v[476+((o<<2)+c|0)>>2]>>23}else I=f=(o<<2)+c|0,f=(s=v[f+476>>2])-((a=s>>O)<<O)|0,v[I+476>>2]=f,T=a+T|0,s=f>>W;if((0|s)<=0)break e;break i}if(s=2,!(i>=.5)){s=0;break e}}if(f=0,a=0,!d)for(;S=v[(I=(c+480|0)+(f<<2)|0)>>2],d=16777215,a||(d=16777216,S)?(v[I>>2]=d-S,a=1):a=0,(0|(f=f+1|0))!=(0|o););i:if(!R){f=8388607;f:switch(0|G){case 1:f=4194303;break;case 0:break f;default:break i}v[476+(d=(o<<2)+c|0)>>2]=v[d+476>>2]&f}T=T+1|0,2==(0|s)&&(i=1-i,s=2,a&&(i-=Gn(1,F)))}if(0!=i)break;if(f=1,d=0,a=o,!((0|o)<=(0|p))){for(;d=v[(c+480|0)+((a=a-1|0)<<2)>>2]|d,(0|a)>(0|p););if(d){for(l=F;l=l-24|0,!v[(c+480|0)+((o=o-1|0)<<2)>>2];);break n}}for(;a=f,f=f+1|0,!v[(c+480|0)+(p-a<<2)>>2];);for(a=o+a|0;;){for(y[(c+320|0)+((o=o+1|0)<<3)>>3]=v[17440+(o+$<<2)>>2],f=0,i=0;i=y[(f<<3)+E>>3]*y[(c+320|0)+(o-f<<3)>>3]+i,1!=(0|(f=f+1|0)););if(y[(o<<3)+c>>3]=i,!((0|o)<(0|a)))break}o=a}(i=Gn(i,24-l|0))>=16777216?(F=(c+480|0)+(o<<2)|0,f=g(h=5.960464477539063e-8*i)<2147483648?~~h:-2147483648,a=g(i=-16777216*+(0|f)+i)<2147483648?~~i:-2147483648,v[F>>2]=a,o=o+1|0):(f=g(i)<2147483648?~~i:-2147483648,l=F),v[(c+480|0)+(o<<2)>>2]=f}if(i=Gn(1,l),(0|o)>=0){for(a=o;y[((f=a)<<3)+c>>3]=i*+v[(c+480|0)+(f<<2)>>2],a=f-1|0,i*=5.960464477539063e-8,f;);for(a=o;;){if(i=0,f=0,(0|(F=(0|(l=o-a|0))>(0|p)?p:l))>=0)for(;i=y[20208+(f<<3)>>3]*y[(f+a<<3)+c>>3]+i,$=(0|f)!=(0|F),f=f+1|0,$;);if(y[(c+160|0)+(l<<3)>>3]=i,f=(0|a)>0,a=a-1|0,!f)break}}if(i=0,(0|o)>=0)for(;a=o,o=o-1|0,i+=y[(c+160|0)+(a<<3)>>3],a;);y[e>>3]=s?-i:i,D=c+560|0,o=7&T,i=y[e>>3],(0|P)<0?(y[n>>3]=-i,o=0-o|0):y[n>>3]=i}return D=e+16|0,o}function ir(r,n){r|=0,n|=0;var e,i=0,t=0,u=0,o=0,a=0,c=0,b=0,k=0,s=w(0),h=0,d=0,y=0,m=w(0),g=0,F=0,A=w(0),T=0,$=0,I=w(0);if(D=e=D-16|0,l[r+88|0]){if(f[r+88|0]=0,t=v[r+80>>2])for(;i=v[t+12>>2],da[v[v[t>>2]+4>>2]](t),t=i,i;);if(v[r+80>>2]=0,v[r+84>>2]=0,g=v[r+44>>2]){for(;;){if(i=v[v[r+52>>2]+(d<<2)>>2]){for(;t=i,i=v[i+24>>2];);for(;;){if(i=v[t+28>>2],3!=v[t+112>>2]||!i){b=0,D=a=D-16|0,y=v[t+16>>2],c=v[y+8>>2],i=v[t+28>>2],v[a+12>>2]=0,mn(t+116|0,c,o=a+12|0),v[a+12>>2]=0,Fn(t+132|0,c,o);r:if(!i|!l[i+37|0]){if(c)for(T=r+76|0;;){o=v[(k=b<<2)+v[y+16>>2]>>2],$=0|da[v[v[o>>2]+16>>2]](o),v[a+12>>2]=$;n:{if(u=v[r+80>>2])for(;;){if(v[u+4>>2]==(0|$)){u=0;break n}if(!(u=v[u+12>>2]))break}if(u=1,f[a+11|0]=1,Ln(T,a+12|0,a+11|0),i&&!lu(0|da[v[v[o>>2]+8>>2]](o),20700)&&!lu(0|da[v[v[o>>2]+8>>2]](o),20808)&&!lu(0|da[v[v[o>>2]+8>>2]](o),20820)&&(o=i,wi(v[i+16>>2],v[a+12>>2]))){for(;;){if(u=2,!(o=v[o+28>>2]))break n;if(!wi(v[o+16>>2],v[a+12>>2]))break}if(p[t+100>>2]>w(0)){if(v[k+v[t+128>>2]>>2]=3,v[k+v[t+144>>2]>>2]=t,c>>>0>(b=b+1|0)>>>0)continue;break r}}}if(v[k+v[t+128>>2]>>2]=u,!(c>>>0>(b=b+1|0)>>>0))break}}else{if(!c)break r;for(o=r+76|0,i=0;;){u=v[(b=i<<2)+v[y+16>>2]>>2],k=0|da[v[v[u>>2]+16>>2]](u),v[a+12>>2]=k;n:{if(u=v[r+80>>2])for(;;){if((0|k)==v[u+4>>2])break n;if(!(u=v[u+12>>2]))break}f[a+11|0]=1,Ln(o,a+12|0,a+11|0)}if(v[b+v[t+128>>2]>>2]=2,(0|c)==(0|(i=i+1|0)))break}}D=a+16|0,i=v[t+28>>2]}if(!(t=i))break}}if((0|g)==(0|(d=d+1|0)))break}if(t=v[r+80>>2])for(;i=v[t+12>>2],da[v[v[t>>2]+4>>2]](t),t=i,i;);if(v[r+80>>2]=0,v[r+84>>2]=0,!((0|(i=v[r+44>>2]))<=0))for(;;){if(o=i,i=i-1|0,t=v[v[r+52>>2]+(i<<2)>>2])for(;;){if(u=0,D=a=D-16|0,b=v[t+16>>2],d=v[b+8>>2])for(y=r+76|0;;){c=v[(k=u<<2)+v[b+16>>2]>>2];r:if(lu(0|da[v[v[c>>2]+8>>2]](c),20700)){g=v[k+v[b+16>>2]>>2],T=v[g+4>>2];n:{if(c=v[r+80>>2])for(;;){if((0|T)==v[c+4>>2])break n;if(!(c=v[c+12>>2]))break}v[a+12>>2]=v[g+4>>2],f[a+11|0]=1,Ln(y,a+12|0,a+11|0);break r}c=k+v[t+128>>2]|0,v[c>>2]=4|v[c>>2]}if((0|d)==(0|(u=u+1|0)))break}if(D=a+16|0,!(t=v[t+24>>2]))break}if(!((0|o)>1))break}}}if(d=v[r+44>>2])for(a=r+56|0;;){if(!(!(i=v[v[r+52>>2]+(F<<2)>>2])|p[i+68>>2]>w(0))){t=F?v[i+112>>2]:1,m=p[i+92>>2],v[i+24>>2]?m=w(m*lr(r,i,n,t)):p[i+72>>2]>=p[i+84>>2]&&(m=v[i+20>>2]?m:w(0)),s=p[i+52>>2],I=p[i+60>>2];r:if(l[i+36|0]){if((A=w(p[i+56>>2]-s))==w(0))break r;s=w(s+Cr(p[i+72>>2],A))}else s=(s=w(s+p[i+72>>2]))<(A=p[i+56>>2])?s:A;c=v[i+16>>2],o=v[c+8>>2];r:if(!(!F&m==w(1))&3!=(0|t)){if(b=i+148|0,(y=v[i+152>>2])||(v[e+12>>2]=0,yn(b,o<<1,e+12|0)),o)for(h=0;k=3&v[(u=h<<2)+v[i+128>>2]>>2]?0:t,u=v[u+v[c+16>>2]>>2],lu(0|da[v[v[u>>2]+8>>2]](u),21020)?hr(u,n,s,m,k,b,h<<1,!y):da[v[v[u>>2]+12>>2]](u,n,I,s,a,m,k,0),(0|o)!=(0|(h=h+1|0)););}else{if(!o)break r;for(h=0;u=v[v[c+16>>2]+(h<<2)>>2],da[v[v[u>>2]+12>>2]](u,n,I,s,a,m,t,0),(0|o)!=(0|(h=h+1|0)););}Jr(r,i,s),v[r+60>>2]=0,p[i+64>>2]=s,p[i+80>>2]=p[i+72>>2],h=1}if((0|d)==(0|(F=F+1|0)))break}return pr(v[r+72>>2]),D=e+16|0,1&h}function fr(r,n,e,i,t){var u,o,a=0,c=0,b=0,k=0,s=0,h=0,d=0,y=0,m=0,g=0,F=0,A=w(0),T=w(0),$=0,I=0,C=0,P=0,E=0;if(D=u=D+-64|0,v[u+60>>2]=n,(0|(o=Qn(i)))>0&&(d=47!=(0|(c=l[(i+o|0)-1|0]))&92!=(0|c)),v[u+56>>2]=0,ie(u+60|0,c=n+e|0,u+48|0))for($=r+4|0,I=r+20|0,C=1+(o+d|0)|0,m=u+44|0,g=u+36|0,s=12|(n=u+16|0),h=4|n,F=u+52|0;;){if((0|(a=v[u+52>>2]))!=(0|(e=v[u+48>>2])))if(v[u+56>>2])Bf((n=Xf(112))+4|0,0,108),v[n>>2]=8952,ht(n+8|0),v[n+108>>2]=0,v[n+100>>2]=0,v[n+104>>2]=0,v[n+96>>2]=8680,v[n+92>>2]=0,v[n+84>>2]=0,v[n+88>>2]=0,v[n+80>>2]=8680,v[u>>2]=n,v[n+4>>2]=v[u+56>>2],b=v[5208],n=a-e|0,e=Ff(0|da[v[v[b>>2]+12>>2]](b,n+1|0,8524,308),e,n),f[n+e|0]=0,n=gu(u+4|0,e,1),ee(v[u>>2]+8|0,n),lf(n),Ui(u+60|0,c,u+48|0),n=90,Te(4979,e=v[u+48>>2],a=v[u+52>>2]-e|0)&&(n=0,Te(5156,e,a)&&(n=du(e,F))),e=v[u>>2],v[e+76>>2]=n,f[e+72|0]=90==(0|n),cn(u+60|0,c,u+16|0),n=du(v[u+16>>2],h),v[v[u>>2]+20>>2]=n,n=du(v[u+24>>2],s),v[v[u>>2]+24>>2]=n,cn(u+60|0,c,u+16|0),n=du(v[u+16>>2],h),v[v[u>>2]+28>>2]=n,e=du(v[u+24>>2],s),n=v[u>>2],v[n+32>>2]=e,a=v[n+20>>2],k=v[u+56>>2],A=w(v[k+60>>2]),p[n+36>>2]=w(0|a)/A,b=v[n+24>>2],T=w(v[k+64>>2]),p[n+40>>2]=w(0|b)/T,l[n+72|0]?(b=b+v[n+28>>2]|0,e=e+a|0):(b=e+b|0,e=a+v[n+28>>2]|0),p[n+48>>2]=w(0|b)/T,p[n+44>>2]=w(0|e)/A,4==(0|cn(u+60|0,c,u+16|0))&&(v[u+4>>2]=0,mn(n+80|0,4,u+4|0),n=du(v[u+16>>2],h),v[v[v[u>>2]+92>>2]>>2]=n,n=du(v[u+24>>2],s),v[v[v[u>>2]+92>>2]+4>>2]=n,n=du(v[u+32>>2],g),v[v[v[u>>2]+92>>2]+8>>2]=n,n=du(v[u+40>>2],m),e=v[u>>2],v[v[e+92>>2]+12>>2]=n,4==(0|cn(u+60|0,c,u+16|0))&&(v[u+4>>2]=0,mn(e+96|0,4,u+4|0),n=du(v[u+16>>2],h),v[v[v[u>>2]+108>>2]>>2]=n,n=du(v[u+24>>2],s),v[v[v[u>>2]+108>>2]+4>>2]=n,n=du(v[u+32>>2],g),v[v[v[u>>2]+108>>2]+8>>2]=n,n=du(v[u+40>>2],m),v[v[v[u>>2]+108>>2]+12>>2]=n,cn(u+60|0,c,u+16|0))),n=du(v[u+16>>2],h),v[v[u>>2]+60>>2]=n,n=du(v[u+24>>2],s),v[v[u>>2]+64>>2]=n,cn(n=u+60|0,c,u+16|0),e=du(v[u+16>>2],h),p[v[u>>2]+52>>2]=0|e,e=du(v[u+24>>2],s),p[v[u>>2]+56>>2]=0|e,Ui(n,c,u+48|0),n=du(v[u+48>>2],F),v[v[u>>2]+68>>2]=n,Sn(I,u);else{for(n=v[5208],a=a-e|0,n=Ff(0|da[v[v[n>>2]+12>>2]](n,a+1|0,8524,308),e,a),f[n+a|0]=0,e=Qn(n),a=v[5208],b=Ff(0|da[v[v[a>>2]+12>>2]](a,e+C|0,8524,116),i,o),d&&(f[b+o|0]=47),hn((b+o|0)+d|0,n),P=u,E=Be(e=Xf(68),n=gu(u+4|0,n,1)),v[P+56>>2]=E,lf(n),cn(u+60|0,c,u+16|0),n=du(v[u+16>>2],h),v[v[u+56>>2]+60>>2]=n,n=du(v[u+24>>2],s),a=v[u+56>>2],v[a+64>>2]=n,cn(u+60|0,c,u+16|0),e=v[u+16>>2],k=v[u+20>>2]-e|0,n=8;;){if(n){if(Te(v[8848+((n=n-1|0)<<2)>>2],e,k))continue}else n=0;break}for(v[a+40>>2]=n,cn(u+60|0,c,u+16|0),e=v[u+16>>2],k=v[u+20>>2]-e|0,n=8;;){if(n){if(Te(v[8880+((n=n-1|0)<<2)>>2],e,k))continue}else n=0;break}for(v[a+44>>2]=n,e=v[u+24>>2],k=v[u+28>>2]-e|0,n=8;;){if(n){if(Te(v[8880+((n=n-1|0)<<2)>>2],e,k))continue}else n=0;break}v[a+48>>2]=n,Ui(u+60|0,c,u+48|0),v[a+52>>2]=1,v[a+56>>2]=1;r:if(Te(5309,k=v[u+48>>2],y=v[u+52>>2]-k|0)){e=a+56|0,n=a+52|0;n:{e:{if(1==(0|y))switch(l[0|k]-120|0){case 0:break n;case 1:break e;default:break r}if(Te(1056,k,y))break r;v[a+52>>2]=2}n=e}v[n>>2]=2}t?((n=v[r+36>>2])&&(e=gu(u+4|0,b,0),da[v[v[n>>2]+8>>2]](n,a,e),lf(e)),n=v[5208],da[v[v[n>>2]+20>>2]](n,b,8524,156)):(n=gu(u+4|0,b,1),ee(v[u+56>>2]+28|0,n),lf(n)),Sn($,u+56|0)}else v[u+56>>2]=0;if(!ie(u+60|0,c,u+48|0))break}D=u- -64|0}function tr(r,n){var e=0,i=0,t=0,u=0,o=0,a=0,c=0,b=0;r:{n:{e:{i:{f:{if((0|(e=f[0|n]))<=90)switch(e-34|0){case 11:case 14:case 15:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:break n;case 0:break f;default:break r}if((0|e)<=109){if(91==(0|e))break i;if(102!=(0|e))break r;if(Te(n+1|0,5157,4))break r;return v[r+8>>2]=0,n+5|0}if(123==(0|e))break e;if(116!=(0|e)){if(110!=(0|e))break r;if(Te(n+1|0,4507,3))break r;return v[r+8>>2]=2,n+4|0}if(Te(n+1|0,4980,3))break r;return v[r+20>>2]=1,v[r+8>>2]=1,n+4|0}return yr(r,n)}for(t=r,v[r+8>>2]=5,n=n+1|0;n=(r=n)+1|0,((e=l[0|r])-1&255)>>>0<32;);i:{f:{if(93!=(0|e)){for(He(e=Tt(32),0),v[t+4>>2]=e;n=r,r=r+1|0,(l[0|n]-1&255)>>>0<32;);if(!(n=tr(e,n)))break f;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);for(n=1;;){t:{if(v[t+12>>2]=n,44!=(0|(n=l[0|r]))){if(93!=(0|n))break t;n=r+1|0;break i}for(He(i=Tt(32),0),v[e>>2]=i;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(!(n=tr(i,r)))break f;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);n=v[t+12>>2]+1|0,e=i;continue}break}v[5218]=r,n=0}break i}n=0}return n}for(o=r,v[r+8>>2]=6,e=n+1|0;e=(r=e)+1|0,((n=l[0|r])-1&255)>>>0<32;);e:{i:{f:if(125!=(0|n)){for(He(i=Tt(32),0),v[o+4>>2]=i;n=r,r=r+1|0,(l[0|n]-1&255)>>>0<32;);if(r=0,!(e=yr(i,n)))break e;for(;e=(r=e)+1|0,(l[0|r]-1&255)>>>0<32;);if(v[i+28>>2]=v[i+16>>2],e=0,v[i+16>>2]=0,58!=l[0|r])break i;for(;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(n=tr(i,r)){for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);for(e=1;;){t:{if(v[o+12>>2]=e,44!=(0|(n=l[0|r]))){if(125!=(0|n))break t;r=r+1|0;break e}for(He(t=Tt(32),0),v[i>>2]=t;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(e=0,!(n=yr(t,r)))break f;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);if(v[t+28>>2]=v[t+16>>2],v[t+16>>2]=0,58!=l[0|r]){v[5218]=r,r=0;break e}for(;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(!(n=tr(t,r)))break f;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);e=v[o+12>>2]+1|0,i=t;continue}break}e=0,v[5218]=r}}r=e;break e}v[5218]=r,r=0}return r}if(i=r,t=n,a=l[0|n],((n=l[0|(e=(45==(0|a))+n|0)])-48&255)>>>0>9)r=e;else for(;u=10*u+ +((255&n)-48|0),n=l[e+1|0],e=r=e+1|0,(n-48&255)>>>0<10;);if(46==(255&n)){if(n=r+1|0,((e=l[r+1|0])-48&255)>>>0>9)r=n;else for(;c=10*c+ +((255&e)-48|0),o=o+1|0,e=l[n+1|0],n=r=n+1|0,(e-48&255)>>>0<10;);u+=c/(+(yi(o)>>>0)+4294967296*+(V>>>0)),n=l[0|r]}if(b=c=45==(0|a)?-u:u,101==(255&n|32)){if(n=(e=45==(0|(a=l[r+1|0]))|43==(0|a))?r+2|0:r+1|0,o=0,((e=l[(e?2:1)+r|0])-48&255)>>>0>9)r=n;else for(;o=(m(o,10)+(255&e)|0)-48|0,e=l[n+1|0],n=r=n+1|0,(e-48&255)>>>0<10;);b=c/(u=+(yi(o)>>>0)+4294967296*+(V>>>0)),45!=(0|a)&&(b=c*u)}return u=b,(0|r)==(0|t)?(v[5218]=t,r=0):(v[i+8>>2]=3,n=g(u)<2147483648?~~u:-2147483648,v[i+20>>2]=n,p[i+24>>2]=u),r}return v[5218]=n,0}function ur(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t|=0,u=w(u),o=w(o);var a,c,b,k,s=w(0),h=w(0),d=w(0),y=w(0),m=w(0),F=w(0),A=0,$=w(0),I=w(0),C=w(0),P=w(0),E=w(0),O=w(0),R=0,S=w(0),W=w(0),G=w(0),U=w(0),j=w(0),H=w(0);if(A=v[r+12>>2],o!=w(0)){if(l[r+88|0]||Fr(r),l[n+88|0]||Fr(n),P=p[n+60>>2],G=p[r+64>>2],U=p[r+60>>2],h=p[n+72>>2],$=p[A+96>>2],y=p[A+104>>2],I=p[A+92>>2],C=p[v[n+4>>2]+24>>2],d=p[A+108>>2],R=(O=p[r+72>>2])<w(0),a=(s=p[r+76>>2])<w(0),F=R?w(-O):O,j=a?w(-s):s,(S=(s=w(F-j))<w(0)?w(-s):s)<=w(9999999747378752e-20)?(W=p[n+64>>2],s=w(p[r+112>>2]+w(w(p[r+104>>2]*P)+w(W*p[r+108>>2]))),m=w(p[r+100>>2]+w(w(p[r+92>>2]*P)+w(W*p[r+96>>2])))):(s=w(w(p[r+104>>2]*P)+p[r+112>>2]),m=w(w(p[r+92>>2]*P)+p[r+100>>2])),E=w(m-p[A+100>>2]),s=w(s-p[A+112>>2]),m=w(w(1)/w(w(I*d)-w(y*$))),H=w(w(w(w(d*E)-w($*s))*m)-U),s=w(w(w(w(s*I)-w(y*E))*m)-G),+(s=w(T(w(w(H*H)+w(s*s)))))<1e-4)return gr(r,e,i,0,t,0,o),void cr(n,P,W,w(0),p[n+72>>2],p[n+76>>2],p[n+80>>2],p[n+84>>2]);k=0-(c=R?-1:1)|0,E=(b=h<w(0))?w(-h):h,C=w(E*C),h=w(e-p[A+100>>2]),i=w(i-p[A+112>>2]),e=w(w(w(w(h*d)-w($*i))*m)-U),h=w(w(w(w(i*I)-w(y*h))*m)-G),d=w(w(e*e)+w(h*h)),u!=w(0)&&(i=w(w(w(F*w(E+w(1)))*w(.5))*u),$=w(T(d)),(u=w(i+w(w($-s)-w(C*F))))>w(0)&&(d=i,i=(i=w(u/w(i+i)))>w(1)?w(0):w(i+w(-1)),i=w(w(-w(u-w(d*w(w(1)-w(i*i)))))/$),e=w(w(i*e)+e),h=w(w(i*h)+h),d=w(w(e*e)+w(h*h)))),A=a?k:c,E=w(R?180:0),i=w(F*C),S<=w(9999999747378752e-20)?(u=w(-1),(F=w(w(w(d-w(s*s))-w(i*i))/w(i*w(s+s))))<w(-1)||(u=(R=F>w(1))?w(1):F,!R|!t||(u=w(1),O=w(O*w(w(w(w(w(T(d))/w(i+s))+w(-1))*o)+w(1))))),s=w(w(i*u)+s),d=w(Yr(u)*w(0|f)),i=w(i*Wr(d)),u=Pr(w(w(h*s)-w(i*e)),w(w(e*s)+w(h*i)))):($=w(0),S=Pr(h,e),C=w(j*C),e=w(C*C),u=w(w(e*w(-2))*s),h=w(i*i),y=w(e-h),I=w(w(w(e*w(s*s))+w(h*d))-w(h*e)),(m=w(w(u*u)+w(w(y*w(-4))*I)))>=w(0)&&(m=w(T(m)),u=w(w(u+(u<w(0)?w(-m):m))*w(-.5)),y=w(u/y),u=w(I/u),y=w(g(y))<w(g(u))?y:u,(u=w(y*y))<=d)?(e=w(w(T(w(d-u)))*w(0|f)),u=w(S-Pr(e,y)),d=Pr(w(e/j),w(w(y-s)/F))):(y=w(i+s),I=w(y*y),u=w(s-i),F=w(u*u),m=w(3.1415927410125732),!(!((e=w(w(s*w(-i))/w(h-e)))>=w(-1))|!(e<=w(1)))&&(h=Wr(e=Yr(e)),s=w(w(i*Rr(e))+s),h=w(C*h),F>(i=w(w(s*s)+w(h*h)))&&(m=e,F=i,$=h,u=s),i>I)||(e=w(0),s=y,i=I,h=w(0)),t=w(w(F+i)*w(.5))>=d,i=w(0|f),u=w(S-Pr(w((t?$:h)*i),t?u:s)),d=w((t?m:e)*i))),e=w(b?180:0),s=u,i=w(0|A),u=w(Pr(W,P)*i),(s=w(w(w(w(s-u)*w(57.2957763671875))+E)-p[r+68>>2]))>w(180)?s=w(s+w(-360)):s<w(-180)&&(s=w(s+w(360))),cr(r,U,G,w(w(s*o)+p[r+40>>2]),O,p[r+76>>2],w(0),w(0)),s=w(w(d+u)*w(57.2957763671875)),u=p[n+80>>2],i=w(w(w(s-u)*i)+e),e=p[n+68>>2],(s=w(i-e))>w(180)?s=w(s+w(-360)):s<w(-180)&&(s=w(s+w(360))),cr(n,P,W,w(w(s*o)+e),p[n+72>>2],p[n+76>>2],u,p[n+84>>2])}else Of(n)}function or(r){var n=0,e=0,i=0,f=0,t=0,u=0,o=0,a=0,c=0;r:if(r|=0){t=(i=r-8|0)+(r=-8&(n=v[r-4>>2]))|0;n:if(!(1&n)){if(!(3&n))break r;if((i=i-(n=v[i>>2])|0)>>>0<d[5378])break r;r=r+n|0;e:{i:{if(v[5379]!=(0|i)){if(n>>>0<=255){if(f=n>>>3|0,(0|(n=v[i+12>>2]))==(0|(e=v[i+8>>2]))){a=21496,c=v[5374]&Qf(f),v[a>>2]=c;break n}v[e+12>>2]=n,v[n+8>>2]=e;break n}if(u=v[i+24>>2],(0|i)!=(0|(n=v[i+12>>2]))){e=v[i+8>>2],v[e+12>>2]=n,v[n+8>>2]=e;break e}if(!(e=v[(f=i+20|0)>>2])){if(!(e=v[i+16>>2]))break i;f=i+16|0}for(;o=f,(e=v[(f=(n=e)+20|0)>>2])||(f=n+16|0,e=v[n+16>>2]););v[o>>2]=0;break e}if(3&~(n=v[t+4>>2]))break n;return v[5376]=r,v[t+4>>2]=-2&n,v[i+4>>2]=1|r,void(v[t>>2]=r)}n=0}if(u){e=v[i+28>>2];e:{if(v[(f=21800+(e<<2)|0)>>2]==(0|i)){if(v[f>>2]=n,n)break e;a=21500,c=v[5375]&Qf(e),v[a>>2]=c;break n}if(v[u+(v[u+16>>2]==(0|i)?16:20)>>2]=n,!n)break n}v[n+24>>2]=u,(e=v[i+16>>2])&&(v[n+16>>2]=e,v[e+24>>2]=n),(e=v[i+20>>2])&&(v[n+20>>2]=e,v[e+24>>2]=n)}}if(!(i>>>0>=t>>>0)&&1&(n=v[t+4>>2])){n:{e:{i:{f:{if(!(2&n)){if(v[5380]==(0|t)){if(v[5380]=i,r=v[5377]+r|0,v[5377]=r,v[i+4>>2]=1|r,v[5379]!=(0|i))break r;return v[5376]=0,void(v[5379]=0)}if(v[5379]==(0|t))return v[5379]=i,r=v[5376]+r|0,v[5376]=r,v[i+4>>2]=1|r,void(v[r+i>>2]=r);if(r=(-8&n)+r|0,n>>>0<=255){if(f=n>>>3|0,(0|(n=v[t+12>>2]))==(0|(e=v[t+8>>2]))){a=21496,c=v[5374]&Qf(f),v[a>>2]=c;break e}v[e+12>>2]=n,v[n+8>>2]=e;break e}if(u=v[t+24>>2],(0|t)!=(0|(n=v[t+12>>2]))){e=v[t+8>>2],v[e+12>>2]=n,v[n+8>>2]=e;break i}if(!(e=v[(f=t+20|0)>>2])){if(!(e=v[t+16>>2]))break f;f=t+16|0}for(;o=f,(e=v[(f=(n=e)+20|0)>>2])||(f=n+16|0,e=v[n+16>>2]););v[o>>2]=0;break i}v[t+4>>2]=-2&n,v[i+4>>2]=1|r,v[r+i>>2]=r;break n}n=0}if(u){e=v[t+28>>2];i:{if(v[(f=21800+(e<<2)|0)>>2]==(0|t)){if(v[f>>2]=n,n)break i;a=21500,c=v[5375]&Qf(e),v[a>>2]=c;break e}if(v[u+(v[u+16>>2]==(0|t)?16:20)>>2]=n,!n)break e}v[n+24>>2]=u,(e=v[t+16>>2])&&(v[n+16>>2]=e,v[e+24>>2]=n),(e=v[t+20>>2])&&(v[n+20>>2]=e,v[e+24>>2]=n)}}if(v[i+4>>2]=1|r,v[r+i>>2]=r,v[5379]==(0|i))return void(v[5376]=r)}if(r>>>0<=255)return n=21536+(-8&r)|0,(e=v[5374])&(r=1<<(r>>>3))?r=v[n+8>>2]:(v[5374]=r|e,r=n),v[n+8>>2]=i,v[r+12>>2]=i,v[i+12>>2]=n,void(v[i+8>>2]=r);e=31,r>>>0<=16777215&&(e=62+((r>>>38-(n=F(r>>>8|0))&1)-(n<<1)|0)|0),v[i+28>>2]=e,v[i+16>>2]=0,v[i+20>>2]=0,n=21800+(e<<2)|0;n:{e:{if((f=v[5375])&(o=1<<e)){for(e=r<<(31!=(0|e)?25-(e>>>1|0):0),n=v[n>>2];;){if(f=n,(-8&v[n+4>>2])==(0|r))break e;if(n=e>>>29|0,e<<=1,!(n=v[16+(o=f+(4&n)|0)>>2]))break}v[o+16>>2]=i,v[i+24>>2]=f}else v[5375]=f|o,v[n>>2]=i,v[i+24>>2]=n;v[i+12>>2]=i,v[i+8>>2]=i;break n}r=v[f+8>>2],v[r+12>>2]=i,v[f+8>>2]=i,v[i+24>>2]=0,v[i+12>>2]=f,v[i+8>>2]=r}r=v[5382]-1|0,v[5382]=r||-1}}}function ar(r,n){var e,i=0,f=0,t=0,u=0,o=0,a=0,c=0;e=r+n|0;r:{n:if(!(1&(i=v[r+4>>2]))){if(!(3&i))break r;n=(i=v[r>>2])+n|0;e:{i:{f:{if((0|(r=r-i|0))!=v[5379]){if(i>>>0<=255){if((0|(f=v[r+8>>2]))!=(0|(t=v[r+12>>2])))break f;a=21496,c=v[5374]&Qf(i>>>3|0),v[a>>2]=c;break n}if(u=v[r+24>>2],(0|(i=v[r+12>>2]))!=(0|r)){f=v[r+8>>2],v[f+12>>2]=i,v[i+8>>2]=f;break e}if(!(f=v[(t=r+20|0)>>2])){if(!(f=v[r+16>>2]))break i;t=r+16|0}for(;o=t,(f=v[(t=(i=f)+20|0)>>2])||(t=i+16|0,f=v[i+16>>2]););v[o>>2]=0;break e}if(3&~(i=v[e+4>>2]))break n;return v[5376]=n,v[e+4>>2]=-2&i,v[r+4>>2]=1|n,void(v[e>>2]=n)}v[f+12>>2]=t,v[t+8>>2]=f;break n}i=0}if(u){f=v[r+28>>2];e:{if(v[(t=21800+(f<<2)|0)>>2]==(0|r)){if(v[t>>2]=i,i)break e;a=21500,c=v[5375]&Qf(f),v[a>>2]=c;break n}if(v[u+(v[u+16>>2]==(0|r)?16:20)>>2]=i,!i)break n}v[i+24>>2]=u,(f=v[r+16>>2])&&(v[i+16>>2]=f,v[f+24>>2]=i),(f=v[r+20>>2])&&(v[i+20>>2]=f,v[f+24>>2]=i)}}n:{e:{i:{f:{if(!(2&(i=v[e+4>>2]))){if(v[5380]==(0|e)){if(v[5380]=r,n=v[5377]+n|0,v[5377]=n,v[r+4>>2]=1|n,v[5379]!=(0|r))break r;return v[5376]=0,void(v[5379]=0)}if(v[5379]==(0|e))return v[5379]=r,n=v[5376]+n|0,v[5376]=n,v[r+4>>2]=1|n,void(v[r+n>>2]=n);if(n=(-8&i)+n|0,i>>>0<=255){if(t=i>>>3|0,(0|(i=v[e+12>>2]))==(0|(f=v[e+8>>2]))){a=21496,c=v[5374]&Qf(t),v[a>>2]=c;break e}v[f+12>>2]=i,v[i+8>>2]=f;break e}if(u=v[e+24>>2],(0|e)!=(0|(i=v[e+12>>2]))){f=v[e+8>>2],v[f+12>>2]=i,v[i+8>>2]=f;break i}if(!(f=v[(t=e+20|0)>>2])){if(!(f=v[e+16>>2]))break f;t=e+16|0}for(;o=t,(f=v[(t=(i=f)+20|0)>>2])||(t=i+16|0,f=v[i+16>>2]););v[o>>2]=0;break i}v[e+4>>2]=-2&i,v[r+4>>2]=1|n,v[r+n>>2]=n;break n}i=0}if(u){f=v[e+28>>2];i:{if(v[(t=21800+(f<<2)|0)>>2]==(0|e)){if(v[t>>2]=i,i)break i;a=21500,c=v[5375]&Qf(f),v[a>>2]=c;break e}if(v[u+(v[u+16>>2]==(0|e)?16:20)>>2]=i,!i)break e}v[i+24>>2]=u,(f=v[e+16>>2])&&(v[i+16>>2]=f,v[f+24>>2]=i),(f=v[e+20>>2])&&(v[i+20>>2]=f,v[f+24>>2]=i)}}if(v[r+4>>2]=1|n,v[r+n>>2]=n,v[5379]==(0|r))return void(v[5376]=n)}if(n>>>0<=255)return i=21536+(-8&n)|0,(f=v[5374])&(n=1<<(n>>>3))?n=v[i+8>>2]:(v[5374]=n|f,n=i),v[i+8>>2]=r,v[n+12>>2]=r,v[r+12>>2]=i,void(v[r+8>>2]=n);f=31,n>>>0<=16777215&&(f=62+((n>>>38-(i=F(n>>>8|0))&1)-(i<<1)|0)|0),v[r+28>>2]=f,v[r+16>>2]=0,v[r+20>>2]=0,i=21800+(f<<2)|0;n:{if((t=v[5375])&(o=1<<f)){for(f=n<<(31!=(0|f)?25-(f>>>1|0):0),i=v[i>>2];;){if(t=i,(-8&v[i+4>>2])==(0|n))break n;if(i=f>>>29|0,f<<=1,!(i=v[16+(o=t+(4&i)|0)>>2]))break}v[o+16>>2]=r,v[r+24>>2]=t}else v[5375]=t|o,v[i>>2]=r,v[r+24>>2]=i;return v[r+12>>2]=r,void(v[r+8>>2]=r)}n=v[t+8>>2],v[n+12>>2]=r,v[t+8>>2]=r,v[r+24>>2]=0,v[r+12>>2]=t,v[r+8>>2]=n}}function cr(r,n,e,i,t,u,o,a){r|=0,n=w(n),e=w(e),i=w(i),t=w(t),u=w(u),o=w(o),a=w(a);var c,b=w(0),k=w(0),s=w(0),l=w(0),h=0,d=w(0),y=w(0),m=w(0),F=0,A=w(0);if(f[r+88|0]=1,p[r+84>>2]=a,p[r+80>>2]=o,p[r+76>>2]=u,p[r+72>>2]=t,p[r+68>>2]=i,p[r+64>>2]=e,p[r+60>>2]=n,c=v[r+8>>2],d=p[c+168>>2],y=p[c+164>>2],!(h=v[r+12>>2]))return o=w(w(i+o)*w(.01745329238474369)),F=r,A=w(w(Wr(o)*t)*d),p[F+104>>2]=A,F=r,A=w(w(Rr(o)*t)*y),p[F+92>>2]=A,i=w(w(w(i+w(90))+a)*w(.01745329238474369)),F=r,A=w(w(Wr(i)*u)*d),p[F+108>>2]=A,F=r,A=w(w(Rr(i)*u)*y),p[F+96>>2]=A,p[r+100>>2]=w(n*y)+p[c+172>>2],void(p[r+112>>2]=w(e*d)+p[c+176>>2]);b=p[h+104>>2],s=p[h+108>>2],k=p[h+92>>2],l=p[h+96>>2],p[r+100>>2]=w(w(k*n)+w(l*e))+p[h+100>>2],p[r+112>>2]=w(w(b*n)+w(s*e))+p[h+112>>2];r:{n:{e:{i:{f:{switch(0|(h=v[v[r+4>>2]+56>>2])){case 3:case 4:break n;case 2:break e;case 1:break i;case 0:break f}i=p[r+108>>2],n=p[r+104>>2],u=p[r+96>>2],t=p[r+92>>2];break r}return e=Wr(n=w(w(i+o)*w(.01745329238474369))),n=w(Rr(n)*t),e=w(e*t),p[r+104>>2]=w(b*n)+w(e*s),p[r+92>>2]=w(k*n)+w(e*l),e=Wr(n=w(w(w(i+w(90))+a)*w(.01745329238474369))),n=w(Rr(n)*u),e=w(e*u),p[r+108>>2]=w(b*n)+w(e*s),void(p[r+96>>2]=w(k*n)+w(e*l))}e=w(w(i+o)*w(.01745329238474369)),n=w(Wr(e)*t),t=w(Rr(e)*t),e=w(w(w(i+w(90))+a)*w(.01745329238474369)),i=w(Wr(e)*u),u=w(Rr(e)*u);break r}n=w(i+o),(e=w(w(k*k)+w(b*b)))>w(9999999747378752e-20)?(e=w(w(g(w(w(k*s)-w(b*l))))/e),s=w(k*e),l=w(b*e),e=w(Pr(b,k)*w(57.2957763671875))):(b=w(0),k=w(0),e=w(w(Pr(s,l)*w(-57.2957763671875))+w(90))),o=Wr(n=w(w(n-e)*w(.01745329238474369))),m=w(Rr(n)*t),t=w(o*t),n=w(w(b*m)+w(s*t)),t=w(w(k*m)-w(t*l)),i=Wr(e=w(w(w(w(i+a)-e)+w(90))*w(.01745329238474369))),e=w(Rr(e)*u),u=w(i*u),i=w(w(b*e)+w(s*u)),u=w(w(k*e)-w(u*l));break r}n=Wr(e=w(i*w(.01745329238474369))),i=Rr(e),m=e=w(w(w(k*i)+w(n*l))/y),n=w(w(w(b*i)+w(n*s))/d),i=(e=w(T(w(w(e*e)+w(n*n)))))>w(9999999747378752e-21)?w(w(1)/e):e,e=w(m*i),n=w(n*i),i=w(T(w(w(e*e)+w(n*n)))),b=3==(0|h)&&y<w(0)^d<w(0)^w(w(k*s)-w(b*l))<w(0)?w(-i):i,i=Wr(k=w(Pr(n,e)+w(1.5707963705062866))),s=Wr(a=w(w(a+w(90))*w(.01745329238474369))),a=w(Rr(a)*u),u=w(s*u),s=w(b*i),i=w(w(n*a)+w(u*s)),l=Wr(o=w(o*w(.01745329238474369))),o=w(Rr(o)*t),t=w(l*t),n=w(w(n*o)+w(t*s)),m=w(e*a),a=w(b*Rr(k)),u=w(m+w(u*a)),t=w(w(e*o)+w(t*a))}p[r+108>>2]=d*i,p[r+104>>2]=d*n,p[r+96>>2]=y*u,p[r+92>>2]=y*t}function br(r,n){var e,i=0,f=0,t=0,u=0,o=0,a=0,c=0;if(D=e=D-16|0,v[r+4>>2]=n,v[r+120>>2]=9196,v[r+116>>2]=0,v[r+108>>2]=0,v[r+112>>2]=0,v[r+104>>2]=10232,v[r+100>>2]=0,v[r+92>>2]=0,v[r+96>>2]=0,v[r+88>>2]=10216,v[r+84>>2]=0,v[r+76>>2]=0,v[r+80>>2]=0,v[r+72>>2]=10200,v[r+68>>2]=0,v[r+60>>2]=0,v[r+64>>2]=0,v[r+56>>2]=10184,v[r+52>>2]=0,v[r+44>>2]=0,v[r+48>>2]=0,v[r+40>>2]=10168,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+24>>2]=10168,v[r+20>>2]=0,v[r+12>>2]=0,v[r+16>>2]=0,v[r+8>>2]=9196,v[r>>2]=10152,v[r+132>>2]=0,v[r+136>>2]=0,v[r+124>>2]=0,v[r+128>>2]=0,v[r+140>>2]=9308,v[r+144>>2]=1065353216,v[r+148>>2]=1065353216,v[r+152>>2]=1065353216,v[r+156>>2]=1065353216,ci(r+140|0),v[r+176>>2]=0,v[r+168>>2]=1065353216,v[r+172>>2]=0,v[r+160>>2]=0,v[r+164>>2]=1065353216,u=r+8|0,i=v[r+4>>2],ca(u,v[(n=i)+20>>2]),v[n+20>>2])for(n=0;f=v[v[i+28>>2]+(n<<2)>>2],(t=v[f+20>>2])?(t=v[v[r+20>>2]+(v[t+4>>2]<<2)>>2],a=e,c=ye(Xf(120),f,r,t),v[a+12>>2]=c,Sn(t+16|0,e+12|0)):(a=e,c=ye(Xf(120),f,r,0),v[a+12>>2]=c),Sn(u,e+12|0),(n=n+1|0)>>>0<d[i+20>>2];);if(f=r+24|0,i=v[r+4>>2],ca(f,v[(n=i)+36>>2]),ca(t=r+40|0,v[n+36>>2]),v[n+36>>2])for(n=0;u=v[v[i+44>>2]+(n<<2)>>2],o=v[v[r+20>>2]+(v[v[u+20>>2]+4>>2]<<2)>>2],a=e,c=Tn(Xf(84),u,o),v[a+12>>2]=c,Sn(f,u=e+12|0),Sn(t,u),(n=n+1|0)>>>0<d[i+36>>2];);if(f=r+56|0,i=v[r+4>>2],ca(f,v[(n=i)+104>>2]),v[n+104>>2])for(n=0;t=v[v[i+112>>2]+(n<<2)>>2],a=e,c=en(Xf(48),t,r),v[a+12>>2]=c,Sn(f,e+12|0),(n=n+1|0)>>>0<d[i+104>>2];);if(f=r+72|0,i=v[r+4>>2],ca(f,v[(n=i)+120>>2]),v[n+120>>2])for(n=0;t=v[v[i+128>>2]+(n<<2)>>2],a=e,c=fn(Xf(48),t,r),v[a+12>>2]=c,Sn(f,e+12|0),(n=n+1|0)>>>0<d[i+120>>2];);if(f=r+88|0,i=v[r+4>>2],ca(f,v[(n=i)+136>>2]),v[n+136>>2])for(n=0;t=v[v[i+144>>2]+(n<<2)>>2],a=e,c=Ar(Xf(144),t,r),v[a+12>>2]=c,Sn(f,e+12|0),(n=n+1|0)>>>0<d[i+136>>2];);return rr(r),D=e+16|0,r}function kr(r,n,e,i,f,t,u){var o,a=0,c=w(0),b=w(0),k=0,s=0,l=w(0),h=w(0),d=w(0),y=0,g=0,F=w(0),A=w(0),T=0,$=w(0),I=0,C=0;o=m(i>>>1|0,u)+t|0,k=v[n+8>>2];r:{n:{e:{if(v[r+24>>2]){if(e)break e;i=0;break n}if(t>>>0>=o>>>0)break r;for(c=p[k+108>>2],b=p[k+104>>2],l=p[k+96>>2],h=p[k+92>>2],d=p[k+112>>2],$=p[k+100>>2],r=v[12+(v[n+72>>2]?n+68|0:r+36|0)>>2];n=(t<<2)+f|0,F=p[(i=r+(e<<2)|0)>>2],A=p[i+4>>2],p[n+4>>2]=d+w(w(F*b)+w(c*A)),p[n>>2]=$+w(w(F*h)+w(l*A)),e=e+2|0,o>>>0>(t=t+u|0)>>>0;);break r}for(y=v[r+32>>2],i=0;i=(g=v[y+(a<<2)>>2])+i|0,a=1+(a+g|0)|0,(s=s+2|0)>>>0<e>>>0;);}if(k=v[k+8>>2],v[n+72>>2]){if(!(t>>>0>=o>>>0))for(s=m(i,3),e=i<<1,g=v[n+80>>2],I=v[r+48>>2],n=v[r+32>>2];;){if(i=a+1|0,(0|(r=v[n+(a<<2)>>2]))<=0)c=w(0),a=i,b=w(0);else for(a=(0|(r=r+i|0))>(0|(a=a+2|0))?r:a,C=v[k+20>>2],b=w(0),c=w(0);r=v[(v[n+(i<<2)>>2]<<2)+C>>2],T=g+(e<<2)|0,l=w(p[(y=(s<<2)+I|0)>>2]+p[T>>2]),h=w(p[y+4>>2]+p[T+4>>2]),d=p[y+8>>2],b=w(w(w(p[r+112>>2]+w(w(l*p[r+104>>2])+w(h*p[r+108>>2])))*d)+b),c=w(w(w(p[r+100>>2]+w(w(l*p[r+92>>2])+w(h*p[r+96>>2])))*d)+c),e=e+2|0,s=s+3|0,(0|a)!=(0|(i=i+1|0)););if(p[(r=(t<<2)+f|0)>>2]=c,p[r+4>>2]=b,!(o>>>0>(t=t+u|0)>>>0))break}}else{if(t>>>0>=o>>>0)break r;for(s=m(i,3),y=v[r+48>>2],n=v[r+32>>2];;){if(i=a+1|0,(0|(r=v[n+(a<<2)>>2]))<=0)c=w(0),b=w(0),a=i;else for(a=(0|(r=r+i|0))>(0|(e=a+2|0))?r:e,g=v[k+20>>2],b=w(0),c=w(0);r=v[g+(v[n+(i<<2)>>2]<<2)>>2],l=p[(e=y+(s<<2)|0)>>2],h=p[e+4>>2],d=p[e+8>>2],b=w(w(w(p[r+112>>2]+w(w(l*p[r+104>>2])+w(h*p[r+108>>2])))*d)+b),c=w(w(w(p[r+100>>2]+w(w(l*p[r+92>>2])+w(h*p[r+96>>2])))*d)+c),s=s+3|0,(0|a)!=(0|(i=i+1|0)););if(p[(r=(t<<2)+f|0)>>2]=c,p[r+4>>2]=b,!(o>>>0>(t=t+u|0)>>>0))break}}}}function sr(r){r|=0;var n,e=w(0),i=0,f=w(0),t=0,u=w(0),o=w(0),a=0,c=0,b=w(0),k=w(0),s=w(0),l=0;D=n=D-16|0,(0|(c=v[r+108>>2]))!=(0|(t=v[r+124>>2]))&&(v[n+12>>2]=0,yn(r+104|0,t,n+12|0),c=v[r+124>>2]),e=p[r+184>>2],f=p[r+180>>2];r:{n:{if(270!=(0|(t=v[r+232>>2]))){if(180!=(0|t)){if(90!=(0|t))break n;if((0|c)<=0)break r;for(u=p[r+84>>2],o=w(u/w(p[r+192>>2]-e)),b=p[r+92>>2],u=w(e-w(w(w(b-p[r+76>>2])-u)/o)),k=p[r+88>>2],e=w(k/w(p[r+188>>2]-f)),s=f,f=p[r+96>>2],k=w(s-w(w(w(f-p[r+80>>2])-k)/e)),o=w(b/o),e=w(f/e),t=v[r+116>>2],a=v[r+132>>2],r=0;l=4|(i=r<<2),p[t+i>>2]=w(p[a+l>>2]*e)+k,p[t+l>>2]=w(w(w(1)-p[i+a>>2])*o)+u,(0|c)>(0|(r=r+2|0)););break r}if((0|c)<=0)break r;for(u=p[r+84>>2],o=w(u/w(p[r+188>>2]-f)),s=f,f=p[r+92>>2],u=w(s-w(w(w(f-p[r+76>>2])-u)/o)),b=w(p[r+88>>2]/w(p[r+192>>2]-e)),k=w(p[r+96>>2]/b),f=w(f/o),e=w(e-w(p[r+80>>2]/b)),t=v[r+116>>2],a=v[r+132>>2],r=0;p[(i=r<<2)+t>>2]=w(w(w(1)-p[i+a>>2])*f)+u,p[(i|=4)+t>>2]=w(w(w(1)-p[i+a>>2])*k)+e,(0|c)>(0|(r=r+2|0)););break r}if((0|c)<=0)break r;for(o=w(p[r+88>>2]/w(p[r+192>>2]-e)),u=w(p[r+92>>2]/o),b=w(p[r+84>>2]/w(p[r+188>>2]-f)),k=w(p[r+96>>2]/b),e=w(e-w(p[r+76>>2]/o)),f=w(f-w(p[r+80>>2]/b)),t=v[r+116>>2],a=v[r+132>>2],r=0;l=4|(i=r<<2),p[t+i>>2]=w(w(w(1)-p[a+l>>2])*k)+f,p[t+l>>2]=w(p[i+a>>2]*u)+e,(0|c)>(0|(r=r+2|0)););break r}if(!((0|c)<=0))for(u=p[r+88>>2],o=w(u/w(p[r+192>>2]-e)),s=e,e=p[r+96>>2],u=w(s-w(w(w(e-p[r+80>>2])-u)/o)),e=w(e/o),o=w(p[r+84>>2]/w(p[r+188>>2]-f)),b=w(p[r+92>>2]/o),f=w(f-w(p[r+76>>2]/o)),t=v[r+116>>2],a=v[r+132>>2],r=0;p[(i=r<<2)+t>>2]=w(p[i+a>>2]*b)+f,p[(i|=4)+t>>2]=w(p[i+a>>2]*e)+u,(0|c)>(0|(r=r+2|0)););}D=n+16|0}function vr(r,n,e,i){var f,t=0,u=0,o=0,a=0,c=0,b=0,k=w(0),s=0,l=0;if(D=f=D-32|0,d[r+8>>2]<=n>>>0){if(v[f+24>>2]=0,v[f+16>>2]=0,v[f+20>>2]=0,v[f+12>>2]=10696,o=f+12|0,b=v[r+8>>2],t=n+1|0,v[r+8>>2]=t,t>>>0>d[r+12>>2]&&(k=w(w(t>>>0)*w(1.75)),t=(t=w(g(k))<w(2147483648)?~~k:-2147483648)>>>0<=8?8:t,v[r+12>>2]=t,u=v[5208],s=r,l=0|da[v[v[u>>2]+16>>2]](u,v[r+16>>2],t<<4,8524,85),v[s+16>>2]=l,t=v[r+8>>2]),t>>>0>b>>>0)for(;;){if(t=v[r+16>>2]+(b<<4)|0,v[t>>2]=10696,v[t+4>>2]=v[o+4>>2],u=v[o+8>>2],v[t+12>>2]=0,v[t+8>>2]=u,u&&(a=v[5208],s=t,l=0|da[v[v[a>>2]+12>>2]](a,m(u,20),8524,206),v[s+12>>2]=l,v[t+4>>2]))for(u=0;a=(c=m(u,20))+v[t+12>>2]|0,c=c+v[o+12>>2]|0,v[a>>2]=v[c>>2],Re(a+4|0,c+4|0),v[a+16>>2]=v[c+16>>2],(u=u+1|0)>>>0<d[t+4>>2];);if(!((b=b+1|0)>>>0<d[r+8>>2]))break}se(o)}r:{n:{e:{if(t=v[r+16>>2]+(n<<4)|0,v[t+4>>2])for(r=0;;){if(qi(4+(v[t+12>>2]+m(r,20)|0)|0,e))break e;if(!((r=r+1|0)>>>0<d[t+4>>2]))break}Fo(i);break n}if(Fo(i),!((0|r)<0)){r=m(r,20),Sf(v[16+(r+v[t+12>>2]|0)>>2]),v[16+(r+v[t+12>>2]|0)>>2]=i;break r}}v[f+12>>2]=n,o=Re(f+16|0,e),v[f+28>>2]=i,r=f+12|0,D=n=D-32|0,(0|(e=v[t+4>>2]))!=v[t+8>>2]?(v[t+4>>2]=e+1,e=v[t+12>>2]+m(e,20)|0,v[e>>2]=v[r>>2],Re(e+4|0,r+4|0),v[e+16>>2]=v[r+16>>2]):(v[n+12>>2]=v[r>>2],e=Re(n+16|0,r+4|0),v[n+28>>2]=v[r+16>>2],k=w(w(d[t+4>>2])*w(1.75)),r=(r=w(g(k))<w(2147483648)?~~k:-2147483648)>>>0<=8?8:r,v[t+8>>2]=r,i=v[5208],r=0|da[v[v[i>>2]+16>>2]](i,v[t+12>>2],m(r,20),8524,109),v[t+12>>2]=r,i=v[t+4>>2],v[t+4>>2]=i+1,r=r+m(i,20)|0,v[r>>2]=v[n+12>>2],Re(r+4|0,e),v[r+16>>2]=v[n+28>>2],lf(e)),D=n+32|0,lf(o)}D=f+32|0}function lr(r,n,e,i){var f,t,u,o,a,c=w(0),b=0,k=w(0),s=w(0),h=0,d=w(0),y=0,m=0,g=0,F=w(0),A=w(0),T=w(0),$=w(0),I=0,C=0;D=u=D-16|0,f=v[n+24>>2],v[f+24>>2]&&lr(r,f,e,i),(c=p[n+100>>2])!=w(0)?(s=(c=w(p[n+96>>2]/c))>w(1)?w(1):c,h=1,1!=(0|i)&&(h=v[f+112>>2])):(s=w(1),h=1!=(0|i)?i:0),i=r+56|0,b=p[f+40>>2]>s,k=p[f+52>>2],F=p[f+60>>2],A=p[f+48>>2],T=p[f+44>>2];r:if(l[f+36|0]){if((c=w(p[f+56>>2]-k))==w(0))break r;k=w(k+Cr(p[f+72>>2],c))}else k=(c=w(k+p[f+72>>2]))<(d=p[f+56>>2])?c:d;a=b?i:0,d=w(p[f+92>>2]*p[n+104>>2]),$=w(w(w(1)-s)*d),o=v[f+16>>2],t=v[o+8>>2];r:if(3!=(0|h)){if(I=f+148|0,(C=v[f+152>>2])||(v[u+12>>2]=0,yn(I,t<<1,u+12|0)),v[f+108>>2]=0,t)for(;;){b=v[(m=g<<2)+v[o+16>>2]>>2],i=0,c=$;n:{e:{switch(3&v[v[f+128>>2]+m>>2]){case 0:if(i=h,!(s<T)&&lu(0|da[v[v[b>>2]+8>>2]](b),20700)&&(i=0,4&l[v[f+128>>2]+m|0]))break n;if(s<A)break e;if(!lu(0|da[v[v[b>>2]+8>>2]](b),20808))break e;break n;case 2:c=d;break e;case 1:break e}y=v[v[f+144>>2]+m>>2],c=w(w(1)-w(p[y+96>>2]/p[y+100>>2])),c=w(d*(c<w(0)?w(0):c))}if(p[f+108>>2]=c+p[f+108>>2],lu(0|da[v[v[b>>2]+8>>2]](b),21020))hr(b,e,k,c,i,I,g<<1,!C);else{y=1;e:if(!i)if(lu(0|da[v[v[b>>2]+8>>2]](b),20700)){if(!(s<T|4&l[v[f+128>>2]+m|0]))break e;y=0}else y=!(s<A)|1^lu(0|da[v[v[b>>2]+8>>2]](b),20808);da[v[v[b>>2]+12>>2]](b,e,F,k,a,c,i,y)}}if((0|(g=g+1|0))==(0|t))break}}else{if(!t)break r;for(i=0;h=v[v[o+16>>2]+(i<<2)>>2],da[v[v[h>>2]+12>>2]](h,e,F,k,a,$,3,1),(0|t)!=(0|(i=i+1|0)););}return p[n+100>>2]>w(0)&&Jr(r,f,k),v[r+60>>2]=0,p[f+64>>2]=k,p[f+80>>2]=p[f+72>>2],D=u+16|0,s}function hr(r,n,e,i,f,t,u,o){var a,c=w(0),b=0,k=w(0),s=0,l=w(0);if(o&&(v[v[t+12>>2]+(u<<2)>>2]=0),i!=w(1)){a=v[v[n+20>>2]+(v[r+20>>2]<<2)>>2];r:if(0|da[v[v[a>>2]+16>>2]](a)){if(s=v[r+36>>2],p[s>>2]>e){n:switch(0|f){case 0:return void(p[a+40>>2]=p[v[a+4>>2]+36>>2]);case 1:break n;default:break r}l=p[a+40>>2],e=p[v[a+4>>2]+36>>2]}else n=f?a+40|0:v[a+4>>2]+36|0,l=p[n>>2],n=s+(v[r+28>>2]<<2)|0,p[n-8>>2]<=e?e=w(p[v[a+4>>2]+36>>2]+p[n-4>>2]):(n=(s=(f=Pe(r+24|0,e,2))<<2)+v[r+36>>2]|0,k=p[n-4>>2],c=e,e=p[n>>2],e=bn(r,(f>>1)-1|0,w(w(1)-w(w(c-e)/w(p[n-8>>2]-e)))),c=w(p[4+(v[r+36>>2]+s|0)>>2]-k),b=16384.499999999996-+w(c/w(360)),r=g(b)<2147483648?~~b:-2147483648,e=w(p[v[a+4>>2]+36>>2]+w(w(w(c-w(0|m(16384-r|0,360)))*e)+k)),b=16384.499999999996-+w(e/w(360)),r=g(b)<2147483648?~~b:-2147483648,e=w(e-w(0|m(16384-r|0,360))));e=w(e-l),b=+w(e/w(-360))+16384.499999999996,r=g(b)<2147483648?~~b:-2147483648,c=w(0),(k=w(e-w(0|m(16384-r|0,360))))!=w(0)?(e=k,o||(r=v[t+12>>2]+(u<<2)|0,c=p[r>>2],e=p[r+4>>2]),r=c>=w(0),n=k>w(0),!(w(g(e))<=w(90))|Bt(e)==Bt(k)||(w(g(c))>w(180)&&(c=w(w(Bt(c)*w(360))+c)),r=n),e=w(w(k+c)-Cr(c,w(360))),(0|r)!=(0|n)&&(e=w(w(Bt(c)*w(360))+e)),r=v[t+12>>2],p[r+(u<<2)>>2]=e):(r=v[t+12>>2],e=p[r+(u<<2)>>2]),p[4+((u<<2)+r|0)>>2]=k,e=w(w(e*i)+l),b=+w(e/w(-360))+16384.499999999996,r=g(b)<2147483648?~~b:-2147483648,p[a+40>>2]=e-w(0|m(16384-r|0,360))}}else da[v[v[r>>2]+12>>2]](r,n,w(0),e,0,w(1),f,0)}function dr(r,n,e,i){var f,t=0,u=0,o=0,a=0,c=w(0),b=0,k=0,s=0;if(D=f=D+-64|0,v[f+60>>2]=0,v[f+52>>2]=0,v[f+56>>2]=0,v[f+48>>2]=8712,v[e+52>>2]=i,n=In(n,2872),(u=v[n+12>>2])&&(v[f+56>>2]=u,t=v[5208],k=f,s=0|da[v[v[t>>2]+16>>2]](t,0,u<<2,8524,97),v[k+60>>2]=s),v[f+12>>2]=0,yn(f+48|0,u,f+12|0),n=v[n+4>>2])for(t=0,o=v[f+60>>2];p[o+(t<<2)>>2]=p[n+24>>2],t=t+1|0,n=v[n>>2];);if((0|i)!=(0|u)){if(v[f+44>>2]=0,v[f+36>>2]=0,v[f+40>>2]=0,v[f+28>>2]=0,v[f+20>>2]=0,v[f+24>>2]=0,v[f+32>>2]=8712,v[f+16>>2]=10632,v[f+12>>2]=10616,i&&(v[f+24>>2]=m(i,3),n=v[5208],n=0|da[v[v[n>>2]+16>>2]](n,0,m(i,12),8524,97),v[f+40>>2]=m(i,9),v[f+28>>2]=n,n=v[5208],k=f,s=0|da[v[v[n>>2]+16>>2]](n,0,m(i,36),8524,97),v[k+44>>2]=s),n=f+32|0,i=f+16|0,u)for(t=0;;){if(c=p[v[f+60>>2]+(t<<2)>>2],o=w(g(c))<w(2147483648)?~~c:-2147483648,v[f+8>>2]=o,On(i,f+8|0),(t=t+1|0)>>>0<(b=t+(o<<2)|0)>>>0)for(;c=p[(o=t<<2)+v[f+60>>2]>>2],a=w(g(c))<w(2147483648)?~~c:-2147483648,v[f+8>>2]=a,On(i,a=f+8|0),p[f+8>>2]=p[4+(o+v[f+60>>2]|0)>>2]*p[r+24>>2],Rn(n,a),p[f+8>>2]=p[8+(o+v[f+60>>2]|0)>>2]*p[r+24>>2],Rn(n,a),Rn(n,12+(o+v[f+60>>2]|0)|0),b>>>0>(t=t+4|0)>>>0;);if(!(t>>>0<u>>>0))break}v[e+40>>2]=0,_i(e+36|0,n),v[e+24>>2]=0,zi(e+20|0,i),v[f+12>>2]=10616,vo(n),Fu(i)}else{if(!(!i|p[r+24>>2]==w(1)))for(n=0,t=v[f+60>>2];p[(u=t+(n<<2)|0)>>2]=p[r+24>>2]*p[u>>2],(0|i)!=(0|(n=n+1|0)););v[e+40>>2]=0,_i(e+36|0,f+48|0)}vo(f+48|0),D=f- -64|0}function pr(r){var n=0,e=0,i=0,t=0,u=0,o=0;if(!l[r+28|0]){if(f[r+28|0]=1,v[r+8>>2]){for(e=v[r+20>>2];;){t=v[r+16>>2]+(u<<4)|0,n=v[t+8>>2];r:{n:{e:{i:{f:switch(0|(i=v[t+4>>2])){case 5:break n;case 3:break e;case 2:break i;case 0:case 1:case 4:break f;default:break r}if((o=v[n+168>>2])?da[v[v[o>>2]+8>>2]](o,e,i,n,0):da[v[n+164>>2]](e,i,n,0),!(i=v[e+96>>2])){da[v[e+92>>2]](e,v[t+4>>2],n,0);break r}da[v[v[i>>2]+8>>2]](i,e,v[t+4>>2],n,0);break r}(i=v[n+168>>2])?da[v[v[i>>2]+8>>2]](i,e,2,n,0):da[v[n+164>>2]](e,2,n,0),(i=v[e+96>>2])?da[v[v[i>>2]+8>>2]](i,e,v[t+4>>2],n,0):da[v[e+92>>2]](e,v[t+4>>2],n,0)}(t=v[n+168>>2])?da[v[v[t>>2]+8>>2]](t,e,3,n,0):da[v[n+164>>2]](e,3,n,0),(t=v[e+96>>2])?da[v[v[t>>2]+8>>2]](t,e,3,n,0):da[v[e+92>>2]](e,3,n,0),v[n+16>>2]=0,v[n+20>>2]=0,v[n+24>>2]=0,v[n+28>>2]=0,(t=v[n+12>>2])&&(i=v[n+8>>2])&&da[0|t](i),v[n+168>>2]=0,v[n+164>>2]=9,v[n+8>>2]=0,v[n+12>>2]=0,v[n+152>>2]=0,v[n+136>>2]=0,v[n+120>>2]=0,he(v[r+24>>2],n);break r}(i=v[n+168>>2])?da[v[v[i>>2]+8>>2]](i,e,5,n,v[t+12>>2]):da[v[n+164>>2]](e,5,n,v[t+12>>2]),(i=v[e+96>>2])?da[v[v[i>>2]+8>>2]](i,e,v[t+4>>2],n,v[t+12>>2]):da[v[e+92>>2]](e,v[t+4>>2],n,v[t+12>>2])}if(!((n=v[r+8>>2])>>>0>(u=u+1|0)>>>0))break}if(n)for(e=0;n=v[r+16>>2]+(~e+n<<4)|0,da[v[v[n>>2]>>2]](n),(n=v[r+8>>2])>>>0>(e=e+1|0)>>>0;);}f[r+28|0]=0,v[r+8>>2]=0}}function yr(r,n){var e,i=0,t=0,u=0,o=0,a=0;D=e=D-16|0;r:if(34!=l[0|n])v[5218]=n,u=0;else{for(i=n=n+1|0;!(!(o=l[0|i])|34==(0|o));)t=t+1|0,i=(92==(0|o)?2:1)+i|0;if(o=v[5208],u=0,!(o=0|da[v[v[o>>2]+8>>2]](o,t+1|0,8524,265)))break r;for(i=o;;){n:{e:if(92==(0|(u=l[0|n]))){u=n+1|0;i:{f:{t:{u:{o:{a:{switch((a=f[n+1|0])-110|0){case 1:case 2:case 3:case 5:break f;case 7:break t;case 6:break u;case 4:break o;case 0:break a}c:switch(a-98|0){case 0:f[0|i]=8;break i;case 4:break c;default:break f}f[0|i]=12;break i}f[0|i]=10;break i}f[0|i]=13;break i}f[0|i]=9;break i}if(_n(n+2|0,e+12|0),u=n+5|0,!(t=v[e+12>>2]))break e;if(56320==(0|(a=-1024&t)))break e;t:{u:{o:{if(55296!=(0|a)){if(n=1,t>>>0<128)break t;if(n=2,t>>>0<2048)break u;if(n=3,t>>>0<65536)break o}else{if(92!=l[n+6|0]|117!=l[n+7|0])break e;if(_n(n+8|0,e+8|0),u=n+11|0,(n=v[e+8>>2])-57344>>>0<4294966272)break e;t=65536+(1023&n|t<<10&1047552)|0,v[e+12>>2]=t}f[i+3|0]=63&t|128,t=v[e+12>>2]>>>6|0,v[e+12>>2]=t,n=4}f[i+2|0]=63&t|128,t=v[e+12>>2]>>>6|0,v[e+12>>2]=t}f[i+1|0]=63&t|128,t=v[e+12>>2]>>>6|0,v[e+12>>2]=t}f[0|i]=l[n+9660|0]|t,i=n+i|0;break e}f[0|i]=a}i=i+1|0}else{if(!u|34==(0|u))break n;f[0|i]=u,i=i+1|0,u=n}n=u+1|0;continue}break}f[0|i]=0,u=l[0|n],v[r+8>>2]=4,v[r+16>>2]=o,u=(34==(0|u))+n|0}return D=e+16|0,u}function mr(r,n,e,i){var t,u=0,o=0,a=0,c=0,b=0,k=0,s=0,h=0,d=0,p=0,y=0,m=0,w=0,g=0,F=0;D=t=D-16|0;r:{n:{e:{if((0|e)<=36){if(u=l[0|r])break e;o=r;break n}v[5373]=28,i=0;break r}o=r;e:{for(;;){if(!eo(u<<24>>24))break e;if(u=l[o+1|0],o=o+1|0,!u)break}break n}e:switch((u&=255)-43|0){case 0:case 2:break e;default:break n}s=45==(0|u)?-1:0,o=o+1|0}n:if(16!=(16|e)|48!=l[0|o])d=e||10;else{if(w=1,88==(223&l[o+1|0])){o=o+2|0,d=16;break n}o=o+1|0,d=e||8}for(e=0;u=-48,(((a=f[0|o])-48&255)>>>0<10||(u=-87,(a-97&255)>>>0<26||(u=-55,!((a-65&255)>>>0>25))))&&!((0|(a=u+a|0))>=(0|d));)c=Se(h,0,0,0),b=V,F=Se(k,0,d,0),u=V,p=Se(0,0,k,0),y=V,c=c+(y=(u=u+p|0)>>>0<p>>>0?y+1|0:y)|0,p=Se(h,0,d,0)+u|0,m=V,m=(u=u>>>0>p>>>0?m+1|0:m)+c|0,v[t+8>>2]=m,b=c>>>0<y>>>0?b+1|0:b,v[t+12>>2]=u>>>0>m>>>0?b+1|0:b,v[t>>2]=F,v[t+4>>2]=p,u=1,v[t+8>>2]|v[t+12>>2]||(b=Se(k,h,d,0),-1==(0|(c=V))&~a>>>0<b>>>0||(h=(k=a+b|0)>>>0<a>>>0?c+1|0:c,w=1,u=e)),o=o+1|0,e=u;n&&(v[n>>2]=w?o:r);n:{if(e)v[5373]=68,s=(r=1&i)?0:s,k=i,h=0;else{if(!h&i>>>0>k>>>0)break n;r=1&i}if(!(r|s)){v[5373]=68,i=(r=i)-1|0,g=0-!r|0;break r}if(!(!h&i>>>0>=k>>>0)){v[5373]=68;break r}}i=(r=s^k)-s|0,g=((n=s>>31)^h)-((r>>>0<s>>>0)+n|0)|0}return D=t+16|0,V=g,i}function wr(r,n){var e=0,i=0,f=0,c=0,s=0,l=w(0),h=0,d=0;b(n),c=au(f=t(2));r:{n:{e:{b(r);i:{if((e=t(2))-2139095040>>>0>=2164260864){if(c)break i;break n}if(!c)break e}if(l=w(1),1065353216==(0|e))break r;if(!(s=f<<1))break r;if(!(s>>>0<4278190081&(e<<=1)>>>0<=4278190080))return w(r+n);if(2130706432==(0|e))break r;return w(e>>>0>2130706431^(0|f)>=0?0:n*n)}if(au(e)){if(l=w(r*r),(0|e)<0&&(l=1==(0|Li(f))?w(-l):l),(0|f)>=0)break r;return st(w(w(1)/l))}if((0|e)<0){if(!(f=Li(f)))return r=w(r-r),w(r/r);s=(1==(0|f))<<16,e&=2147483647}e>>>0>8388607||(e=(2147483647&(b(w(r*w(8388608))),t(2)))-192937984|0)}if(c=(f=e-1060306944|0)>>>15&240,u(2,e-(-8388608&f)|0),a(+(i=((h=(i=+k()*y[c+20272>>3]-1)*i)*h*(.288457581109214*i-.36092606229713164)+((.480898481472577*i-.7213474675006291)*h+(1.4426950408774342*i+(y[c+20280>>3]+ +(f>>23)))))*+n)),e=0|t(1),t(0),!(1079967744==(0|(e&=2147450880))|e>>>0<1079967744)){if(i>127.99999995700433)return wu(s,w(15845632502852868e13));if(i<=-150)return wu(s,w(2524354896707238e-44))}i-=(d=(h=y[2152])+i)-h,i=(y[2153]*i+y[2154])*i*i+y[2155]*i+1,a(+d),t(1),e=s+(c=0|t(0))|0,f=c=v[(s=16960+((31&c)<<3)|0)>>2],e=v[s+4>>2]+(e<<15)|0,u(0,0|f),u(1,0|(f>>>0<f>>>0?e+1:e)),l=w(i*+o())}return l}function gr(r,n,e,i,f,t,u){r|=0,n=w(n),e=w(e),i|=0,f|=0,t|=0,u=w(u);var o=w(0),a=w(0),c=0,b=w(0),k=w(0),s=w(0),h=w(0),d=w(0),y=w(0);h=p[r+68>>2],s=w(-p[r+80>>2]),c=v[r+12>>2],k=p[c+108>>2],o=p[c+104>>2],b=p[c+96>>2],a=p[c+92>>2],l[r+88|0]||Fr(r),h=w(s-h);r:{switch(v[v[r+4>>2]+56>>2]-1|0){case 0:c=r+112|0,o=e,a=w(n-p[r+100>>2]);break r;case 1:b=w(w(g(w(w(a*k)-w(o*b))))/w(w(a*a)+w(o*o))),k=w(a*b),b=w(b*w(-o)),h=w(w(Pr(o,a)*w(57.2957763671875))+h)}s=w(e-p[c+112>>2]),d=w(n-p[c+100>>2]),y=w(w(s*a)-w(o*d)),a=w(w(a*k)-w(o*b)),o=w(y/a),c=r- -64|0,a=w(w(w(w(d*k)-w(b*s))/a)-p[r+60>>2])}s=w(o-p[c>>2]),o=w(w(Pr(s,a)*w(57.2957763671875))+h),(o=(k=p[r+72>>2])<w(0)?w(o+w(180)):o)>w(180)?o=w(o+w(-360)):o<w(-180)&&(o=w(o+w(360))),b=p[r+76>>2];r:if(i|f){c=i,n=(i=v[v[r+4>>2]+56>>2]-3>>>0<2)?w(n-p[r+100>>2]):a,a=w(n*n),n=i?w(e-p[r+112>>2]):s;n:{if(!((e=w(T(w(a+w(n*n)))))<(n=w(k*p[v[r+4>>2]+24>>2]))&&c)){if(!f|!(n>w(9999999747378752e-20)))break r;if(n<e)break n;break r}if(!(n>w(9999999747378752e-20)))break r}n=w(w(w(w(e/n)+w(-1))*u)+w(1)),k=w(k*n),b=w(b*(t?n:w(1)))}cr(r,p[r+60>>2],p[r+64>>2],w(w(o*u)+p[r+68>>2]),k,b,p[r+80>>2],p[r+84>>2])}function Fr(r){var n,e=w(0),i=w(0),t=w(0),u=w(0),o=w(0),a=w(0),c=w(0),b=w(0),k=w(0),s=0,l=w(0);return f[r+88|0]=1,(n=v[r+12>>2])?(e=p[n+112>>2],o=p[n+100>>2],i=p[n+108>>2],u=p[n+92>>2],c=p[n+104>>2],t=p[n+96>>2],v[r+80>>2]=0,a=w(p[r+112>>2]-e),e=w(w(1)/w(w(u*i)-w(c*t))),o=w(p[r+100>>2]-o),p[r+64>>2]=w(w(u*a)*e)-w(e*w(c*o)),p[r+60>>2]=w(w(i*o)*e)-w(e*w(t*a)),i=w(i*e),o=p[r+92>>2],a=w(-e),k=w(t*a),b=p[r+104>>2],t=w(w(i*o)+w(k*b)),e=w(u*e),a=w(c*a),u=w(w(e*b)+w(a*o)),c=w(T(w(w(t*t)+w(u*u)))),p[r+72>>2]=c,o=p[r+108>>2],b=a,a=p[r+96>>2],e=w(w(e*o)+w(b*a)),i=w(w(i*a)+w(k*o)),c>w(9999999747378752e-20)?(o=w(w(t*e)-w(u*i)),p[r+76>>2]=o/c,s=r,l=w(Pr(w(w(t*i)+w(u*e)),o)*w(57.2957763671875)),p[s+84>>2]=l,s=r,l=w(Pr(u,t)*w(57.2957763671875)),void(p[s+68>>2]=l)):(v[r+84>>2]=0,v[r+72>>2]=0,p[r+76>>2]=T(w(w(i*i)+w(e*e))),s=r,l=w(w(Pr(e,i)*w(-57.2957763671875))+w(90)),void(p[s+68>>2]=l))):(p[r+60>>2]=p[r+100>>2],p[r+64>>2]=p[r+112>>2],e=p[r+104>>2],i=p[r+92>>2],v[r+80>>2]=0,p[r+72>>2]=T(w(w(i*i)+w(e*e))),s=r,l=w(Pr(e,i)*w(57.2957763671875)),p[s+68>>2]=l,t=p[r+96>>2],u=p[r+108>>2],p[r+76>>2]=T(w(w(t*t)+w(u*u))),s=r,l=w(Pr(w(w(i*t)+w(e*u)),w(w(i*u)-w(e*t)))*w(57.2957763671875)),void(p[s+84>>2]=l))}function Ar(r,n,e){var i,t,u,o=w(0),a=0,c=0;if(D=t=D-16|0,i=Oo(r),v[i+8>>2]=9196,v[i+4>>2]=n,v[i>>2]=9828,r=0,v[i+20>>2]=0,v[i+12>>2]=0,v[i+16>>2]=0,a=i,c=tu(e,Bo(v[n+40>>2])),v[a+24>>2]=c,p[i+28>>2]=p[n+60>>2],p[i+32>>2]=p[n+64>>2],p[i+36>>2]=p[n+68>>2],o=p[n+72>>2],v[i+124>>2]=8712,v[i+120>>2]=0,v[i+112>>2]=0,v[i+116>>2]=0,v[i+108>>2]=8712,v[i+104>>2]=0,v[i+96>>2]=0,v[i+100>>2]=0,v[i+92>>2]=8712,v[i+88>>2]=0,v[i+80>>2]=0,v[i+84>>2]=0,v[i+76>>2]=8712,v[i+72>>2]=0,v[(n=i- -64|0)>>2]=0,v[n+4>>2]=0,v[i+60>>2]=8712,v[i+56>>2]=0,v[i+48>>2]=0,v[i+52>>2]=0,v[i+44>>2]=8712,p[i+40>>2]=o,f[i+133|0]=0,f[i+134|0]=0,f[i+135|0]=0,f[i+136|0]=0,f[i+137|0]=0,f[i+138|0]=0,f[i+139|0]=0,f[i+140|0]=0,v[i+128>>2]=0,v[i+132>>2]=0,ca(u=i+8|0,v[v[i+4>>2]+28>>2]),n=v[i+4>>2],v[n+28>>2])for(;a=t,c=ku(e,Bo(v[v[n+36>>2]+(r<<2)>>2])),v[a+12>>2]=c,Sn(u,t+12|0),r=r+1|0,n=v[i+4>>2],r>>>0<d[n+28>>2];);return v[t+8>>2]=0,yn(i+124|0,10,t+8|0),D=t+16|0,i}function Tr(r,n){r|=0,n=w(n);var e,i=0,f=0,t=w(0),u=w(0),o=0,a=w(0),c=0,b=w(0);if(e=v[r+44>>2])for(n=w(p[r+100>>2]*n);;){c=v[r+52>>2]+(o<<2)|0;r:if(i=v[c>>2]){if(p[i+60>>2]=p[i+64>>2],u=p[i+80>>2],p[i+76>>2]=u,a=p[i+88>>2],t=w(n*a),(b=p[i+68>>2])>w(0)){if(t=w(b-t),p[i+68>>2]=t,t>w(0))break r;v[i+68>>2]=0,t=w(-t)}n:{if(f=v[i+20>>2]){if(!((u=w(u-p[f+68>>2]))>=w(0)))break n;if(v[f+68>>2]=0,p[f+72>>2]=(a!=w(0)?w(w(n+w(u/a))*p[f+88>>2]):w(0))+p[f+72>>2],p[i+72>>2]=t+p[i+72>>2],gn(r,o,f,1),!(i=v[f+24>>2]))break r;for(;p[f+96>>2]=n+p[f+96>>2],f=i,i=v[i+24>>2];);break r}if(!(v[i+24>>2]|!(u>=p[i+84>>2]))){v[c>>2]=0,jf(v[r+72>>2],i),pi(r,i);break r}}if(v[i+24>>2]&&dn(r,i,n)&&(f=v[i+24>>2],v[i+24>>2]=0,f))for(v[f+28>>2]=0;jf(v[r+72>>2],f),f=v[f+24>>2];);p[i+72>>2]=t+p[i+72>>2]}if((0|e)==(0|(o=o+1|0)))break}pr(v[r+72>>2])}function $r(r,n,e,i){var f,t,o=0,a=0,c=0,b=0,s=w(0),h=0,d=0,y=0,g=0,F=0;if(D=f=D-16|0,s=p[r+36>>2],r=i<<1,v[e+52>>2]=r,o=v[n+4>>2],v[n+4>>2]=o+1,t=e+36|0,l[0|o]){if(ca(t,m(i,18)),ca(o=e+20|0,m(i,6)),!((0|i)<=0))for(;;){if(h=Wn(n,1),v[f+12>>2]=h,On(o,f+12|0),d=0,(0|h)>0)for(;g=f,F=Wn(n,1),v[g+12>>2]=F,On(o,e=f+12|0),r=v[n+4>>2],v[n+4>>2]=r+1,a=l[0|r],v[n+4>>2]=r+2,c=l[r+1|0],v[n+4>>2]=r+3,b=l[r+2|0],v[n+4>>2]=r+4,p[f+12>>2]=s*(u(2,l[r+3|0]|(b|c<<8|a<<16)<<8),k()),Rn(t,e),r=v[n+4>>2],v[n+4>>2]=r+1,a=l[0|r],v[n+4>>2]=r+2,c=l[r+1|0],v[n+4>>2]=r+3,b=l[r+2|0],v[n+4>>2]=r+4,p[f+12>>2]=s*(u(2,l[r+3|0]|(b|c<<8|a<<16)<<8),k()),Rn(t,e),r=v[n+4>>2],v[n+4>>2]=r+1,a=l[0|r],v[n+4>>2]=r+2,c=l[r+1|0],v[n+4>>2]=r+3,b=l[r+2|0],v[n+4>>2]=r+4,v[f+12>>2]=l[r+3|0]|(b|c<<8|a<<16)<<8,Rn(t,e),(0|(d=d+1|0))!=(0|h););if((0|(y=y+1|0))==(0|i))break}}else Nr(n,r,s,t);D=f+16|0}function Ir(r){return v[r+208>>2]=8712,v[r+204>>2]=0,v[r+196>>2]=0,v[r+200>>2]=0,v[r+192>>2]=8712,v[r+188>>2]=0,v[r+180>>2]=0,v[r+184>>2]=0,v[r+176>>2]=9788,v[r+172>>2]=0,v[r+164>>2]=0,v[r+168>>2]=0,v[r+160>>2]=8712,v[r+156>>2]=0,v[r+148>>2]=0,v[r+152>>2]=0,v[r+144>>2]=8712,v[r+140>>2]=0,v[r+132>>2]=0,v[r+136>>2]=0,v[r+128>>2]=8712,v[r+124>>2]=0,v[r+116>>2]=0,v[r+120>>2]=0,v[r+112>>2]=10392,v[r+108>>2]=10440,v[r+104>>2]=0,v[r+96>>2]=0,v[r+100>>2]=0,v[r+92>>2]=10376,v[r+88>>2]=10424,v[r+84>>2]=0,v[r+76>>2]=0,v[r+80>>2]=0,v[r+72>>2]=8680,v[r+68>>2]=0,v[r+60>>2]=0,v[r+64>>2]=0,v[r+56>>2]=10408,v[r+52>>2]=0,v[r+44>>2]=0,v[r+48>>2]=0,v[r+40>>2]=8680,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+24>>2]=10392,v[r+20>>2]=0,v[r+12>>2]=0,v[r+16>>2]=0,v[r+8>>2]=10376,v[r+4>>2]=10944,v[r>>2]=10360,v[r+220>>2]=0,v[r+224>>2]=0,v[r+212>>2]=0,v[r+216>>2]=0,ca(r+144|0,128),ca(r+160|0,128),ba(r+176|0,128),ca(r+192|0,128),r}function Cr(r,n){var e=0,i=0,f=0,o=0,a=0,c=0;if(b(n),!((e=(o=t(2))<<1)&&(b(r),255!=(0|(f=(c=t(2))>>>23&255))&&(2147483647&(b(n),t(2)))>>>0<2139095041)))return r=w(r*n),w(r/r);if((i=c<<1)>>>0<=e>>>0)return(0|i)==(0|e)?w(r*w(0)):r;if(a=o>>>23&255,f)i=8388607&c|8388608;else{if(f=0,(0|(i=c<<9))>=0)for(;f=f-1|0,(0|(i<<=1))>=0;);i=c<<1-f}if(a)e=8388607&o|8388608;else{if(a=0,(0|(e=o<<9))>=0)for(;a=a-1|0,(0|(e<<=1))>=0;);e=o<<1-a}if(o=e,(0|f)>(0|a)){for(;;){if(!((0|(e=i-o|0))<0||(i=e)))return w(r*w(0));if(i<<=1,!((0|a)<(0|(f=f-1|0))))break}f=a}if(!((0|(e=i-o|0))<0||(i=e)))return w(r*w(0));if(i>>>0>8388607)e=i;else for(;f=f-1|0,o=i>>>0<4194304,i=e=i<<1,o;);return u(2,-2147483648&c|((0|f)>0?e-8388608|f<<23:e>>>1-f)),k()}function Pr(r,n){var e,i,f=0,o=0,a=w(0);if(!((2147483647&(b(r),t(2)))>>>0<2139095041&(2147483647&(b(n),t(2)))>>>0<=2139095040))return w(r+n);if(b(n),1065353216==(0|(f=t(2))))return Er(r);i=f>>>30&2,b(r),e=i|(o=t(2))>>>31;r:{n:{e:{if(!(o&=2147483647)){i:switch(e-2|0){case 0:return w(3.1415927410125732);case 1:break i;default:break e}return w(-3.1415927410125732)}if(2139095040!=(0|(f&=2147483647))){if(!f|!(2139095040!=(0|o)&o>>>0<=f+218103808>>>0))break r;switch(i&&(a=w(0),f>>>0>o+218103808>>>0)||(a=Er(w(g(w(r/n))))),r=a,0|e){case 1:return w(-r);case 2:return w(w(3.1415927410125732)-w(r+w(8.742277657347586e-8)));case 0:break e}return w(w(r+w(8.742277657347586e-8))+w(-3.1415927410125732))}if(2139095040==(0|o))break n;r=p[17364+(e<<2)>>2]}return r}return p[17348+(e<<2)>>2]}return u(2,-2147483648&(b(r),t(2))|1070141403),k()}function Er(r){var n,e,i=0,f=w(0),o=w(0),a=w(0);if(b(r),(n=2147483647&(e=t(2)))>>>0>=1283457024)return(2147483647&(b(r),t(2)))>>>0>2139095040?r:(u(2,-2147483648&(b(r),t(2))|1070141402),k());r:{n:{if(n>>>0<=1054867455){if(i=-1,n>>>0>=964689920)break n;break r}if(r=w(g(r)),n>>>0<=1066926079){if(n>>>0<=1060110335){r=w(w(w(r+r)+w(-1))/w(r+w(2))),i=0;break n}r=w(w(r+w(-1))/w(r+w(1))),i=1}else n>>>0<=1075576831?(r=w(w(r+w(-1.5))/w(w(r*w(1.5))+w(1))),i=2):(r=w(w(-1)/r),i=3)}if(o=w(r*r),f=w(o*o),a=w(f*w(w(f*w(-.106480173766613))+w(-.19999158382415771))),f=w(o*w(w(f*w(w(f*w(.06168760731816292))+w(.14253635704517365)))+w(.333333283662796))),n>>>0<=1054867455)return w(r-w(r*w(a+f)));r=w(p[17392+(i<<=2)>>2]-w(w(w(r*w(a+f))-p[i+17408>>2])-r)),r=(0|e)<0?w(-r):r}return r}function Or(r,n){var e,i,f=0,t=0,u=0,o=0,a=w(0);if(D=i=D-16|0,e=si(r,n),v[e+40>>2]=9400,v[e+24>>2]=8712,v[e+20>>2]=0,v[e>>2]=9372,v[e+44>>2]=0,v[e+48>>2]=0,v[e+36>>2]=0,v[e+28>>2]=0,v[e+32>>2]=0,v[e+52>>2]=0,v[e+56>>2]=0,ca(r=e+24|0,n),aa(e+40|0,n),v[i>>2]=0,yn(r,n,i),(0|n)>0)for(r=0;v[i+12>>2]=0,v[i+4>>2]=0,v[i+8>>2]=0,v[i>>2]=8712,D=u=D-16|0,(0|(f=v[e+44>>2]))!=v[e+48>>2]?(v[e+44>>2]=f+1,xn(v[e+52>>2]+(f<<4)|0,i)):(o=xn(u,i),a=w(w(d[e+44>>2])*w(1.75)),t=(f=w(g(a))<w(2147483648)?~~a:-2147483648)>>>0<=8?8:f,v[e+48>>2]=t,f=v[5208],t=0|da[v[v[f>>2]+16>>2]](f,v[e+52>>2],t<<4,8524,109),v[e+52>>2]=t,f=v[e+44>>2],v[e+44>>2]=f+1,xn(t+(f<<4)|0,o),vo(o)),D=u+16|0,vo(i),(0|n)!=(0|(r=r+1|0)););return D=i+16|0,e}function Rr(r){var n,e,i=w(0),f=0,u=0;D=n=D-16|0,b(r);r:if((e=2147483647&(f=t(2)))>>>0<=1061752794){if(i=w(1),e>>>0<964689920)break r;i=Bi(+r)}else if(e>>>0<=1081824209){if(e>>>0>=1075235812){i=w(-Bi(+r+((0|f)<0?3.141592653589793:-3.141592653589793)));break r}if(u=+r,(0|f)<0){i=Gi(u+1.5707963267948966);break r}i=Gi(1.5707963267948966-u)}else if(e>>>0<=1088565717){if(e>>>0>=1085271520){i=Bi(+r+((0|f)<0?6.283185307179586:-6.283185307179586));break r}if((0|f)<0){i=Gi(-4.71238898038469-+r);break r}i=Gi(+r-4.71238898038469)}else if(i=w(r-r),!(e>>>0>=2139095040)){switch(3&er(r,n+8|0)){case 0:i=Bi(y[n+8>>3]);break r;case 1:i=Gi(-y[n+8>>3]);break r;case 2:i=w(-Bi(y[n+8>>3]));break r}i=Gi(y[n+8>>3])}return D=n+16|0,i}function Sr(r){var n=0,e=0,i=0,t=0,u=0;if(n=v[12+(r|=0)>>2])for(;Ke(v[v[r+20>>2]+(e<<2)>>2]),(0|n)!=(0|(e=e+1|0)););if(t=v[r+60>>2])for(u=v[r+68>>2],e=0;n=v[u+(e<<2)>>2],i=v[n+4>>2],v[n+24>>2]=v[i+44>>2],f[n+28|0]=l[i+48|0],f[n+29|0]=l[i+49|0],p[n+32>>2]=p[i+52>>2],p[n+36>>2]=p[i+56>>2],(0|t)!=(0|(e=e+1|0)););if(t=v[r+76>>2])for(u=v[r+84>>2],e=0;n=v[u+(e<<2)>>2],i=v[n+4>>2],p[n+28>>2]=p[i+44>>2],p[n+32>>2]=p[i+48>>2],p[n+36>>2]=p[i+52>>2],p[n+40>>2]=p[i+56>>2],(0|t)!=(0|(e=e+1|0)););if(i=v[r+92>>2])for(t=v[r+100>>2],e=0;r=v[t+(e<<2)>>2],n=v[r+4>>2],p[r+28>>2]=p[n+60>>2],p[r+32>>2]=p[n+64>>2],p[r+36>>2]=p[n+68>>2],p[r+40>>2]=p[n+72>>2],(0|i)!=(0|(e=e+1|0)););}function Wr(r){var n,e,i=0,f=0;D=n=D-16|0,b(r);r:if((e=2147483647&(f=t(2)))>>>0<=1061752794){if(e>>>0<964689920)break r;r=Gi(+r)}else if(e>>>0<=1081824209){if(i=+r,e>>>0<=1075235811){if((0|f)<0){r=w(-Bi(i+1.5707963267948966));break r}r=Bi(i+-1.5707963267948966);break r}r=Gi(-(((0|f)>=0?-3.141592653589793:3.141592653589793)+i))}else if(e>>>0<=1088565717){if(e>>>0<=1085271519){if(i=+r,(0|f)<0){r=Bi(i+4.71238898038469);break r}r=w(-Bi(i+-4.71238898038469));break r}r=Gi(+r+((0|f)<0?6.283185307179586:-6.283185307179586))}else if(e>>>0>=2139095040)r=w(r-r);else{switch(3&er(r,n+8|0)){case 0:r=Gi(y[n+8>>3]);break r;case 1:r=Bi(y[n+8>>3]);break r;case 2:r=Gi(-y[n+8>>3]);break r}r=w(-Bi(y[n+8>>3]))}return D=n+16|0,r}function Gr(r,n){n|=0;var e=0,i=0,f=0,t=0,u=0,o=0,a=0;if((0|(t=v[136+(r|=0)>>2]))!=(0|n)){r:if(n)if(t){if(!(e=v[t+24>>2]))break r;for(a=n+16|0;;){if(u=v[t+32>>2],d[4+(u+(i<<4)|0)>>2]<=o>>>0){n:{e:{for(;;){if((0|(i=i+1|0))==(0|e))break e;if(v[4+(u+(i<<4)|0)>>2])break}f=i;break n}f=e}if(e=e>>>0<=i>>>0,o=0,i=f,e)break r}if(f=v[12+(u+(i<<4)|0)>>2]+m(o,20)|0,e=v[f>>2],u=v[v[r+36>>2]+(e<<2)>>2],v[u+60>>2]==v[f+16>>2]&&(f=Dn(a,e,f+4|0))&&af(u,f),o=o+1|0,!(i>>>0<(e=v[t+24>>2])>>>0))break}}else if(f=v[r+28>>2])for(;i=v[v[r+36>>2]+(e<<2)>>2],t=Lo(v[i+4>>2]),v[t+4>>2]&&(t=gt(n,e,t))&&af(i,t),(0|f)!=(0|(e=e+1|0)););v[r+136>>2]=n,rr(r)}}function Ur(r,n){var e,i,f=0,t=0,u=0,o=0,a=w(0);if(D=i=D-16|0,e=Eo(r),v[e+20>>2]=9444,v[e+4>>2]=8712,v[e>>2]=9416,v[e+32>>2]=0,v[e+24>>2]=0,v[e+28>>2]=0,v[e+16>>2]=0,v[e+8>>2]=0,v[e+12>>2]=0,ca(r=e+4|0,n),aa(e+20|0,n),v[i>>2]=0,yn(r,n,i),(0|n)>0)for(r=0;v[i+12>>2]=0,v[i+4>>2]=0,v[i+8>>2]=0,v[i>>2]=8680,D=u=D-16|0,(0|(f=v[e+24>>2]))!=v[e+28>>2]?(v[e+24>>2]=f+1,Jn(v[e+32>>2]+(f<<4)|0,i)):(o=Jn(u,i),a=w(w(d[e+24>>2])*w(1.75)),t=(f=w(g(a))<w(2147483648)?~~a:-2147483648)>>>0<=8?8:f,v[e+28>>2]=t,f=v[5208],t=0|da[v[v[f>>2]+16>>2]](f,v[e+32>>2],t<<4,8524,109),v[e+32>>2]=t,f=v[e+24>>2],v[e+24>>2]=f+1,Jn(t+(f<<4)|0,o),Qu(o)),D=u+16|0,Qu(i),(0|n)!=(0|(r=r+1|0)););return D=i+16|0,e}function jr(r,n,e){var i,f=0,t=0,o=0,a=0,c=0,b=0,s=0,h=0,d=0,p=0,y=0,m=0,w=0,g=0,F=0;switch(i=v[r+4>>2],v[r+4>>2]=i+1,l[0|i]-1|0){case 0:return void ct(e,n);case 1:v[r+4>>2]=i+2,f=l[i+1|0],v[r+4>>2]=i+3,t=l[i+2|0],v[r+4>>2]=i+4,o=l[i+3|0],v[r+4>>2]=i+5,a=l[i+4|0],v[r+4>>2]=i+6,c=l[i+5|0],v[r+4>>2]=i+7,b=l[i+6|0],v[r+4>>2]=i+8,s=l[i+7|0],v[r+4>>2]=i+9,h=l[i+8|0],v[r+4>>2]=i+10,d=l[i+9|0],v[r+4>>2]=i+11,p=l[i+10|0],v[r+4>>2]=i+12,y=l[i+11|0],v[r+4>>2]=i+13,m=l[i+12|0],v[r+4>>2]=i+14,w=l[i+13|0],v[r+4>>2]=i+15,g=l[i+14|0],v[r+4>>2]=i+16,F=l[i+15|0],v[r+4>>2]=i+17,_r(e,n,(u(2,(t<<8|f<<16|o)<<8|a),k()),(u(2,(b<<8|c<<16|s)<<8|h),k()),(u(2,(p<<8|d<<16|y)<<8|m),k()),(u(2,l[i+16|0]|(g<<8|w<<16|F)<<8),k()))}}function Hr(r){return v[r>>2]=10456,ht(r+4|0),v[r+52>>2]=0,v[r+56>>2]=0,v[r+48>>2]=10488,v[r+44>>2]=0,v[r+36>>2]=0,v[r+40>>2]=0,v[r+32>>2]=10472,v[r+28>>2]=0,v[r+20>>2]=0,v[r+24>>2]=0,v[r+16>>2]=9612,v[r+60>>2]=0,v[r+64>>2]=0,v[r+132>>2]=10568,v[r+128>>2]=0,v[r+120>>2]=0,v[r+124>>2]=0,v[r+116>>2]=10552,v[r+112>>2]=0,v[r+104>>2]=0,v[r+108>>2]=0,v[r+100>>2]=10536,v[r+96>>2]=0,v[r+88>>2]=0,v[r+92>>2]=0,v[r+84>>2]=10520,v[r+80>>2]=0,v[r+72>>2]=0,v[r+76>>2]=0,v[r+68>>2]=10504,v[r+160>>2]=0,v[r+152>>2]=0,v[r+156>>2]=0,v[r+144>>2]=0,v[r+148>>2]=0,v[r+136>>2]=0,v[r+140>>2]=0,ht(r+164|0),ht(r+176|0),v[r+192>>2]=0,v[r+196>>2]=0,v[r+188>>2]=10584,v[r+200>>2]=0,v[r+204>>2]=0,ht(r+208|0),ht(r+220|0),r}function Lr(r,n){var e;return D=e=D-16|0,r=kt(r,n),v[r+52>>2]=0,v[r+56>>2]=0,v[r+48>>2]=1065353216,v[r+40>>2]=0,v[r+44>>2]=1065353216,v[r+32>>2]=0,v[r+36>>2]=0,v[r+20>>2]=10052,v[r>>2]=10028,v[r+24>>2]=0,v[r+28>>2]=0,v[r+100>>2]=8712,v[r+84>>2]=8712,v[r+60>>2]=0,v[r+64>>2]=0,v[r+68>>2]=0,v[r+72>>2]=0,v[r+76>>2]=0,v[r+80>>2]=0,v[r+112>>2]=0,v[r+104>>2]=0,v[r+108>>2]=0,v[r+96>>2]=0,v[r+88>>2]=0,v[r+92>>2]=0,ht(r+116|0),v[r+136>>2]=0,v[r+140>>2]=0,v[r+128>>2]=0,v[r+132>>2]=0,v[r+156>>2]=1065353216,v[r+160>>2]=1065353216,v[r+148>>2]=1065353216,v[r+152>>2]=1065353216,v[r+144>>2]=9308,ci(r+144|0),v[e+12>>2]=0,yn(r+84|0,8,e+12|0),v[e+8>>2]=0,yn(r+100|0,8,e+8|0),D=e+16|0,r}function Mr(r,n){return r=Fe(r,n),v[r+76>>2]=0,v[r+80>>2]=0,v[r+64>>2]=9772,v[r>>2]=9748,v[r+68>>2]=0,v[r+72>>2]=0,v[r+152>>2]=9788,v[r+136>>2]=9788,v[r+120>>2]=8712,v[r+104>>2]=8712,v[r+84>>2]=0,v[r+88>>2]=0,v[r+92>>2]=0,v[r+96>>2]=0,v[r+100>>2]=0,v[r+164>>2]=0,v[r+156>>2]=0,v[r+160>>2]=0,v[r+148>>2]=0,v[r+140>>2]=0,v[r+144>>2]=0,v[r+132>>2]=0,v[r+124>>2]=0,v[r+128>>2]=0,v[r+116>>2]=0,v[r+108>>2]=0,v[r+112>>2]=0,ht(r+168|0),v[r+196>>2]=0,v[r+200>>2]=0,v[r+188>>2]=0,v[r+192>>2]=0,v[r+180>>2]=0,v[r+184>>2]=0,v[r+216>>2]=1065353216,v[r+220>>2]=1065353216,v[r+208>>2]=1065353216,v[r+212>>2]=1065353216,v[r+204>>2]=9308,ci(r+204|0),v[r+232>>2]=0,f[r+228|0]=0,v[r+224>>2]=0,r}function _r(r,n,e,i,f,t){r|=0,n|=0,e=w(e),i=w(i),f=w(f),t=w(t);var u,o=w(0),a=w(0),c=w(0),b=w(0),k=0;if(u=v[r+16>>2],r=m(n,19),v[u+(r<<2)>>2]=1073741824,(n=r+1|0)>>>0<(r=r+19|0)>>>0)for(o=w(w(f-w(e+e))*w(.029999999329447746)),c=w(w(w(w(e-f)*w(3))+w(1))*w(.006000000052154064)),f=w(w(o+o)+c),a=w(w(t-w(i+i))*w(.029999999329447746)),b=w(w(w(w(i-t)*w(3))+w(1))*w(.006000000052154064)),t=w(w(a+a)+b),a=i=w(w(b*w(.1666666716337204))+w(w(i*w(.30000001192092896))+a)),o=e=w(w(c*w(.1666666716337204))+w(w(e*w(.30000001192092896))+o));p[(k=(n<<2)+u|0)>>2]=e,p[k+4>>2]=i,a=w(t+a),i=w(a+i),o=w(o+f),e=w(e+o),f=w(c+f),t=w(b+t),r>>>0>(n=n+2|0)>>>0;);}function zr(r){r|=0;var n,e=w(0),i=w(0),f=w(0),t=w(0),u=w(0),o=w(0),a=w(0),c=w(0),b=w(0),k=w(0),s=w(0),l=w(0),h=w(0);f=p[r+52>>2],e=p[r+44>>2],u=p[r+76>>2],c=p[r+60>>2],b=p[r+68>>2],i=Wr(o=w(p[r+40>>2]*w(.01745329238474369))),t=p[r+56>>2],a=p[r+48>>2],k=p[r+80>>2],s=p[r+64>>2],o=Rr(o),l=p[r+32>>2],h=p[r+72>>2],u=w(e*w(f/u)),f=w(w(e*w(f*w(-.5)))+w(u*c)),n=v[r+96>>2],e=w(a*w(t*w(-.5))),t=w(a*w(t/k)),e=w(e+w(s*t)),a=p[r+36>>2],c=w(w(o*e)+a),u=w(w(b*u)+f),b=w(i*u),p[n+28>>2]=c+b,k=w(l+w(f*o)),t=w(w(h*t)+e),s=w(i*t),p[n+8>>2]=k-s,f=w(f*i),p[n+4>>2]=c+f,i=w(e*i),p[n>>2]=k-i,e=w(l+w(u*o)),p[n+24>>2]=e-i,i=w(a+w(t*o)),p[n+20>>2]=i+b,p[n+16>>2]=e-s,p[n+12>>2]=i+f}function xr(r){r|=0;var n,e,i=0,t=0;return Mr(n=Xf(236),No(r)),i=v[r+68>>2],(e=v[n+72>>2])&&(!(t=v[n+68>>2])|(0|i)==(0|t)||da[0|e](t)),v[n+72>>2]=0,v[n+68>>2]=i,p[n+180>>2]=p[r+180>>2],p[n+184>>2]=p[r+184>>2],p[n+188>>2]=p[r+188>>2],p[n+192>>2]=p[r+192>>2],f[n+228|0]=l[r+228|0],v[n+232>>2]=v[r+232>>2],p[n+76>>2]=p[r+76>>2],p[n+80>>2]=p[r+80>>2],p[n+84>>2]=p[r+84>>2],p[n+88>>2]=p[r+88>>2],p[n+92>>2]=p[r+92>>2],p[n+96>>2]=p[r+96>>2],ee(n+168|0,r+168|0),p[n+208>>2]=p[r+208>>2],p[n+212>>2]=p[r+212>>2],p[n+216>>2]=p[r+216>>2],p[n+220>>2]=p[r+220>>2],ci(n+204|0),v[n+56>>2]=v[r+56>>2],zn(n,(i=v[r+100>>2])||r),sr(n),0|n}function Jr(r,n,e){var i,f,t=0,u=0,o=w(0),a=w(0),c=0,b=w(0),k=w(0),s=w(0);D=i=D-16|0,f=v[r+60>>2],o=p[n+56>>2],b=p[n+52>>2],a=w(o-b),k=Cr(p[n+76>>2],a);r:if(f){for(;;){if(u=v[v[r+68>>2]+(t<<2)>>2],(s=p[u+8>>2])<k)break r;if(o<s||(c=v[r+72>>2],ut(i,5,n,u),qr(c+4|0,i)),(0|f)==(0|(t=t+1|0)))break}t=f}r:{n:{if(l[n+36|0]){if(a==w(0))break n;if(Cr(p[n+72>>2],a)<k)break n;break r}if(!(e>=o)|!(p[n+60>>2]<o))break r}u=v[r+72>>2],ut(i,4,n,0),qr(u+4|0,i)}if(t>>>0<f>>>0)for(;u=v[v[r+68>>2]+(t<<2)>>2],p[u+8>>2]<b||(c=v[r+72>>2],ut(i,5,n,u),qr(c+4|0,i)),(0|f)!=(0|(t=t+1|0)););D=i+16|0}function Kr(r,n,e,i){var t,u=0,o=0,a=0,c=0,b=0;if(D=t=D-16|0,v[r>>2]=1032,v[r+4>>2]=1048,v[r+8>>2]=v[e+4>>2],u=v[e+8>>2],v[r+16>>2]=0,v[r+12>>2]=u,u&&(o=v[5208],c=r,b=0|da[v[v[o>>2]+12>>2]](o,u<<2,8524,206),v[c+16>>2]=b,o=v[r+8>>2]))for(u=0;v[(a=u<<2)+v[r+16>>2]>>2]=v[v[e+12>>2]+a>>2],(0|o)!=(0|(u=u+1|0)););if(p[r+32>>2]=i,v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=8536,Re(r+36|0,n),v[e+4>>2]>0)for(u=r+20|0,n=0;o=v[v[e+12>>2]+(n<<2)>>2],c=t,b=0|da[v[v[o>>2]+16>>2]](o),v[c+12>>2]=b,f[t+11|0]=1,Ln(u,t+12|0,t+11|0),(0|(n=n+1|0))<v[e+4>>2];);return D=t+16|0,r}function Br(r,n,e,i,f,u,o,a,c,k,s,l){var h=w(0),d=w(0),y=w(0),m=w(0),g=0,F=w(0),A=w(0),T=w(0),$=w(0);r:{if(!(r<w(9999999747378752e-21))&2143289344!=(0|(b(r),t(2)))){if(g=(k=v[k+12>>2])+(s<<2)|0,d=w(r*r),F=w(d*r),$=w(c*F),c=w(w(1)-r),y=w(c*r),h=w(y*w(3)),A=w(h*r),m=w(c*c),T=w(c*m),c=w(c*h),h=w($+w(w(o*A)+w(w(e*T)+w(c*f)))),p[g+4>>2]=h,a=w(w(a*F)+w(w(u*A)+w(w(n*T)+w(c*i)))),p[g>>2]=a,!l)break r;+r<.001?r=Pr(w(f-e),w(i-n)):(r=w(y*f),e=w(h-w(w(o*d)+w(w(e*m)+w(r+r)))),r=w(y*i),r=Pr(e,w(a-w(w(u*d)+w(w(n*m)+w(r+r))))))}else k=v[k+12>>2],p[(l=k+(s<<2)|0)>>2]=n,p[l+4>>2]=e,r=Pr(w(f-e),w(i-n));p[8+((s<<2)+k|0)>>2]=r}}function Nr(r,n,e,i){var f,t=0,o=0,a=0,c=0,b=0,s=0;D=f=D-16|0,v[f+12>>2]=0,yn(i,n,f+12|0);r:if(e==w(1)){if(!((0|n)<=0))for(t=v[r+4>>2],a=v[i+12>>2],i=0;v[r+4>>2]=t+1,c=l[0|t],v[r+4>>2]=t+2,b=l[t+1|0],v[r+4>>2]=t+3,s=l[t+2|0],o=t+4|0,v[r+4>>2]=o,v[a+(i<<2)>>2]=l[t+3|0]|(s|b<<8|c<<16)<<8,t=o,(0|(i=i+1|0))!=(0|n););}else{if((0|n)<=0)break r;for(t=v[r+4>>2],a=v[i+12>>2],i=0;v[r+4>>2]=t+1,c=l[0|t],v[r+4>>2]=t+2,b=l[t+1|0],v[r+4>>2]=t+3,s=l[t+2|0],o=t+4|0,v[r+4>>2]=o,p[a+(i<<2)>>2]=(u(2,l[t+3|0]|(s|b<<8|c<<16)<<8),k()*e),t=o,(0|(i=i+1|0))!=(0|n););}D=f+16|0}function qr(r,n){var e,i=0,f=w(0),t=0;D=e=D-16|0,(0|(i=v[r+4>>2]))!=v[r+8>>2]?(v[r+4>>2]=i+1,r=v[r+12>>2]+(i<<4)|0,v[r>>2]=8600,i=v[n+8>>2],v[r+4>>2]=v[n+4>>2],v[r+8>>2]=i,v[r+12>>2]=v[n+12>>2]):(v[e>>2]=8600,v[e+12>>2]=v[n+12>>2],t=v[n+8>>2],v[e+4>>2]=v[n+4>>2],v[e+8>>2]=t,f=w(w(i>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,i=v[5208],n=0|da[v[v[i>>2]+16>>2]](i,v[r+12>>2],n<<4,8524,109),v[r+12>>2]=n,i=r,r=v[r+4>>2],v[i+4>>2]=r+1,v[(r=n+(r<<4)|0)>>2]=8600,n=v[e+8>>2],v[r+4>>2]=v[e+4>>2],v[r+8>>2]=n,v[r+12>>2]=v[e+12>>2]),D=e+16|0}function Dr(){C(21457,21458,21459,0,11232,1675,11235,0,11235,0,4511,11237,1676),O(21457,2146,1,16816,11237,1677,1678,0),O(21457,1164,1,16816,11237,1677,1679,0),O(21457,1100,2,16820,11280,1680,1681,0),O(21457,6768,2,16828,11280,1682,1683,0),O(21457,3887,3,16836,11296,1684,1685,0),O(21457,1117,3,16848,11296,1686,1687,0),O(21457,6830,3,16860,13176,1688,1689,0),O(21457,6797,2,16872,13528,1690,1691,0),O(21457,3866,2,16224,13528,1692,1693,0),O(21457,6747,1,15016,11232,1694,1695,0),O(21457,5182,1,16880,11232,1696,1697,0),O(21457,1079,1,15788,11232,1397,1698,0),O(21457,1780,1,16884,11232,1699,1700,0)}function Vr(r,n,e,i){var f,t=0,u=0,o=0,a=0,c=0;D=f=D-32|0,xt(f+20|0,n),n=v[f+24>>2],t=v[f+20>>2];r:if(!(n>>>0>=(u=v[t+4>>2])>>>0))for(c=f+4|0;;){if(o=v[f+28>>2],a=v[t+12>>2],o>>>0>=d[4+(a+(n<<4)|0)>>2]){n:{e:{for(;;){if((0|u)==(0|(n=n+1|0)))break e;if(v[4+((n<<4)+a|0)>>2])break}t=n;break n}t=u}if(v[f+24>>2]=t,u=n>>>0>=u>>>0,o=0,n=t,u)break r}if(n=v[12+((n<<4)+a|0)>>2],v[f+28>>2]=o+1,n=n+m(o,20)|0,v[f>>2]=v[n>>2],t=Re(c,n+4|0),n=v[n+16>>2],v[f+16>>2]=n,v[f>>2]==(0|e)&&jn(r,n,i),lf(t),n=v[f+24>>2],t=v[f+20>>2],!(n>>>0<(u=v[t+4>>2])>>>0))break}D=f+32|0}function Zr(r,n,e,i,f){var t,u=w(0),o=w(0),a=w(0),c=w(0),b=w(0),k=w(0),s=w(0),l=w(0);b=p[n+100>>2],k=p[n+92>>2],s=p[n+96>>2],t=(i<<2)+e|0,a=p[n+112>>2],r=v[r+96>>2],u=p[r+24>>2],c=p[n+104>>2],l=p[n+108>>2],o=p[r+28>>2],p[t+4>>2]=a+w(w(u*c)+w(l*o)),p[t>>2]=b+w(w(u*k)+w(s*o)),i=((n=i+f|0)<<2)+e|0,u=p[r>>2],o=p[r+4>>2],p[i+4>>2]=a+w(w(c*u)+w(l*o)),p[i>>2]=b+w(w(u*k)+w(s*o)),i=((n=n+f|0)<<2)+e|0,u=p[r+8>>2],o=p[r+12>>2],p[i+4>>2]=a+w(w(c*u)+w(l*o)),p[i>>2]=b+w(w(u*k)+w(s*o)),n=(n+f<<2)+e|0,u=a,a=p[r+16>>2],o=w(c*a),c=p[r+20>>2],p[n+4>>2]=u+w(o+w(l*c)),p[n>>2]=b+w(w(a*k)+w(s*c))}function Yr(r){var n,e,i=w(0),f=w(0);if(b(r),(n=2147483647&(e=t(2)))>>>0>=1065353216)return w(1065353216==(0|n)?(0|e)>=0?0:3.141592502593994:w(0)/w(r-r));r:{if(n>>>0<=1056964607){if(i=w(1.570796251296997),n>>>0<847249409)break r;return w(w(w(w(7.549789415861596e-8)-w(r*Hi(w(r*r))))-r)+w(1.570796251296997))}if((0|e)<0)return r=w(w(r+w(1))*w(.5)),i=w(T(r)),r=w(w(1.570796251296997)-w(i+w(w(i*Hi(r))+w(-7.549789415861596e-8)))),w(r+r);i=w(w(w(1)-r)*w(.5)),u(2,-4096&(b(f=w(T(i))),t(2))),r=k(),r=w(w(w(f*Hi(i))+w(w(i-w(r*r))/w(f+r)))+r),i=w(r+r)}return i}function Xr(r){var n=0,e=0,i=0,f=0;if(v[4+(r|=0)>>2]=8648,v[r>>2]=8632,n=v[r+44>>2])for(;;){if(i=v[v[r+52>>2]+(f<<2)>>2]){if(n=v[i+24>>2])for(;e=v[n+24>>2],da[v[v[n>>2]+4>>2]](n),n=e;);if(n=v[i+20>>2])for(;e=v[n+20>>2],da[v[v[n>>2]+4>>2]](n),n=e;);da[v[v[i>>2]+4>>2]](i),n=v[r+44>>2]}if(!((f=f+1|0)>>>0<n>>>0))break}return(n=v[r+72>>2])&&da[v[v[n>>2]+4>>2]](n),no(r+76|0),ho(r+56|0),oo(r+40|0),it(r+20|0),v[r+4>>2]=8664,(n=v[r+12>>2])&&(e=v[r+8>>2])&&da[0|n](e),0|r}function Qr(r){var n,e,i=0,f=0,t=0,u=w(0),o=w(0),a=w(0),c=0;n=v[r+12>>2],u=p[n>>2],f=v[r+4>>2],o=w(w(p[((e=f-2|0)<<2)+n>>2]*p[n+4>>2])-w(p[((f<<2)+n|0)-4>>2]*u));r:{if(i=f-3|0){for(r=0;a=w(u*p[12+((t=r<<2)+n|0)>>2]),u=p[((r=r+2|0)<<2)+n>>2],o=w(o+w(a-w(p[(4|t)+n>>2]*u))),r>>>0<i>>>0;);if(f>>>0<2|o<w(0))break r;f=f>>>1|0}else if(f=1,o<w(0))break r;for(r=0;o=p[(i=(t=r<<2)+n|0)>>2],u=p[(t=(4|t)+n|0)>>2],c=i,i=(e-r<<2)+n|0,p[c>>2]=p[i>>2],p[t>>2]=p[i+4>>2],p[i+4>>2]=u,p[i>>2]=o,f>>>0>(r=r+2|0)>>>0;);}}function rn(r){var n,e=0,i=0,f=0,t=0,u=0,o=0;D=n=D-32|0,v[(r|=0)>>2]=10664;r:if(e=v[r+24>>2])for(o=n+16|0;;){if(u=v[r+32>>2],d[4+(u+(i<<4)|0)>>2]<=t>>>0){n:{e:{for(;;){if((0|(i=i+1|0))==(0|e))break e;if(v[4+((i<<4)+u|0)>>2])break}f=i;break n}f=e}if(e=e>>>0<=i>>>0,t=0,i=f,e)break r}if(f=v[12+((i<<4)+u|0)>>2]+m(t,20)|0,v[n+12>>2]=v[f>>2],e=Re(o,f+4|0),f=v[f+16>>2],v[n+28>>2]=f,Sf(f),lf(e),t=t+1|0,!(i>>>0<(e=v[r+24>>2])>>>0))break}return Vu(r+52|0),bo(r+36|0),v[r+16>>2]=10648,qu(r+20|0),lf(r+4|0),D=n+32|0,0|r}function nn(r,n,e,i,t){var u=0,o=w(0),a=0;return(u=v[r+28>>2])?(a=u-1|0,u=v[v[r+36>>2]+(a<<2)>>2],v[r+28>>2]=a):on(u=Xf(172)),v[u+40>>2]=0,v[u+44>>2]=0,f[u+37|0]=0,f[u+36|0]=i,v[u+16>>2]=e,v[u+32>>2]=n,v[u+48>>2]=0,v[u+52>>2]=0,o=p[e+32>>2],v[u+104>>2]=1065353216,v[u+84>>2]=2139095039,v[u+88>>2]=1065353216,v[u+76>>2]=-1082130432,v[u+80>>2]=-1082130432,v[u+68>>2]=0,v[u+72>>2]=0,v[u+60>>2]=-1082130432,v[u+64>>2]=-1082130432,p[u+56>>2]=o,v[u+92>>2]=1065353216,v[u+96>>2]=0,o=t?Un(v[r+16>>2],v[t+16>>2],e):w(0),p[u+100>>2]=o,u}function en(r,n,e){var i,t,u,o=0,a=0;if(D=t=D-16|0,i=Oo(r),v[i+8>>2]=9196,v[i+4>>2]=n,v[i>>2]=9556,r=0,v[i+20>>2]=0,v[i+12>>2]=0,v[i+16>>2]=0,v[i+24>>2]=v[n+44>>2],f[i+28|0]=l[n+48|0],f[i+29|0]=l[n+49|0],p[i+32>>2]=p[n+52>>2],p[i+36>>2]=p[n+56>>2],n=ku(e,Bo(v[n+40>>2])),f[i+44|0]=0,v[i+40>>2]=n,ca(u=i+8|0,v[v[i+4>>2]+28>>2]),n=v[i+4>>2],v[n+28>>2])for(;o=t,a=ku(e,Bo(v[v[n+36>>2]+(r<<2)>>2])),v[o+12>>2]=a,Sn(u,t+12|0),r=r+1|0,n=v[i+4>>2],r>>>0<d[n+28>>2];);return D=t+16|0,i}function fn(r,n,e){var i,t,u,o=w(0),a=0,c=0;if(D=t=D-16|0,i=Oo(r),v[i+8>>2]=9196,v[i+4>>2]=n,v[i>>2]=10804,r=0,v[i+20>>2]=0,v[i+12>>2]=0,v[i+16>>2]=0,a=i,c=ku(e,Bo(v[n+40>>2])),v[a+24>>2]=c,p[i+28>>2]=p[n+44>>2],p[i+32>>2]=p[n+48>>2],p[i+36>>2]=p[n+52>>2],o=p[n+56>>2],f[i+44|0]=0,p[i+40>>2]=o,ca(u=i+8|0,v[v[i+4>>2]+28>>2]),n=v[i+4>>2],v[n+28>>2])for(;a=t,c=ku(e,Bo(v[v[n+36>>2]+(r<<2)>>2])),v[a+12>>2]=c,Sn(u,t+12|0),r=r+1|0,n=v[i+4>>2],r>>>0<d[n+28>>2];);return D=t+16|0,i}function tn(r,n,e,i,f){r|=0,n|=0,e|=0,i|=0,f=w(f);var t=0,u=0,o=w(0),a=w(0);r:if(t=Me(r,n)){for(;t=v[(u=t)+20>>2];);if(t=nn(r,n,e,i,u),v[u+20>>2]=t,!(f<=w(0)))break r;if((a=w(p[u+56>>2]-p[u+52>>2]))!=w(0)){o=p[u+72>>2],l[u+36|0]?(o=w(o/a),n=w(g(o))<w(2147483648)?~~o:-2147483648,f=w(w(a*w(n+1|0))+f)):f=w((o<a?a:o)+f),f=w(f-Un(v[r+16>>2],v[u+16>>2],e));break r}f=p[u+72>>2]}else gn(r,n,t=nn(r,n,e,i,0),1),pr(v[r+72>>2]);return p[t+68>>2]=f,0|t}function un(r,n,e){r|=0,n|=0,e|=0;var i=0,f=0,t=w(0),u=0,o=0;if(f=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(t=w(w(n>>>0)*w(1.75)),n=(n=w(g(t))<w(2147483648)?~~t:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,i=v[5208],u=r,o=0|da[v[v[i>>2]+16>>2]](i,v[r+12>>2],m(n,20),8524,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>f>>>0)for(;i=v[e+4>>2],n=v[r+12>>2]+m(f,20)|0,v[n>>2]=v[e>>2],v[n+4>>2]=i,v[n+16>>2]=v[e+16>>2],i=v[e+12>>2],v[n+8>>2]=v[e+8>>2],v[n+12>>2]=i,(f=f+1|0)>>>0<d[r+4>>2];);}function on(r){return v[r+16>>2]=0,v[r+20>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[r+4>>2]=8584,v[r>>2]=8568,v[r+24>>2]=0,v[r+28>>2]=0,s[r+30>>1]=0,s[r+32>>1]=0,s[r+34>>1]=0,s[r+36>>1]=0,Bf(r+40|0,0,48),v[r+92>>2]=0,v[r+96>>2]=0,v[r+88>>2]=1065353216,v[r+100>>2]=0,v[r+104>>2]=0,v[r+168>>2]=0,v[r+164>>2]=9,v[r+160>>2]=0,v[r+152>>2]=0,v[r+156>>2]=0,v[r+148>>2]=8712,v[r+144>>2]=0,v[r+136>>2]=0,v[r+140>>2]=0,v[r+132>>2]=8696,v[r+128>>2]=0,v[r+120>>2]=0,v[r+124>>2]=0,v[r+116>>2]=8680,v[r+108>>2]=0,v[r+112>>2]=2,r}function an(r,n){return v[r+16>>2]=n,v[r+8>>2]=0,v[r+12>>2]=0,v[r+68>>2]=0,v[r+60>>2]=0,v[r+64>>2]=0,v[r+56>>2]=8760,v[r+52>>2]=0,v[r+44>>2]=0,v[r+48>>2]=0,v[r+40>>2]=8696,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+24>>2]=8696,v[r+20>>2]=8744,v[r+4>>2]=8648,v[r>>2]=8632,n=Xf(32),f[n+28|0]=0,v[n+24>>2]=r+20,v[n+20>>2]=r,v[n+16>>2]=0,v[n+8>>2]=0,v[n+12>>2]=0,v[n+4>>2]=8728,v[n>>2]=8616,v[r+80>>2]=0,v[r+84>>2]=0,v[r+76>>2]=8536,v[r+72>>2]=n,f[r+88|0]=0,v[r+96>>2]=0,v[r+100>>2]=1065353216,v[r+92>>2]=9,r}function cn(r,n,e){var i,f,t=0,u=0,o=0,a=0;D=i=D-16|0,v[i+8>>2]=0,v[i+12>>2]=0,ie(r,n,i+8|0),f=v[i+12>>2],n=v[i+8>>2];r:{for(;;){if(r=0,(0|n)==(0|f))break r;if(r=l[0|n],n=n+1|0,58==(0|r))break}for(v[i+8>>2]=n,r=n;;){v[(a=(t<<3)+e|0)>>2]=n;n:{for(;;){if(u=r,(0|r)==(0|f)){r=n,o=t;break n}if(r=u+1|0,44==l[0|u])break}if(o=3,v[4+((t<<3)+e|0)>>2]=u-1,Kn(a),n=r,3!=(0|(t=t+1|0)))continue}break}v[4+(n=(o<<3)+e|0)>>2]=f,v[n>>2]=r,Kn(n),r=o+1|0}return D=i+16|0,r}function bn(r,n,e){r|=0,n|=0,e=w(e);var i,f=w(0),t=w(0),u=0,o=0,a=w(0);if(e=hu(e),i=v[r+16>>2],r=m(n,19),(f=p[i+(r<<2)>>2])==w(0))return w(e);if(f!=w(1)){if((u=r+1|0)>>>0<(o=r+19|0)>>>0){for(r=u;;){if(n=r,e<=(t=p[(r<<2)+i>>2]))return f=w(0),(0|n)!=(0|u)&&(a=p[(r=(n<<2)+i|0)-4>>2],f=p[r-8>>2]),w(w(a+w(w(w(e-f)*w(p[4+((n<<2)+i|0)>>2]-a))/w(t-f))));if(!(o>>>0>(r=n+2|0)>>>0))break}r=n+1|0}f=p[(r<<2)+i>>2],e=w(f+w(w(w(e-t)*w(w(1)-f))/w(w(1)-t)))}else e=w(0);return w(e)}function kn(r){var n,e,i,f,t,u,o,a,c=0,b=0;if(v[(r|=0)>>2]=10456,Pn(n=r+16|0),Pn(e=r+32|0),Pn(i=r+48|0),v[r+64>>2]=0,Pn(f=r+68|0),Pn(t=r+84|0),Pn(u=r+100|0),Pn(o=r+116|0),Pn(a=r+132|0),v[r+192>>2])for(;b=v[5208],da[v[v[b>>2]+20>>2]](b,v[v[r+200>>2]+(c<<2)>>2],8524,74),(c=c+1|0)>>>0<d[r+192>>2];);return lf(r+220|0),lf(r+208|0),Hu(r+188|0),lf(r+176|0),lf(r+164|0),Lu(a),Mu(o),_u(u),xu(t),Ju(f),Bu(i),Nu(e),bo(n),lf(r+4|0),0|r}function sn(r,n){var e,i,f,t=0,u=0,o=0,a=0;if(D=i=D-16|0,e=Eo(r),v[e+24>>2]=9148,v[e+8>>2]=8712,r=0,v[e+4>>2]=0,v[e>>2]=9120,v[e+36>>2]=0,v[e+28>>2]=0,v[e+32>>2]=0,v[e+20>>2]=0,v[e+12>>2]=0,v[e+16>>2]=0,ca(t=e+8|0,n),d[8+(f=e+24|0)>>2]<n>>>0&&(v[f+8>>2]=n,u=v[5208],o=f,a=0|da[v[v[u>>2]+16>>2]](u,v[f+12>>2],m(n,12),8524,97),v[o+12>>2]=a),v[i+4>>2]=0,yn(t,n,i+4|0),(0|n)>0)for(;wn(f,t=ht(i+4|0)),lf(t),(0|n)!=(0|(r=r+1|0)););return D=i+16|0,e}function vn(r,n){var e=0,i=0,f=0,t=0,u=w(0);if(ba(r,v[n+4>>2]+v[r+4>>2]|0),v[n+4>>2])for(;f=v[n+12>>2]+(t<<1)|0,(0|(e=v[r+4>>2]))!=v[r+8>>2]?s[v[r+12>>2]+(e<<1)>>1]=h[f>>1]:(f=h[f>>1],u=w(w(e>>>0)*w(1.75)),e=(e=w(g(u))<w(2147483648)?~~u:-2147483648)>>>0<=8?8:e,v[r+8>>2]=e,i=v[5208],i=0|da[v[v[i>>2]+16>>2]](i,v[r+12>>2],e<<1,8524,109),v[r+12>>2]=i,e=v[r+4>>2],s[i+(e<<1)>>1]=f),v[r+4>>2]=e+1,(t=t+1|0)>>>0<d[n+4>>2];);}function ln(r){var n=0,e=0,i=0,t=0,u=0;if(i=v[124+(r|=0)>>2])for(u=v[r+132>>2];n=v[(e<<2)+u>>2],f[n+88|0]=1,p[n+60>>2]=p[n+32>>2],t=v[n+40>>2],v[n+64>>2]=v[n+36>>2],v[n+68>>2]=t,t=v[n+48>>2],v[n+72>>2]=v[n+44>>2],v[n+76>>2]=t,t=v[n+56>>2],v[n+80>>2]=v[n+52>>2],v[n+84>>2]=t,(0|i)!=(0|(e=e+1|0)););if(e=v[r+108>>2])for(n=0;i=v[v[r+116>>2]+(n<<2)>>2],da[v[v[i>>2]+12>>2]](i),(0|e)!=(0|(n=n+1|0)););}function hn(r,n){var e=0,i=0;r:{if(3&((i=r)^n))e=l[0|n];else{if(3&n)for(;;){if(e=l[0|n],f[0|i]=e,!e)break r;if(i=i+1|0,!(3&(n=n+1|0)))break}if(!(~(e=v[n>>2])&e-16843009&-2139062144))for(;v[i>>2]=e,e=v[n+4>>2],i=i+4|0,n=n+4|0,!(e-16843009&~e&-2139062144););}if(f[0|i]=e,255&e)for(;e=l[n+1|0],f[i+1|0]=e,i=i+1|0,n=n+1|0,e;);}return r}function dn(r,n,e){var i,f=0,t=w(0),u=w(0),o=0;if(!(i=v[n+24>>2]))return 1;f=dn(r,i,e),p[i+60>>2]=p[i+64>>2],p[i+76>>2]=p[i+80>>2];r:{if((t=p[n+96>>2])>w(0)&&(u=p[n+100>>2])<=t){if(u!=w(0)&p[i+108>>2]!=w(0))break r;return v[n+24>>2]=v[i+24>>2],(o=v[i+24>>2])&&(v[o+28>>2]=n),p[n+104>>2]=p[i+104>>2],jf(v[r+72>>2],i),f}p[i+72>>2]=w(e*p[i+88>>2])+p[i+72>>2],p[n+96>>2]=t+e,f=0}return f}function pn(r,n,e){r|=0,n|=0,e|=0;var i=0,f=w(0),t=0,u=0,o=0;if(i=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(f=w(w(n>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5208],u=r,o=0|da[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<1,8524,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>i>>>0)for(r=v[r+12>>2];s[r+(i<<1)>>1]=h[e>>1],(0|(i=i+1|0))!=(0|n););}function yn(r,n,e){r|=0,n|=0,e|=0;var i=0,f=w(0),t=0,u=0,o=0;if(i=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(f=w(w(n>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5208],u=r,o=0|da[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<2,8524,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>i>>>0)for(r=v[r+12>>2];p[r+(i<<2)>>2]=p[e>>2],(0|(i=i+1|0))!=(0|n););}function mn(r,n,e){r|=0,n|=0,e|=0;var i=0,f=w(0),t=0,u=0,o=0;if(i=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(f=w(w(n>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5208],u=r,o=0|da[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<2,8524,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>i>>>0)for(r=v[r+12>>2];v[r+(i<<2)>>2]=v[e>>2],(0|(i=i+1|0))!=(0|n););}function wn(r,n){var e,i=0,f=0,t=w(0);D=e=D-16|0,(0|(i=v[r+4>>2]))!=v[r+8>>2]?(v[r+4>>2]=i+1,Re(v[r+12>>2]+m(i,12)|0,n)):(i=Re(e+4|0,n),t=w(w(d[r+4>>2])*w(1.75)),n=(n=w(g(t))<w(2147483648)?~~t:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,f=v[5208],n=0|da[v[v[f>>2]+16>>2]](f,v[r+12>>2],m(n,12),8524,109),v[r+12>>2]=n,f=r,r=v[r+4>>2],v[f+4>>2]=r+1,Re(n+m(r,12)|0,i),lf(i)),D=e+16|0}function gn(r,n,e,i){var t,u,o=w(0);D=u=D-16|0,t=Me(r,n),v[v[r+52>>2]+(n<<2)>>2]=e,t&&(i&&(n=v[r+72>>2],ut(u,1,t,0),qr(n+4|0,u)),v[e+24>>2]=t,v[t+28>>2]=e,v[e+96>>2]=0,v[t+24>>2]&&(o=p[t+100>>2])>w(0)&&(o=w(p[t+96>>2]/o),p[e+104>>2]=p[e+104>>2]*(o>w(1)?w(1):o)),v[t+152>>2]=0),n=v[r+72>>2],D=r=D-16|0,ut(r,0,e,0),qr(n+4|0,r),f[v[n+20>>2]+88|0]=1,D=r+16|0,D=u+16|0}function Fn(r,n,e){r|=0,n|=0,e|=0;var i=0,f=w(0),t=0,u=0,o=0;if(i=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(f=w(w(n>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5208],u=r,o=0|da[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<2,8524,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>i>>>0)for(;v[v[r+12>>2]+(i<<2)>>2]=v[e>>2],(0|(i=i+1|0))!=(0|n););}function An(){B(21288,6226),K(21397,4501,1,1,0),E(22004,3678,1,-128,127),E(22005,3671,1,-128,127),E(22006,3669,1,0,255),E(22007,1475,2,-32768,32767),E(21315,1466,2,0,65535),E(21295,1776,4,-2147483648,2147483647),E(21305,1767,4,0,-1),E(22008,4821,4,-2147483648,2147483647),E(21289,4812,4,0,-1),vu(22009,2390,-2147483648,2147483647),vu(22010,2389,0,-1),H(21294,2334,4),H(22011,5797,8),J(21438,4536)}function Tn(r,n,e){return v[r+8>>2]=e,v[r+4>>2]=n,v[r>>2]=10728,e=v[e+8>>2],v[r+28>>2]=1065353216,v[r+32>>2]=1065353216,v[r+20>>2]=1065353216,v[r+24>>2]=1065353216,v[r+16>>2]=9308,v[r+12>>2]=e,ci(r+16|0),v[r+40>>2]=0,v[r+44>>2]=0,v[r+36>>2]=9308,v[r+48>>2]=0,v[r+52>>2]=0,ci(r+36|0),n=l[n+64|0],v[r+80>>2]=0,v[r+72>>2]=0,v[r+76>>2]=0,v[r+68>>2]=8712,v[r+60>>2]=0,v[r+64>>2]=0,f[r+56|0]=n,En(r),r}function $n(r,n,e){var i=0,f=0;r:{if(i=v[r+4>>2])for(;;){if($f(i+4|0,n))break r;if(!(i=v[i+20>>2]))break}return i=Xf(28),v[i>>2]=8824,Ot(i+4|0,0,0),v[i+20>>2]=0,v[i+24>>2]=0,f=v[n+8>>2],v[i+8>>2]=v[n+4>>2],v[i+12>>2]=f,p[i+16>>2]=p[e>>2],(n=v[r+4>>2])&&(v[n+24>>2]=i,v[i+20>>2]=n),v[r+4>>2]=i,void(v[r+8>>2]=v[r+8>>2]+1)}r=v[n+8>>2],v[i+8>>2]=v[n+4>>2],v[i+12>>2]=r,p[i+16>>2]=p[e>>2]}function In(r,n){var e=0,i=0,f=0,t=0,u=0;for(r=r+4|0;;){if(r=v[r>>2]){if(!n|!(i=v[r+28>>2]))e=-1,n>>>0>i>>>0||(e=(0|n)!=(0|i));else{f=n,u=0;r:if(e=l[0|i]){for(;;){if((t=l[0|f])&&((0|e)==(0|t)||(0|po(e))==(0|po(t)))){if(f=f+1|0,e=l[i+1|0],i=i+1|0,e)continue;break r}break}u=e}e=po(255&u)-po(l[0|f])|0}if(e)continue}break}return r}function Cn(r,n,e){var i=0,t=w(0),u=0,o=0,a=0;if(i=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(t=w(w(n>>>0)*w(1.75)),n=(n=w(g(t))<w(2147483648)?~~t:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,u=v[5208],o=r,a=0|da[v[v[u>>2]+16>>2]](u,v[r+12>>2],n,8524,85),v[o+12>>2]=a,n=v[r+4>>2]),n>>>0>i>>>0)for(r=v[r+12>>2];f[r+i|0]=l[0|e],(0|(i=i+1|0))!=(0|n););}function Pn(r){var n=0,e=0,i=0,f=0,t=0,u=0,o=0,a=0;if((0|(e=v[r+4>>2]))>0)for(f=e;;){if(t=f-1|0,(n=v[v[r+12>>2]+(t<<2)>>2])&&(da[v[v[n>>2]+4>>2]](n),e=v[r+4>>2]),e=e-1|0,v[r+4>>2]=e,e>>>0>(n=t)>>>0)for(;i=v[r+12>>2],o=v[(u=i+(n<<2)|0)>>2],a=i,i=(n=n+1|0)<<2,v[u>>2]=v[a+i>>2],v[i+v[r+12>>2]>>2]=o,(0|n)!=(0|e););if(n=(0|f)>1,f=t,!n)break}}function En(r){r|=0;var n=0,e=w(0);n=Ko(v[r+4>>2]),p[r+20>>2]=p[n+4>>2],p[r+24>>2]=p[n+8>>2],p[r+28>>2]=p[n+12>>2],p[r+32>>2]=p[n+16>>2],ci(r+16|0),n=Lo(v[r+4>>2]);r:{if(v[n+4>>2]){if(v[r+60>>2]=0,(0|(n=Fi(v[r+12>>2],v[v[r+4>>2]+4>>2],n)))==v[r+60>>2])break r;v[r+60>>2]=n}else{if(!v[r+60>>2])break r;v[r+60>>2]=0}e=p[v[r+12>>2]+160>>2],v[r+72>>2]=0,p[r+64>>2]=e}}function On(r,n){var e=0,i=w(0),f=0;if((0|(e=v[r+4>>2]))==v[r+8>>2])return f=v[n>>2],i=w(w(e>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5208],n=0|da[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<2,8524,109),v[r+12>>2]=n,e=r,r=v[r+4>>2],v[e+4>>2]=r+1,void(v[n+(r<<2)>>2]=f);v[r+4>>2]=e+1,v[v[r+12>>2]+(e<<2)>>2]=v[n>>2]}function Rn(r,n){var e=0,i=w(0),f=w(0);(0|(e=v[r+4>>2]))!=v[r+8>>2]?p[v[r+12>>2]+(e<<2)>>2]=p[n>>2]:(f=p[n>>2],i=w(w(e>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5208],n=0|da[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<2,8524,109),v[r+12>>2]=n,e=v[r+4>>2],p[n+(e<<2)>>2]=f),v[r+4>>2]=e+1}function Sn(r,n){var e=0,i=w(0),f=0;(0|(e=v[r+4>>2]))!=v[r+8>>2]?v[v[r+12>>2]+(e<<2)>>2]=v[n>>2]:(f=v[n>>2],i=w(w(e>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5208],n=0|da[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<2,8524,109),v[r+12>>2]=n,e=v[r+4>>2],v[n+(e<<2)>>2]=f),v[r+4>>2]=e+1}function Wn(r,n){var e,i=0,t=0;return e=v[r+4>>2],v[r+4>>2]=e+1,t=127&(i=f[0|e]),(0|i)>=0||(v[r+4>>2]=e+2,t|=(i=f[e+1|0])<<7&16256,(0|i)>=0||(v[r+4>>2]=e+3,t|=(i=f[e+2|0])<<14&2080768,(0|i)>=0||(v[r+4>>2]=e+4,t|=(i=f[e+3|0])<<21&266338304,(0|i)>=0||(v[r+4>>2]=e+5,t=l[e+4|0]<<28|t)))),n?t:0-(1&t)^t>>>1}function Gn(r,n){r:if((0|n)>=1024){if(r*=898846567431158e293,n>>>0<2047){n=n-1023|0;break r}r*=898846567431158e293,n=((0|n)>=3069?3069:n)-2046|0}else(0|n)>-1023||(r*=2004168360008973e-307,n>>>0>4294965304?n=n+969|0:(r*=2004168360008973e-307,n=((0|n)<=-2960?-2960:n)+1938|0));return u(0,0),u(1,n+1023<<20),r*+o()}function Un(r,n,e){r|=0,n|=0,e|=0;var i,f=w(0);D=i=D-16|0,Ot(i+4|0,n,e);r:{n:{e:{if(e=v[r+16>>2])for(;;){if($f(e+4|0,i+4|0))break e;if(!(e=v[e+20>>2]))break}r=r+8|0;break n}if(!(e=v[r+16>>2]))break r;for(;;){if(!$f(e+4|0,i+4|0)){if(e=v[e+20>>2])continue;break r}break}r=e+16|0}f=p[r>>2]}return D=i+16|0,w(f)}function jn(r,n,e){var i=0,f=0;if(n&&Tf(0|da[v[v[n>>2]+8>>2]](n),20924))if(f=v[n+24>>2])for(e=0;;){if(i=e<<2,(e=e+1|0)>>>0<(i=e+v[i+v[n+32>>2]>>2]|0)>>>0){for(;Ki(r,v[v[r+20>>2]+(v[v[n+32>>2]+(e<<2)>>2]<<2)>>2]),(0|i)!=(0|(e=e+1|0)););e=i}if(!(e>>>0<f>>>0))break}else Ki(r,e)}function Hn(r,n){var e=0,i=0,f=0,t=0;if(v[r>>2]=9148,v[r+4>>2]=v[n+4>>2],e=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=e,e&&(i=v[5208],f=r,t=0|da[v[v[i>>2]+12>>2]](i,m(e,12),8524,206),v[f+12>>2]=t,v[r+4>>2]))for(e=0;Re((i=m(e,12))+v[r+12>>2]|0,i+v[n+12>>2]|0),(e=e+1|0)>>>0<d[r+4>>2];);return r}function Ln(r,n,e){var i=0,t=0;r:{if(i=v[r+4>>2])for(t=v[n>>2];;){if(v[i+4>>2]==(0|t))break r;if(!(i=v[i+12>>2]))break}return i=Xf(20),v[i+12>>2]=0,v[i+16>>2]=0,v[i>>2]=8552,v[i+4>>2]=v[n>>2],f[i+8|0]=l[0|e],(n=v[r+4>>2])&&(v[n+16>>2]=i,v[i+12>>2]=n),v[r+4>>2]=i,void(v[r+8>>2]=v[r+8>>2]+1)}v[i+4>>2]=t,f[i+8|0]=l[0|e]}function Mn(r,n,e){var i=0,f=0,t=0,u=0;if(v[r>>2]=e,v[r+4>>2]=v[n+4>>2],e=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=e,e&&(i=v[5208],t=r,u=0|da[v[v[i>>2]+12>>2]](i,e<<2,8524,206),v[t+12>>2]=u,i=v[r+4>>2]))for(e=0;v[(f=e<<2)+v[r+12>>2]>>2]=v[v[n+12>>2]+f>>2],(0|i)!=(0|(e=e+1|0)););return r}function _n(r,n){var e=0,i=0,f=0,t=0;for(v[n>>2]=0;;){r:{n:{e:{if(((i=l[r+f|0])-58&255)>>>0<=245){if(i-65>>>0<6|i-97>>>0<6)break e;return void(v[n>>2]=0)}e<<=4,t=-48;break n}if(e<<=4,v[n>>2]=e,t=-55,!((i-65&255)>>>0<6)&&(t=-87,(i-97&255)>>>0>5))break r}e|=(i<<24>>24)+t,v[n>>2]=e}if(4==(0|(f=f+1|0)))break}}function zn(r,n){n|=0;var e=0;v[100+(r|=0)>>2]=n,n&&(v[r+24>>2]=0,zi(r+20|0,n+20|0),v[r+40>>2]=0,_i(r+36|0,n+36|0),e=v[n+52>>2],v[r+124>>2]=0,v[r+52>>2]=e,_i(r+120|0,n+120|0),v[r+140>>2]=0,vn(r+136|0,n+136|0),e=v[n+224>>2],v[r+156>>2]=0,v[r+224>>2]=e,vn(r+152|0,n+152|0),p[r+196>>2]=p[n+196>>2],p[r+200>>2]=p[n+200>>2])}function xn(r,n){var e=0,i=0,f=0,t=0;if(v[r>>2]=8712,v[r+4>>2]=v[n+4>>2],e=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=e,e&&(i=v[5208],e=0|da[v[v[i>>2]+12>>2]](i,e<<2,8524,206),v[r+12>>2]=e,i=v[r+4>>2]))for(t=v[n+12>>2],n=0;p[e+(f=n<<2)>>2]=p[t+f>>2],(0|i)!=(0|(n=n+1|0)););return r}function Jn(r,n){var e=0,i=0,f=0,t=0;if(v[r>>2]=8680,v[r+4>>2]=v[n+4>>2],e=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=e,e&&(i=v[5208],e=0|da[v[v[i>>2]+12>>2]](i,e<<2,8524,206),v[r+12>>2]=e,i=v[r+4>>2]))for(t=v[n+12>>2],n=0;v[e+(f=n<<2)>>2]=v[t+f>>2],(0|i)!=(0|(n=n+1|0)););return r}function Kn(r){var n=0,e=0,i=0,f=0;e=v[r+4>>2];r:{n:if(n=v[r>>2],eo(l[0|n])){for(;;){if(n>>>0>=e>>>0)break n;if(i=n+1|0,v[r>>2]=i,f=l[n+1|0],n=i,!eo(f))break}break r}i=n}if((0|e)!=(0|i)){for(;e=(n=e)-1|0,v[r+4>>2]=e,!(e>>>0<i>>>0||13!=l[0|e]););v[r+4>>2]=n}}function Bn(r,n,e,i){return v[r+4>>2]=n,v[r>>2]=10744,Re(r+8|0,e),v[r+36>>2]=1065353216,v[r+40>>2]=1065353216,v[r+28>>2]=1065353216,v[r+32>>2]=1065353216,v[r+24>>2]=9308,v[r+20>>2]=i,ci(r+24|0),v[r+48>>2]=0,v[r+52>>2]=0,v[r+44>>2]=9308,v[r+56>>2]=0,v[r+60>>2]=0,ci(r+44|0),f[r+64|0]=0,ht(r+68|0),v[r+80>>2]=0,r}function Nn(r,n,e,i){var f,t=0,u=0,o=0;return D=f=D-16|0,o=1,(t=Me(r,n))?p[t+80>>2]!=w(-1)?pi(r,t):(v[v[r+52>>2]+(n<<2)>>2]=v[t+24>>2],u=v[r+72>>2],o=0,ut(f,1,t,0),qr(u+4|0,f),jf(v[r+72>>2],t),pi(r,t),t=v[t+24>>2]):t=0,gn(r,u=n,n=nn(r,n,e,i,t),o),pr(v[r+72>>2]),D=f+16|0,n}function qn(r,n){n|=0;var e=0,i=0;if(!(d[44+(r|=0)>>2]<=n>>>0)&&(e=v[v[r+52>>2]+(n<<2)>>2])){if(jf(v[r+72>>2],e),pi(r,e),n=v[e+24>>2])for(i=e;jf(v[r+72>>2],n),v[i+24>>2]=0,v[i+28>>2]=0,i=n,n=v[n+24>>2];);v[v[r+52>>2]+(v[e+32>>2]<<2)>>2]=0,pr(v[r+72>>2])}}function Dn(r,n,e){var i=0,f=0,t=0;if(!(d[r+8>>2]<=n>>>0)&&(f=v[r+16>>2]+(n<<4)|0,v[f+4>>2])){r:{for(;;){if(qi(4+(v[f+12>>2]+m(i,20)|0)|0,e))break r;if(!((i=i+1|0)>>>0<d[f+4>>2]))break}return 0}(0|i)<0||(t=v[16+(v[12+(v[r+16>>2]+(n<<4)|0)>>2]+m(i,20)|0)>>2])}return t}function Vn(r,n,e,i){var f,t,u,o,a,c,b=0;D=f=D-16|0,e=Re(f+4|0,e),t=v[e+4>>2],b=(u=v[i+4>>2])+t|0,v[e+4>>2]=b,c=v[i+8>>2],o=v[5208],a=v[e+8>>2],b=0|da[v[v[o>>2]+16>>2]](o,a,b+1|0,8524,150),v[e+8>>2]=b,Ff(b+t|0,(0|c)==(0|a)?b:v[i+8>>2],u+1|0),ee(r+32|0,e),lf(e),n&&or(we(n)),D=f+16|0}function Zn(r,n,e,i,f,t){r|=0,n=w(n),e=w(e),i=w(i),f=w(f),t|=0;var u=0;r=v[r+112>>2],t?(p[r+28>>2]=e,p[r+24>>2]=n,p[r+20>>2]=f,p[r+16>>2]=n,p[r+4>>2]=e,p[r>>2]=i,u=r+8|0,t=3):(p[r+28>>2]=e,p[r+24>>2]=i,p[r+20>>2]=e,p[r+16>>2]=n,p[r+12>>2]=f,p[r+8>>2]=n,u=r,t=1),p[u>>2]=i,p[r+(t<<2)>>2]=f}function Yn(r,n,e,i){var f,t,u=w(0),o=w(0),a=w(0);return e=v[e+12>>2],i=v[i+12>>2],f=e+(v[i+((r+1|0)%(0|n)<<2)>>2]<<3)|0,t=e+(v[i+(r<<2)>>2]<<3)|0,u=p[t+4>>2],r=e+(v[i+(((r+n|0)-1|0)%(0|n)<<2)>>2]<<3)|0,o=p[r+4>>2],a=p[f+4>>2],!(w(w(p[f>>2]*w(u-o))+w(w(p[r>>2]*w(a-u))+w(p[t>>2]*w(o-a))))>=w(0))}function Xn(r,n,e,i){return v[r+4>>2]=n,v[r>>2]=9212,Re(r+8|0,e),v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=i,v[r+32>>2]=0,v[r+36>>2]=0,v[r+48>>2]=0,v[r+52>>2]=0,v[r+40>>2]=1065353216,v[r+44>>2]=1065353216,f[r+53|0]=0,f[r+54|0]=0,f[r+55|0]=0,f[r+56|0]=0,f[r+57|0]=0,f[r+58|0]=0,f[r+59|0]=0,f[r+60|0]=0,r}function Qn(r){var n=0,e=0,i=0;r:{n:if(3&(n=r)){if(!l[0|r])return 0;for(;;){if(!(3&(n=n+1|0)))break n;if(!l[0|n])break}break r}for(;e=n,n=n+4|0,!(~(i=v[e>>2])&i-16843009&-2139062144););for(;e=(n=e)+1|0,l[0|n];);}return n-r|0}function re(r,n){var e,i,f=0,t=0,u=0,o=0,a=0;if(D=e=D-16|0,i=Wn(r,1),s[e+14>>1]=0,pn(n,i,e+14|0),(0|i)>0)for(f=v[r+4>>2],a=v[n+12>>2];v[r+4>>2]=f+1,u=(t<<1)+a|0,o=l[0|f]<<8,s[u>>1]=o,n=f+2|0,v[r+4>>2]=n,s[u>>1]=l[f+1|0]|o,f=n,(0|(t=t+1|0))!=(0|i););D=e+16|0}function ne(r,n,e){var i=0,f=0,t=w(0),u=w(0),o=w(0);return f=Se(v[5504],v[5505],1284865837,1481765933),i=V,i=(f=f+1|0)?i:i+1|0,v[5504]=f,v[5505]=i,t=w(w(i>>>1|0)*w(4.656612873077393e-10)),o=w(e-r),u=w(n-r),t<=w(o/u)?w(w(T(w(o*w(u*t))))+r):w(n-w(T(w(w(n-e)*w(u*w(w(1)-t))))))}function ee(r,n){var e=0,i=0;if((0|r)!=(0|n)){if((e=v[r+8>>2])&&(i=v[5208],da[v[v[i>>2]+20>>2]](i,e,8524,106)),!v[n+8>>2])return v[r+4>>2]=0,void(v[r+8>>2]=0);v[r+4>>2]=v[n+4>>2],e=r,r=v[5208],r=0|da[v[v[r>>2]+12>>2]](r,v[n+4>>2]+1|0,8524,113),v[e+8>>2]=r,Ff(r,v[n+8>>2],v[n+4>>2]+1|0)}}function ie(r,n,e){var i=0,f=0;if((0|(i=v[r>>2]))==(0|n))return 0;v[e>>2]=i,f=n;r:if((0|(i=v[r>>2]))!=(0|n)){for(;;){if(f=i,10==l[0|i])break r;if(i=i+1|0,v[r>>2]=i,(0|n)==(0|i))break}f=n}return v[e+4>>2]=f,Kn(e),(0|(e=n))!=(0|(n=v[r>>2]))&&(v[r>>2]=n+1),1}function fe(r,n,e){var i,t,u=0,o=0;if(D=i=D-272|0,t=hn(i+16|0,n),e){u=Qn(n),n=Qn(n=u+t|0)+n|0;r:if(u=255-u|0)for(;;){if(!(o=l[0|e]))break r;if(f[0|n]=o,n=n+1|0,e=e+1|0,!(u=u-1|0))break}f[0|n]=0}ee(n=r+24|0,r=gu(i+4|0,t,0)),lf(r),D=i+272|0}function te(r,n,e,i,f){var t,u,o,a=0,c=0;if(v[r>>2]=11112,t=Tt(16),v[r+4>>2]=t,u=Tt((o=Se(n,0,24,0),V?-1:o)),n)for(c=m(n,24)+u|0,a=u;v[a+20>>2]=0,(0|c)!=(0|(a=a+24|0)););v[t+8>>2]=n,v[t>>2]=u,v[t+12>>2]=i,v[t+4>>2]=e,v[r+8>>2]=f}function ue(r){var n=0,e=0,i=0;if(v[44+(r|=0)>>2]=0,e=v[r+28>>2]){for(i=r+40|0;Sn(i,v[r+36>>2]+(n<<2)|0),(0|e)!=(0|(n=n+1|0)););if(e=v[r+28>>2])for(n=0;En(v[v[r+36>>2]+(n<<2)>>2]),(0|e)!=(0|(n=n+1|0)););}}function oe(r){var n=0,e=0,i=0;if(v[(r|=0)>>2]=8840,v[r+36>>2]&&(e=v[r+8>>2]))for(;i=v[r+36>>2],da[v[v[i>>2]+12>>2]](i,v[v[v[r+16>>2]+(n<<2)>>2]+8>>2]),(0|e)!=(0|(n=n+1|0)););return Pn(n=r+4|0),Pn(e=r+20|0),io(e),to(n),0|r}function ae(r){var n=0,e=0;if(v[(r|=0)>>2]=9148,n=v[r+4>>2])for(;n=v[r+12>>2]+m(~e+n|0,12)|0,da[v[v[n>>2]>>2]](n),(n=v[r+4>>2])>>>0>(e=e+1|0)>>>0;);return v[r+4>>2]=0,(n=v[r+12>>2])&&(e=v[5208],da[v[v[e>>2]+20>>2]](e,n,8524,215)),0|r}function ce(r){var n=0;return v[r+12>>2]=0,(n=v[r+24>>2])&&da[v[v[n>>2]+4>>2]](n),(n=v[r+20>>2])&&da[v[v[n>>2]+4>>2]](n),(n=v[r+16>>2])&&da[v[v[n>>2]+4>>2]](n),(n=v[r+8>>2])&&da[v[v[n>>2]+4>>2]](n),(n=v[r+32>>2])&&or(Iu(n)),Tu(r+80|0),$u(r- -64|0),r}function be(r,n,e,i,f){n|=0,e|=0,i|=0,f|=0;var t,u,o=0;return D=t=D-32|0,n=((o=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&o?v[v[n>>2]+r>>2]:r,o=n,u=e,n=Ae(t+20|0,i+4|0,v[i>>2],0),e=Ae(t+8|0,f+4|0,v[f>>2],0),r=0|da[0|r](o,u,n,e),lf(e),lf(n),D=t+32|0,0|r}function ke(r,n){var e=0,i=0;if((0|(e=v[r+8>>2]))!=(0|n)){if(e&&(i=v[5208],da[v[v[i>>2]+20>>2]](i,e,8524,122)),!n)return v[r+4>>2]=0,void(v[r+8>>2]=0);e=Qn(n),v[r+4>>2]=e,i=v[5208],e=0|da[v[v[i>>2]+12>>2]](i,e+1|0,8524,129),v[r+8>>2]=e,Ff(e,n,v[r+4>>2]+1|0)}}function se(r){var n=0,e=0;if(v[(r|=0)>>2]=10696,n=v[r+4>>2])for(;lf(4+(v[r+12>>2]+m(~e+n|0,20)|0)|0),(e=e+1|0)>>>0<(n=v[r+4>>2])>>>0;);return v[r+4>>2]=0,(n=v[r+12>>2])&&(e=v[5208],da[v[v[e>>2]+20>>2]](e,n,8524,215)),0|r}function ve(r,n){var e=0;if(v[r>>2]=n,n=v[r+4>>2])for(;n=v[r+12>>2]+(~e+n<<4)|0,da[v[v[n>>2]>>2]](n),(n=v[r+4>>2])>>>0>(e=e+1|0)>>>0;);return v[r+4>>2]=0,(n=v[r+12>>2])&&(e=v[5208],da[v[v[e>>2]+20>>2]](e,n,8524,215)),r}function le(r,n){var e;return D=e=D-16|0,r=Eo(r),v[r+20>>2]=8760,v[r+4>>2]=8712,v[r>>2]=9492,v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+16>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[e+12>>2]=0,yn(r+4|0,n,e+12|0),v[e+8>>2]=0,Fn(r+20|0,n,e+8|0),D=e+16|0,r}function he(r,n){var e,i,f=0,t=0;D=e=D-16|0,v[e+12>>2]=n;r:{if(i=v[r+8>>2]){if(t=v[r+16>>2],v[t>>2]==(0|n))break r;for(;(0|i)!=(0|(f=f+1|0))&v[(f<<2)+t>>2]!=(0|n););if(f>>>0<i>>>0)break r}Sn(r+4|0,e+12|0)}D=e+16|0}function de(r,n){var e,i=w(0);return D=e=D-16|0,i=w(-1),Qn(r)>>>1>>>0<=n>>>0||(r=(n<<1)+r|0,f[e+13|0]=l[0|r],r=l[r+1|0],f[e+15|0]=0,f[e+14|0]=r,r=mr(e+13|0,e+8|0,16,-1),l[v[e+8>>2]]||(i=w(w(0|r)/w(255)))),D=e+16|0,i}function pe(r,n,e,i,f){r|=0,n|=0,e|=0,i|=0,f=w(f);var t,u=0;D=t=D-32|0,n=((u=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=Ae(t+20|0,e+4|0,v[e>>2],0),e=Ae(t+8|0,i+4|0,v[i>>2],0),da[0|r](u,n,e,f),lf(e),lf(n),D=t+32|0}function ye(r,n,e,i){return r=Oo(r),v[r+16>>2]=9196,v[r+12>>2]=i,v[r+8>>2]=e,v[r+4>>2]=n,v[r>>2]=9164,Bf(r+20|0,0,69),s[r+116>>1]=0,v[r+108>>2]=1065353216,v[r+112>>2]=0,v[r+100>>2]=0,v[r+104>>2]=0,v[r+92>>2]=1065353216,v[r+96>>2]=0,Ke(r),r}function me(r,n,e,i,f){var t=w(0),u=w(0),o=w(0),a=0,c=w(0);i=v[i+12>>2]+(f<<2)|0,n=v[n+12>>2]+(e<<2)|0,u=p[n+12>>2],o=p[n+8>>2],t=Pr(w(u-p[n+4>>2]),w(o-p[n>>2])),p[i+8>>2]=t,a=i,c=w(u+w(r*Wr(t))),p[a+4>>2]=c,a=i,c=w(o+w(r*Rr(t))),p[a>>2]=c}function we(r){var n=0,e=0;for(n=v[r+4>>2];n&&(e=v[n>>2],we(n),or(n),n=e););return(n=v[r+16>>2])&&(e=v[5208],da[v[v[e>>2]+20>>2]](e,n,8524,164)),(n=v[r+28>>2])&&(e=v[5208],da[v[v[e>>2]+20>>2]](e,n,8524,168)),r}function ge(r,n){return v[r>>2]=10664,Re(r+4|0,n),v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=10680,v[r+16>>2]=10648,v[r- -64>>2]=0,v[r+56>>2]=0,v[r+60>>2]=0,v[r+52>>2]=10712,v[r+48>>2]=0,v[r+40>>2]=0,v[r+44>>2]=0,v[r+36>>2]=9612,r}function Fe(r,n){return r=kt(r,n),v[r+36>>2]=8712,v[r+20>>2]=10632,v[r>>2]=11024,v[r+40>>2]=0,v[r+44>>2]=0,v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+48>>2]=0,v[r+52>>2]=0,v[r+56>>2]=r,n=v[5294],v[5294]=n+1,v[r+60>>2]=n<<11&134215680,r}function Ae(r,n,e,i){return v[r>>2]=10760,n?(v[r+4>>2]=e,i?(v[r+8>>2]=n,r):(i=v[5208],e=0|da[v[v[i>>2]+12>>2]](i,e+1|0,8524,49),v[r+8>>2]=e,f[e+v[r+4>>2]|0]=0,Ff(v[r+8>>2],n,v[r+4>>2]),r)):(v[r+4>>2]=0,v[r+8>>2]=0,r)}function Te(r,n,e){var i=0,f=0,t=0;if(!e)return 0;r:if(i=l[0|r]){for(;;){if(!((0|(f=l[0|n]))!=(0|i)|!f)&&(e=e-1|0)){if(n=n+1|0,i=l[r+1|0],r=r+1|0,i)continue;break r}break}t=i}return(255&t)-l[0|n]|0}function $e(r,n,e,i){if(r|=0,n|=0,e=w(e),i|=0,p[v[r+16>>2]+(n<<2)>>2]=e,r=v[r+32>>2]+(n<<4)|0,v[r+4>>2]=0,n=0,ca(r,v[i+4>>2]+v[r+4>>2]|0),v[i+4>>2])for(;Sn(r,v[i+12>>2]+(n<<2)|0),(n=n+1|0)>>>0<d[i+4>>2];);}function Ie(r,n,e,i){var f=w(0),t=w(0),u=w(0),o=0,a=w(0);n=v[n+12>>2],t=p[n+4>>2],e=v[e+12>>2]+(i<<2)|0,u=p[n>>2],f=Pr(w(p[n+12>>2]-t),w(p[n+8>>2]-u)),p[e+8>>2]=f,o=e,a=w(t+w(r*Wr(f))),p[o+4>>2]=a,o=e,a=w(u+w(r*Rr(f))),p[o>>2]=a}function Ce(r){var n=0;r:{n:if(n=In(r,4352)){if(r=v[n+16>>2])return!ri(r,4979);r=1;e:switch(v[n+8>>2]){case 0:case 2:return 0;case 1:break r;case 3:break e;default:break n}return p[n+24>>2]!=w(0)}r=0}return r}function Pe(r,n,e){var i=0,f=0,t=0,u=0,o=0;if(i=(v[r+4>>2]/(0|e)|0)-2|0){for(o=v[r+12>>2],r=0,f=i;f=(r=(u=p[(m(f=1+(t=f>>>1|0)|0,e)<<2)+o>>2]<=n)?f:r)+(i=u?i:t)|0,(0|r)!=(0|i););e=m(r+1|0,e)}return e}function Ee(r){var n,e=0;return v[64+(r|=0)>>2]=9772,v[r>>2]=9748,lf(r+168|0),lo(r+152|0),lo(r+136|0),vo(r+120|0),vo(r+104|0),v[r+64>>2]=8664,(n=v[r+72>>2])&&(e=v[r+68>>2])&&da[0|n](e),0|tt(r)}function Oe(r,n,e,i,f,t,u,o,a,c){r|=0,n|=0,e=w(e),i=w(i),f=w(f),t=w(t),u=w(u),o=w(o),a=w(a),c=w(c),r=v[r+32>>2]+(n<<5)|0,p[r>>2]=e,p[r+4>>2]=i,p[r+8>>2]=f,p[r+12>>2]=t,p[r+16>>2]=u,p[r+20>>2]=o,p[r+24>>2]=a,p[r+28>>2]=c}function Re(r,n){var e=0;return v[r>>2]=10760,v[n+8>>2]?(v[r+4>>2]=v[n+4>>2],e=v[5208],e=0|da[v[v[e>>2]+12>>2]](e,v[n+4>>2]+1|0,8524,67),v[r+8>>2]=e,Ff(e,v[n+8>>2],v[n+4>>2]+1|0),r):(v[r+4>>2]=0,v[r+8>>2]=0,r)}function Se(r,n,e,i){var f,t,u,o,a=0,c=0;return o=m(a=e>>>16|0,c=r>>>16|0),a=(65535&(c=((u=m(f=65535&e,t=65535&r))>>>16|0)+m(c,f)|0))+m(a,t)|0,V=(m(n,e)+o|0)+m(r,i)+(c>>>16)+(a>>>16)|0,65535&u|a<<16}function We(r,n){var e,i=0,f=0,t=0,u=0;if(!(i=v[r+4>>2]-2|0))return 1;for(e=v[r+12>>2],r=0,f=i;f=(r=(u=p[((f=1+(t=f>>>1|0)|0)<<2)+e>>2]<=n)?f:r)+(i=u?i:t)|0,(0|r)!=(0|i););return r+1|0}function Ge(r){var n,e,i,f,t;return v[(r|=0)>>2]=10152,Pn(n=r+8|0),Pn(e=r+24|0),Pn(i=r+56|0),Pn(f=r+72|0),Pn(t=r+88|0),so(r+120|0),Cu(r+104|0),Pu(t),Eu(f),Ou(i),Du(r+40|0),Du(e),so(n),0|r}function Ue(r,n){var e,i;return D=e=D-16|0,r=si(r,n),v[r+20>>2]=8712,v[r>>2]=10916,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,ca(i=r+20|0,n=m(n,3)),v[e+12>>2]=0,yn(i,n,e+12|0),D=e+16|0,r}function je(r,n,e,i){n|=0,e|=0,i|=0;var f,t=0;return D=f=D-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=Ae(f+4|0,i+4|0,v[i>>2],0),r=0|da[0|r](t,e,n),lf(n),D=f+16|0,0|r}function He(r,n){var e=0;if(v[r>>2]=0,v[r+4>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+16>>2]=0,v[r+20>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,n){for(;n=(e=n)+1|0,(l[0|e]-1&255)>>>0<32;);tr(r,e)}return r}function Le(r,n){var e,i;return D=e=D-16|0,r=si(r,n),v[r+20>>2]=8712,v[r>>2]=10964,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,ca(i=r+20|0,n<<=3),v[e+12>>2]=0,yn(i,n,e+12|0),D=e+16|0,r}function Me(r,n){var e,i=0,f=0;if(D=e=D-16|0,d[r+44>>2]<=n>>>0)for(i=r+40|0;v[e+12>>2]=0,Sn(i,e+12|0),d[r+44>>2]<=n>>>0;);else f=v[v[r+52>>2]+(n<<2)>>2];return D=e+16|0,f}function _e(r,n,e){var i;if(i=In(r,4917)){if(v[i+8>>2]==v[2414]&&!ri(v[i+16>>2],6270))return void ct(n,e);_r(n,e,Pt(r,4917,w(0)),Pt(r,6932,w(0)),Pt(r,6914,w(1)),Pt(r,6902,w(1)))}}function ze(r,n,e){n|=0,e|=0;var i,f=0;return D=i=D-16|0,n=((f=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&f?v[v[n>>2]+r>>2]:r,f=n,n=Ae(i+4|0,e+4|0,v[e>>2],0),r=0|da[0|r](f,n),lf(n),D=i+16|0,0|r}function xe(r,n){n|=0;var e=0,i=0;r:{n:if(v[88+(r|=0)>>2]){for(;;){if(!qi(Jo(i=v[v[r+96>>2]+(e<<2)>>2]),n)){if((e=e+1|0)>>>0<d[r+88>>2])continue;break n}break}break r}i=0}return 0|i}function Je(r){var n,e,i=0;if(i=v[72+(r|=0)>>2],e=l[i+28|0],f[i+28|0]=1,n=v[r+44>>2]){for(i=0;qn(r,i),(0|n)!=(0|(i=i+1|0)););i=v[r+72>>2]}v[r+44>>2]=0,f[i+28|0]=e,pr(i)}function Ke(r){var n;n=v[4+(r|=0)>>2],p[r+32>>2]=p[n+28>>2],p[r+36>>2]=p[n+32>>2],p[r+40>>2]=p[n+36>>2],p[r+44>>2]=p[n+40>>2],p[r+48>>2]=p[n+44>>2],p[r+52>>2]=p[n+48>>2],p[r+56>>2]=p[n+52>>2]}function Be(r,n){return v[r+8>>2]=0,v[r+12>>2]=0,v[r+4>>2]=8936,v[r>>2]=8920,Re(r+16|0,n),ht(r+28|0),v[r+64>>2]=0,v[r+56>>2]=1,v[r+60>>2]=0,v[r+48>>2]=1,v[r+52>>2]=1,v[r+40>>2]=6,v[r+44>>2]=1,r}function Ne(r,n,e,i){n|=0,e|=0,i|=0;var f,t=0;D=f=D-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=Ae(f+4|0,i+4|0,v[i>>2],0),da[0|r](t,e,n),lf(n),D=f+16|0}function qe(){var r,n=0;D=r=D-16|0,l[20608]||(v[5149]=0,v[5150]=0,v[5148]=1048,v[5151]=0,f[20608]=1),l[20660]||(Kr(20612,n=gu(r+4|0,6864,0),20592,w(0)),lf(n),f[20660]=1),D=r+16|0}function De(r){var n,e=0;return v[4+(r|=0)>>2]=8584,v[r>>2]=8568,vo(r+148|0),oo(r+132|0),Qu(r+116|0),v[r+4>>2]=8664,(n=v[r+12>>2])&&(e=v[r+8>>2])&&da[0|n](e),0|r}function Ve(r,n){return r=rt(r,n),v[r+24>>2]=9612,v[r>>2]=9592,v[r+52>>2]=1065353216,v[r+56>>2]=0,f[r+50|0]=0,s[r+48>>1]=0,v[r+44>>2]=1,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,v[r+40>>2]=0,r}function Ze(r,n,e,i){var f;return D=f=D-16|0,r=si(r,n),v[r+20>>2]=8712,v[r>>2]=i,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,v[f+12>>2]=0,yn(r+20|0,m(n,e),f+12|0),D=f+16|0,r}function Ye(r,n){n|=0;var e,i=0;if(e=v[24+(r|=0)>>2])for(;;){if(qi(v[v[r+32>>2]+(i<<2)>>2]+8|0,n))return v[v[r+32>>2]+(i<<2)>>2];if((0|e)==(0|(i=i+1|0)))break}return 0}function Xe(r,n){var e;return D=e=D-16|0,r=si(r,n),v[r+24>>2]=8712,v[r+20>>2]=0,v[r>>2]=9280,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[e+12>>2]=0,yn(r+24|0,m(n,5),e+12|0),D=e+16|0,r}function Qe(r,n){var e;return D=e=D-16|0,r=si(r,n),v[r+24>>2]=8712,v[r+20>>2]=0,v[r>>2]=10068,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[e+12>>2]=0,yn(r+24|0,n<<1,e+12|0),D=e+16|0,r}function ri(r,n){var e=0,i=0;r:if(!(!(e=l[0|r])|(0|(i=l[0|n]))!=(0|e)))for(;;){if(i=l[n+1|0],!(e=l[r+1|0]))break r;if(n=n+1|0,r=r+1|0,(0|e)!=(0|i))break}return e-i|0}function ni(r,n){var e;return D=e=D-16|0,r=si(r,n),v[r+20>>2]=8712,v[r>>2]=9932,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,v[e+12>>2]=0,yn(r+20|0,n<<1,e+12|0),D=e+16|0,r}function ei(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f=w(f),t|=0,u|=0,o|=0,r=v[r+32>>2]+m(n,24)|0,p[r>>2]=e,p[r+4>>2]=i,p[r+8>>2]=f,p[r+12>>2]=0|t,p[r+16>>2]=u>>>0,p[r+20>>2]=o>>>0}function ii(r,n,e){n|=0,e|=0;var i,f=0;D=i=D-16|0,n=((f=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&f?v[v[n>>2]+r>>2]:r,f=n,n=Ae(i+4|0,e+4|0,v[e>>2],0),da[0|r](f,n),lf(n),D=i+16|0}function fi(r,n){var e=0,i=0;r:if(v[r+4>>2]){for(;;){if(i=v[v[r+12>>2]+(e<<2)>>2],!qi(Bo(v[i+4>>2]),n)){if((e=e+1|0)>>>0<d[r+4>>2])continue;break r}break}return i}return 0}function ti(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),e=0|da[0|e](n),n=Y((r=v[e+4>>2])+4|0),v[n>>2]=r,Ff(n+4|0,v[e+8>>2],r),0|n}function ui(r){var n,e=0,i=0;if(n=v[r+4>>2])for(;e=v[v[r+12>>2]+(i<<2)>>2],l[e+117|0]&&(l[e+116|0]&&ui(e+16|0),f[e+116|0]=0),(0|n)!=(0|(i=i+1|0)););}function oi(r,n,e){return v[r+36>>2]=0,v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=8984,v[r+16>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[r+4>>2]=8968,v[r>>2]=8840,fr(r,n,e,8524,0),r}function ai(r,n,e,i){var f;return r|=0,n|=0,e|=0,i|=0,D=f=D-32|0,v[f+28>>2]=n,n=Ae(f+16|0,e+4|0,v[e>>2],0),v[f+12>>2]=i,r=0|da[0|r](f+28|0,n,f+12|0),lf(n),D=f+32|0,0|r}function ci(r){r|=0;var n=0,e=w(0);return n=r,e=hu(p[r+4>>2]),p[n+4>>2]=e,n=r,e=hu(p[r+8>>2]),p[n+8>>2]=e,n=r,e=hu(p[r+12>>2]),p[n+12>>2]=e,n=r,e=hu(p[r+16>>2]),p[n+16>>2]=e,0|r}function bi(r){var n,e=0;return v[(r|=0)>>2]=10248,Pn(n=r+8|0),v[r+12>>2]=0,l[r+40|0]&&(e=v[r+4>>2])&&da[v[v[e>>2]+4>>2]](e),lf(r+24|0),Zu(n),0|r}function ki(r,n){var e=0,i=0;r:if(v[r+4>>2]){for(;;){if(!qi(Bo(i=v[v[r+12>>2]+(e<<2)>>2]),n)){if((e=e+1|0)>>>0<d[r+4>>2])continue;break r}break}return i}return 0}function si(r,n){var e;return D=e=D-16|0,r=Eo(r),v[r+4>>2]=8712,v[r>>2]=9344,v[r+16>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[e+12>>2]=0,yn(r+4|0,m(n,19)-19|0,e+12|0),D=e+16|0,r}function vi(r,n){var e=0,i=0;r:if(v[r+4>>2]){for(;;){if(!qi(No(i=v[v[r+12>>2]+(e<<2)>>2]),n)){if((e=e+1|0)>>>0<d[r+4>>2])continue;break r}break}return i}return 0}function li(r,n,e,i,f,t,u,o){r|=0,n|=0,e|=0,i=w(i),f=w(f),t=w(t),u=w(u),o=w(o);var a=0;a=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(a=v[a+v[n>>2]>>2]),da[0|a](n,e,i,f,t,u,o)}function hi(r,n,e,i){var f,t;n|=0,e|=0,i|=0,D=f=D-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,v[f+12>>2]=i,da[0|r](n,e,f+12|0),D=f+16|0}function di(r){var n,e;return(r=(n=v[5146])+(e=r+7&-8)|0)>>>0<=n>>>0&&e||r>>>0>pa()<<16>>>0&&!(0|z(0|r))?(v[5373]=48,-1):(v[5146]=r,n)}function pi(r,n){var e,i=0,f=0;if(D=e=D-16|0,i=v[n+20>>2])for(;f=v[r+72>>2],ut(e,3,i,0),qr(f+4|0,e),i=v[i+20>>2];);v[n+20>>2]=0,D=e+16|0}function yi(r){var n=0,e=0,i=0,f=0,t=0;if(n=10,i=1,r)for(;i=Se((e=1&r)?n:1,e?f:0,i,t),t=V,e=r>>>0>1,n=Se(n,f,n,f),f=V,r=r>>>1|0,e;);return V=t,i}function mi(r){var n=0,e=0;return(n=Wn(r,1))?(e=v[5208],e=0|da[v[v[e>>2]+8>>2]](e,n,8524,373),n=n-1|0,e=Ff(e,v[r+4>>2],n),v[r+4>>2]=n+v[r+4>>2],f[n+e|0]=0,e):0}function wi(r,n){n|=0;var e=0;e=0;r:if(r=v[24+(r|=0)>>2]){for(;;){if(e=r,v[r+4>>2]==(0|n))break r;if(!(r=v[r+12>>2]))break}e=0}return 0|!!(0|e)}function gi(r,n){var e;n|=0,!(e=v[224+(r|=0)>>2])|v[e+64>>2]!=v[n+4>>2]||(v[r+224>>2]=0,v[r+228>>2]=0,v[r+196>>2]=0,v[r+164>>2]=0,v[r+180>>2]=0,v[r+132>>2]=0)}function Fi(r,n,e){n|=0,e|=0;var i=0;if(!(i=v[136+(r|=0)>>2])||!(i=gt(i,n,e))){if(!v[v[r+4>>2]+64>>2])return 0;i=gt(v[v[r+4>>2]+64>>2],n,e)}return 0|i}function Ai(r,n){var e=0,i=0;(0|(e=v[r+8>>2]))!=(0|n)&&(e&&(i=v[5208],da[v[v[i>>2]+20>>2]](i,e,8524,86)),n?e=Qn(n):(n=0,e=0),v[r+8>>2]=n,v[r+4>>2]=e)}function Ti(r,n){var e,i;return n|=0,D=e=D-16|0,i=e+4|0,da[v[(r|=0)>>2]](i,n),n=Y((r=v[e+8>>2])+4|0),v[n>>2]=r,Ff(n+4|0,v[e+12>>2],r),lf(i),D=e+16|0,0|n}function $i(r){var n,e=0;return v[(r|=0)>>2]=10600,Pn(n=r+8|0),l[r+28|0]&&(e=v[r+4>>2])&&da[v[v[e>>2]+4>>2]](e),lf(r+32|0),Zu(n),0|r}function Ii(r,n){var e=0;return(e=v[r+8>>2])?(n=e-1|0,e=v[v[r+16>>2]+(n<<2)>>2],v[r+8>>2]=n,e):(r=Xf(16),v[r+12>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=n,r)}function Ci(r,n){var e,i=0;if(e=v[r+4>>2])for(;;){if(qi(Bo(v[v[v[r+12>>2]+(i<<2)>>2]+4>>2]),n))return i;if((0|e)==(0|(i=i+1|0)))break}return-1}function Pi(r,n,e,i){var f;r|=0,n|=0,e|=0,i=w(i),D=f=D-16|0,n=xe(v[r+4>>2],n),e=xe(v[r+4>>2],e),p[f+12>>2]=i,Ot(f,n,e),$n(r+12|0,f,f+12|0),D=f+16|0}function Ei(r,n){var e=0;if(v[r>>2]=n,n=v[r+4>>2])for(;e=v[n+20>>2],da[v[v[n>>2]+4>>2]](n),n=e;);return v[r+4>>2]=0,v[r+8>>2]=0,r}function Oi(r,n){var e=0;if(v[r>>2]=n,n=v[r+4>>2])for(;e=v[n+12>>2],da[v[v[n>>2]+4>>2]](n),n=e;);return v[r+4>>2]=0,v[r+8>>2]=0,r}function Ri(r,n,e,i,f,t,u){r|=0,n|=0,e=w(e),i=w(i),f=w(f),t=w(t),u=w(u),r=v[r+36>>2]+m(n,20)|0,p[r>>2]=e,p[r+16>>2]=u,p[r+12>>2]=t,p[r+8>>2]=f,p[r+4>>2]=i}function Si(r,n,e,i,f,t,u){r|=0,n|=0,e=w(e),i=w(i),f=w(f),t=w(t),u=w(u),r=v[r+32>>2]+m(n,20)|0,p[r>>2]=e,p[r+16>>2]=u,p[r+12>>2]=t,p[r+8>>2]=f,p[r+4>>2]=i}function Wi(r){var n,e;return v[(r|=0)>>2]=10944,Pn(n=r+4|0),Pn(e=r+20|0),nt(r+104|0),et(r+84|0),Qu(r+68|0),ju(r+52|0),Qu(r+36|0),Xu(e),Yu(n),0|r}function Gi(r){var n,e;return w((e=(n=r*r)*r)*n*n*(2718311493989822e-21*n-.00019839334836096632)+(e*(.008333329385889463*n-.16666666641626524)+r))}function Ui(r,n,e){var i=0;for(ie(r,n,e),r=v[e+4>>2],n=v[e>>2];;){if((0|r)==(0|n))return;if(i=l[0|n],n=n+1|0,58==(0|i))break}v[e>>2]=n,Kn(e)}function ji(r,n){var e,i=0;if(e=v[r+4>>2])for(;;){if(qi(Bo(v[v[r+12>>2]+(i<<2)>>2]),n))return i;if((0|e)==(0|(i=i+1|0)))break}return-1}function Hi(r){return w(w(w(w(r*w(w(r*w(-.008656363002955914))+w(-.04274342209100723)))+w(.16666586697101593))*r)/w(w(r*w(-.7066296339035034))+w(1)))}function Li(r){var n=0,e=0;return n=0,(e=r>>>23&255)>>>0<127||(n=2,e>>>0>150||(n=0,(e=1<<150-e)-1&r||(n=r&e?1:2))),n}function Mi(r,n,e){var i=0,f=0,t=0;d[r+8>>2]<n>>>0&&(v[r+8>>2]=n,i=v[5208],f=r,t=0|da[v[v[i>>2]+16>>2]](i,v[r+12>>2],n<<e,8524,97),v[f+12>>2]=t)}function _i(r,n){var e=0;if(ca(r,v[n+4>>2]+v[r+4>>2]|0),v[n+4>>2])for(;Rn(r,v[n+12>>2]+(e<<2)|0),(e=e+1|0)>>>0<d[n+4>>2];);}function zi(r,n){var e=0;if(ca(r,v[n+4>>2]+v[r+4>>2]|0),v[n+4>>2])for(;On(r,v[n+12>>2]+(e<<2)|0),(e=e+1|0)>>>0<d[n+4>>2];);}function xi(r,n){return r=Fe(r,n),v[r+64>>2]=8712,v[r>>2]=9804,v[r+68>>2]=0,v[r+72>>2]=0,s[r+74>>1]=0,s[r+76>>1]=0,s[r+78>>1]=0,s[r+80>>1]=0,r}function Ji(r,n){var e=0,i=0,f=0;d[r+8>>2]<n>>>0&&(v[r+8>>2]=n,e=v[5208],i=r,f=0|da[v[v[e>>2]+16>>2]](e,v[r+12>>2],n,8524,97),v[i+12>>2]=f)}function Ki(r,n){var e,i=0;D=e=D-16|0,l[n+116|0]||((i=v[n+12>>2])&&Ki(r,i),f[n+116|0]=1,v[e+12>>2]=n,Sn(r+104|0,e+12|0)),D=e+16|0}function Bi(r){var n;return w((r*=r)*(n=r*r)*(2439044879627741e-20*r-.001388676377460993)+.04166662332373906*n+-.499999997251031*r+1)}function Ni(r,n,e,i,f){r|=0,n|=0,e|=0,i=w(i),f=w(f);var t=0;t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),da[0|t](n,e,i,f)}function qi(r,n){var e,i;return(0|(e=v[r+8>>2]))==(0|(i=v[n+8>>2]))?1:!(r=!i|!e|v[r+4>>2]!=v[n+4>>2]?1:ri(e,i))}function Di(r,n){var e,i=0;for(i=Qn(r)+1|0,e=255&n;n=0,i&&(0|e)!=l[0|(n=(i=i-1|0)+r|0)];);return n}function Vi(r,n,e){return v[r+12>>2]=0,v[r+16>>2]=0,p[r+8>>2]=n,v[r+4>>2]=e,v[r>>2]=9460,ht(r+20|0),v[r+32>>2]=1065353216,v[r+36>>2]=0,r}function Zi(r,n,e,i,f){p[i>>2]=p[r+100>>2]+w(w(n*p[r+92>>2])+w(p[r+96>>2]*e)),p[f>>2]=p[r+112>>2]+w(w(n*p[r+104>>2])+w(p[r+108>>2]*e))}function Yi(r,n){r|=0,v[24+(n|=0)>>2]=0,zi(n+20|0,r+20|0),v[n+40>>2]=0,_i(n+36|0,r+36|0),v[n+52>>2]=v[r+52>>2],v[n+56>>2]=v[r+56>>2]}function Xi(r,n,e){var i;r|=0,n=w(n),e=w(e),D=i=D-16|0,p[i+12>>2]=n,p[i+8>>2]=e,da[v[v[r>>2]+16>>2]](r,i+12|0,i+8|0),D=i+16|0}function Qi(r,n,e){var i;return r|=0,n=w(n),e=w(e),D=i=D-16|0,p[i+12>>2]=n,p[i+8>>2]=e,r=0|da[0|r](i+12|0,i+8|0),D=i+16|0,0|r}function rf(r,n){return v[r>>2]=9476,Re(r+4|0,n),v[r+16>>2]=0,v[r+20>>2]=0,ht(r+24|0),ht(r+36|0),v[r+48>>2]=1065353216,v[r+52>>2]=0,r}function nf(r,n,e){r|=0,n|=0,e=w(e);var i=0;return i=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),w(w(da[0|i](n,e)))}function ef(r,n,e){n|=0,e|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),v[da[0|i](n,e)>>2]}function ff(r,n,e){n|=0,e|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),w(w(da[0|i](n,e)))}function tf(r,n,e,i){n|=0,e|=0,i|=0;var f=0;f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),da[0|f](n,e,i)}function uf(r){var n,e=0;return v[(r|=0)>>2]=11112,e=v[r+4>>2],(!(n=v[e>>2])||(or(n),e=v[r+4>>2]))&&or(e),0|r}function of(r,n,e){n|=0,e|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),0|da[0|i](n,e)}function af(r,n){r|=0,n|=0;var e=w(0);v[r+60>>2]!=(0|n)&&(v[r+60>>2]=n,e=p[v[r+12>>2]+160>>2],v[r+72>>2]=0,p[r+64>>2]=e)}function cf(r,n,e){n|=0,e|=0,r=v[12+(r|=0)>>2]+(n<<4)|0,n=v[e+8>>2],v[r+4>>2]=v[e+4>>2],v[r+8>>2]=n,v[r+12>>2]=v[e+12>>2]}function bf(r){v[224+(r|=0)>>2]&&(v[r+224>>2]=0,v[r+228>>2]=0,v[r+196>>2]=0,v[r+164>>2]=0,v[r+180>>2]=0,v[r+132>>2]=0)}function kf(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),w(w(da[0|e](n)))}function sf(r,n,e){var i;r|=0,n|=0,D=i=D-16|0,e=Ae(i+4|0,4+(e|=0)|0,v[e>>2],0),ee(v[r>>2]+n|0,e),lf(e),D=i+16|0}function vf(r,n){var e;return r|=0,D=e=D-16|0,n=Ae(e+4|0,4+(n|=0)|0,v[n>>2],0),r=0|da[0|r](n),lf(n),D=e+16|0,0|r}function lf(r){var n,e=0;return v[(r|=0)>>2]=10760,(n=v[r+8>>2])&&(e=v[5208],da[v[v[e>>2]+20>>2]](e,n,8524,187)),0|r}function hf(r,n,e,i){var f;n|=0,e|=0,i|=0,D=f=D-16|0,r=v[(r|=0)>>2],v[f+12>>2]=i,da[0|r](n,e,f+12|0),D=f+16|0}function df(r,n,e){r|=0,n|=0,e=w(e);var i=0;i=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),da[0|i](n,e)}function pf(r,n){var e=0;return v[r+4>>2]=0,v[r>>2]=n,(n=v[r+12>>2])&&(e=v[5208],da[v[v[e>>2]+20>>2]](e,n,8524,215)),r}function yf(r,n,e){n|=0,e|=0;var i=0;i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),da[0|i](n,e)}function mf(r,n){var e;return n|=0,e=v[(r|=0)>>2]+n|0,n=Y((r=v[e+4>>2])+4|0),v[n>>2]=r,Ff(n+4|0,v[e+8>>2],r),0|n}function wf(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),0|da[0|e](n)}function gf(r,n,e){var i;return r|=0,n=w(n),e|=0,D=i=D-16|0,p[i+12>>2]=n,r=0|da[0|r](i+12|0,e),D=i+16|0,0|r}function Ff(r,n,e){var i=0;if(e)for(i=r;f[0|i]=l[0|n],i=i+1|0,n=n+1|0,e=e-1|0;);return r}function Af(r,n,e,i,f){r|=0,n|=0,e=w(e),i=w(i),f=w(f),r=v[r+32>>2]+m(n,12)|0,p[r>>2]=e,p[r+8>>2]=f,p[r+4>>2]=i}function Tf(r,n){var e=0;for(n=v[n+4>>2];(e=ri(v[r+4>>2],n))&&(r=v[r+8>>2]););return!e}function $f(r,n){return qi(v[r+4>>2]+36|0,v[n+4>>2]+36|0)?qi(v[r+8>>2]+36|0,v[n+8>>2]+36|0):0}function If(r,n){n|=0;var e=0;e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),da[0|e](n)}function Cf(r,n,e,i,t,u){return v[r+4>>2]=n,v[r>>2]=9676,Re(r+8|0,e),v[r+20>>2]=i,Re(r+24|0,t),f[r+36|0]=u,r}function Pf(r,n){var e;return r|=0,n|=0,D=e=D-16|0,v[e+12>>2]=n,r=0|da[0|r](e+12|0),D=e+16|0,0|r}function Ef(r,n){return v[r+8>>2]=0,v[r+4>>2]=n,v[r+16>>2]=0,v[r+20>>2]=0,v[r+12>>2]=8808,v[r>>2]=8776,r}function Of(r){cr(r|=0,p[r+32>>2],p[r+36>>2],p[r+40>>2],p[r+44>>2],p[r+48>>2],p[r+52>>2],p[r+56>>2])}function Rf(r){var n,e;return e=Tt(12),n=v[r+4>>2],te(e,v[n+8>>2],v[n+4>>2],v[n+12>>2],v[r+8>>2]),e}function Sf(r){r&&(v[r+16>>2]=v[r+16>>2]-1,v[r+16>>2]||da[v[v[r>>2]+4>>2]](r))}function Wf(r,n,e,i){r|=0,n|=0,e=w(e),i|=0,p[v[r+20>>2]+(n<<2)>>2]=e,ee(v[r+36>>2]+m(n,12)|0,i)}function Gf(r,n,e,i,f,t,u){t|=0,u|=0,kr(r|=0,n|=0,e|=0,i|=0,v[12+(f|=0)>>2],t,u)}function Uf(r){var n;return v[(r|=0)>>2]=1032,Pn(n=r+4|0),lf(r+36|0),no(r+20|0),fo(n),0|r}function jf(r,n){var e;D=e=D-16|0,ut(e,2,n,0),qr(r+4|0,e),f[v[r+20>>2]+88|0]=1,D=e+16|0}function Hf(r,n,e){n|=0,e|=0,p[(n<<=2)+v[16+(r|=0)>>2]>>2]=p[e+8>>2],v[n+v[r+32>>2]>>2]=e}function Lf(r,n,e,i){r|=0,n|=0,e=w(e),i=w(i),r=v[r+32>>2]+(n<<3)|0,p[r>>2]=e,p[r+4>>2]=i}function Mf(r,n,e,i){r|=0,n|=0,e=w(e),i=w(i),r=v[r+36>>2]+(n<<3)|0,p[r>>2]=e,p[r+4>>2]=i}function _f(r,n,e,i){p[v[r+36>>2]+(n<<2)>>2]=e,r=v[r+52>>2]+(n<<4)|0,v[r+4>>2]=0,_i(r,i)}function zf(r,n){return r=kt(r,n),v[r+28>>2]=0,v[r+20>>2]=0,v[r+24>>2]=0,v[r>>2]=9988,r}function xf(r){var n;return n=Xf(16),v[n+12>>2]=0,v[n+4>>2]=0,v[n+8>>2]=0,v[n>>2]=r,n}function Jf(r){var n;return v[(r|=0)>>2]=9492,Pn(n=r+20|0),ho(n),vo(r+4|0),0|r}function Kf(r,n){var e;e=Tt(4),v[e>>2]=n,$(21403,0|r,2,16184,11280,1668,0|e,0,0)}function Bf(r,n,e){if(e)for(;f[0|r]=n,r=r+1|0,e=e-1|0;);}function Nf(r,n){var e;e=Tt(4),v[e>>2]=n,$(1982,0|r,2,13504,11280,1624,0|e,0,0)}function qf(r,n){var e;e=Tt(4),v[e>>2]=n,$(1982,0|r,2,13512,11280,1625,0|e,0,0)}function Df(r,n){var e;e=Tt(4),v[e>>2]=n,$(1927,0|r,2,13656,11280,1628,0|e,0,0)}function Vf(r,n){var e;e=Tt(4),v[e>>2]=n,$(5335,0|r,3,14424,13176,1636,0|e,0,0)}function Zf(r,n){return r=rt(r,n),v[r+24>>2]=9612,v[r>>2]=10840,Bf(r+28|0,0,58),r}function Yf(r,n){return r=rt(r,n),v[r+24>>2]=9612,v[r>>2]=9864,Bf(r+28|0,0,48),r}function Xf(r){var n;return n=v[5208],0|da[v[v[n>>2]+12>>2]](n,r,8524,40)}function Qf(r){var n;return(-1>>>(n=31&r)&-2)<<n|(-1<<(r=0-r&31)&-2)>>>r}function rt(r,n){return v[r>>2]=9324,Re(r+4|0,n),f[r+20|0]=0,v[r+16>>2]=0,r}function nt(r){var n;return v[(r|=0)>>2]=10440,Pn(n=r+4|0),Xu(n),0|r}function et(r){var n;return v[(r|=0)>>2]=10424,Pn(n=r+4|0),Yu(n),0|r}function it(r){var n;return v[(r|=0)>>2]=8744,Pn(n=r+4|0),oo(n),0|r}function ft(r,n,e){return r|=0,n=w(n),e=w(e),p[r+4>>2]=e,p[r>>2]=n,0|r}function tt(r){return v[(r|=0)>>2]=11024,vo(r+36|0),Fu(r+20|0),0|jt(r)}function ut(r,n,e,i){v[r+12>>2]=i,v[r+8>>2]=e,v[r+4>>2]=n,v[r>>2]=8600}function ot(r,n,e){n|=0,e|=0,v[v[12+(r|=0)>>2]+(n<<2)>>2]=v[e>>2]}function at(r){var n;n=v[5208],da[v[v[n>>2]+20>>2]](n,r,8524,62)}function ct(r,n){n|=0,v[v[16+(r|=0)>>2]+m(n,76)>>2]=1065353216}function bt(r,n,e,i){n|=0,e|=0,i|=0,da[v[(r|=0)>>2]](n,e,i)}function kt(r,n){return v[r>>2]=9048,Re(r+4|0,n),v[r+16>>2]=0,r}function st(r){var n;return p[12+(n=D-16|0)>>2]=r,p[n+12>>2]}function vt(r,n){return v[r>>2]=9072,v[r+4>>2]=n,v[r>>2]=9e3,r}function lt(r){return v[4+(0|da[v[v[(r|=0)>>2]+8>>2]](r))>>2]}function ht(r){return v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=10760,r}function dt(r,n){return r=Fe(r,n),v[r+64>>2]=0,v[r>>2]=9252,r}function pt(r,n){return r=vt(r,n),v[r+8>>2]=n,v[r>>2]=11128,r}function yt(r,n,e,i){vr(16+(r|=0)|0,n|=0,e|=0,i|=0)}function mt(r){return v[(r|=0)>>2]=10916,vo(r+20|0),0|Ut(r)}function wt(r){return v[4+(0|da[v[v[(r|=0)>>2]>>2]](r))>>2]}function gt(r,n,e){return 0|Dn(16+(r|=0)|0,n|=0,e|=0)}function Ft(r){return v[(r|=0)>>2]=9932,vo(r+20|0),0|Ut(r)}function At(r,n,e){return n|=0,e|=0,0|da[0|(r|=0)](n,e)}function Tt(r){return(r=Y(r>>>0<=1?1:r))||(j(),c()),r}function $t(r,n,e){return(r=In(r,n))&&(e=v[r+20>>2]),e}function It(r,n,e){v[r+8>>2]=e,v[r+4>>2]=n,v[r>>2]=10012}function Ct(r,n,e){return(r=In(r,n))&&(e=v[r+16>>2]),e}function Pt(r,n,e){return(r=In(r,n))&&(e=p[r+24>>2]),e}function Et(r,n){return n|=0,v[12+(r|=0)>>2]+(n<<4)|0}function Ot(r,n,e){v[r+8>>2]=e,v[r+4>>2]=n,v[r>>2]=8792}function Rt(r,n,e){r|=0,n|=0,e=w(e),p[v[r>>2]+n>>2]=e}function St(r,n){return n|=0,v[12+(r|=0)>>2]+(n<<2)|0}function Wt(r,n){var e;e=ni(r,n),v[e>>2]=9960}function Gt(r,n){r|=0,n=w(n),p[r+160>>2]=p[r+160>>2]+n}function Ut(r){return v[(r|=0)>>2]=9344,vo(r+4|0),0|r}function jt(r){return v[(r|=0)>>2]=9048,lf(r+4|0),0|r}function Ht(r,n){v[r+8>>2]=0,v[r+4>>2]=n,v[r>>2]=10012}function Lt(r,n){return n|=0,w(p[v[(r|=0)>>2]+n>>2])}function Mt(r){return v[(r|=0)>>2]=9324,lf(r+4|0),0|r}function _t(r,n,e){n|=0,e|=0,v[v[(r|=0)>>2]+n>>2]=e}function zt(r){return v[(r|=0)>>2]=8616,ro(r+4|0),0|r}function xt(r,n){v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=n+20}function Jt(r,n,e){n|=0,e|=0,f[v[(r|=0)>>2]+n|0]=e}function Kt(r,n){return n|=0,0|da[v[(r|=0)>>2]](n)}function Bt(r){return r<w(0)?w(-1):r>w(0)?w(1):w(0)}function Nt(r){(r|=0)&&da[v[v[r>>2]+16>>2]](r)}function qt(r){return r?31-F(r-1^r)|0:32}function Dt(r,n){return n|=0,v[v[(r|=0)>>2]+n>>2]}function Vt(r,n){return 0|vi(100+(r|=0)|0,n|=0)}function Zt(r,n){return 0|vi(116+(r|=0)|0,n|=0)}function Yt(r,n){return 0|vi(132+(r|=0)|0,n|=0)}function Xt(r,n){return 0|ji(16+(r|=0)|0,n|=0)}function Qt(r,n){return 0|vi(68+(r|=0)|0,n|=0)}function ru(r,n){return 0|ki(16+(r|=0)|0,n|=0)}function nu(r,n){return n|=0,l[v[(r|=0)>>2]+n|0]}function eu(r,n){return 0|ji(32+(r|=0)|0,n|=0)}function iu(r,n){return 0|vi(48+(r|=0)|0,n|=0)}function fu(r,n){n|=0,Gr(r|=0,iu(v[r+4>>2],n))}function tu(r,n){return 0|fi(24+(r|=0)|0,n|=0)}function uu(r){(r|=0)&&da[v[v[r>>2]+4>>2]](r)}function ou(r,n){return 0|ki(32+(r|=0)|0,n|=0)}function au(r){return 16777216+(r<<1)>>>0<16777217}function cu(r,n){return r=Ue(r,n),v[r>>2]=10096,r}function bu(r,n){return r=Ue(r,n),v[r>>2]=10124,r}function ku(r,n){return 0|fi(8+(r|=0)|0,n|=0)}function su(r,n){return r=Fe(r,n),v[r>>2]=9228,r}function vu(r,n,e,i){_(0|r,0|n,8,0,0|e,-1,0|i)}function lu(r,n){return!ri(v[r+4>>2],v[n+4>>2])}function hu(r){return r<w(0)?w(0):r>w(1)?w(1):r}function du(r,n){return mr(r,n,10,-2147483648)}function pu(r){return v[v[4+(r|=0)>>2]+16>>2]}function yu(r){return 0!=v[224+(r|=0)>>2]|0}function mu(r){v[r+4>>2]=v[5498],v[5498]=r}function wu(r,n){return w(st(r?w(-n):n)*n)}function gu(r,n,e){return Ae(r,n,Qn(n),e)}function Fu(r){return 0|pf(r|=0,10632)}function Au(r){return 0|ve(r|=0,12928)}function Tu(r){return 0|Oi(r|=0,11208)}function $u(r){return 0|pf(r|=0,11192)}function Iu(r){return 0|pf(r|=0,11176)}function Cu(r){return 0|pf(r|=0,10232)}function Pu(r){return 0|pf(r|=0,10216)}function Eu(r){return 0|pf(r|=0,10200)}function Ou(r){return 0|pf(r|=0,10184)}function Ru(r){return 0|pf(r|=0,12864)}function Su(r){return 0|Ei(r|=0,16896)}function Wu(r){return 0|pf(r|=0,10328)}function Gu(r){return w(p[60+(r|=0)>>2])}function Uu(r,n){n|=0,v[36+(r|=0)>>2]=n}function ju(r){return 0|pf(r|=0,10408)}function Hu(r){return 0|pf(r|=0,10584)}function Lu(r){return 0|pf(r|=0,10568)}function Mu(r){return 0|pf(r|=0,10552)}function _u(r){return 0|pf(r|=0,10536)}function zu(r,n){n|=0,v[28+(r|=0)>>2]=n}function xu(r){return 0|pf(r|=0,10520)}function Ju(r){return 0|pf(r|=0,10504)}function Ku(r){return 0|pf(r|=0,10344)}function Bu(r){return 0|pf(r|=0,10488)}function Nu(r){return 0|pf(r|=0,10472)}function qu(r){return 0|ve(r|=0,10680)}function Du(r){return 0|pf(r|=0,10168)}function Vu(r){return 0|pf(r|=0,10712)}function Zu(r){return 0|pf(r|=0,10280)}function Yu(r){return 0|pf(r|=0,10376)}function Xu(r){return 0|pf(r|=0,10392)}function Qu(r){return 0|pf(r|=0,8680)}function ro(r){return 0|ve(r|=0,8728)}function no(r){return 0|Oi(r|=0,8536)}function eo(r){return 32==(0|r)|r-9>>>0<5}function io(r){return 0|pf(r|=0,8984)}function fo(r){return 0|pf(r|=0,1048)}function to(r){return 0|pf(r|=0,8968)}function uo(r){return 0|Ei(r|=0,8808)}function oo(r){return 0|pf(r|=0,8696)}function ao(r){return 0|ve(r|=0,9400)}function co(r){return 0|ve(r|=0,9444)}function bo(r){return 0|pf(r|=0,9612)}function ko(r,n){n|=0,f[44+(r|=0)|0]=n}function so(r){return 0|pf(r|=0,9196)}function vo(r){return 0|pf(r|=0,8712)}function lo(r){return 0|pf(r|=0,9788)}function ho(r){return 0|pf(r|=0,8760)}function po(r){return r-65>>>0<26?32|r:r}function yo(r){return 0|da[0|(r|=0)]()}function mo(r,n){return Ze(r,n,5,10864)}function wo(r,n){return Ze(r,n,3,9888)}function go(r){return v[60+(r|=0)>>2]}function Fo(r){v[r+16>>2]=v[r+16>>2]+1}function Ao(r){return v[12+(r|=0)>>2]}function To(r){return v[24+(r|=0)>>2]}function $o(r){return v[20+(r|=0)>>2]}function Io(r){return v[16+(r|=0)>>2]}function Co(r,n){return Ze(r,n,6,9632)}function Po(r,n){return Mn(r,n,10328)}function Eo(r){return v[r>>2]=10776,r}function Oo(r){return v[r>>2]=10992,r}function Ro(r,n){return Mn(r,n,12864)}function So(r){return l[44+(r|=0)|0]}function Wo(r){return v[8+(r|=0)>>2]}function Go(r){return v[4+(r|=0)>>2]}function Uo(r){return(r|=0)- -64|0}function jo(r){return 104+(r|=0)|0}function Ho(r){return 100+(r|=0)|0}function Lo(r){return 68+(r|=0)|0}function Mo(r){return 20+(r|=0)|0}function _o(r){return 16+(r|=0)|0}function zo(r){return 40+(r|=0)|0}function xo(r){return 84+(r|=0)|0}function Jo(r){return 36+(r|=0)|0}function Ko(r){return 24+(r|=0)|0}function Bo(r){return 8+(r|=0)|0}function No(r){return 4+(r|=0)|0}function qo(r){(r|=0)&&or(r)}function Do(r){Sr(r|=0),ue(r)}function Vo(r){return Ii(r,8680)}function Zo(r){return Ii(r,8712)}function Yo(r){return 0|r}function Xo(r){at(tt(r|=0))}function Qo(r){at(mt(r|=0))}function ra(r){return r+204|0}function na(r){return r+140|0}function ea(r,n){v[r+16>>2]=n}function ia(r){return r+144|0}function fa(r){return r+44|0}function ta(){}function ua(r,n){f[r+20|0]=n}function oa(r){f[r+64|0]=1}function aa(r,n){Mi(r,n,4)}function ca(r,n){Mi(r,n,2)}function ba(r,n){Mi(r,n,1)}function ka(r){at(r|=0)}function sa(r){or(r|=0)}function va(){c()}function la(){}var ha,da=(ha=[null,Uf,function(r){Uf(r|=0),at(r)},fo,function(r){at(fo(r|=0))},no,function(r){at(no(r|=0))},Yo,ka,function(){},function(){fo(20592)},function(){Uf(20612)},De,function(r){De(r|=0),at(r)},function(r){return 0|De((r|=0)-4|0)},function(r){De(r=(r|=0)-4|0),at(r)},ka,zt,function(r){zt(r|=0),at(r)},Xr,function(r){Xr(r|=0),at(r)},function(r){return 0|Xr((r|=0)-4|0)},function(r){Xr(r=(r|=0)-4|0),at(r)},function(r){var n,e=0;return v[(r|=0)>>2]=8664,(n=v[r+8>>2])&&(e=v[r+4>>2])&&da[0|n](e),0|r},function(r){var n,e=0;v[(r|=0)>>2]=8664,(n=v[r+8>>2])&&(e=v[r+4>>2])&&da[0|n](e),or(r)},Qu,function(r){at(Qu(r|=0))},oo,function(r){at(oo(r|=0))},vo,function(r){at(vo(r|=0))},ro,function(r){at(ro(r|=0))},it,function(r){at(it(r|=0))},ho,function(r){at(ho(r|=0))},function(r){return v[(r|=0)>>2]=8776,uo(r+12|0),0|r},function(r){v[(r|=0)>>2]=8776,uo(r+12|0),at(r)},ka,uo,function(r){at(uo(r|=0))},function(r){return v[(r|=0)>>2]=8824,0|r},function(r){v[(r|=0)>>2]=8824,at(r)},oe,function(r){oe(r|=0),at(r)},function(r){var n,e=0;return v[4+(r|=0)>>2]=8936,v[r>>2]=8920,lf(r+28|0),lf(r+16|0),v[r+4>>2]=8664,(n=v[r+12>>2])&&(e=v[r+8>>2])&&da[0|n](e),0|r},function(r){var n,e=0;v[4+(r|=0)>>2]=8936,v[r>>2]=8920,lf(r+28|0),lf(r+16|0),v[r+4>>2]=8664,(n=v[r+12>>2])&&(e=v[r+8>>2])&&da[0|n](e),at(r)},function(r){var n,e;return v[(r|=0)>>2]=8936,v[(n=r-4|0)>>2]=8920,lf(r+24|0),lf(r+12|0),v[r>>2]=8664,(e=v[r+8>>2])&&(r=v[r+4>>2])&&da[0|e](r),0|n},function(r){var n,e;v[(r|=0)>>2]=8936,v[(n=r-4|0)>>2]=8920,lf(r+24|0),lf(r+12|0),v[r>>2]=8664,(e=v[r+8>>2])&&(r=v[r+4>>2])&&da[0|e](r),at(n)},function(r){return v[(r|=0)>>2]=8952,Qu(r+96|0),Qu(r+80|0),lf(r+8|0),0|r},function(r){v[(r|=0)>>2]=8952,Qu(r+96|0),Qu(r+80|0),lf(r+8|0),at(r)},to,function(r){at(to(r|=0))},io,function(r){at(io(r|=0))},la,Yo,ka,function(){return 20664},function(r,n,e,i){return n|=0,e|=0,i|=0,(r=Ye(v[4+(r|=0)>>2],i))?(n=Lr(Xf(164),e),(i=v[n+28>>2])&&(!(e=v[n+24>>2])|(0|r)==(0|e)||da[0|i](e)),v[n+28>>2]=0,v[n+24>>2]=r,Zn(n,p[r+36>>2],p[r+40>>2],p[r+44>>2],p[r+48>>2],l[r+72|0]),p[n+60>>2]=p[r+52>>2],p[n+64>>2]=p[r+56>>2],p[n+68>>2]=v[r+28>>2],p[n+72>>2]=v[r+32>>2],p[n+76>>2]=v[r+60>>2],p[n+80>>2]=v[r+64>>2],0|n):0},function(r,n,e,i){return n|=0,e|=0,i|=0,(n=Ye(v[4+(r|=0)>>2],i))?(r=Mr(Xf(236),e),(i=v[r+72>>2])&&(!(e=v[r+68>>2])|(0|n)==(0|e)||da[0|i](e)),v[r+72>>2]=0,v[r+68>>2]=n,p[r+180>>2]=p[n+36>>2],p[r+184>>2]=p[n+40>>2],p[r+188>>2]=p[n+44>>2],p[r+192>>2]=p[n+48>>2],f[r+228|0]=l[n+72|0],v[r+232>>2]=v[n+76>>2],p[r+76>>2]=p[n+52>>2],p[r+80>>2]=p[n+56>>2],p[r+84>>2]=v[n+28>>2],p[r+88>>2]=v[n+32>>2],p[r+92>>2]=v[n+60>>2],p[r+96>>2]=v[n+64>>2],0|r):0},function(r,n,e){return e|=0,0|su(Xf(64),e)},function(r,n,e){return e|=0,0|xi(Xf(84),e)},function(r,n,e){return e|=0,0|zf(Xf(32),e)},function(r,n,e){return e|=0,0|dt(Xf(68),e)},ta,la,jt,va,function(){return 20676},function(){j(),c()},la,va,function(){return 20688},la,function(r){return v[(r|=0)>>2]=9120,ae(r+24|0),vo(r+8|0),0|r},function(r){v[(r|=0)>>2]=9120,ae(r+24|0),vo(r+8|0),at(r)},function(){return 20700},function(r,n,e,i,f,t,u,o){r|=0,n|=0,i=w(i),f|=0,u|=0,o|=0;var a=0;r:if(a=v[v[n+36>>2]+(v[r+4>>2]<<2)>>2],f=v[a+8>>2],0|da[v[v[f>>2]+16>>2]](f)){n:{e:{if(1!=(0|o)|u)if(o=v[r+20>>2],p[o>>2]>i){if(u>>>0>1)break r;if(f=v[a+4>>2],!v[f+72>>2])break e;f=f+68|0}else{if(f=v[r+12>>2]-1|0,p[o+(f<<2)>>2]<=i||(f=Pe(r+8|0,i,1)-1|0),u=v[r+36>>2],!v[4+(u+m(f,12)|0)>>2])break e;f=u+m(f,12)|0}else{if(f=v[a+4>>2],!v[f+72>>2])break e;f=f+68|0}r=Fi(n,v[r+4>>2],f);break n}r=0}af(a,r)}},function(r){return v[4+(r|=0)>>2]+67108864|0},ae,function(r){at(ae(r|=0))},la,function(r){return v[(r|=0)>>2]=9164,so(r+16|0),0|r},function(r){v[(r|=0)>>2]=9164,so(r+16|0),at(r)},function(){return 20712},Of,function(r){return l[117+(r|=0)|0]},function(r,n){n|=0,f[117+(r|=0)|0]=n},so,function(r){at(so(r|=0))},function(r){return v[(r|=0)>>2]=9212,lf(r+8|0),0|r},function(r){v[(r|=0)>>2]=9212,lf(r+8|0),at(r)},la,tt,Xo,function(){return 20724},function(r){var n;return r|=0,su(n=Xf(64),No(r)),Yi(r,n),0|n},la,Xo,function(){return 20736},function(r){var n;return r|=0,dt(n=Xf(68),No(r)),Yi(r,n),v[n+64>>2]=v[r+64>>2],0|n},la,function(r){return v[(r|=0)>>2]=9280,vo(r+24|0),0|Ut(r)},function(r){v[(r|=0)>>2]=9280,vo(r+24|0),at(Ut(r))},function(){return 20748},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a=w(0),c=w(0),b=w(0),k=0,s=0,l=w(0);D=f=D-32|0,n=v[v[n+36>>2]+(v[r+20>>2]<<2)>>2],o=v[n+8>>2];r:if(0|da[v[v[o>>2]+16>>2]](o))if(o=v[r+36>>2],p[o>>2]>i){n:switch(0|u){case 0:r=v[n+4>>2],p[n+20>>2]=p[r+28>>2],p[n+24>>2]=p[r+32>>2],p[n+28>>2]=p[r+36>>2],p[n+32>>2]=p[r+40>>2],ci(n+16|0);break r;case 1:break n;default:break r}r=v[n+4>>2],v[f+12>>2]=9308,u=v[r+40>>2],v[f+24>>2]=v[r+36>>2],v[f+28>>2]=u,u=v[r+32>>2],v[f+16>>2]=v[r+28>>2],v[f+20>>2]=u,e=p[f+24>>2],i=p[f+16>>2],a=p[f+28>>2],c=p[n+24>>2],p[n+24>>2]=w(w(p[f+20>>2]-c)*t)+c,c=e,e=p[n+28>>2],p[n+28>>2]=w(w(c-e)*t)+e,e=p[n+32>>2],p[n+32>>2]=w(w(a-e)*t)+e,e=p[n+20>>2],p[n+20>>2]=w(w(i-e)*t)+e,ci(n+16|0)}else o=o+(v[r+28>>2]<<2)|0,p[o-20>>2]<=i?(i=p[o-4>>2],e=p[o-8>>2],c=p[o-12>>2],a=p[o-16>>2]):(o=(s=(k=Pe(r+24|0,i,5))<<2)+v[r+36>>2]|0,b=p[o-16>>2],c=p[o-12>>2],e=p[o-8>>2],l=p[o-4>>2],a=i,i=p[o>>2],a=bn(r,((k>>>0)/5|0)-1|0,w(w(1)-w(w(a-i)/w(p[o-20>>2]-i)))),r=v[r+36>>2]+s|0,i=w(l+w(a*w(p[r+16>>2]-l))),e=w(e+w(a*w(p[r+12>>2]-e))),c=w(c+w(a*w(p[r+8>>2]-c))),a=w(b+w(a*w(p[r+4>>2]-b)))),r=n+16|0,t!=w(1)?(u||(u=Ko(v[n+4>>2]),p[n+20>>2]=p[u+4>>2],p[n+24>>2]=p[u+8>>2],p[n+28>>2]=p[u+12>>2],p[n+32>>2]=p[u+16>>2],ci(r)),b=p[n+32>>2],p[n+32>>2]=b+w(w(i-b)*t),i=p[n+28>>2],p[n+28>>2]=i+w(w(e-i)*t),e=p[n+24>>2],p[n+24>>2]=e+w(w(c-e)*t),e=p[n+20>>2],p[n+20>>2]=e+w(w(a-e)*t),ci(r)):(p[n+32>>2]=i,p[n+28>>2]=e,p[n+24>>2]=c,p[n+20>>2]=a,ci(r));D=f+32|0},function(r){return v[20+(r|=0)>>2]+83886080|0},ka,la,la,Mt,function(r){Mt(r|=0),at(r)},function(){return 20772},la,Ut,va,function(){return 20784},la,function(r){return v[(r|=0)>>2]=9372,ao(r+40|0),vo(r+24|0),0|Ut(r)},function(r){v[(r|=0)>>2]=9372,ao(r+40|0),vo(r+24|0),at(Ut(r))},function(){return 20796},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a,c=0,b=0,k=0,s=w(0),l=0;D=a=D-16|0,n=v[v[n+36>>2]+(v[r+20>>2]<<2)>>2],f=v[n+8>>2];r:if(0|da[v[v[f>>2]+16>>2]](f)&&(o=v[n+60>>2])&&!(!Tf(0|da[v[v[o>>2]+8>>2]](o),21164)|v[o+56>>2]!=v[r+56>>2])){k=v[n+72>>2]?u:0,u=n+68|0,f=v[v[r+52>>2]+4>>2];n:if(p[v[r+36>>2]>>2]>i){e:switch(0|k){case 0:v[n+72>>2]=0;break r;case 2:case 3:break r;case 1:break e;default:break n}if(t==w(1)){v[n+72>>2]=0;break r}if(v[a+12>>2]=0,yn(u,f,a+12|0),!v[o+24>>2]){if(!f)break r;for(n=v[n+80>>2],u=v[o+48>>2],r=0;e=p[(c=(o=r<<2)+n|0)>>2],p[c>>2]=w(w(p[u+o>>2]-e)*t)+e,(0|f)!=(0|(r=r+1|0)););break r}if(!f)break r;for(e=w(w(1)-t),n=v[n+80>>2],r=0;p[(u=n+(r<<2)|0)>>2]=e*p[u>>2],(0|f)!=(0|(r=r+1|0)););break r}if(v[a+8>>2]=0,yn(u,f,a+8|0),u=v[r+28>>2]-1|0,p[v[r+36>>2]+(u<<2)>>2]<=i){if(r=v[r+52>>2],t==w(1)){if(3==(0|k)){if(v[o+24>>2]){if(!f)break r;for(n=v[n+80>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(c=(o=r<<2)+n|0)>>2]=p[u+o>>2]+p[c>>2],(0|f)!=(0|(r=r+1|0)););break r}if(!f)break r;for(c=v[n+80>>2],o=v[o+48>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(b=(n=r<<2)+c|0)>>2]=w(p[n+u>>2]-p[n+o>>2])+p[b>>2],(0|f)!=(0|(r=r+1|0)););break r}Ff(v[n+80>>2],v[12+(r+(u<<4)|0)>>2],f<<2);break r}n:switch(0|k){case 0:if(v[o+24>>2]){if(!f)break r;for(n=v[n+80>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(o=r<<2)+n>>2]=p[u+o>>2]*t,(0|f)!=(0|(r=r+1|0)););break r}if(!f)break r;for(c=v[n+80>>2],o=v[o+48>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;e=p[(n=r<<2)+o>>2],p[n+c>>2]=w(w(p[n+u>>2]-e)*t)+e,(0|f)!=(0|(r=r+1|0)););break r;case 1:case 2:if(!f)break r;for(n=v[n+80>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;e=p[(c=(o=r<<2)+n|0)>>2],p[c>>2]=w(w(p[u+o>>2]-e)*t)+e,(0|f)!=(0|(r=r+1|0)););break r;case 3:break n;default:break r}if(v[o+24>>2]){if(!f)break r;for(n=v[n+80>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(c=(o=r<<2)+n|0)>>2]=w(p[u+o>>2]*t)+p[c>>2],(0|f)!=(0|(r=r+1|0)););break r}if(!f)break r;for(c=v[n+80>>2],o=v[o+48>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(b=(n=r<<2)+c|0)>>2]=w(w(p[n+u>>2]-p[n+o>>2])*t)+p[b>>2],(0|f)!=(0|(r=r+1|0)););}else if(c=We(r+24|0,i),u=v[r+52>>2],l=r,b=c-1|0,r=v[r+36>>2],e=p[r+(c<<2)>>2],e=bn(l,b,w(w(1)-w(w(i-e)/w(p[r+(b<<2)>>2]-e)))),t!=w(1)){n:switch(0|k){case 0:if(v[o+24>>2]){if(!f)break r;for(o=v[n+80>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;i=p[(n=r<<2)+u>>2],p[n+o>>2]=w(w(w(p[n+c>>2]-i)*e)+i)*t,(0|f)!=(0|(r=r+1|0)););break r}if(!f)break r;for(k=v[n+80>>2],o=v[o+48>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],n=0;i=p[(r=n<<2)+u>>2],s=w(w(w(p[r+c>>2]-i)*e)+i),i=p[r+o>>2],p[r+k>>2]=w(w(s-i)*t)+i,(0|f)!=(0|(n=n+1|0)););break r;case 1:case 2:if(!f)break r;for(o=v[n+80>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;b=(n=r<<2)+o|0,i=p[n+u>>2],s=w(w(w(p[n+c>>2]-i)*e)+i),i=p[b>>2],p[b>>2]=w(w(s-i)*t)+i,(0|f)!=(0|(r=r+1|0)););break r;case 3:break n;default:break r}if(v[o+24>>2]){if(!f)break r;for(o=v[n+80>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;b=(n=r<<2)+o|0,i=p[n+u>>2],p[b>>2]=w(w(w(w(p[n+c>>2]-i)*e)+i)*t)+p[b>>2],(0|f)!=(0|(r=r+1|0)););}else if(f)for(k=v[n+80>>2],o=v[o+48>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],n=0;b=(r=n<<2)+k|0,i=p[r+u>>2],p[b>>2]=w(w(w(w(w(p[r+c>>2]-i)*e)+i)-p[r+o>>2])*t)+p[b>>2],(0|f)!=(0|(n=n+1|0)););}else{if(3!=(0|k)){if(!f)break r;for(o=v[n+80>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;i=p[(n=r<<2)+u>>2],p[n+o>>2]=w(w(p[n+c>>2]-i)*e)+i,(0|f)!=(0|(r=r+1|0)););break r}if(v[o+24>>2]){if(!f)break r;for(o=v[n+80>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;b=(n=r<<2)+o|0,i=p[n+u>>2],p[b>>2]=p[b>>2]+w(w(w(p[n+c>>2]-i)*e)+i),(0|f)!=(0|(r=r+1|0)););break r}if(!f)break r;for(k=v[n+80>>2],o=v[o+48>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],n=0;b=(r=n<<2)+k|0,i=p[r+u>>2],p[b>>2]=w(w(w(w(p[r+c>>2]-i)*e)+i)-p[r+o>>2])+p[b>>2],(0|f)!=(0|(n=n+1|0)););}}D=a+16|0},function(r){return 100663296+(v[v[56+(r|=0)>>2]+60>>2]+v[r+20>>2]|0)|0},ao,function(r){at(ao(r|=0))},la,function(r){return v[(r|=0)>>2]=9416,co(r+20|0),vo(r+4|0),0|r},function(r){v[(r|=0)>>2]=9416,co(r+20|0),vo(r+4|0),at(r)},function(){return 20808},function(r,n,e,i,f,t,u,o){r|=0,n|=0,i=w(i),f|=0,f=n+40|0;r:if(1!=(0|(o|=0))|(u|=0)){if(o=v[r+16>>2],p[o>>2]>i){if(u>>>0>1)break r;if(u=0,v[n+44>>2]=0,ca(f,v[n+28>>2]),!(r=v[n+28>>2]))break r;for(;Sn(f,v[n+36>>2]+(u<<2)|0),(0|r)!=(0|(u=u+1|0)););}else if(u=v[r+8>>2]-1|0,p[o+(u<<2)>>2]<=i||(u=We(r+4|0,i)-1|0),r=v[r+32>>2]+(u<<4)|0,o=v[r+4>>2])for(u=0;v[(f=u<<2)+v[n+52>>2]>>2]=v[v[n+36>>2]+(v[f+v[r+12>>2]>>2]<<2)>>2],(0|o)!=(0|(u=u+1|0)););else if(u=0,v[n+44>>2]=0,r=v[n+28>>2])for(;Sn(f,v[n+36>>2]+(u<<2)|0),(0|r)!=(0|(u=u+1|0)););}else{if(u=0,v[n+44>>2]=0,ca(f,v[n+28>>2]),!(r=v[n+28>>2]))break r;for(;Sn(f,v[n+36>>2]+(u<<2)|0),(0|r)!=(0|(u=u+1|0)););}},function(){return 134217728},co,function(r){at(co(r|=0))},function(r){return v[(r|=0)>>2]=9460,lf(r+20|0),0|r},function(r){v[(r|=0)>>2]=9460,lf(r+20|0),at(r)},function(r){return v[(r|=0)>>2]=9476,lf(r+36|0),lf(r+24|0),lf(r+4|0),0|r},function(r){v[(r|=0)>>2]=9476,lf(r+36|0),lf(r+24|0),lf(r+4|0),at(r)},la,Jf,function(r){Jf(r|=0),at(r)},function(){return 20820},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a=0,c=0;r:if(f){if(a=v[r+8>>2],e>i)da[v[v[r>>2]+12>>2]](r,n,e,w(34028234663852886e22),f,t,u,o),o=v[r+16>>2],e=w(-1);else if(o=v[r+16>>2],p[(o+(a<<2)|0)-4>>2]<=e)break r;if(!((t=p[o>>2])>i)){o=0;n:if(!(e<t))for(n=(u=We(r+4|0,e))&u>>31,c=v[r+16>>2],e=p[c+(u<<2)>>2];;){if((0|(o=u))<=0){o=n;break n}if(e!=p[((u=o-1|0)<<2)+c>>2])break}if(!(o>>>0>=a>>>0))for(;;){if(!(p[(n=o<<2)+v[r+16>>2]>>2]<=i))break r;if(Sn(f,n+v[r+32>>2]|0),(0|a)==(0|(o=o+1|0)))break}}}},function(){return 117440512},Yo,va,la,function(r){return v[(r|=0)>>2]=9556,so(r+8|0),0|r},function(r){v[(r|=0)>>2]=9556,so(r+8|0),at(r)},function(){return 20836},function(r){var n=0,e=0,i=0;switch(v[12+(r|=0)>>2]-1|0){case 0:return n=v[r+40>>2],void gr(v[v[r+20>>2]>>2],p[n+100>>2],p[n+112>>2],l[r+28|0],l[r+29|0],l[v[r+4>>2]+50|0],p[r+32>>2]);case 1:n=v[r+20>>2],e=v[n>>2],i=v[n+4>>2],n=v[r+40>>2],ur(e,i,p[n+100>>2],p[n+112>>2],v[r+24>>2],l[r+29|0],p[r+36>>2],p[r+32>>2])}},So,ko,pu,la,function(r){return v[(r|=0)>>2]=9592,bo(r+24|0),0|Mt(r)},function(r){v[(r|=0)>>2]=9592,bo(r+24|0),at(Mt(r))},function(){return 20848},bo,function(r){at(bo(r|=0))},la,function(r){return v[(r|=0)>>2]=9632,vo(r+20|0),0|Ut(r)},function(r){v[(r|=0)>>2]=9632,vo(r+20|0),at(Ut(r))},function(){return 20860},function(r,n,e,i,t,u,o,a){r|=0,n|=0,e=w(e),i=w(i),t|=0,u=w(u),o|=0,a|=0;var c=0,b=w(0),k=w(0);r:{n:{e:{n=v[v[n+68>>2]+(v[r+36>>2]<<2)>>2];i:if(0|da[v[v[n>>2]+16>>2]](n)){if(t=v[r+32>>2],p[t>>2]>i){f:switch(0|o){case 0:r=v[n+4>>2],p[n+32>>2]=p[r+52>>2],p[n+36>>2]=p[r+56>>2];break e;case 1:break f;default:break i}r=v[n+4>>2],e=p[n+32>>2],p[n+32>>2]=w(w(p[r+52>>2]-e)*u)+e,e=p[n+36>>2],p[n+36>>2]=w(w(p[r+56>>2]-e)*u)+e;break e}if(c=v[r+24>>2],p[(t+(c<<2)|0)-24>>2]<=i){if(!o){if(r=t+(c<<2)|0,t=v[n+4>>2],e=p[t+52>>2],p[n+32>>2]=w(w(p[r-20>>2]-e)*u)+e,e=p[t+56>>2],p[n+36>>2]=w(w(p[r-16>>2]-e)*u)+e,1==(0|a))break n;break r}if(r=t+(c<<2)|0,e=p[n+32>>2],p[n+32>>2]=w(w(p[r-20>>2]-e)*u)+e,e=p[n+36>>2],p[n+36>>2]=w(w(p[r-16>>2]-e)*u)+e,a)break i;break r}if(t=Pe(r+20|0,i,6),c=v[r+32>>2]+(t<<2)|0,e=p[c-16>>2],b=p[c-20>>2],k=i,i=p[c>>2],i=bn(r,((0|t)/6|0)-1|0,w(w(1)-w(w(k-i)/w(p[c-24>>2]-i)))),!o){if(c=v[r+32>>2],k=w(w(w(p[4+(o=c+(t<<2)|0)>>2]-b)*i)+b),t=v[n+4>>2],b=p[t+52>>2],p[n+32>>2]=w(w(k-b)*u)+b,i=w(w(w(p[o+8>>2]-e)*i)+e),e=p[t+56>>2],p[n+36>>2]=w(w(i-e)*u)+e,1==(0|a))break n;return e=p[(c+(v[r+24>>2]<<2)|0)-12>>2],r=w(g(e))<w(2147483648)?~~e:-2147483648,v[n+24>>2]=r,f[n+28|0]=p[o-8>>2]!=w(0),void(f[n+29|0]=p[o-4>>2]!=w(0))}r=v[r+32>>2]+(t<<2)|0,k=w(w(w(p[r+4>>2]-b)*i)+b),b=p[n+32>>2],p[n+32>>2]=w(w(k-b)*u)+b,i=w(w(w(p[r+8>>2]-e)*i)+e),e=p[n+36>>2],p[n+36>>2]=w(w(i-e)*u)+e,a||(e=p[r-12>>2],t=w(g(e))<w(2147483648)?~~e:-2147483648,v[n+24>>2]=t,f[n+28|0]=p[r-8>>2]!=w(0),f[n+29|0]=p[r-4>>2]!=w(0))}return}return v[n+24>>2]=v[r+44>>2],f[n+28|0]=l[r+48|0],void(f[n+29|0]=l[r+49|0])}return v[n+24>>2]=v[t+44>>2],f[n+28|0]=l[t+48|0],void(f[n+29|0]=l[t+49|0])}e=p[r-12>>2],t=w(g(e))<w(2147483648)?~~e:-2147483648,v[n+24>>2]=t,f[n+28|0]=p[r-8>>2]!=w(0),f[n+29|0]=p[r-4>>2]!=w(0)},function(r){return v[36+(r|=0)>>2]+150994944|0},function(r){return v[(r|=0)>>2]=9676,lf(r+24|0),lf(r+8|0),0|r},function(r){v[(r|=0)>>2]=9676,lf(r+24|0),lf(r+8|0),at(r)},la,la,la,function(){return 20888},function(r,n){return r|=0,(n=w(n))<=w(.5)?w(w(wr(w(n+n),w(v[r+4>>2]))*w(.5))):(n=w(n+w(-1)),r=v[r+4>>2],w(w(w(wr(w(n+n),w(0|r))/w(1&r?2:-2))+w(1))))},function(r,n,e,i){return r|=0,n=w(n),e=w(e),i=w(i),w(w(w(w(e-n)*w(da[v[v[r>>2]+4>>2]](r,i)))+n))},Yo,sa,function(){return 20900},function(r,n){return r|=0,n=w(n),r=v[r+4>>2],w(w(w(wr(w(n+w(-1)),w(0|r))*w(1&r?1:-1))+w(1)))},sa,la,Ee,function(r){Ee(r|=0),at(r)},function(){return 20912},function(r){var n,e,i=0,t=0;return v[100+(r|=0)>>2]?0|xr(r):(Mr(n=Xf(236),No(r)),i=v[r+68>>2],(e=v[n+72>>2])&&(!(t=v[n+68>>2])|(0|i)==(0|t)||da[0|e](t)),v[n+72>>2]=0,v[n+68>>2]=i,p[n+180>>2]=p[r+180>>2],p[n+184>>2]=p[r+184>>2],p[n+188>>2]=p[r+188>>2],p[n+192>>2]=p[r+192>>2],f[n+228|0]=l[r+228|0],v[n+232>>2]=v[r+232>>2],p[n+76>>2]=p[r+76>>2],p[n+80>>2]=p[r+80>>2],p[n+84>>2]=p[r+84>>2],p[n+88>>2]=p[r+88>>2],p[n+92>>2]=p[r+92>>2],p[n+96>>2]=p[r+96>>2],ee(n+168|0,r+168|0),p[n+208>>2]=p[r+208>>2],p[n+212>>2]=p[r+212>>2],p[n+216>>2]=p[r+216>>2],p[n+220>>2]=p[r+220>>2],ci(n+204|0),Yi(r,n),v[n+124>>2]=0,_i(n+120|0,r+120|0),v[n+108>>2]=0,_i(n+104|0,r+104|0),v[n+140>>2]=0,vn(n+136|0,r+136|0),i=v[r+224>>2],v[n+156>>2]=0,v[n+224>>2]=i,vn(i=n+152|0,i),p[n+196>>2]=p[r+196>>2],p[n+200>>2]=p[r+200>>2],0|n)},function(r){return 0|Ee((r|=0)-64|0)},function(r){Ee(r=(r|=0)-64|0),at(r)},lo,function(r){at(lo(r|=0))},la,function(r){return v[(r|=0)>>2]=9804,vo(r- -64|0),0|tt(r)},function(r){v[(r|=0)>>2]=9804,vo(r- -64|0),at(tt(r))},function(){return 20924},function(r){var n;return r|=0,xi(n=Xf(84),No(r)),Yi(r,n),v[n+68>>2]=0,_i(n- -64|0,r- -64|0),f[n+80|0]=l[r+80|0],f[n+81|0]=l[r+81|0],0|n},la,function(r){return v[(r|=0)>>2]=9828,vo(r+124|0),vo(r+108|0),vo(r+92|0),vo(r+76|0),vo(r+60|0),vo(r+44|0),so(r+8|0),0|r},function(r){v[(r|=0)>>2]=9828,vo(r+124|0),vo(r+108|0),vo(r+92|0),vo(r+76|0),vo(r+60|0),vo(r+44|0),so(r+8|0),at(r)},function(){return 20936},function(r){r|=0;var n,e=w(0),i=0,t=w(0),u=w(0),o=0,a=w(0),c=w(0),b=0,k=0,s=0,h=0,d=0,y=w(0),g=w(0),F=0,A=w(0),$=w(0),I=w(0),C=0,P=w(0),E=w(0),O=0,R=0,S=0,W=w(0),G=0,U=0,j=w(0),H=w(0),L=w(0),M=w(0),_=w(0),z=w(0),x=0,J=0,K=0,B=0,N=0,q=w(0),V=0,Z=0,Y=w(0);if(D=n=D-16|0,(k=v[v[r+24>>2]+60>>2])&&Tf(0|da[v[v[k>>2]+8>>2]](k),20924)&&(q=p[r+40>>2],M=p[r+36>>2],q>w(0)|M>w(0))){K=v[r+12>>2],x=v[r+4>>2],s=v[x+48>>2],G=v[x+52>>2],v[n+12>>2]=0,yn(r+44|0,C=!!(0|G)+K|0,n+12|0),a=p[r+32>>2];r:if(2==(0|G)|2!=(0|s)){if(2==(0|G)&&(v[n+8>>2]=0,yn(r+108|0,K,n+8|0)),O=C-1|0)for(b=v[x+48>>2];;){o=v[(h=i<<2)+v[r+20>>2]>>2];n:if((c=p[v[o+4>>2]+24>>2])<w(9999999747378752e-21)){if(e=w(0),2!=(0|G))break n;v[h+v[r+120>>2]>>2]=0}else if(2!=(0|s))e=w(c*p[o+92>>2]),t=w(e*e),e=w(c*p[o+104>>2]),e=w(T(w(t+w(e*e)))),2==(0|G)&&(p[h+v[r+120>>2]>>2]=e),e=w(w(w(a+(b?w(-0):c))*e)/c);else{if(e=a,2!=(0|G))break n;e=w(c*p[o+92>>2]),t=w(e*e),e=w(c*p[o+104>>2]),p[h+v[r+120>>2]>>2]=T(w(t+w(e*e))),e=a}if(i=i+1|0,p[v[r+56>>2]+(i<<2)>>2]=e,!(i>>>0<O>>>0))break}}else{if(C>>>0<2)break r;for(o=v[r+56>>2],i=1;p[o+(i<<2)>>2]=a,(0|C)!=(0|(i=i+1|0)););}V=!G,U=1==v[x+44>>2],B=2==(0|s),D=R=D-16|0,h=r,u=p[r+28>>2],d=v[r+24>>2],v[R+12>>2]=0,yn(O=r+60|0,m(C,3)+2|0,R+12|0),F=r+76|0,b=(0|(i=v[k+52>>2]))/6|0,J=l[k+80|0];r:if(l[k+81|0]){if(J?(v[R+12>>2]=0,yn(F,S=i+2|0,R+12|0),Gf(k,d,2,r=i-2|0,F,0,2),Gf(k,d,0,2,F,r,2),r=v[h+88>>2],p[(i=r+(i<<2)|0)>>2]=p[r>>2],p[i+4>>2]=p[r+4>>2]):(v[R+12>>2]=0,yn(F,S=i-4|0,R+12|0),Gf(k,d,2,S,F,0,2),b=b-1|0),i=0,v[R+12>>2]=0,yn(h+92|0,b,R+12|0),s=v[h+88>>2],a=p[s+4>>2],c=p[s>>2],(0|b)>0)for(d=v[h+104>>2],o=2,e=a,t=c;P=p[(r=(N=o<<2)+s|0)>>2],_=p[r+8>>2],c=p[r+16>>2],a=w(w(w(w(w(P-_)*w(3))-t)+c)*w(.09375)),A=w(w(P-t)*w(.75)),t=w(w(w(t-w(P+P))+_)*w(.1875)),y=w(w(a*w(.1666666716337204))+w(A+t)),t=w(w(t+t)+a),W=w(y+t),t=w(a+t),j=w(W+t),A=w(j+w(a+t)),E=p[s+(4|N)>>2],z=p[r+12>>2],a=p[r+20>>2],t=w(w(w(w(w(E-z)*w(3))-e)+a)*w(.09375)),$=w(w(E-e)*w(.75)),e=w(w(w(e-w(E+E))+z)*w(.1875)),g=w(w(t*w(.1666666716337204))+w($+e)),$=w(w(e+e)+t),e=w(g+$),L=w(t+$),$=w(e+L),t=w($+w(t+L)),I=w(w(w(w(I+w(T(w(w(y*y)+w(g*g)))))+w(T(w(w(W*W)+w(e*e)))))+w(T(w(w(j*j)+w($*$)))))+w(T(w(w(A*A)+w(t*t))))),p[d+(i<<2)>>2]=I,o=o+6|0,j=a,e=a,W=c,t=c,(0|b)!=(0|(i=i+1|0)););if(e=I,e=U?e:w(e/p[(v[k+76>>2]+(b<<2)|0)-4>>2]),!(!B|(0|C)<2))for(i=v[h+56>>2],r=1;p[(o=i+(r<<2)|0)>>2]=I*p[o>>2],(0|C)!=(0|(r=r+1|0)););if(!((0|C)<=0))for(y=w(u*e),S=S-4|0,s=-1,i=0,r=0,k=0,b=0,u=w(0);;){Y=p[v[h+56>>2]+(b<<2)>>2],y=w(y+Y);n:{e:{if(J)o=0,e=Cr(y,I),e=w(e+(e<w(0)?I:w(-0)));else{if(y<w(0)){Ie(y,F,O,k);break n}if(y>I)break e;o=r,e=y}for(d=v[h+104>>2];o=(r=o)+1|0,(t=p[(U=d+(r<<2)|0)>>2])<e;);if(r?(A=e,e=p[U-4>>2],e=w(w(A-e)/w(t-e))):e=w(e/t),A=e,(0|r)!=(0|s)){for(d=v[h+136>>2],i=v[h+88>>2]+m(r,24)|0,P=p[i+8>>2],_=p[i+16>>2],c=p[i>>2],W=p[i+24>>2],$=w(w(w(w(w(P-_)*w(3))-c)+W)*w(.006000000052154064)),g=w(w(w(c-w(P+P))+_)*w(.029999999329447746)),e=w(w($*w(.1666666716337204))+w(w(w(P-c)*w(.30000001192092896))+g)),E=p[i+12>>2],z=p[i+20>>2],a=p[i+4>>2],j=p[i+28>>2],L=w(w(w(w(w(E-z)*w(3))-a)+j)*w(.006000000052154064)),u=w(w(w(a-w(E+E))+z)*w(.029999999329447746)),t=w(w(L*w(.1666666716337204))+w(w(w(E-a)*w(.30000001192092896))+u)),H=w(T(w(w(e*e)+w(t*t)))),p[d>>2]=H,u=w(w(u+u)+L),g=w(w(g+g)+$),i=1;e=w(g+e),t=w(u+t),H=w(H+w(T(w(w(e*e)+w(t*t))))),p[(i<<2)+d>>2]=H,u=w(L+u),g=w($+g),8!=(0|(i=i+1|0)););e=w(g+e),t=w(u+t),H=w(H+w(T(w(w(e*e)+w(t*t))))),p[d+32>>2]=H,e=w(w($+g)+e),$=w(e*e),e=w(w(L+u)+t),u=w(H+w(T(w($+w(e*e))))),p[d+36>>2]=u,s=r,i=0}else d=v[h+136>>2];for(o=i,e=w(A*u);o=(i=o)+1|0,(t=p[(U=(i<<2)+d|0)>>2])<e;);i?(A=e,e=p[U-4>>2],e=w(w(w(A-e)/w(t-e))+w(0|i))):e=w(e/t),Br(w(e*w(.10000000149011612)),c,a,P,E,_,z,W,j,O,k,!!(0|b)&Y<w(9999999747378752e-21)|V);break n}me(w(y-I),F,S,O,k)}if(k=k+3|0,(0|C)==(0|(b=b+1|0)))break}}else{if(o=(J?-1:-2)+b|0,a=p[v[k+76>>2]+(o<<2)>>2],!(!B|(0|C)<2))for(s=v[h+56>>2],r=1;p[(b=s+(r<<2)|0)>>2]=a*p[b>>2],(0|C)!=(0|(r=r+1|0)););if(v[R+12>>2]=0,yn(F,8,R+12|0),(0|C)<=0)break r;for(u=w(u*(U?a:w(1))),U=i-4|0,B=i-6|0,s=-1,r=0,b=0;;){t=p[v[h+56>>2]+(S<<2)>>2],u=w(u+t);n:{if(J)e=Cr(u,a),e=w(e+(e<w(0)?a:w(-0))),i=0;else{if(u<w(0)){-2!=(0|s)&&Gf(k,d,2,4,F,0,2),Ie(u,F,O,b),s=-2;break n}if(u>a){-3!=(0|s)&&Gf(k,d,B,4,F,0,2),me(w(u-a),F,0,O,b),s=-3;break n}i=r,e=u}for(N=v[k+76>>2];i=(r=i)+1|0,(c=p[(Z=N+(r<<2)|0)>>2])<e;);r?(A=e,e=p[Z-4>>2],e=w(w(A-e)/w(c-e))):e=w(e/c),(0|r)!=(0|s)&&(!J|(0|r)!=(0|o)?(Gf(k,d,m(r,6)+2|0,8,F,0,2),s=r):(Gf(k,d,U,4,F,0,2),Gf(k,d,0,4,F,4,2),s=o)),i=v[h+88>>2],Br(e,p[i>>2],p[i+4>>2],p[i+8>>2],p[i+12>>2],p[i+16>>2],p[i+20>>2],p[i+24>>2],p[i+28>>2],O,b,!!(0|S)&t<w(9999999747378752e-21)|V)}if(b=b+3|0,(0|C)==(0|(S=S+1|0)))break}}if(D=R+16|0,r=v[O+12>>2],e=p[r+4>>2],a=p[r>>2],(W=p[x+56>>2])==w(0)?r=1==(0|G):(r=v[v[h+24>>2]+8>>2],W=w(W*(w(w(p[r+92>>2]*p[r+108>>2])-w(p[r+104>>2]*p[r+96>>2]))>w(0)?w(.01745329238474369):w(-.01745329238474369))),r=0),K)for(i=0,s=3;o=v[(k=i<<2)+v[h+20>>2]>>2],c=p[o+100>>2],p[o+100>>2]=w(w(a-c)*q)+c,c=p[o+112>>2],p[o+112>>2]=w(w(e-c)*q)+c,b=v[O+12>>2]+(s<<2)|0,c=p[b>>2],y=w(c-a),a=p[b+4>>2],e=w(a-e),2==(0|G)&&(t=p[k+v[h+120>>2]>>2])>=w(9999999747378752e-21)&&(t=w(w(w(w(w(T(w(w(y*y)+w(e*e))))/t)+w(-1))*M)+w(1)),p[o+92>>2]=t*p[o+92>>2],p[o+104>>2]=t*p[o+104>>2]),M>w(0)?(j=p[o+108>>2],t=p[o+104>>2],A=p[o+96>>2],I=p[o+92>>2],u=p[b-4>>2],G&&(u=p[b+8>>2],p[4+(k+v[h+56>>2]|0)>>2]<w(9999999747378752e-21)||(u=Pr(e,y))),u=w(u-Pr(t,I)),r?(P=Rr(u),E=Wr(u),g=p[v[o+4>>2]+24>>2],e=w(w(w(w(g*w(w(E*I)+w(t*P)))-e)*M)+a),a=w(w(w(w(g*w(w(P*I)-w(t*E)))-y)*M)+c)):(u=w(W+u),e=a,a=c),u>w(3.1415927410125732)?u=w(u+w(-6.2831854820251465)):u<w(-3.1415927410125732)&&(u=w(u+w(6.2831854820251465))),c=Rr(u=w(M*u)),u=Wr(u),p[o+108>>2]=w(u*A)+w(j*c),p[o+104>>2]=w(u*I)+w(t*c),p[o+96>>2]=w(c*A)-w(j*u),p[o+92>>2]=w(c*I)-w(t*u)):(e=a,a=c),f[o+88|0]=0,s=s+3|0,(0|K)!=(0|(i=i+1|0)););}D=n+16|0},function(r){return l[140+(r|=0)|0]},function(r,n){n|=0,f[140+(r|=0)|0]=n},pu,la,function(r){return v[(r|=0)>>2]=9864,bo(r+24|0),0|Mt(r)},function(r){v[(r|=0)>>2]=9864,bo(r+24|0),at(Mt(r))},function(){return 20948},la,function(r){return v[(r|=0)>>2]=9888,vo(r+20|0),0|Ut(r)},function(r){v[(r|=0)>>2]=9888,vo(r+20|0),at(Ut(r))},function(){return 20960},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a=w(0),c=0,b=w(0);r:if(n=v[v[n+100>>2]+(v[r+36>>2]<<2)>>2],0|da[v[v[n>>2]+16>>2]](n)){if(f=v[r+32>>2],p[f>>2]>i){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+36>>2]=p[r+68>>2],void(p[n+40>>2]=p[r+72>>2]);case 1:break n;default:break r}return r=v[n+4>>2],e=p[n+36>>2],p[n+36>>2]=w(w(p[r+68>>2]-e)*t)+e,e=p[n+40>>2],void(p[n+40>>2]=w(w(p[r+72>>2]-e)*t)+e)}if(f=f+(v[r+24>>2]<<2)|0,p[f-12>>2]<=i?(i=p[f-4>>2],e=p[f-8>>2]):(f=(c=(o=Pe(r+20|0,i,3))<<2)+v[r+32>>2]|0,e=p[f-8>>2],b=p[f-4>>2],a=i,i=p[f>>2],a=bn(r,((0|o)/3|0)-1|0,w(w(1)-w(w(a-i)/w(p[f-12>>2]-i)))),r=v[r+32>>2]+c|0,i=w(b+w(a*w(p[r+8>>2]-b))),e=w(e+w(a*w(p[r+4>>2]-e)))),!u)return a=e,r=v[n+4>>2],e=p[r+68>>2],p[n+36>>2]=w(w(a-e)*t)+e,e=p[r+72>>2],void(p[n+40>>2]=w(w(i-e)*t)+e);a=e,e=p[n+36>>2],p[n+36>>2]=w(w(a-e)*t)+e,e=p[n+40>>2],p[n+40>>2]=w(w(i-e)*t)+e}},function(r){return v[36+(r|=0)>>2]+218103808|0},la,Ft,function(r){Ft(r|=0),at(r)},function(){return 20972},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0);r:if(n=v[v[n+100>>2]+(v[r+36>>2]<<2)>>2],0|da[v[v[n>>2]+16>>2]](n)){if(f=v[r+32>>2],p[f>>2]>i){n:switch(0|u){case 0:return void(p[n+28>>2]=p[v[n+4>>2]+60>>2]);case 1:break n;default:break r}return e=p[n+28>>2],void(p[n+28>>2]=w(w(p[v[n+4>>2]+60>>2]-e)*t)+e)}if(f=f+(v[r+24>>2]<<2)|0,p[f-8>>2]<=i?e=p[f-4>>2]:(f=(a=(o=Pe(r+20|0,i,2))<<2)+v[r+32>>2]|0,e=p[f-4>>2],c=i,i=p[f>>2],e=w(e+w(bn(r,((0|o)/2|0)-1|0,w(w(1)-w(w(c-i)/w(p[f-8>>2]-i))))*w(p[4+(v[r+32>>2]+a|0)>>2]-e)))),!u)return i=e,e=p[v[n+4>>2]+60>>2],void(p[n+28>>2]=w(w(i-e)*t)+e);i=e,e=p[n+28>>2],p[n+28>>2]=w(w(i-e)*t)+e}},function(r){return v[36+(r|=0)>>2]+184549376|0},la,function(r){at(Ft(r|=0))},function(){return 20984},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0);r:if(n=v[v[n+100>>2]+(v[r+36>>2]<<2)>>2],0|da[v[v[n>>2]+16>>2]](n)){if(f=v[r+32>>2],p[f>>2]>i){n:switch(0|u){case 0:return void(p[n+32>>2]=p[v[n+4>>2]+64>>2]);case 1:break n;default:break r}return e=p[n+32>>2],void(p[n+32>>2]=w(w(p[v[n+4>>2]+64>>2]-e)*t)+e)}if(a=v[r+24>>2],o=v[2477],p[f+(a-o<<2)>>2]<=i?e=p[f+(a+v[2479]<<2)>>2]:(f=Pe(r+20|0,i,o),a=v[r+32>>2],e=p[a+(f+v[2479]<<2)>>2],c=i,i=p[a+(f<<2)>>2],e=w(e+w(bn(r,((0|f)/(0|o)|0)-1|0,w(w(1)-w(w(c-i)/w(p[a+(f+v[2478]<<2)>>2]-i))))*w(p[v[r+32>>2]+(f+v[2480]<<2)>>2]-e)))),!u)return i=e,e=p[v[n+4>>2]+64>>2],void(p[n+32>>2]=w(w(i-e)*t)+e);i=e,e=p[n+32>>2],p[n+32>>2]=w(w(i-e)*t)+e}},function(r){return v[36+(r|=0)>>2]+201326592|0},la,function(r){at(jt(r|=0))},function(){return 20996},function(r){var n;return r|=0,zf(n=Xf(32),No(r)),p[n+20>>2]=p[r+20>>2],p[n+24>>2]=p[r+24>>2],p[n+28>>2]=p[r+28>>2],0|n},ka,la,function(r){var n,e=0;return v[20+(r|=0)>>2]=10052,v[r>>2]=10028,lf(r+116|0),vo(r+100|0),vo(r+84|0),v[r+20>>2]=8664,(n=v[r+28>>2])&&(e=v[r+24>>2])&&da[0|n](e),0|jt(r)},function(r){var n,e=0;v[20+(r|=0)>>2]=10052,v[r>>2]=10028,lf(r+116|0),vo(r+100|0),vo(r+84|0),v[r+20>>2]=8664,(n=v[r+28>>2])&&(e=v[r+24>>2])&&da[0|n](e),at(jt(r))},function(){return 21008},function(r){r|=0;var n,e,i,f=0,t=w(0);return Lr(n=Xf(164),No(r)),p[n+68>>2]=p[r+68>>2],p[n+72>>2]=p[r+72>>2],p[n+60>>2]=p[r+60>>2],p[n+64>>2]=p[r+64>>2],p[n+76>>2]=p[r+76>>2],p[n+80>>2]=p[r+80>>2],e=v[r+24>>2],(i=v[n+28>>2])&&(!(f=v[n+24>>2])|(0|e)==(0|f)||da[0|i](f)),v[n+28>>2]=0,v[n+24>>2]=e,ee(n+116|0,r+116|0),p[n+32>>2]=p[r+32>>2],p[n+36>>2]=p[r+36>>2],p[n+44>>2]=p[r+44>>2],p[n+48>>2]=p[r+48>>2],p[n+40>>2]=p[r+40>>2],p[n+52>>2]=p[r+52>>2],t=p[r+56>>2],v[n+104>>2]=0,p[n+56>>2]=t,_i(n+100|0,r+100|0),v[n+88>>2]=0,_i(n+84|0,r+84|0),p[n+148>>2]=p[r+148>>2],p[n+152>>2]=p[r+152>>2],p[n+156>>2]=p[r+156>>2],p[n+160>>2]=p[r+160>>2],ci(n+144|0),0|n},function(r){var n,e;return v[(r|=0)>>2]=10052,v[(n=r-20|0)>>2]=10028,lf(r+96|0),vo(r+80|0),vo(r- -64|0),v[r>>2]=8664,(e=v[r+8>>2])&&(r=v[r+4>>2])&&da[0|e](r),0|jt(n)},function(r){var n,e;v[(r|=0)>>2]=10052,v[(n=r-20|0)>>2]=10028,lf(r+96|0),vo(r+80|0),vo(r- -64|0),v[r>>2]=8664,(e=v[r+8>>2])&&(r=v[r+4>>2])&&da[0|e](r),at(jt(n))},la,function(r){return v[(r|=0)>>2]=10068,vo(r+24|0),0|Ut(r)},function(r){v[(r|=0)>>2]=10068,vo(r+24|0),at(Ut(r))},function(){return 21020},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0),b=0;r:if(n=v[v[n+20>>2]+(v[r+20>>2]<<2)>>2],l[n+117|0]){if(f=v[r+36>>2],p[f>>2]>i){n:switch(0|u){case 0:return void(p[n+40>>2]=p[v[n+4>>2]+36>>2]);case 1:break n;default:break r}return e=p[n+40>>2],i=w(p[v[n+4>>2]+36>>2]-e),a=+w(i/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,void(p[n+40>>2]=w(w(i-w(0|m(16384-r|0,360)))*t)+e)}if(f=f+(v[r+28>>2]<<2)|0,p[f-8>>2]<=i){i=p[f-4>>2];n:{e:switch(0|u){case 3:e=p[n+40>>2];break n;case 0:return void(p[n+40>>2]=w(i*t)+p[v[n+4>>2]+36>>2]);case 1:case 2:break e;default:break r}e=p[n+40>>2],i=w(i+w(p[v[n+4>>2]+36>>2]-e)),a=+w(i/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,i=w(i-w(0|m(16384-r|0,360)))}return void(p[n+40>>2]=w(i*t)+e)}f=(b=(o=Pe(r+24|0,i,2))<<2)+v[r+36>>2]|0,e=p[f-4>>2],c=i,i=p[f>>2],i=bn(r,(o>>1)-1|0,w(w(1)-w(w(c-i)/w(p[f-8>>2]-i)))),c=w(p[4+(v[r+36>>2]+b|0)>>2]-e),a=+w(c/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,i=w(w(w(c-w(0|m(16384-r|0,360)))*i)+e);n:{e:switch(0|u){case 3:e=p[n+40>>2];break n;case 0:return a=+w(i/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,void(p[n+40>>2]=w(w(i-w(0|m(16384-r|0,360)))*t)+p[v[n+4>>2]+36>>2]);case 1:case 2:break e;default:break r}e=p[n+40>>2],i=w(i+w(p[v[n+4>>2]+36>>2]-e))}a=+w(i/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,p[n+40>>2]=w(w(i-w(0|m(16384-r|0,360)))*t)+e}},$o,la,mt,Qo,function(){return 21032},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a=w(0),c=w(0),b=0,k=w(0),s=w(0),h=0,d=w(0),y=0,m=w(0);r:{n=v[v[n+20>>2]+(v[r+36>>2]<<2)>>2];n:if(l[n+117|0]){if(f=v[r+32>>2],p[f>>2]>i){e:switch(0|u){case 0:return r=v[n+4>>2],p[n+44>>2]=p[r+40>>2],void(p[n+48>>2]=p[r+44>>2]);case 1:break e;default:break n}return r=v[n+4>>2],e=p[n+44>>2],p[n+44>>2]=w(w(p[r+40>>2]-e)*t)+e,e=p[n+48>>2],void(p[n+48>>2]=w(w(p[r+44>>2]-e)*t)+e)}if(b=v[r+24>>2],h=v[2721],p[f+(b-h<<2)>>2]<=i?(r=v[n+4>>2],c=p[r+44>>2],i=w(p[f+(b+v[2724]<<2)>>2]*c),a=p[r+40>>2],e=w(p[f+(b+v[2723]<<2)>>2]*a)):(f=Pe(r+20|0,i,h),b=v[r+32>>2],e=p[b+(f+v[2723]<<2)>>2],a=p[b+(f+v[2724]<<2)>>2],k=i,i=p[b+(f<<2)>>2],s=bn(r,((0|f)/(0|h)|0)-1|0,w(w(1)-w(w(k-i)/w(p[b+(f+v[2722]<<2)>>2]-i)))),b=v[n+4>>2],c=p[b+44>>2],r=v[r+32>>2],i=w(c*w(a+w(s*w(p[r+(f+v[2726]<<2)>>2]-a)))),a=p[b+40>>2],e=w(w(e+w(s*w(p[r+(f+v[2725]<<2)>>2]-e)))*a)),t==w(1))return 3==(0|u)?(p[n+44>>2]=p[n+44>>2]+w(e-a),void(p[n+48>>2]=p[n+48>>2]+w(i-c))):(p[n+48>>2]=i,void(p[n+44>>2]=e));if(1==(0|o)){e:switch(0|u){case 0:return y=n,m=w(w(w(w(w(g(e))*Bt(a))-a)*t)+a),p[y+44>>2]=m,y=n,m=w(w(w(w(w(g(i))*Bt(c))-c)*t)+c),void(p[y+48>>2]=m);case 1:case 2:return c=p[n+48>>2],a=p[n+44>>2],y=n,m=w(a+w(w(w(w(g(e))*Bt(a))-a)*t)),p[y+44>>2]=m,y=n,m=w(c+w(w(w(w(g(i))*Bt(c))-c)*t)),void(p[y+48>>2]=m);case 3:break e;default:break n}return c=p[n+48>>2],k=w(g(e)),e=p[n+44>>2],a=w(k*Bt(e)),p[n+44>>2]=e+w(w(a-p[v[n+4>>2]+40>>2])*t),y=n,m=w(c+w(w(w(w(g(i))*Bt(c))-p[v[n+4>>2]+44>>2])*t)),void(p[y+48>>2]=m)}e:switch(0|u){case 0:c=Bt(e),s=p[v[n+4>>2]+44>>2];break r;case 1:case 2:a=p[n+44>>2],c=Bt(e),s=p[n+48>>2];break r;case 3:break e;default:break n}c=Bt(e),a=Bt(i),r=v[n+4>>2],p[n+44>>2]=w(c*w(g(p[n+44>>2])))+w(w(e-w(c*w(g(p[r+40>>2]))))*t),p[n+48>>2]=w(a*w(g(p[n+48>>2])))+w(w(i-w(a*w(g(p[r+44>>2]))))*t)}return}d=Bt(i),k=e,e=w(c*w(g(a))),p[n+44>>2]=w(w(k-e)*t)+e,e=w(d*w(g(s))),p[n+48>>2]=w(w(i-e)*t)+e},function(r){return v[36+(r|=0)>>2]+33554432|0},la,Qo,function(){return 21044},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a=w(0),c=0,b=w(0);r:if(n=v[v[n+20>>2]+(v[r+36>>2]<<2)>>2],l[n+117|0]){if(f=v[r+32>>2],p[f>>2]>i){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+52>>2]=p[r+48>>2],void(p[n+56>>2]=p[r+52>>2]);case 1:break n;default:break r}return r=v[n+4>>2],e=p[n+52>>2],p[n+52>>2]=w(w(p[r+48>>2]-e)*t)+e,e=p[n+56>>2],void(p[n+56>>2]=w(w(p[r+52>>2]-e)*t)+e)}o=v[r+24>>2],c=v[2721],p[f+(o-c<<2)>>2]<=i?(i=p[f+(o+v[2724]<<2)>>2],e=p[f+(o+v[2723]<<2)>>2]):(f=Pe(r+20|0,i,c),o=v[r+32>>2],e=p[o+(f+v[2723]<<2)>>2],b=p[o+(f+v[2724]<<2)>>2],a=i,i=p[o+(f<<2)>>2],a=bn(r,((0|f)/(0|c)|0)-1|0,w(w(1)-w(w(a-i)/w(p[o+(f+v[2722]<<2)>>2]-i)))),r=v[r+32>>2],i=w(b+w(a*w(p[r+(f+v[2726]<<2)>>2]-b))),e=w(e+w(a*w(p[r+(f+v[2725]<<2)>>2]-e))));n:switch(0|u){case 0:return r=v[n+4>>2],p[n+52>>2]=w(e*t)+p[r+48>>2],void(p[n+56>>2]=w(i*t)+p[r+52>>2]);case 1:case 2:return r=v[n+4>>2],a=w(e+p[r+48>>2]),e=p[n+52>>2],p[n+52>>2]=w(w(a-e)*t)+e,e=p[n+56>>2],void(p[n+56>>2]=w(w(w(i+p[r+52>>2])-e)*t)+e);case 3:break n;default:break r}p[n+52>>2]=w(e*t)+p[n+52>>2],p[n+56>>2]=w(i*t)+p[n+56>>2]}},function(r){return v[36+(r|=0)>>2]+50331648|0},Ge,function(r){Ge(r|=0),at(r)},Du,function(r){at(Du(r|=0))},Ou,function(r){at(Ou(r|=0))},Eu,function(r){at(Eu(r|=0))},Pu,function(r){at(Pu(r|=0))},Cu,function(r){at(Cu(r|=0))},la,bi,function(r){bi(r|=0),at(r)},ka,Zu,function(r){at(Zu(r|=0))},function(r){return v[(r|=0)>>2]=10296,Wu(r+36|0),Ku(r+20|0),Wu(r+4|0),0|r},function(r){v[(r|=0)>>2]=10296,Wu(r+36|0),Ku(r+20|0),Wu(r+4|0),at(r)},function(r){return v[(r|=0)>>2]=10312,vo(r+4|0),0|r},function(r){v[(r|=0)>>2]=10312,vo(r+4|0),at(r)},Wu,function(r){at(Wu(r|=0))},Ku,function(r){at(Ku(r|=0))},function(r){return v[(r|=0)>>2]=10360,vo(r+208|0),vo(r+192|0),lo(r+176|0),vo(r+160|0),vo(r+144|0),vo(r+128|0),Wi(r+4|0),0|r},function(r){v[(r|=0)>>2]=10360,vo(r+208|0),vo(r+192|0),lo(r+176|0),vo(r+160|0),vo(r+144|0),vo(r+128|0),Wi(r+4|0),at(r)},Yu,function(r){at(Yu(r|=0))},Xu,function(r){at(Xu(r|=0))},ju,function(r){at(ju(r|=0))},et,function(r){at(et(r|=0))},nt,function(r){at(nt(r|=0))},kn,function(r){kn(r|=0),at(r)},Nu,function(r){at(Nu(r|=0))},Bu,function(r){at(Bu(r|=0))},Ju,function(r){at(Ju(r|=0))},xu,function(r){at(xu(r|=0))},_u,function(r){at(_u(r|=0))},Mu,function(r){at(Mu(r|=0))},Lu,function(r){at(Lu(r|=0))},Hu,function(r){at(Hu(r|=0))},$i,function(r){$i(r|=0),at(r)},function(r){return v[(r|=0)>>2]=10616,vo(r+20|0),Fu(r+4|0),0|r},function(r){v[(r|=0)>>2]=10616,vo(r+20|0),Fu(r+4|0),at(r)},Fu,function(r){at(Fu(r|=0))},function(r){return v[(r|=0)>>2]=10648,qu(r+4|0),0|r},function(r){v[(r|=0)>>2]=10648,qu(r+4|0),at(r)},rn,function(r){rn(r|=0),at(r)},qu,function(r){at(qu(r|=0))},se,function(r){at(se(r|=0))},Vu,function(r){at(Vu(r|=0))},function(r){return v[(r|=0)>>2]=10728,vo(r+68|0),0|r},function(r){v[(r|=0)>>2]=10728,vo(r+68|0),at(r)},function(r){return v[(r|=0)>>2]=10744,lf(r+68|0),lf(r+8|0),0|r},function(r){v[(r|=0)>>2]=10744,lf(r+68|0),lf(r+8|0),at(r)},lf,function(r){lf(r|=0),at(r)},la,Yo,va,function(){return 21080},la,function(r){return v[(r|=0)>>2]=10804,so(r+8|0),0|r},function(r){v[(r|=0)>>2]=10804,so(r+8|0),at(r)},function(){return 21092},function(r){r|=0;var n=w(0),e=0,i=0,t=w(0),u=0,o=w(0),a=w(0),c=0,b=0,k=w(0),s=w(0),h=w(0),y=w(0),F=w(0),A=w(0),$=w(0),I=w(0),C=w(0),P=w(0),E=w(0),O=w(0),R=w(0),S=w(0),W=0,G=0,U=0,j=w(0);if(i=v[r+4>>2],b=l[i+84|0],l[i+85|0]){if(b){if(k=p[r+40>>2],s=p[r+36>>2],h=p[r+32>>2],a=p[r+28>>2],u=v[r+24>>2],l[u+88|0]||Fr(u),v[r+12>>2])for(;i=v[v[r+20>>2]+(c<<2)>>2],l[i+88|0]||Fr(i),o=p[i+68>>2],o=a!=w(0)?w(w(w(p[u+68>>2]+p[v[r+4>>2]+60>>2])*a)+o):o,$=p[i+64>>2],I=p[i+60>>2],h!=w(0)&&(b=v[r+4>>2],$=w(w(w(p[u+64>>2]+p[b+68>>2])*h)+$),I=w(w(w(p[u+60>>2]+p[b+64>>2])*h)+I)),n=p[i+76>>2],t=p[i+72>>2],s!=w(0)&&(t=t>w(9999999747378752e-21)?w(t*w(w(w(w(p[u+72>>2]+w(-1))+p[v[r+4>>2]+72>>2])*s)+w(1))):t,n>w(9999999747378752e-21)&&(n=w(n*w(w(w(w(p[u+76>>2]+w(-1))+p[v[r+4>>2]+76>>2])*s)+w(1))))),C=p[i+84>>2],y=p[i+80>>2],k!=w(0)&&(C=w(w(w(p[u+84>>2]+p[v[r+4>>2]+80>>2])*k)+C)),cr(i,I,$,o,t,n,y,C),(c=c+1|0)>>>0<d[r+12>>2];);return}if(y=p[r+40>>2],k=p[r+36>>2],a=p[r+32>>2],C=p[r+28>>2],b=v[r+24>>2],l[b+88|0]||Fr(b),v[r+12>>2])for(;u=v[v[r+20>>2]+(c<<2)>>2],l[u+88|0]||Fr(u),s=p[u+68>>2],C!=w(0)&&(n=w(w(p[b+68>>2]-s)+p[v[r+4>>2]+60>>2]),G=+w(n/w(-360))+16384.499999999996,i=g(G)<2147483648?~~G:-2147483648,s=w(w(w(n-w(0|m(16384-i|0,360)))*C)+s)),h=p[u+64>>2],o=p[u+60>>2],a!=w(0)&&(i=v[r+4>>2],h=w(w(w(w(p[b+64>>2]-h)+p[i+68>>2])*a)+h),o=w(w(w(w(p[b+60>>2]-o)+p[i+64>>2])*a)+o)),n=p[u+76>>2],t=p[u+72>>2],k!=w(0)&&(t=t>w(9999999747378752e-21)?w(w(w(w(w(p[b+72>>2]-t)+p[v[r+4>>2]+72>>2])*k)+t)/t):t,n>w(9999999747378752e-21)&&(n=w(w(w(w(w(p[b+76>>2]-n)+p[v[r+4>>2]+76>>2])*k)+n)/n))),I=p[u+84>>2],y!=w(0)&&($=w(w(p[b+84>>2]-I)+p[v[r+4>>2]+80>>2]),G=+w($/w(-360))+16384.499999999996,i=g(G)<2147483648?~~G:-2147483648,p[u+56>>2]=w(w($-w(0|m(16384-i|0,360)))*y)+p[u+56>>2]),cr(u,o,h,s,t,n,p[u+80>>2],I),(c=c+1|0)>>>0<d[r+12>>2];);}else if(b){if(D=u=D-16|0,v[r+12>>2])for(i=v[r+4>>2],c=v[r+24>>2],F=p[c+92>>2],P=p[c+108>>2],A=p[c+104>>2],E=p[c+96>>2],n=w(w(F*P)-w(A*E))>w(0)?w(.01745329238474369):w(-.01745329238474369),C=w(p[i+80>>2]*n),I=w(p[i+60>>2]*n),R=p[r+40>>2],S=p[r+36>>2],s=p[r+32>>2],b=(h=p[r+28>>2])!=w(0),$=w(w(T(w(w(E*E)+w(P*P))))+w(-1)),o=w(w(T(w(w(F*F)+w(A*A))))+w(-1));;){e=v[v[r+20>>2]+(W<<2)>>2],h!=w(0)&&(k=p[e+108>>2],a=p[e+104>>2],y=p[e+96>>2],t=p[e+92>>2],(n=w(I+Pr(A,F)))>w(3.1415927410125732)?n=w(n+w(-6.2831854820251465)):n<w(-3.1415927410125732)&&(n=w(n+w(6.2831854820251465))),O=Rr(n=w(h*n)),n=Wr(n),p[e+108>>2]=w(n*y)+w(k*O),p[e+104>>2]=w(n*t)+w(a*O),p[e+96>>2]=w(O*y)-w(k*n),p[e+92>>2]=w(O*t)-w(a*n)),i=b,s!=w(0)&&(i=v[r+4>>2],Zi(c,p[i+64>>2],p[i+68>>2],u+12|0,u+8|0),p[e+100>>2]=w(p[u+12>>2]*s)+p[e+100>>2],p[e+112>>2]=w(p[u+8>>2]*s)+p[e+112>>2],i=1);r:{n:{e:{if(!(S>w(0))){if(R>w(0))break e;if(i)break n;break r}if(i=v[r+4>>2],n=w(w(w(o+p[i+72>>2])*S)+w(1)),p[e+92>>2]=p[e+92>>2]*n,p[e+104>>2]=n*p[e+104>>2],n=w(w(w($+p[i+76>>2])*S)+w(1)),p[e+96>>2]=p[e+96>>2]*n,p[e+108>>2]=n*p[e+108>>2],!(R>w(0)))break n}(n=w(Pr(P,E)-Pr(A,F)))>w(3.1415927410125732)?n=w(n+w(-6.2831854820251465)):n<w(-3.1415927410125732)&&(n=w(n+w(6.2831854820251465))),t=w(w(C+w(n+w(-1.5707963705062866)))*R),y=p[e+108>>2],n=p[e+96>>2],t=w(t+Pr(y,n)),n=w(T(w(w(n*n)+w(y*y)))),U=e,j=w(Wr(t)*n),p[U+108>>2]=j,U=e,j=w(Rr(t)*n),p[U+96>>2]=j}f[e+88|0]=0}if(!((W=W+1|0)>>>0<d[r+12>>2]))break}D=u+16|0}else{if(D=u=D-16|0,v[r+12>>2])for(i=v[r+4>>2],c=v[r+24>>2],F=p[c+92>>2],P=p[c+108>>2],A=p[c+104>>2],E=p[c+96>>2],n=w(w(F*P)-w(A*E))>w(0)?w(.01745329238474369):w(-.01745329238474369),y=w(p[i+80>>2]*n),C=w(p[i+60>>2]*n),R=p[r+40>>2],S=p[r+36>>2],s=p[r+32>>2],b=(h=p[r+28>>2])!=w(0),I=w(T(w(w(E*E)+w(P*P)))),$=w(T(w(w(F*F)+w(A*A))));;){e=v[v[r+20>>2]+(W<<2)>>2],h!=w(0)&&(k=p[e+108>>2],a=p[e+96>>2],n=Pr(A,F),o=p[e+104>>2],t=p[e+92>>2],(n=w(C+w(n-Pr(o,t))))>w(3.1415927410125732)?n=w(n+w(-6.2831854820251465)):n<w(-3.1415927410125732)&&(n=w(n+w(6.2831854820251465))),O=Rr(n=w(h*n)),n=Wr(n),p[e+108>>2]=w(n*a)+w(k*O),p[e+104>>2]=w(n*t)+w(o*O),p[e+96>>2]=w(O*a)-w(k*n),p[e+92>>2]=w(O*t)-w(o*n)),i=b,s!=w(0)&&(i=v[r+4>>2],Zi(c,p[i+64>>2],p[i+68>>2],u+12|0,u+8|0),n=p[e+100>>2],p[e+100>>2]=w(w(p[u+12>>2]-n)*s)+n,n=p[e+112>>2],p[e+112>>2]=w(w(p[u+8>>2]-n)*s)+n,i=1);r:{n:{e:{i:{if(S>w(0)){if(o=p[e+92>>2],t=p[e+104>>2],(n=w(T(w(w(o*o)+w(t*t)))))>w(9999999747378752e-21)&&(n=w(w(w(w(w($-n)+p[v[r+4>>2]+72>>2])*S)+n)/n)),k=w(t*n),p[e+104>>2]=k,t=w(o*n),p[e+92>>2]=t,a=p[e+96>>2],o=p[e+108>>2],(n=w(T(w(w(a*a)+w(o*o)))))>w(9999999747378752e-21)&&(n=w(w(w(w(w(I-n)+p[v[r+4>>2]+76>>2])*S)+n)/n)),o=w(o*n),p[e+108>>2]=o,n=w(a*n),p[e+96>>2]=n,R>w(0))break i;break n}if(!(R>w(0)))break e;t=p[e+92>>2],k=p[e+104>>2],o=p[e+108>>2],n=p[e+96>>2]}a=Pr(o,n),(t=w(w(Pr(P,E)-Pr(A,F))-w(a-Pr(k,t))))>w(3.1415927410125732)?t=w(t+w(-6.2831854820251465)):t<w(-3.1415927410125732)&&(t=w(t+w(6.2831854820251465))),o=w(T(w(w(n*n)+w(o*o)))),n=w(w(w(y+t)*R)+a),U=e,j=w(o*Wr(n)),p[U+108>>2]=j,U=e,j=w(o*Rr(n)),p[U+96>>2]=j;break n}if(!i)break r}f[e+88|0]=0}if(!((W=W+1|0)>>>0<d[r+12>>2]))break}D=u+16|0}},So,ko,pu,la,function(r){return v[(r|=0)>>2]=10840,bo(r+24|0),0|Mt(r)},function(r){v[(r|=0)>>2]=10840,bo(r+24|0),at(Mt(r))},function(){return 21104},la,function(r){return v[(r|=0)>>2]=10864,vo(r+20|0),0|Ut(r)},function(r){v[(r|=0)>>2]=10864,vo(r+20|0),at(Ut(r))},function(){return 21116},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a=w(0),c=w(0),b=w(0),k=0,s=w(0);r:if(n=v[v[n+84>>2]+(v[r+36>>2]<<2)>>2],0|da[v[v[n>>2]+16>>2]](n)){if(f=v[r+32>>2],p[f>>2]>i){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+28>>2]=p[r+44>>2],p[n+32>>2]=p[r+48>>2],p[n+36>>2]=p[r+52>>2],void(p[n+40>>2]=p[r+56>>2]);case 1:break n;default:break r}return r=v[n+4>>2],e=p[n+28>>2],p[n+28>>2]=w(w(p[r+44>>2]-e)*t)+e,e=p[n+32>>2],p[n+32>>2]=w(w(p[r+48>>2]-e)*t)+e,e=p[n+36>>2],p[n+36>>2]=w(w(p[r+52>>2]-e)*t)+e,e=p[n+40>>2],void(p[n+40>>2]=w(w(p[r+56>>2]-e)*t)+e)}if(f=f+(v[r+24>>2]<<2)|0,p[f-20>>2]<=i?(i=p[f-4>>2],e=p[f-8>>2],c=p[f-12>>2],a=p[f-16>>2]):(f=(k=(o=Pe(r+20|0,i,5))<<2)+v[r+32>>2]|0,b=p[f-16>>2],c=p[f-12>>2],e=p[f-8>>2],s=p[f-4>>2],a=i,i=p[f>>2],a=bn(r,((0|o)/5|0)-1|0,w(w(1)-w(w(a-i)/w(p[f-20>>2]-i)))),r=v[r+32>>2]+k|0,i=w(s+w(a*w(p[r+16>>2]-s))),e=w(e+w(a*w(p[r+12>>2]-e))),c=w(c+w(a*w(p[r+8>>2]-c))),a=w(b+w(a*w(p[r+4>>2]-b)))),!u)return b=a,r=v[n+4>>2],a=p[r+44>>2],p[n+28>>2]=w(w(b-a)*t)+a,a=p[r+48>>2],p[n+32>>2]=w(w(c-a)*t)+a,a=e,e=p[r+52>>2],p[n+36>>2]=w(w(a-e)*t)+e,e=p[r+56>>2],void(p[n+40>>2]=w(w(i-e)*t)+e);b=a,a=p[n+28>>2],p[n+28>>2]=w(w(b-a)*t)+a,a=p[n+32>>2],p[n+32>>2]=w(w(c-a)*t)+a,a=e,e=p[n+36>>2],p[n+36>>2]=w(w(a-e)*t)+e,e=p[n+40>>2],p[n+40>>2]=w(w(i-e)*t)+e}},function(r){return v[36+(r|=0)>>2]+167772160|0},la,function(r){mt(r|=0),at(r)},function(){return 21128},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a=w(0),c=0,b=w(0);r:if(n=v[v[n+20>>2]+(v[r+36>>2]<<2)>>2],l[n+117|0]){if(f=v[r+32>>2],p[f>>2]>i){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+32>>2]=p[r+28>>2],void(p[n+36>>2]=p[r+32>>2]);case 1:break n;default:break r}return r=v[n+4>>2],e=p[n+32>>2],p[n+32>>2]=w(w(p[r+28>>2]-e)*t)+e,e=p[n+36>>2],void(p[n+36>>2]=w(w(p[r+32>>2]-e)*t)+e)}f=f+(v[r+24>>2]<<2)|0,p[f-12>>2]<=i?(i=p[f-4>>2],e=p[f-8>>2]):(f=(c=(o=Pe(r+20|0,i,3))<<2)+v[r+32>>2]|0,e=p[f-8>>2],b=p[f-4>>2],a=i,i=p[f>>2],a=bn(r,((0|o)/3|0)-1|0,w(w(1)-w(w(a-i)/w(p[f-12>>2]-i)))),r=v[r+32>>2]+c|0,i=w(b+w(a*w(p[r+8>>2]-b))),e=w(e+w(a*w(p[r+4>>2]-e))));n:switch(0|u){case 0:return r=v[n+4>>2],p[n+32>>2]=w(e*t)+p[r+28>>2],void(p[n+36>>2]=w(i*t)+p[r+32>>2]);case 1:case 2:return r=v[n+4>>2],a=w(e+p[r+28>>2]),e=p[n+32>>2],p[n+32>>2]=w(w(a-e)*t)+e,e=p[n+36>>2],void(p[n+36>>2]=w(w(w(i+p[r+32>>2])-e)*t)+e);case 3:break n;default:break r}p[n+32>>2]=w(e*t)+p[n+32>>2],p[n+36>>2]=w(i*t)+p[n+36>>2]}},function(r){return v[36+(r|=0)>>2]+16777216|0},Wi,function(r){Wi(r|=0),at(r)},la,function(r){return v[(r|=0)>>2]=10964,vo(r+20|0),0|Ut(r)},function(r){v[(r|=0)>>2]=10964,vo(r+20|0),at(Ut(r))},function(){return 21140},function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0),b=w(0),k=w(0),s=w(0),l=w(0),h=w(0),d=0,y=0,m=0,g=0,F=w(0);r:if(n=v[v[n+36>>2]+(v[r+36>>2]<<2)>>2],f=v[n+8>>2],0|da[v[v[f>>2]+16>>2]](f)){if(d=n+36|0,y=n+16|0,f=Ko(v[n+4>>2]),o=fa(v[n+4>>2]),a=v[r+32>>2],p[a>>2]>i){n:switch(0|u){case 0:return p[n+20>>2]=p[f+4>>2],p[n+24>>2]=p[f+8>>2],p[n+28>>2]=p[f+12>>2],p[n+32>>2]=p[f+16>>2],ci(y),p[n+40>>2]=p[o+4>>2],p[n+44>>2]=p[o+8>>2],p[n+48>>2]=p[o+12>>2],p[n+52>>2]=p[o+16>>2],void ci(d);case 1:break n;default:break r}return e=p[n+20>>2],p[n+20>>2]=w(w(e-p[f+4>>2])*t)+e,e=p[n+24>>2],p[n+24>>2]=w(w(e-p[f+8>>2])*t)+e,e=p[n+28>>2],p[n+28>>2]=w(w(e-p[f+12>>2])*t)+e,e=p[n+32>>2],p[n+32>>2]=w(w(e-p[f+16>>2])*t)+e,e=p[n+40>>2],p[n+40>>2]=w(w(e-p[o+4>>2])*t)+e,e=p[n+44>>2],p[n+44>>2]=w(w(e-p[o+8>>2])*t)+e,e=p[n+48>>2],void(p[n+48>>2]=w(w(e-p[o+12>>2])*t)+e)}if(a=a+(v[r+24>>2]<<2)|0,p[a-32>>2]<=i?(i=p[a-4>>2],b=p[a-8>>2],k=p[a-12>>2],c=p[a-16>>2],s=p[a-20>>2],l=p[a-24>>2],e=p[a-28>>2]):(a=(g=(m=Pe(r+20|0,i,8))<<2)+v[r+32>>2]|0,h=p[a-28>>2],l=p[a-24>>2],s=p[a-20>>2],c=p[a-16>>2],k=p[a-12>>2],b=p[a-8>>2],F=p[a-4>>2],e=p[a>>2],e=bn(r,(m>>>3|0)-1|0,w(w(1)-w(w(i-e)/w(p[a-32>>2]-e)))),r=v[r+32>>2]+g|0,i=w(F+w(e*w(p[r+28>>2]-F))),b=w(b+w(e*w(p[r+24>>2]-b))),k=w(k+w(e*w(p[r+20>>2]-k))),c=w(c+w(e*w(p[r+16>>2]-c))),s=w(s+w(e*w(p[r+12>>2]-s))),l=w(l+w(e*w(p[r+8>>2]-l))),e=w(h+w(e*w(p[r+4>>2]-h)))),t==w(1))return p[n+32>>2]=c,p[n+28>>2]=s,p[n+24>>2]=l,p[n+20>>2]=e,ci(y),v[n+52>>2]=1065353216,p[n+48>>2]=i,p[n+44>>2]=b,p[n+40>>2]=k,void ci(d);u||(p[n+20>>2]=p[f+4>>2],p[n+24>>2]=p[f+8>>2],p[n+28>>2]=p[f+12>>2],p[n+32>>2]=p[f+16>>2],ci(y),p[n+40>>2]=p[o+4>>2],p[n+44>>2]=p[o+8>>2],p[n+48>>2]=p[o+12>>2],p[n+52>>2]=p[o+16>>2],ci(d)),h=p[n+32>>2],p[n+32>>2]=h+w(w(c-h)*t),c=p[n+28>>2],p[n+28>>2]=c+w(w(s-c)*t),c=p[n+24>>2],p[n+24>>2]=c+w(w(l-c)*t),c=p[n+20>>2],p[n+20>>2]=c+w(w(e-c)*t),ci(y),p[n+52>>2]=p[n+52>>2]+w(0),e=p[n+48>>2],p[n+48>>2]=e+w(w(i-e)*t),e=p[n+44>>2],p[n+44>>2]=e+w(w(b-e)*t),e=p[n+40>>2],p[n+40>>2]=e+w(w(k-e)*t),ci(d)}},function(r){return v[36+(r|=0)>>2]+234881024|0},la,Yo,va,function(){return 21152},la,va,function(){return 21164},la,la,la,ka,function(){return 21192},ta,function(r,n,e){r|=0,n|=0,e|=0;var i=w(0),f=w(0),t=w(0),u=0,o=w(0);f=p[r+8>>2],i=p[r+4>>2],t=w(-i),u=n,o=w(ne(t,i,w(w(i-i)*w(.5)))+p[n>>2]),p[u>>2]=o,u=e,o=w(ne(t,f,w(w(f-i)*w(.5)))+p[e>>2]),p[u>>2]=o},la,ka,function(){return 21204},function(r,n){n|=0,p[20+(r|=0)>>2]=p[n+172>>2]+p[r+4>>2],p[r+24>>2]=p[n+176>>2]+p[r+8>>2]},function(r,n,e){r|=0,n|=0,e|=0;var i=w(0),f=w(0),t=w(0),u=w(0),o=0;t=w(p[n>>2]-p[r+20>>2]),u=w(p[e>>2]-p[r+24>>2]),(i=w(T(w(w(t*t)+w(u*u)))))<(f=p[r+12>>2])&&(o=v[r+28>>2],f=Wr(i=w(da[v[v[o>>2]+8>>2]](o,w(0),p[r+16>>2],w(w(f-i)/f)))),i=Rr(i),p[n>>2]=p[r+20>>2]+w(w(i*t)-w(u*f)),p[e>>2]=w(w(f*t)+w(u*i))+p[r+24>>2])},la,uu,uf,function(r){uf(r|=0),or(r)},Yo,ka,function(r,n){r|=0;var e=0,i=0,f=0,t=0,u=0,o=0,a=0,c=0;r:{if(lu(0|da[v[v[(n|=0)>>2]+8>>2]](n),21008)){r=v[r+8>>2],i=v[n+24>>2],f=Tt(12),e=-1;n:if(u=v[r+8>>2])for(o=v[r+16>>2],i=v[i+4>>2],r=0;;){if((0|i)==v[o+(r<<2)>>2]){e=r;break n}if((0|u)==(0|(r=r+1|0)))break}for(te(f,4,20568,6,e),i=v[n+112>>2],u=v[v[f+4>>2]>>2],r=0,e=0;o=u+m(r,24)|0,t=e<<2,p[o+12>>2]=p[t+i>>2],p[o+16>>2]=p[i+(4|t)>>2],e=e+2|0,4!=(0|(r=r+1|0)););(e=v[n+28>>2])&&(!(r=v[n+24>>2])|(0|r)==(0|f)||da[0|e](r)),v[n+24>>2]=f,r=n+28|0}else{if(!lu(0|da[v[v[n>>2]+8>>2]](n),20912))break r;i=v[n+68>>2],r=v[r+8>>2],f=Tt(12),u=v[n+52>>2]>>>1|0,o=v[n+140>>2],t=v[n+148>>2],e=-1;n:if(a=v[r+8>>2])for(c=v[r+16>>2],i=v[i+4>>2],r=0;;){if((0|i)==v[(r<<2)+c>>2]){e=r;break n}if((0|a)==(0|(r=r+1|0)))break}if(te(f,u,t,o,e),r=v[n+52>>2])for(u=v[v[f+4>>2]>>2],o=r-1>>>1|0,i=v[n+116>>2],e=0,r=0;t=u+m(r,24)|0,a=e<<2,p[t+12>>2]=p[a+i>>2],p[t+16>>2]=p[i+(4|a)>>2],e=e+2|0,t=(0|r)==(0|o),r=r+1|0,!t;);(e=v[n+72>>2])&&(!(r=v[n+68>>2])|(0|r)==(0|f)||da[0|e](r)),v[n+68>>2]=f,r=n+72|0}v[r>>2]=385}},Iu,function(r){at(Iu(r|=0))},function(r,n,e,i){n|=0,e|=0,i|=0,r=v[8+(r|=0)>>2],v[5369]=e,v[5145]=n,v[5370]=i,(r=v[r+36>>2])&&(v[5368]=r,q())},function(r,n,e,i){n|=0,e|=0,i|=0,(r=v[8+(r|=0)>>2])&&(v[e+8>>2]&&(r=v[r+40>>2],v[5145]=n,v[5368]=r,v[5369]=e,v[5370]=i,N()),3==(0|n)&&(r=v[e+8>>2])&&((n=v[e+12>>2])&&da[0|n](r),v[e+8>>2]=0,v[e+12>>2]=0))},$u,function(r){at($u(r|=0))},Tu,function(r){at(Tu(r|=0))},ka,function(){return 21285},uu,yo,function(){return 0|xf(11192)},un,tf,Go,wf,function(r,n){return n|=0,v[12+(r|=0)>>2]+m(n,20)|0},function(r,n,e){n|=0,e|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),r=0|da[0|i](n,e),n=Tt(20),v[n+16>>2]=v[r+16>>2],e=v[r+12>>2],v[n+8>>2]=v[r+8>>2],v[n+12>>2]=e,e=v[r+4>>2],v[n>>2]=v[r>>2],v[n+4>>2]=e,0|n},function(){return 21291},uu,yo,function(){return 0|xf(8712)},yn,function(r,n,e,i){var f,t;r|=0,n|=0,e|=0,i=w(i),D=f=D-16|0,n=((t=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,p[f+12>>2]=i,da[0|r](n,e,f+12|0),D=f+16|0},Go,wf,St,function(r,n,e){n|=0,e|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),w(p[da[0|i](n,e)>>2])},function(r,n,e){n|=0,e|=0,p[v[12+(r|=0)>>2]+(n<<2)>>2]=p[e>>2]},function(){return 21296},uu,yo,function(){return 0|xf(9400)},function(r,n,e){r|=0,n|=0,e|=0;var i=0,f=w(0),t=0,u=0,o=0;if(i=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(f=w(w(n>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5208],u=r,o=0|da[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<4,8524,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>i>>>0)for(;xn(v[r+12>>2]+(i<<4)|0,e),(i=i+1|0)>>>0<d[r+4>>2];);},tf,Go,wf,Et,function(r,n,e){n|=0,e|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),r=0|da[0|i](n,e),0|xn(Xf(16),r)},cf,function(){return 21299},uu,yo,function(){return 0|xf(8680)},mn,hi,Go,wf,St,ef,ot,function(){return 21302},uu,yo,function(){return 0|xf(11176)},mn,hi,Go,wf,St,ef,ot,function(){return 21306},uu,yo,function(){return 0|xf(9444)},function(r,n,e){r|=0,n|=0,e|=0;var i=0,f=w(0),t=0,u=0,o=0;if(i=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(f=w(w(n>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5208],u=r,o=0|da[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<4,8524,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>i>>>0)for(;Jn(v[r+12>>2]+(i<<4)|0,e),(i=i+1|0)>>>0<d[r+4>>2];);},tf,Go,wf,Et,function(r,n,e){n|=0,e|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),r=0|da[0|i](n,e),0|Jn(Xf(16),r)},cf,function(){return 21309},uu,yo,function(){return 0|xf(10632)},function(r,n,e){r|=0,n|=0,e|=0;var i=0,f=w(0),t=0,u=0,o=0;if(i=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(f=w(w(n>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5208],u=r,o=0|da[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<2,8524,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>i>>>0)for(n=v[r+12>>2];v[n+(i<<2)>>2]=v[e>>2],(i=i+1|0)>>>0<d[r+4>>2];);},hi,Go,wf,St,ef,ot,function(){return 21312},uu,yo,function(){return 0|xf(9788)},pn,function(r,n,e,i){var f,t;n|=0,e|=0,i|=0,D=f=D-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,s[f+14>>1]=i,da[0|r](n,e,f+14|0),D=f+16|0},Go,wf,function(r,n){return n|=0,v[12+(r|=0)>>2]+(n<<1)|0},function(r,n,e){n|=0,e|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),h[da[0|i](n,e)>>1]},function(r,n,e){n|=0,e|=0,s[v[12+(r|=0)>>2]+(n<<1)>>1]=h[e>>1]},function(){return 21316},uu,yo,function(){return 0|xf(9148)},function(r,n,e){r|=0,n|=0,e|=0;var i=0,f=w(0),t=0,u=0,o=0;if(i=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(f=w(w(n>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5208],u=r,o=0|da[v[v[t>>2]+16>>2]](t,v[r+12>>2],m(n,12),8524,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>i>>>0)for(;Re(v[r+12>>2]+m(i,12)|0,e),(i=i+1|0)>>>0<d[r+4>>2];);},Ne,Go,wf,function(r,n){return n|=0,v[12+(r|=0)>>2]+m(n,12)|0},function(r,n,e){n|=0,e|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),e=0|da[0|i](n,e),n=Y((r=v[e+4>>2])+4|0),v[n>>2]=r,Ff(n+4|0,v[e+8>>2],r),0|n},function(r,n,e){n|=0,e|=0,ee(v[12+(r|=0)>>2]+m(n,12)|0,e)},function(){return 21319},uu,yo,function(){return 0|xf(9196)},Fn,hi,Go,wf,St,ef,function(){return 21322},uu,yo,function(){return 0|xf(9612)},Fn,hi,Go,wf,St,ef,function(){return 21326},uu,yo,function(){return 0|xf(10472)},Fn,hi,Go,wf,St,ef,function(){return 21330},uu,yo,function(){return 0|xf(10552)},Fn,hi,Go,wf,St,ef,function(){return 21333},uu,yo,function(){return 0|xf(10568)},Fn,hi,Go,wf,St,ef,function(){return 21336},uu,yo,function(){return 0|xf(10712)},Fn,hi,Go,wf,St,ef,function(){return 21339},uu,yo,function(){return 0|xf(10168)},Fn,hi,Go,wf,St,ef,function(){return 21343},uu,yo,function(){return 0|xf(10488)},Fn,hi,Go,wf,St,ef,function(){return 21347},uu,yo,function(){return 0|xf(10504)},Fn,hi,Go,wf,St,ef,function(){return 21351},uu,yo,function(){return 0|xf(8760)},Fn,hi,Go,wf,St,ef,function(){return 21355},uu,yo,function(){return 0|xf(10520)},Fn,hi,Go,wf,St,ef,function(){return 21359},uu,yo,function(){return 0|xf(10184)},Fn,hi,Go,wf,St,ef,function(){return 21362},uu,yo,function(){return 0|xf(10536)},Fn,hi,Go,wf,St,ef,function(){return 21365},uu,yo,function(){return 0|xf(10200)},Fn,hi,Go,wf,St,ef,function(){return 21368},uu,yo,function(){return 0|xf(10216)},Fn,hi,Go,wf,St,ef,function(){return 21371},uu,yo,function(){return 0|xf(1048)},Fn,hi,Go,wf,St,ef,ot,function(){return 21374},uu,yo,function(){return 0|xf(8696)},Fn,hi,Go,wf,St,ef,function(){return 21378},uu,yo,function(){return 0|xf(10232)},Fn,hi,Go,wf,St,ef,function(){return 21381},uu,yo,function(){return 0|xf(12864)},Fn,hi,Go,wf,St,ef,function(){return 21385},uu,yo,function(){return 0|xf(12928)},function(r,n,e){r|=0,n|=0,e|=0;var i=0,f=w(0),t=0,u=0,o=0;if(i=v[r+4>>2],v[r+4>>2]=n,d[r+8>>2]<n>>>0&&(f=w(w(n>>>0)*w(1.75)),n=(n=w(g(f))<w(2147483648)?~~f:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5208],u=r,o=0|da[v[v[t>>2]+16>>2]](t,v[r+12>>2],n<<4,8524,85),v[u+12>>2]=o,n=v[r+4>>2]),n>>>0>i>>>0)for(;Ro(v[r+12>>2]+(i<<4)|0,e),(i=i+1|0)>>>0<d[r+4>>2];);},tf,Go,wf,Et,function(r,n,e){n|=0,e|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),r=0|da[0|i](n,e),0|Ro(Xf(16),r)},function(){return 21388},qo,yo,function(){var r;return r=Tt(8),v[r>>2]=0,v[r+4>>2]=0,0|r},Qi,function(r,n){return r|=0,n|=0,0|ft(Tt(8),p[r>>2],p[n>>2])},Lt,Rt,ft,function(r,n,e,i){r|=0,n|=0,e=w(e),i=w(i);var f=0;return f=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),r=0|da[0|f](n,e,i),n=Tt(8),f=v[r+4>>2],v[n>>2]=v[r>>2],v[n+4>>2]=f,0|n},function(r){r|=0;var n=w(0),e=w(0);return n=p[r>>2],e=w(n*n),n=p[r+4>>2],w(w(T(w(e+w(n*n)))))},kf,function(r){r|=0;var n=w(0),e=w(0),i=w(0);return n=p[r>>2],e=p[r+4>>2],i=w(w(1)/w(T(w(w(n*n)+w(e*e))))),p[r+4>>2]=e*i,p[r>>2]=n*i,0|r},function(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),r=0|da[0|e](n),n=Tt(8),e=v[r+4>>2],v[n>>2]=v[r>>2],v[n+4>>2]=e,0|n},function(){return 21391},uu,yo,function(){var r;return r=Xf(20),v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=9308,v[r+12>>2]=0,v[r+16>>2]=0,0|r},function(r,n,e,i,f){var t;return r|=0,n=w(n),e=w(e),i=w(i),f=w(f),D=t=D-16|0,p[t+12>>2]=n,p[t+8>>2]=e,p[t+4>>2]=i,p[t>>2]=f,r=0|da[0|r](t+12|0,t+8|0,t+4|0,t),D=t+16|0,0|r},function(r,n,e,i){r|=0,n|=0,e|=0,i|=0;var f,t=w(0),u=w(0),o=w(0);return f=Xf(20),t=p[r>>2],u=p[n>>2],o=p[e>>2],p[f+16>>2]=p[i>>2],p[f+12>>2]=o,p[f+8>>2]=u,p[f+4>>2]=t,v[f>>2]=9308,ci(f),0|f},function(r,n,e,i,f){return r|=0,n=w(n),e=w(e),i=w(i),f=w(f),p[r+16>>2]=f,p[r+12>>2]=i,p[r+8>>2]=e,p[r+4>>2]=n,ci(r),0|r},function(r,n,e,i,f,t){r|=0,n|=0,e=w(e),i=w(i),f=w(f),t=w(t);var u=0;return u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),n=0|da[0|u](n,e,i,f,t),r=Xf(20),v[r>>2]=9308,u=v[n+8>>2],v[r+4>>2]=v[n+4>>2],v[r+8>>2]=u,u=v[n+16>>2],v[r+12>>2]=v[n+12>>2],v[r+16>>2]=u,0|r},function(r,n,e,i,f){return r|=0,n=w(n),e=w(e),i=w(i),f=w(f),p[r+4>>2]=p[r+4>>2]+n,p[r+8>>2]=p[r+8>>2]+e,p[r+12>>2]=p[r+12>>2]+i,p[r+16>>2]=p[r+16>>2]+f,ci(r),0|r},ci,function(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),n=0|da[0|e](n),r=Xf(20),v[r>>2]=9308,e=v[n+8>>2],v[r+4>>2]=v[n+4>>2],v[r+8>>2]=e,e=v[n+16>>2],v[r+12>>2]=v[n+12>>2],v[r+16>>2]=e,0|r},Lt,Rt,wt,Nt,nf,function(){return 21394},uu,yo,function(){var r;return r=Tt(12),v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=8664,0|r},lt,uu,vf,function(r){return r|=0,0|rt(Xf(24),r)},No,ti,Dt,_t,nu,Jt,lt,Yo,Yo,uu,vf,function(r){return r|=0,0|Ve(Xf(60),r)},Ko,Dt,_t,Dt,_t,nu,Jt,Lt,Rt,lt,Yo,Yo,uu,vf,function(r){return r|=0,0|Yf(Xf(76),r)},Ko,Dt,_t,Dt,_t,Dt,_t,Dt,_t,Lt,Rt,function(){return 21400},uu,yo,function(){var r;return r=Xf(68),v[r+40>>2]=0,v[r+44>>2]=0,v[r+36>>2]=10328,v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=10344,v[r+16>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[r+4>>2]=10328,v[r>>2]=10296,v[r+48>>2]=0,v[r+52>>2]=0,v[r+56>>2]=0,v[r+60>>2]=0,v[r- -64>>2]=0,0|r},function(r,n,e){r|=0,n|=0,e|=0;var i,f=0,t=0,u=0,o=w(0),a=0,c=w(0),b=w(0),k=w(0),s=w(0),l=0,h=0,y=0,m=0,g=0;if(D=i=D-16|0,l=v[n+28>>2],v[r+24>>2]=0,f=v[r+40>>2])for(a=r+4|0;Sn(a,v[r+48>>2]+(t<<2)|0),(0|f)!=(0|(t=t+1|0)););if(v[r+40>>2]=0,l)for(m=r+36|0,g=r+20|0,t=0;a=v[v[n+36>>2]+(t<<2)>>2],f=v[a+8>>2],0|da[v[v[f>>2]+16>>2]](f)&&(f=v[a+60>>2])&&Tf(0|da[v[v[f>>2]+8>>2]](f),20724)&&(v[i+12>>2]=f,Sn(g,i+12|0),v[i+8>>2]=0,(f=v[r+8>>2])?(u=f-1|0,f=v[v[r+16>>2]+(u<<2)>>2],v[r+8>>2]=u):(f=Xf(24),v[f+4>>2]=8712,v[f>>2]=10312,v[f+8>>2]=0,v[f+12>>2]=0,v[f+16>>2]=0,v[f+20>>2]=0,ca(f+4|0,16)),v[i+8>>2]=f,Sn(m,i+8|0),u=v[i+8>>2],f=v[i+12>>2],h=v[f+52>>2],v[u+20>>2]=h,y=u+4|0,d[u+8>>2]<h>>>0&&(v[i+4>>2]=0,yn(y,h,i+4|0),f=v[i+12>>2]),kr(f,a,0,v[f+52>>2],v[y+12>>2],0,2)),(0|l)!=(0|(t=t+1|0)););if(e){if(n=0,t=v[r+40>>2])for(l=v[r+48>>2],k=w(34028234663852886e22),s=w(11754943508222875e-54),c=w(11754943508222875e-54),b=w(34028234663852886e22);;){if(e=v[l+(n<<2)>>2],(0|(a=v[e+20>>2]))>0)for(f=v[e+16>>2],e=0;k=(o=p[(u=e<<2)+f>>2])<k?k:o,s=o>s?s:o,b=(o=p[f+(4|u)>>2])<b?b:o,c=o>c?c:o,(0|a)>(0|(e=e+2|0)););if((0|t)==(0|(n=n+1|0)))break}else c=w(11754943508222875e-54),b=w(34028234663852886e22),k=w(34028234663852886e22),s=w(11754943508222875e-54);p[r+64>>2]=b,p[r+60>>2]=k,p[r+56>>2]=c,p[r+52>>2]=s}else v[r+60>>2]=2139095039,v[r+64>>2]=2139095039,v[r+52>>2]=8388608,v[r+56>>2]=8388608;D=i+16|0},tf,function(r,n,e){r|=0,n=w(n),e=w(e);var i=0;return!(p[r+56>>2]<=e)|!(p[r+52>>2]<=n)|!(p[r+60>>2]>=n)||(i=p[r+64>>2]>=e),0|i},function(r,n,e,i){r|=0,n|=0,e=w(e),i=w(i);var f=0;return f=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),0|da[0|f](n,e,i)},function(r,n,e,i,f){r|=0,n=w(n),e=w(e),i=w(i),f=w(f);var t=w(0),u=w(0),o=w(0),a=w(0),c=0;return(t=p[r+52>>2])>=n&i<=t|(u=p[r+56>>2])>=e&f<=u|(o=p[r+60>>2])<=n&i>=o|(a=p[r+64>>2])<=e&f>=a||(c=1,i=w(w(f-e)/w(i-n)),(f=w(w(i*w(t-n))+e))>u&f<a||(f=w(w(i*w(o-n))+e))>u&f<a||(f=w(w(w(u-e)/i)+n))>t&f<o||(c=(n=w(w(w(a-e)/i)+n))>t&n<o)),0|c},function(r,n,e,i,f,t){r|=0,n|=0,e=w(e),i=w(i),f=w(f),t=w(t);var u=0;return u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),0|da[0|u](n,e,i,f,t)},function(r,n){n|=0;var e=0;return!(p[56+(r|=0)>>2]<p[n+64>>2])|!(p[r+52>>2]<p[n+60>>2])|!(p[r+60>>2]>p[n+52>>2])||(e=p[r+64>>2]>p[n+56>>2]),0|e},function(r,n,e){e|=0;var i,f,t,u=0,o=0,a=0,c=0,b=0;if(D=i=D-80|0,f=(u=n|=0)+((n=v[4+(r|=0)>>2])>>1)|0,r=v[r>>2],r=1&n?v[v[f>>2]+r>>2]:r,v[i+12>>2]=10296,t=Po(i+16|0,e+4|0),v[(n=i+32|0)>>2]=10344,v[n+4>>2]=v[e+24>>2],u=v[e+28>>2],v[n+12>>2]=0,v[n+8>>2]=u,u&&(o=v[5208],c=n,b=0|da[v[v[o>>2]+12>>2]](o,u<<2,8524,206),v[c+12>>2]=b,o=v[n+4>>2]))for(u=0;v[(a=u<<2)+v[n+12>>2]>>2]=v[v[e+32>>2]+a>>2],(0|o)!=(0|(u=u+1|0)););return u=Po(i+48|0,e+36|0),o=v[e+64>>2],v[i+72>>2]=v[e+60>>2],v[i+76>>2]=o,o=v[e+56>>2],v[i+64>>2]=v[e+52>>2],v[i+68>>2]=o,r=0|da[0|r](f,i+12|0),v[i+12>>2]=10296,Wu(u),Ku(n),Wu(t),D=i+80|0,0|r},function(r,n,e){r|=0,n=w(n),e=w(e);var i,f=w(0),t=0,u=w(0),o=0,a=0,c=0,b=0,k=0,s=0,l=0;r:{if(i=v[r+40>>2])for(s=r,l=v[r+48>>2];;){if(a=0,r=v[(o<<2)+l>>2],(0|(c=v[r+20>>2]))>0)for(t=c-2|0,b=v[r+16>>2],r=0;!((f=p[(4|(k=r<<2))+b>>2])<e&(u=p[4+(t=(t<<2)+b|0)>>2])>=e)&(!(e<=f)|!(e>u))||(u=w(w(e-f)/w(u-f)),f=p[b+k>>2],w(w(u*w(p[t>>2]-f))+f)<n&&(a^=1)),t=r,(0|c)>(0|(r=r+2|0)););if(r=v[v[s+32>>2]+(o<<2)>>2],1&a)break r;if((0|i)==(0|(o=o+1|0)))break}r=0}return 0|r},function(r,n,e,i,f){r|=0,n=w(n),e=w(e),i=w(i),f=w(f);var t,u=0,o=w(0),a=w(0),c=0,b=w(0),k=w(0),s=0,l=w(0),h=0,d=0,y=w(0),m=w(0),g=w(0),F=w(0),A=w(0),T=w(0),$=0,I=w(0);b=n,k=e;r:{if(t=v[r+40>>2])for($=v[r+48>>2];;){u=v[(s<<2)+$>>2];n:{if(h=v[u+20>>2])for(y=w(w(b*f)-w(k*i)),m=w(-w(k-f)),d=v[u+16>>2],n=p[(u=d+(h<<2)|0)-8>>2],e=p[u-4>>2],g=w(b-i),I=w(-g),u=0;;){if(l=e,o=n,n=p[(c=u<<2)+d>>2],a=w(o-n),e=p[(4|c)+d>>2],F=w(w(o*e)-w(n*l)),A=w(l-e),T=w(w(g*A)+w(a*m)),!(!((a=w(w(w(y*a)+w(F*I))/T))>=o&n>=a)&(!(n<=a)|!(o>=a))|!(a>=b&i>=a)&(!(i<=a)|!(a<=b))||!((o=w(w(w(y*A)+w(F*m))/T))>=l&e>=o)&(!(e<=o)|!(o<=l)))){if(c=1,o>=k&f>=o)break n;if(f<=o&&o<=k)break n}if(!(h>>>0>(u=u+2|0)>>>0))break}c=0}if(u=v[v[r+32>>2]+(s<<2)>>2],c)break r;if((0|t)==(0|(s=s+1|0)))break}u=0}return 0|u},function(r){return w(w(p[60+(r|=0)>>2]-p[r+52>>2]))},kf,function(r){return w(w(p[64+(r|=0)>>2]-p[r+56>>2]))},function(){return 21404},uu,gf,function(r,n){return r|=0,n|=0,0|Vi(Xf(40),p[r>>2],n)},Go,Kt,Dt,_t,Lt,Rt,mf,sf,function(){return 21406},uu,vf,function(r){return r|=0,0|rf(Xf(56),r)},No,ti,Dt,_t,Lt,Rt,mf,sf,lt,uu,No,ti,lt,Yo,Yo,uu,go,wf,Mo,Jo,Dt,_t,Dt,_t,Gf,function(r,n,e,i,f,t,u,o){n|=0,e|=0,i|=0,f|=0,t|=0,u|=0,o|=0;var a=0;a=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(a=v[a+v[n>>2]>>2]),da[0|a](n,e,i,f,t,u,o)},Yi,yf,lt,Yo,Yo,uu,vf,function(r){return r|=0,0|su(Xf(64),r)},ti,wf,lt,Yo,Yo,uu,vf,function(r){return r|=0,0|dt(Xf(68),r)},function(r){return v[64+(r|=0)>>2]},function(r,n){n|=0,v[64+(r|=0)>>2]=n},wf,yf,wf,lt,Yo,Yo,uu,vf,function(r){return r|=0,0|Mr(Xf(236),r)},mf,sf,function(r){return 120+(r|=0)|0},jo,function(r){return 136+(r|=0)|0},function(r){return 0|ra(r|=0)},Kt,Lt,Rt,Dt,_t,function(r){return 152+(r|=0)|0},sr,If,function(r){return v[100+(r|=0)>>2]},wf,zn,yf,wf,xr,lt,Yo,Yo,uu,vf,function(r){return r|=0,0|xi(Xf(84),r)},Uo,nu,Jt,wf,lt,Yo,Yo,uu,vf,function(r){return r|=0,0|zf(Xf(32),r)},Lt,Rt,function(r,n,e,i){var f;r|=0,n|=0,e=w(e),i=w(i),D=f=D-16|0,p[f+12>>2]=e,p[f+8>>2]=i,Zi(n,p[r+20>>2],p[r+24>>2],f+12|0,f+8|0),D=f+16|0},function(r,n){r|=0,n|=0;var e=w(0),i=w(0),f=w(0),t=w(0);return f=p[n+96>>2],i=Wr(e=w(p[r+28>>2]*w(.01745329238474369))),t=p[n+92>>2],e=Rr(e),w(w(Pr(w(w(e*p[n+104>>2])+w(i*p[n+108>>2])),w(w(e*t)+w(i*f)))*w(57.2957763671875)))},ff,wf,lt,Yo,Yo,uu,vf,function(r){return r|=0,0|Lr(Xf(164),r)},Lt,Rt,function(r){return 0|ia(r|=0)},Kt,mf,sf,xo,Zn,function(r,n,e,i,f,t,u){r|=0,n|=0,e=w(e),i=w(i),f=w(f),t=w(t),u|=0;var o=0;o=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(o=v[o+v[n>>2]>>2]),da[0|o](n,e,i,f,t,u)},Ho,zr,If,function(r,n,e,i,f){i|=0,f|=0,Zr(r|=0,n|=0,v[12+(e|=0)>>2],i,f)},function(r,n,e,i,f,t){n|=0,e|=0,i|=0,f|=0,t|=0;var u=0;u=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),da[0|u](n,e,i,f,t)},wf,lt,uu,je,je,je,je,be,be,lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|vt(Xf(8),v[r>>2])},be,be,je,je,je,je,function(){return 21411},uu,vf,function(r){return r|=0,0|Be(Xf(68),r)},function(r,n){Re(r|=0,16+(n|=0)|0)},Dt,_t,Dt,_t,Dt,_t,function(){return 21414},uu,function(r,n){Re(r|=0,8+(n|=0)|0)},Dt,_t,nu,Jt,function(){return 21417},uu,function(){return 21420},uu,function(r,n,e,i){var t;return r|=0,e|=0,i|=0,D=t=D-32|0,n=Ae(t+20|0,4+(n|=0)|0,v[n>>2],0),v[t+16>>2]=e,f[t+15|0]=i,r=0|da[0|r](n,t+16|0,t+15|0),lf(n),D=t+32|0,0|r},function(r,n,e){var i,t,u,o,a;return r|=0,n|=0,e|=0,i=Xf(40),a=l[0|e],D=t=D-16|0,v[i+36>>2]=v[n>>2],v[i+32>>2]=0,v[i+24>>2]=0,v[i+28>>2]=0,v[i+20>>2]=8984,v[i+16>>2]=0,v[i+8>>2]=0,v[i+12>>2]=0,v[i+4>>2]=8968,v[i>>2]=8840,u=t+4|0,e=Di(n=v[r+8>>2],47),n=qi(e=gu(u,o=(n=Di(n,92))>>>0<e>>>0?e:n,0),r),lf(e),e=v[5208],n=(n=n+o|0)?n-v[r+8>>2]|0:0,e=Ff(0|da[v[v[e>>2]+12>>2]](e,n+1|0,8524,54),v[r+8>>2],n),f[n+e|0]=0,n=v[5208],(n=0|da[v[v[n>>2]+24>>2]](n,r,u))&&fr(i,n,v[t+4>>2],e,a),r=v[5208],da[v[v[r>>2]+20>>2]](r,n,8524,63),r=v[5208],da[v[v[r>>2]+20>>2]](r,e,8524,64),D=t+16|0,0|i},Ye,ze,wt,Yo,Yo,Nt,Pf,function(r){var n;return r|=0,n=Tt(8),v[n+4>>2]=v[r>>2],v[n>>2]=9692,0|n},wt,Yo,Yo,Nt,Pf,function(r){var n;return r|=0,n=Tt(8),v[n+4>>2]=v[r>>2],v[n>>2]=9720,0|n},function(){return 21399},uu,function(r,n,e,i){var f;return r|=0,n|=0,e|=0,i|=0,D=f=D-16|0,v[f+12>>2]=n,n=Ae(f,e+4|0,v[e>>2],0),r=0|da[0|r](f+12|0,n,i),lf(n),D=f+16|0,0|r},function(r,n,e){return r|=0,n|=0,e|=0,0|Bn(Xf(84),v[r>>2],n,e)},Go,wf,Bo,ti,$o,Kt,function(r){return 0|Ko(r|=0)},Kt,function(r){return 0|fa(r|=0)},Dt,_t,lt,uu,If,wf,wf,yf,lt,Yo,Yo,uu,At,function(r,n){return r|=0,n|=0,0|en(Xf(48),r,n)},Go,Kt,Bo,Dt,_t,Dt,_t,nu,Jt,Lt,Rt,function(r,n,e,i,f,t,u,o){r|=0,n|=0,e=w(e),i=w(i),f|=0,t|=0,u|=0,o=w(o),da[0|r](n,e,i,f,t,u,o)},gr,function(r,n,e,i,f,t,u,o,a){r|=0,n|=0,e|=0,i=w(i),f=w(f),t|=0,u|=0,o=w(o),a=w(a),da[0|r](n,e,i,f,t,u,o,a)},ur,lt,Yo,Yo,uu,At,function(r,n){return r|=0,n|=0,0|Ar(Xf(144),r,n)},Go,Kt,Bo,Dt,_t,Lt,Rt,lt,Yo,Yo,uu,vf,function(r){return r|=0,0|Zf(Xf(88),r)},Ko,function(r){return v[40+(r|=0)>>2]},wf,function(r){return w(p[44+(r|=0)>>2])},kf,function(r){return w(p[48+(r|=0)>>2])},function(r){return w(p[52+(r|=0)>>2])},function(r){return w(p[56+(r|=0)>>2])},Gu,function(r){return w(p[64+(r|=0)>>2])},function(r){return w(p[68+(r|=0)>>2])},function(r){return w(p[72+(r|=0)>>2])},function(r){return w(p[76+(r|=0)>>2])},function(r){return w(p[80+(r|=0)>>2])},function(r){return l[84+(r|=0)|0]},wf,function(r){return l[85+(r|=0)|0]},lt,Yo,Yo,uu,At,function(r,n){return r|=0,n|=0,0|fn(Xf(48),r,n)},Go,Kt,Bo,To,wf,Lt,Rt,lt,Yo,Yo,uu,function(r,n,e,i){var f;return r|=0,n|=0,e|=0,i|=0,D=f=D-16|0,v[f+12>>2]=i,r=0|da[0|r](n,e,f+12|0),D=f+16|0,0|r},function(r,n,e){return r|=0,n|=0,e|=0,0|ye(Xf(120),r,n,v[e>>2])},Go,Kt,Wo,Kt,Ao,wf,_o,Lt,Rt,nu,Jt,Of,If,cr,function(r,n,e,i,f,t,u,o,a){r|=0,n|=0,e=w(e),i=w(i),f=w(f),t=w(t),u=w(u),o=w(o),a=w(a);var c=0;c=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(c=v[c+v[n>>2]>>2]),da[0|c](n,e,i,f,t,u,o,a)},Ke,function(r){return w(w(Pr(p[104+(r|=0)>>2],p[r+92>>2])*w(57.2957763671875)))},kf,function(r){return w(w(Pr(p[108+(r|=0)>>2],p[r+96>>2])*w(57.2957763671875)))},function(r){r|=0;var n=w(0),e=w(0);return n=p[r+92>>2],e=w(n*n),n=p[r+104>>2],w(w(T(w(e+w(n*n)))))},function(r){r|=0;var n=w(0),e=w(0);return n=p[r+96>>2],e=w(n*n),n=p[r+108>>2],w(w(T(w(e+w(n*n)))))},function(r,n){r|=0,n|=0;var e,i=w(0),f=w(0),t=w(0),u=w(0),o=w(0),a=w(0),c=w(0);D=e=D-16|0,u=p[r+92>>2],i=p[r+108>>2],o=p[r+104>>2],f=p[r+96>>2],t=w(w(1)/w(w(u*i)-w(o*f))),a=i,i=w(p[n>>2]-p[r+100>>2]),c=f,f=w(p[n+4>>2]-p[r+112>>2]),p[e+12>>2]=w(w(a*i)*t)-w(t*w(c*f)),p[e+8>>2]=w(w(u*f)*t)-w(t*w(o*i)),p[n>>2]=p[e+12>>2],p[n+4>>2]=p[e+8>>2],D=e+16|0},function(r,n){var e;D=e=D-16|0,Zi(r|=0,p[(n|=0)>>2],p[n+4>>2],e+12|0,e+8|0),p[n>>2]=p[e+12>>2],p[n+4>>2]=p[e+8>>2],D=e+16|0},function(r,n){r|=0,n=w(n);var e=w(0),i=w(0),f=w(0);return i=p[r+96>>2],n=Wr(e=w(n*w(.01745329238474369))),f=p[r+108>>2],e=Rr(e),w(w(w(w(Pr(w(w(n*p[r+92>>2])-w(e*p[r+104>>2])),w(w(f*e)-w(n*i)))*w(57.2957763671875))+p[r+40>>2])-p[r+52>>2]))},nf,function(r,n){r|=0,n=w(n);var e=w(0),i=w(0),f=w(0);return i=p[r+96>>2],n=Wr(e=w(w(n-w(p[r+40>>2]-p[r+52>>2]))*w(.01745329238474369))),f=p[r+92>>2],e=Rr(e),w(w(Pr(w(w(e*p[r+104>>2])+w(n*p[r+108>>2])),w(w(e*f)+w(n*i)))*w(57.2957763671875)))},function(r,n){r|=0,n=w(n);var e=w(0),i=w(0),t=w(0),u=w(0),o=w(0);f[r+88|0]=0,i=p[r+108>>2],n=Rr(e=w(n*w(.01745329238474369))),t=p[r+96>>2],e=Wr(e),p[r+108>>2]=w(t*e)+w(n*i),u=p[r+92>>2],o=p[r+104>>2],p[r+104>>2]=w(e*u)+w(n*o),p[r+96>>2]=w(n*t)-w(i*e),p[r+92>>2]=w(n*u)-w(o*e)},df,function(){return 21398},uu,ai,function(r,n,e){return r|=0,n|=0,e|=0,0|Xn(Xf(64),v[r>>2],n,v[e>>2])},Go,wf,Bo,ti,Dt,_t,Lt,Rt,Dt,_t,nu,Jt,function(){return 21408},uu,At,function(r,n){return r|=0,n|=0,0|Tn(Xf(84),r,n)},Go,Kt,Wo,Kt,_o,Kt,Jo,Lo,Ao,go,wf,af,yf,function(r,n){r|=0,n=w(n),p[r+64>>2]=p[v[r+12>>2]+160>>2]-n},df,function(r){return w(w(p[v[12+(r|=0)>>2]+160>>2]-p[r+64>>2]))},kf,En,If,function(){return 21409},uu,vf,function(r){return r|=0,0|ge(Xf(68),r)},No,ti,Jo,function(r){return 52+(r|=0)|0},yt,function(r,n,e,i,f){n|=0,e|=0,i|=0,f|=0;var t,u=0;D=t=D-16|0,n=((u=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=Ae(t+4|0,i+4|0,v[i>>2],0),da[0|r](u,e,n,f),lf(n),D=t+16|0},function(r,n){r|=0;var e=0,i=0,f=0,t=0,u=0,o=0,a=0,c=0;if(i=v[40+(n|=0)>>2])for(c=r+36|0;;){t=v[n+48>>2]+(f<<2)|0;r:{if(u=v[r+40>>2]){if(e=0,o=v[t>>2],a=v[r+48>>2],(0|o)==v[a>>2])break r;for(;(0|u)!=(0|(e=e+1|0))&(0|o)!=v[a+(e<<2)>>2];);if(e>>>0<u>>>0)break r}Sn(c,t),i=v[n+40>>2]}if(!((f=f+1|0)>>>0<i>>>0))break}if(i=v[n+56>>2])for(c=r+52|0,f=0;;){t=v[n+64>>2]+(f<<2)|0;r:{if(u=v[r+56>>2]){if(e=0,o=v[t>>2],a=v[r+64>>2],(0|o)==v[a>>2])break r;for(;(0|u)!=(0|(e=e+1|0))&(0|o)!=v[a+(e<<2)>>2];);if(e>>>0<u>>>0)break r}Sn(c,t),i=v[n+56>>2]}if(!((f=f+1|0)>>>0<i>>>0))break}r:if(i=v[n+24>>2])for(u=r+16|0,r=0,e=0;;){if(t=v[n+32>>2],d[4+(t+(e<<4)|0)>>2]<=r>>>0){n:{e:{for(;;){if((0|i)==(0|(e=e+1|0)))break e;if(v[4+(t+(e<<4)|0)>>2])break}r=e;break n}r=i}if(f=r,i=e>>>0>=i>>>0,r=0,e=f,i)break r}if(f=v[12+(t+(e<<4)|0)>>2]+m(r,20)|0,vr(u,v[f>>2],f+4|0,v[f+16>>2]),r=r+1|0,!((i=v[n+24>>2])>>>0>e>>>0))break}},yf,function(r,n){r|=0;var e=0,i=0,f=0,t=0,u=0,o=0,a=0,c=0;if(f=v[40+(n|=0)>>2])for(c=r+36|0;;){u=v[n+48>>2]+(e<<2)|0;r:{if(t=v[r+40>>2]){if(i=0,o=v[u>>2],a=v[r+48>>2],(0|o)==v[a>>2])break r;for(;(0|t)!=(0|(i=i+1|0))&(0|o)!=v[a+(i<<2)>>2];);if(i>>>0<t>>>0)break r}Sn(c,u),f=v[n+40>>2]}if(!(f>>>0>(e=e+1|0)>>>0))break}if(f=v[n+56>>2])for(c=r+52|0,e=0;;){u=v[n+64>>2]+(e<<2)|0;r:{if(t=v[r+56>>2]){if(i=0,o=v[u>>2],a=v[r+64>>2],(0|o)==v[a>>2])break r;for(;(0|t)!=(0|(i=i+1|0))&(0|o)!=v[a+(i<<2)>>2];);if(i>>>0<t>>>0)break r}Sn(c,u),f=v[n+56>>2]}if(!(f>>>0>(e=e+1|0)>>>0))break}r:if(e=v[n+24>>2])for(u=r+16|0,f=0,i=0;;){if(t=v[n+32>>2],d[4+(t+(i<<4)|0)>>2]<=f>>>0){n:{e:{for(;;){if((0|(i=i+1|0))==(0|e))break e;if(v[4+(t+(i<<4)|0)>>2])break}r=i;break n}r=e}if(e=e>>>0<=i>>>0,f=0,i=r,e)break r}if(r=v[12+(t+(i<<4)|0)>>2]+m(f,20)|0,e=v[r+16>>2],t=lu(0|da[v[v[e>>2]+8>>2]](e),20912),e=v[r+16>>2],f=f+1|0,vr(u,o=v[r>>2],a=r+4|0,r=t?xr(e):0|da[v[v[e>>2]+12>>2]](e)),!(i>>>0<(e=v[n+24>>2])>>>0))break}},function(r,n,e){n|=0,e|=0;var i,f=0,t=0,u=0,o=0;D=i=D-16|0,v[12+(r|=0)>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=9148,xt(i+4|0,n),n=v[i+8>>2],t=v[i+4>>2];r:if(!(n>>>0>=(f=v[t+4>>2])>>>0))for(;;){if(u=v[i+12>>2],o=v[t+12>>2],u>>>0>=d[4+(o+(n<<4)|0)>>2]){for(;;){if((0|f)==(0|(n=n+1|0)))break r;if(v[4+((n<<4)+o|0)>>2])break}if(v[i+8>>2]=n,u=0,n>>>0>=f>>>0)break r}if(f=v[12+((n<<4)+o|0)>>2],v[i+12>>2]=u+1,f=f+m(u,20)|0,v[f>>2]==(0|e)&&(wn(r,f+4|0),t=v[i+4>>2],n=v[i+8>>2]),!((f=v[t+4>>2])>>>0>n>>>0))break}D=i+16|0},gt,je,function(r,n){n|=0;var e,i=0,f=0,t=0;D=e=D-16|0,v[12+(r|=0)>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=12864,xt(e+4|0,n),n=v[e+8>>2],i=v[e+4>>2];r:if(!(n>>>0>=(f=v[i+4>>2])>>>0))for(;;){if(t=v[e+12>>2],i=v[i+12>>2],t>>>0>=d[4+(i+(n<<4)|0)>>2]){for(;;){if((0|f)==(0|(n=n+1|0)))break r;if(v[4+(i+(n<<4)|0)>>2])break}if(v[e+8>>2]=n,t=0,n>>>0>=f>>>0)break r}if(n=v[12+(i+(n<<4)|0)>>2],v[e+12>>2]=t+1,v[e>>2]=n+m(t,20),Sn(r,e),n=v[e+8>>2],i=v[e+4>>2],!(n>>>0<(f=v[i+4>>2])>>>0))break}D=e+16|0},function(r,n,e){n|=0,e|=0;var i=0,f=0,t=0,u=0,o=0;r:if(!(d[24+(r|=0)>>2]<=n>>>0)&&(i=v[r+32>>2]+(n<<4)|0,v[i+4>>2])){for(;;){if(!qi(4+(v[i+12>>2]+m(f,20)|0)|0,e)){if((f=f+1|0)>>>0<d[i+4>>2])continue;break r}break}if(!((0|f)<0)){if(Sf(v[16+(v[12+((e=n<<4)+v[r+32>>2]|0)>>2]+m(f,20)|0)>>2]),D=n=D-32|0,r=e+v[r+32>>2]|0,e=v[r+4>>2]-1|0,v[r+4>>2]=e,e>>>0>f>>>0)for(o=n+16|0;i=(e=m(f,20))+v[r+12>>2]|0,v[n+12>>2]=v[i>>2],u=Re(o,i+4|0),v[n+28>>2]=v[i+16>>2],i=e+(t=v[r+12>>2])|0,e=t+(t=e+20|0)|0,v[i>>2]=v[e>>2],ee(i+4|0,e+4|0),v[i+16>>2]=v[e+16>>2],e=v[r+12>>2]+t|0,v[e>>2]=v[n+12>>2],ee(e+4|0,u),v[e+16>>2]=v[n+28>>2],lf(u),(e=v[r+4>>2])>>>0>(f=f+1|0)>>>0;);lf(4+(v[r+12>>2]+m(e,20)|0)|0),D=n+32|0}}},Ne,function(r,n,e){n|=0,e|=0;var i,f=0,t=0,u=0,o=0;D=i=D-16|0,v[12+(r|=0)>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=12864,xt(i+4|0,n),n=v[i+8>>2],t=v[i+4>>2];r:if(!(n>>>0>=(f=v[t+4>>2])>>>0))for(;;){if(u=v[i+12>>2],o=v[t+12>>2],u>>>0>=d[4+(o+(n<<4)|0)>>2]){for(;;){if((0|f)==(0|(n=n+1|0)))break r;if(v[4+((n<<4)+o|0)>>2])break}if(v[i+8>>2]=n,u=0,n>>>0>=f>>>0)break r}if(f=v[12+((n<<4)+o|0)>>2],v[i+12>>2]=u+1,f=f+m(u,20)|0,v[f>>2]==(0|e)&&(v[i>>2]=f,Sn(r,i),t=v[i+4>>2],n=v[i+8>>2]),!((f=v[t+4>>2])>>>0>n>>>0))break}D=i+16|0},function(){return 21427},function(r){(r|=0)&&(lf(r+4|0),or(r))},ai,function(r,n,e){var i;return r|=0,n|=0,e|=0,i=Tt(20),e=v[e>>2],v[i>>2]=v[r>>2],Re(i+4|0,n),v[i+16>>2]=e,0|i},Dt,_t,function(r,n){Re(r|=0,4+(n|=0)|0)},Io,function(){return 21430},uu,yo,function(){return 0|Ir(Xf(232))},function(r){return 160+(r|=0)|0},Kt,function(r){return 176+(r|=0)|0},Kt,function(r){return 192+(r|=0)|0},Q,function(r,n,e,i){n|=0,e|=0,i|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),0|da[0|f](n,e,i)},gi,yf,bf,If,yu,wf,function(){return 21433},uu,yo,function(){return 0|Hr(Xf(232))},mf,sf,_o,function(r){return 32+(r|=0)|0},function(r){return 48+(r|=0)|0},Dt,_t,Lo,xo,Ho,function(r){return 116+(r|=0)|0},function(r){return 132+(r|=0)|0},Lt,Rt,ru,ze,Xt,ze,ou,ze,eu,iu,ze,Qt,ze,xe,ze,Vt,ze,Zt,ze,Yt,ze,function(r,n){n|=0;var e,i=0;r:{if(e=v[136+(r|=0)>>2])for(;;){if(qi(No(v[v[r+144>>2]+(i<<2)>>2]),n))break r;if((0|e)==(0|(i=i+1|0)))break}i=-1}return 0|i},function(){return 21436},uu,function(r,n,e,i){var f;return r|=0,n|=0,e|=0,i=w(i),D=f=D-16|0,n=Ae(f+4|0,n+4|0,v[n>>2],0),v[f>>2]=e,r=0|da[0|r](n,f,i),R(v[f>>2]),lf(n),D=f+16|0,0|r},function(r,n,e){r|=0,n|=0,e=w(e);var i,f=0,t=0,u=0,o=0,a=0,c=0,b=0;if(D=i=D-32|0,f=v[n>>2],t=0|M(4667),u=0|U(0|f,0|t),R(0|t),o=+G(0|u,21305,0|(t=i+12|0)),W(v[i+12>>2]),R(0|u),v[i+24>>2]=0,v[i+16>>2]=0,v[i+20>>2]=0,v[i+12>>2]=1048,v[i+28>>2]=0,Fn(f=t,t=o<4294967296&o>=0?~~o>>>0:0,i+28|0),v[i+8>>2]=0,t)for(;D=u=D-16|0,f=v[n>>2],v[u+8>>2]=v[i+8>>2],a=f,f=0|L(21305,u+8|0),c=i,b=0|U(0|a,0|f),v[c+4>>2]=b,R(0|f),D=u+16|0,o=+G(v[i+4>>2],7868,i+28|0),W(v[i+28>>2]),a=v[i+24>>2]+(v[i+8>>2]<<2)|0,f=o<4294967296&o>=0?~~o>>>0:0,v[a>>2]=f,R(v[i+4>>2]),u=v[i+8>>2]+1|0,v[i+8>>2]=u,u>>>0<t>>>0;);return n=r,r=i+12|0,n=Kr(Xf(48),n,r,e),fo(r),D=i+32|0,0|n},Jo,ti,No,wi,of,Lt,Rt,lt,uu,wf,lt,Yo,Yo,uu,function(r){return 1+(d[8+(r|=0)>>2]/19|0)|0},wf,function(r,n){n|=0,v[v[16+(r|=0)>>2]+m(n,76)>>2]=0},yf,ct,_r,function(r,n,e,i,f,t,u){r|=0,n|=0,e|=0,i=w(i),f=w(f),t=w(t),u=w(u);var o=0;o=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(o=v[o+v[n>>2]>>2]),da[0|o](n,e,i,f,t,u)},bn,function(r,n,e,i){r|=0,n|=0,e|=0,i=w(i);var f=0;return f=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),w(w(da[0|f](n,e,i)))},function(r,n){return n|=0,w(p[v[16+(r|=0)>>2]+m(n,76)>>2])},ff,lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|Ue(Xf(40),v[r>>2])},function(r){return v[(r|=0)>>2]},Af,function(r,n,e,i,f,t){r|=0,n|=0,e|=0,i=w(i),f=w(f),t=w(t);var u=0;u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),da[0|u](n,e,i,f,t)},lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|cu(Xf(40),v[r>>2])},lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|bu(Xf(40),v[r>>2])},lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|Qe(Xf(40),v[r>>2])},Dt,_t,Ko,Mf,Ni,lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|Xe(Xf(40),v[r>>2])},Dt,_t,Ko,Ri,li,lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|Le(Xf(40),v[r>>2])},function(r){return v[36+(r|=0)>>2]},Uu,wf,yf,Oe,function(r,n,e,i,f,t,u,o,a,c,b){r|=0,n|=0,e|=0,i=w(i),f=w(f),t=w(t),u=w(u),o=w(o),a=w(a),c=w(c),b=w(b);var k=0;k=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(k=v[k+v[n>>2]>>2]),da[0|k](n,e,i,f,t,u,o,a,c,b)},lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|sn(Xf(40),v[r>>2])},Dt,_t,Bo,Ko,function(r,n){n|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),r=0|da[0|e](n),0|Hn(Xf(16),r)},Ao,wf,Wf,function(r,n,e,i,f){r|=0,n|=0,e|=0,i=w(i),f|=0;var t,u=0;D=t=D-16|0,n=((u=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=Ae(t+4|0,f+4|0,v[f>>2],0),da[0|r](u,e,i,n),lf(n),D=t+16|0},lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|Or(Xf(60),v[r>>2])},Dt,_t,Dt,_t,Ko,zo,function(r,n,e,i){r|=0,n|=0,e=w(e);var f,t=0,u=0,o=0,a=0,c=0,b=0,k=0;if(D=f=D-32|0,o=v[(i|=0)>>2],t=0|M(4667),u=0|U(0|o,0|t),R(0|t),a=+G(0|u,21305,0|(t=f+12|0)),W(v[f+12>>2]),R(0|u),v[f+24>>2]=0,v[f+16>>2]=0,v[f+20>>2]=0,v[f+12>>2]=8712,v[f+28>>2]=0,yn(o=t,t=a<4294967296&a>=0?~~a>>>0:0,f+28|0),v[f+8>>2]=0,t)for(;D=u=D-16|0,o=v[i>>2],v[u+8>>2]=v[f+8>>2],c=o,o=0|L(21295,u+8|0),b=f,k=0|U(0|c,0|o),v[b+4>>2]=k,R(0|o),D=u+16|0,a=+G(v[f+4>>2],21294,f+28|0),W(v[f+28>>2]),p[v[f+24>>2]+(v[f+8>>2]<<2)>>2]=a,R(v[f+4>>2]),u=v[f+8>>2]+1|0,v[f+8>>2]=u,u>>>0<t>>>0;);_f(i=r,n,e,r=f+12|0),vo(r),D=f+32|0},lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|le(Xf(36),v[r>>2])},No,Mo,Wo,wf,Hf,tf,lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|Ur(Xf(36),v[r>>2])},No,Wo,wf,Mo,$e,function(r,n,e,i,f){r|=0,n|=0,e|=0,i=w(i),f|=0;var t=0;t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),da[0|t](n,e,i,f)},lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|Co(Xf(40),v[r>>2])},ei,function(r,n,e,i,f,t,u,o,a){r|=0,n|=0,e|=0,i=w(i),f=w(f),t=w(t),u|=0,o|=0,a|=0;var c=0;c=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(c=v[c+v[n>>2]>>2]),da[0|c](n,e,i,f,t,u,o,a)},lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|mo(Xf(40),v[r>>2])},Si,li,lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|ni(Xf(40),v[r>>2])},Lf,Ni,lt,Yo,Yo,uu,Pf,function(r){return r|=0,0|wo(Xf(40),v[r>>2])},function(){return 21439},uu,yo,function(){return 0|on(Xf(172))},Io,wf,$o,wf,To,function(r){return v[28+(r|=0)>>2]},function(r){return v[32+(r|=0)>>2]},wf,nu,Jt,Lt,Rt,Gu,function(r,n){r|=0,n=w(n),p[r+64>>2]=n,p[r+60>>2]=n},kf,df,Dt,_t,function(r){r|=0;var n=w(0),e=w(0);n=p[r+52>>2];r:{if(l[r+36|0]){if((e=w(p[r+56>>2]-n))==w(0))break r;return w(w(n+Cr(p[r+72>>2],e)))}n=(n=w(p[r+72>>2]+n))<(e=p[r+56>>2])?n:e}return w(n)},kf,function(r){return p[72+(r|=0)>>2]>=w(p[r+56>>2]-p[r+52>>2])|0},wf,function(r){v[152+(r|=0)>>2]=0},If,function(){return 21441},uu,Pf,function(r){return r|=0,0|Ef(Xf(24),v[r>>2])},Lt,Rt,Go,wf,Pi,pe,function(r,n,e,i){var f;r|=0,n|=0,e|=0,i=w(i),D=f=D-16|0,p[f+12>>2]=i,Ot(f,n,e),$n(r+12|0,f,f+12|0),D=f+16|0},function(r,n,e,i,f){r|=0,n|=0,e|=0,i|=0,f=w(f);var t=0;t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),da[0|t](n,e,i,f)},Un,function(r,n,e,i){n|=0,e|=0,i|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),w(w(da[0|f](n,e,i)))},function(){return 21444},uu,Pf,function(r){return r|=0,0|an(Xf(104),v[r>>2])},Io,wf,zo,function(r){return w(p[100+(r|=0)>>2])},function(r,n){r|=0,n=w(n),p[r+100>>2]=n},kf,df,Tr,df,ir,of,Je,If,qn,yf,function(r,n,e,i){return e|=0,i|=0,0|Nn(r|=0,n|=0,xe(v[v[r+16>>2]+4>>2],e),i)},function(r,n,e,i,f){n|=0,e|=0,i|=0,f|=0;var t,u=0;return D=t=D-16|0,n=((u=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=Ae(t+4|0,i+4|0,v[i>>2],0),r=0|da[0|r](u,e,n,f),lf(n),D=t+16|0,0|r},function(r,n,e,i){return 0|Nn(r|=0,n|=0,e|=0,i|=0)},function(r,n,e,i,f){return r|=0,n|=0,e|=0,i|=0,f=w(f),0|tn(r,n,xe(v[v[r+16>>2]+4>>2],e),i,f)},function(r,n,e,i,f,t){r|=0,n|=0,e|=0,i|=0,f|=0,t=w(t);var u,o=0;return D=u=D-16|0,n=((o=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&o?v[v[n>>2]+r>>2]:r,o=n,n=Ae(u+4|0,i+4|0,v[i>>2],0),r=0|da[0|r](o,e,n,f,t),lf(n),D=u+16|0,0|r},tn,function(r,n,e,i,f,t){r|=0,n|=0,e|=0,i|=0,f|=0,t=w(t);var u=0;return u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),0|da[0|u](n,e,i,f,t)},function(r,n,e){return r|=0,n|=0,e=w(e),qe(),r=Nn(r,n,20612,0),p[r+84>>2]=e,p[r+100>>2]=e,0|r},function(r,n,e,i){r|=0,n|=0,e|=0,i=w(i);var f=0;return f=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),0|da[0|f](n,e,i)},function(r,n,e,i){return r|=0,n|=0,e=w(e),i=w(i),qe(),r=tn(r,n,20612,0,w(i-(i<=w(0)?e:w(0)))),p[r+84>>2]=e,p[r+100>>2]=e,0|r},function(r,n,e,i,f){r|=0,n|=0,e|=0,i=w(i),f=w(f);var t=0;return t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),0|da[0|t](n,e,i,f)},function(r,n){r|=0,n=w(n);var e,i,t=0,u=0;if(t=v[r+72>>2],i=l[t+28|0],f[t+28|0]=1,e=v[r+44>>2]){for(t=0;v[v[r+52>>2]+(t<<2)>>2]&&(qe(),u=Nn(r,t,20612,0),p[u+84>>2]=n,p[u+100>>2]=n),(0|e)!=(0|(t=t+1|0)););t=v[r+72>>2]}f[t+28|0]=i,pr(t)},function(r,n){return n|=0,0|(d[44+(r|=0)>>2]>n>>>0?v[v[r+52>>2]+(n<<2)>>2]:0)},of,function(r){f[v[72+(r|=0)>>2]+28|0]=1},function(r){f[v[72+(r|=0)>>2]+28|0]=0},function(){return 21403},uu,Pf,function(r){return r|=0,0|br(Xf(180),v[r>>2])},Go,wf,Bo,Ko,zo,function(r){return 56+(r|=0)|0},function(r){return 72+(r|=0)|0},function(r){return 88+(r|=0)|0},jo,function(r){return v[136+(r|=0)>>2]},wf,function(r){return 0|na(r|=0)},Kt,Lt,Rt,rr,If,ln,Do,Sr,ue,function(r){return v[12+(r|=0)>>2]?v[v[r+20>>2]>>2]:0},wf,ku,ze,function(r,n){return 0|Ci(8+(r|=0)|0,n|=0)},ze,tu,ze,function(r,n){return 0|Ci(24+(r|=0)|0,n|=0)},fu,ii,Gr,yf,function(r,n,e){return n|=0,e|=0,0|Fi(r|=0,eu(v[r+4>>2],n),e)},function(r,n,e,i){n|=0,e|=0,i|=0;var f,t=0;return D=f=D-32|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=Ae(f+20|0,e+4|0,v[e>>2],0),e=Ae(f+8|0,i+4|0,v[i>>2],0),r=0|da[0|r](t,n,e),lf(e),lf(n),D=f+32|0,0|r},Fi,je,function(r,n,e){n|=0,e|=0;var i,f=0,t=0;if(i=v[28+(r|=0)>>2])for(;;){if(t=v[v[r+36>>2]+(f<<2)>>2],qi(Bo(v[t+4>>2]),n))return void af(t,r=v[e+4>>2]?Fi(r,f,e):0);if((0|i)==(0|(f=f+1|0)))break}},function(r,n,e,i){n|=0,e|=0,i|=0;var f,t=0;D=f=D-32|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=Ae(f+20|0,e+4|0,v[e>>2],0),e=Ae(f+8|0,i+4|0,v[i>>2],0),da[0|r](t,n,e),lf(e),lf(n),D=f+32|0},function(r,n){n|=0;var e=0,i=0,f=0;r:if(i=v[60+(r|=0)>>2]){for(;;){if(f=v[v[r+68>>2]+(e<<2)>>2],!qi(No(v[f+4>>2]),n)){if((0|i)!=(0|(e=e+1|0)))continue;break r}break}return 0|f}return 0},ze,function(r,n){n|=0;var e=0,i=0,f=0;r:if(i=v[76+(r|=0)>>2]){for(;;){if(f=v[v[r+84>>2]+(e<<2)>>2],!qi(No(v[f+4>>2]),n)){if((0|i)!=(0|(e=e+1|0)))continue;break r}break}return 0|f}return 0},ze,function(r,n){n|=0;var e=0,i=0,f=0;r:if(i=v[92+(r|=0)>>2]){for(;;){if(f=v[v[r+100>>2]+(e<<2)>>2],!qi(No(v[f+4>>2]),n)){if((0|i)!=(0|(e=e+1|0)))continue;break r}break}return 0|f}return 0},ze,Gt,df,lt,uu,yf,Xi,If,lt,Yo,Yo,uu,Qi,function(r,n){r|=0,n|=0;var e,i=w(0);return e=Xf(12),i=p[r>>2],p[e+8>>2]=p[n>>2],p[e+4>>2]=i,v[e>>2]=11048,0|e},Lt,Rt,yf,Xi,If,lt,Yo,Yo,uu,gf,function(r,n){r|=0,n|=0;var e,i=w(0);return e=Xf(32),i=p[r>>2],v[e+28>>2]=n,v[e+24>>2]=0,v[e+16>>2]=0,v[e+20>>2]=0,p[e+12>>2]=i,v[e+4>>2]=0,v[e+8>>2]=0,v[e>>2]=11080,0|e},yf,Xi,If,Lt,Rt,function(r){return w(p[16+(r|=0)>>2])},function(r,n){r|=0,n=w(n),p[r+16>>2]=n*w(.01745329238474369)},kf,df,function(){return 21449},function(r){(r|=0)&&or(Iu(r))},Dt,_t,Yo,wf,function(){return 21290},qo,Dt,_t,function(){return 21454},function(r){(r|=0)&&or(ce(r))},yo,function(){var r,n,e;return r=Tt(92),v[r+4>>2]=1065353216,s[r>>1]=256,Bf(r+8|0,0,40),v[r+84>>2]=0,v[r+88>>2]=0,v[r+80>>2]=11208,v[r+76>>2]=0,v[r+68>>2]=0,v[r+72>>2]=0,v[r+64>>2]=11192,v[r+56>>2]=1065353216,v[r+60>>2]=1065353216,v[r+48>>2]=1065353216,v[r+52>>2]=1065353216,n=Tt(36),D=e=D-16|0,v[n+4>>2]=0,v[n+8>>2]=0,v[n>>2]=11176,v[n+12>>2]=0,v[n+16>>2]=0,v[n+20>>2]=0,v[n+24>>2]=0,v[n+28>>2]=0,v[n+32>>2]=0,v[e+12>>2]=0,mn(n,6,e+12|0),D=e+16|0,v[r+32>>2]=n,0|r},nu,Jt,Lt,Rt,function(r,n){n|=0;var e,i=0,f=0,t=0,u=0;return(i=v[24+(r|=0)>>2])&&da[v[v[i>>2]+4>>2]](i),(i=v[r+20>>2])&&da[v[v[i>>2]+4>>2]](i),(i=v[r+16>>2])&&da[v[v[i>>2]+4>>2]](i),(i=v[r+8>>2])&&da[v[v[i>>2]+4>>2]](i),v[r+12>>2]=n,t=r,u=br(Xf(180),v[r+12>>2]),v[t+8>>2]=u,t=r,u=Ef(Xf(24),v[r+12>>2]),v[t+16>>2]=u,t=r,u=an(Xf(104),v[r+16>>2]),v[t+20>>2]=u,t=r,u=Ir(Xf(232)),v[t+24>>2]=u,Do(v[r+8>>2]),ln(v[r+8>>2]),n=i=v[r+20>>2],(e=v[i+12>>2])&&(!(f=v[i+8>>2])|(0|r)==(0|f)||(da[0|e](f),n=v[r+20>>2])),v[i+12>>2]=0,v[i+8>>2]=r,v[n+96>>2]=0,v[n+92>>2]=393,v[r+8>>2]},of,function(r,n,e,i){r|=0,n=w(n),e|=0,i|=0;var f=0,t=0;if(f=v[r+8>>2]){if(f=xe(v[f+4>>2],e),e=v[r+20>>2],!f)return Je(e),Do(v[r+8>>2]),0;t=Nn(e,t=n<w(4294967296)&n>=w(0)?~~n>>>0:0,f,i),ir(v[r+20>>2],v[r+8>>2]),ln(v[r+8>>2])}return 0|t},function(r,n,e,i,f){r|=0,n|=0,e=w(e),i|=0,f|=0;var t,u=0;return D=t=D-16|0,n=((u=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=Ae(t+4|0,i+4|0,v[i>>2],0),r=0|da[0|r](u,e,n,f),lf(n),D=t+16|0,0|r},function(r,n){var e;n|=0,(e=v[8+(r|=0)>>2])&&(fu(e,n),ue(v[r+8>>2]))},ii,function(r,n){var e;r|=0,n=w(n),(e=v[r+8>>2])&&(Gt(e,n=w(p[r+4>>2]*n)),Tr(v[r+20>>2],n),ir(v[r+20>>2],v[r+8>>2]))},df,function(r){r|=0;var n,e,i,f=0,t=0,u=0,o=0,a=0,c=0,b=0,k=0,d=0,y=0,g=w(0),F=w(0),A=0,T=0,$=0,I=0,C=0,P=0,E=w(0),O=w(0),R=w(0),S=0,W=0,G=w(0),U=w(0),j=w(0);if(l[r+46|0]&&(v[r+68>>2]=0),ln(v[r+8>>2]),v[5305]=v[5304],v[5307]=v[5306],o=v[r+32>>2],D=f=D-16|0,v[f+12>>2]=0,mn(o,0,f+12|0),v[o+16>>2]=0,v[o+20>>2]=0,D=f+16|0,v[v[r+32>>2]+32>>2]=l[r+44|0]?28:24,D=o=D-48|0,e=v[r+8>>2],i=v[e+44>>2],t=l[r+44|0],f=v[5313],v[o+40>>2]=v[5312],v[o+44>>2]=f,f=v[5311],v[o+32>>2]=v[5310],v[o+36>>2]=f,f=v[5309],v[o+24>>2]=v[5308],v[o+28>>2]=f,(f=v[r+28>>2])?(da[v[v[f>>2]+12>>2]](f,e),f=v[r+8>>2]):f=e,n=na(f),i)for(P=(C=t?28:24)>>>2|0,W=r- -64|0;;){y=v[v[e+52>>2]+(S<<2)>>2],b=v[y+8>>2];r:if(0|da[v[v[b>>2]+16>>2]](b))if(v[y+60>>2]){F=p[r+60>>2],E=p[r+56>>2],g=p[r+52>>2],O=p[r+48>>2];n:{e:{i:{f:{t:{if(t=v[y+60>>2],lu(f=0|da[v[v[t>>2]+8>>2]](t),21008)){if(f=v[t+24>>2],u=v[f+4>>2],I=(c=v[u+12>>2])<<1,k=v[u+8>>2],a=v[5305],d=v[5307],!l[r+44|0])break t;if(u=0,(0|k)<=0)break e;for(;A=v[v[f+4>>2]>>2]+m(u,24)|0,$=v[A+16>>2],T=a+m(u,28)|0,v[T+12>>2]=v[A+12>>2],v[T+16>>2]=$,(0|k)!=(0|(u=u+1|0)););break e}if(lu(f,20912)){if(f=v[t+68>>2],u=v[f+4>>2],c=(b=v[u+12>>2])<<1,k=v[u+8>>2],a=v[5305],I=v[5307],!l[r+44|0])break f;if(u=0,(0|k)<=0)break i;for(;T=v[v[f+4>>2]>>2]+m(u,24)|0,A=v[T+16>>2],d=a+m(u,28)|0,v[d+12>>2]=v[T+12>>2],v[d+16>>2]=A,(0|k)!=(0|(u=u+1|0)););break i}if(u=lu(f,20736),f=v[r+24>>2],u){Q(f,y,t);break r}gi(f,y);break r}Ff(a,v[v[f+4>>2]>>2],m(k,C));break e}Ff(a,v[v[f+4>>2]>>2],m(k,C))}u=Ff(I,v[v[f+4>>2]+4>>2],c),kr(t,y,0,v[t+52>>2],a,0,P),v[o+36>>2]=b,v[o+32>>2]=k,v[o+28>>2]=u,v[o+24>>2]=a,I=1,t=ra(t);break n}u=Ff(d,v[v[f+4>>2]+4>>2],I),I=0,Zr(t,b,a,0,P),v[o+36>>2]=c,v[o+32>>2]=k,v[o+28>>2]=u,v[o+24>>2]=a,t=ia(t)}G=p[t+12>>2],U=p[t+4>>2],R=p[t+8>>2],j=p[t+16>>2],v[o+44>>2]=v[f+8>>2],f=(F=w(w(F*j)*w(w(p[n+16>>2]*w(255))*p[y+32>>2])))<w(4294967296)&F>=w(0)?~~F>>>0:0,F=l[r+45|0]?w(f>>>0):w(255),R=w(g*R),u=(t=(g=w(F*w(R*w(p[n+8>>2]*p[y+24>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0)<<8,O=w(O*U),k=u+((t=(g=w(w(O*w(p[n+4>>2]*p[y+20>>2]))*F))<w(4294967296)&g>=w(0)?~~g>>>0:0)+(f<<24)|0)|0,E=w(E*G),a=(f=(g=w(F*w(E*w(p[n+12>>2]*p[y+28>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0)<<16,f=0,t=0,u=0,l[y+56|0]&&(f=(g=w(F*w(E*w(p[n+12>>2]*p[y+48>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0,t=(g=w(F*w(R*w(p[n+8>>2]*p[y+44>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0,u=(F=w(F*w(O*w(p[n+4>>2]*p[y+40>>2]))))<w(4294967296)&F>=w(0)?~~F>>>0:0),k=a+k|0,b=l[r+45|0],d=l[r+44|0],c=yu(v[r+24>>2]),a=v[o+24>>2];n:if(d){if(b=(u+((t<<8)+(f<<16)|0)|0)+(b?-16777216:0)|0,c){if(nr(v[r+24>>2],a,v[o+28>>2],v[o+36>>2],a+12|0,P),f=v[r+24>>2],!(u=v[f+180>>2])){gi(f,y);break r}if(c=v[5305],a=v[5307],t=(d=v[f+164>>2])>>1,v[o+32>>2]=t,v[o+28>>2]=a,v[o+24>>2]=c,v[o+36>>2]=u,Ff(a,v[f+188>>2],u<<1),a=v[f+204>>2],c=v[f+172>>2],T=v[o+24>>2],v[r+28>>2]){if((0|d)<2)break n;for(A=(0|t)<=1?1:t,u=0,f=0;t=T+m(f,28)|0,d=u<<2,p[t>>2]=p[d+c>>2],$=4|d,p[t+4>>2]=p[$+c>>2],p[t+12>>2]=p[a+d>>2],p[t+16>>2]=p[a+$>>2],d=v[r+28>>2],da[v[v[d>>2]+16>>2]](d,t,t+4|0),v[t+24>>2]=b,v[t+20>>2]=k,u=u+2|0,(0|A)!=(0|(f=f+1|0)););break n}if((0|d)<2)break n;for(A=(0|t)<=1?1:t,u=0,f=0;t=T+m(f,28)|0,d=u<<2,p[t>>2]=p[d+c>>2],$=4|d,p[t+4>>2]=p[$+c>>2],p[t+12>>2]=p[a+d>>2],F=p[a+$>>2],v[t+24>>2]=b,v[t+20>>2]=k,p[t+16>>2]=F,u=u+2|0,(0|A)!=(0|(f=f+1|0)););}else if(u=v[o+32>>2],v[r+28>>2]){if(t=0,!u)break n;for(;c=v[r+28>>2],f=a+m(t,28)|0,da[v[v[c>>2]+16>>2]](c,f,f+4|0),v[f+24>>2]=b,v[f+20>>2]=k,(0|u)!=(0|(t=t+1|0)););}else if(f=0,u)for(;t=a+m(f,28)|0,v[t+24>>2]=b,v[t+20>>2]=k,(0|u)!=(0|(f=f+1|0)););}else{if(c){if(nr(v[r+24>>2],a,v[o+28>>2],v[o+36>>2],a+12|0,P),f=v[r+24>>2],!(u=v[f+180>>2])){gi(f,y);break r}if(b=v[5305],a=v[5307],t=(c=v[f+164>>2])>>1,v[o+32>>2]=t,v[o+28>>2]=a,v[o+24>>2]=b,v[o+36>>2]=u,Ff(a,v[f+188>>2],u<<1),a=v[f+204>>2],b=v[f+172>>2],d=v[o+24>>2],v[r+28>>2]){if((0|c)<2)break n;for(T=(0|t)<=1?1:t,f=0,u=0;t=d+m(f,24)|0,c=u<<2,p[t>>2]=p[c+b>>2],A=4|c,p[t+4>>2]=p[A+b>>2],p[t+12>>2]=p[a+c>>2],p[t+16>>2]=p[a+A>>2],c=v[r+28>>2],da[v[v[c>>2]+16>>2]](c,t,t+4|0),v[t+20>>2]=k,u=u+2|0,(0|T)!=(0|(f=f+1|0)););break n}if((0|c)<2)break n;for(T=(0|t)<=1?1:t,f=0,u=0;t=d+m(f,24)|0,c=u<<2,p[t>>2]=p[c+b>>2],A=4|c,p[t+4>>2]=p[A+b>>2],p[t+12>>2]=p[a+c>>2],F=p[a+A>>2],v[t+20>>2]=k,p[t+16>>2]=F,u=u+2|0,(0|T)!=(0|(f=f+1|0)););break n}if(t=v[o+32>>2],v[r+28>>2]){if(f=0,!t)break n;for(;b=v[r+28>>2],u=a+m(f,24)|0,da[v[v[b>>2]+16>>2]](b,u,u+4|0),v[u+20>>2]=k,(0|t)!=(0|(f=f+1|0)););break n}if(f=0,!t)break n;for(;v[20+(a+m(f,24)|0)>>2]=k,(0|t)!=(0|(f=f+1|0)););}v[5305]=v[5305]+m(v[o+32>>2],C),v[5307]=v[5307]+(v[o+36>>2]<<1),l[r+46|0]&&(f=v[r+68>>2],v[o+16>>2]=0,v[o+8>>2]=0,v[o+12>>2]=0,v[o>>2]=0,v[o+4>>2]=0,un(W,f+1|0,o),f=v[r+76>>2]+m(f,20)|0,v[f>>2]=I,t=v[r+32>>2],v[f+4>>2]=v[t+16>>2],v[f+8>>2]=v[o+32>>2],v[f+12>>2]=v[t+20>>2],v[f+16>>2]=v[o+36>>2]),v[o+40>>2]=v[v[y+4>>2]+80>>2];n:if(l[r+47|0]&&(u=f=v[r+84>>2],f)){for(;;){if(v[u+4>>2]!=(0|y)){if(u=v[u+12>>2])continue;break n}break}e:{i:{for(;;){if(v[f+4>>2]==(0|y))break i;if(!(f=v[f+12>>2]))break}f=0;break e}f=v[f+8>>2]}v[o+44>>2]=f}if(D=k=D-16|0,f=v[r+32>>2],!(t=v[f+4>>2])||(u=v[f+12>>2],v[(a=u+(t<<2)|0)-8>>2]!=v[o+40>>2]|v[a-4>>2]!=v[o+44>>2])?(v[k+12>>2]=0,mn(f,t+6|0,k+12|0),u=v[f+12>>2]+(t<<2)|0,v[u>>2]=v[o+24>>2],t=v[o+28>>2],v[u+4>>2]=t,v[u+8>>2]=v[o+32>>2],v[u+12>>2]=v[o+36>>2],v[u+16>>2]=v[o+40>>2],v[u+20>>2]=v[o+44>>2]):(v[(u=(t=u+(t<<2)|0)-16|0)>>2]=v[u>>2]+v[o+32>>2],v[(t=t-12|0)>>2]=v[t>>2]+v[o+36>>2],t=v[o+28>>2]),a=v[o+36>>2])for(b=v[f+16>>2],u=0;s[(c=(u<<1)+t|0)>>1]=b+h[c>>1],(0|a)!=(0|(u=u+1|0)););if(t=v[o+32>>2])for(b=v[f+32>>2]>>>2|0,c=v[o+24>>2],u=0;v[8+(c+(m(u,b)<<2)|0)>>2]=0,(0|t)!=(0|(u=u+1|0)););v[f+16>>2]=t+v[f+16>>2],v[f+20>>2]=a+v[f+20>>2],D=k+16|0,gi(v[r+24>>2],y)}else gi(v[r+24>>2],y);if((0|(S=S+1|0))==(0|i))break}return bf(v[r+24>>2]),(f=v[r+28>>2])&&da[v[v[f>>2]+20>>2]](f),D=o+48|0,o=v[5304],f=v[r+32>>2],v[f+28>>2]=v[5306],v[f+24>>2]=o,v[r+32>>2]},wf,function(r,n){n|=0,f[45+(r|=0)|0]=n},yf,ko,function(r,n,e,i,f){r|=0,n=w(n),e=w(e),i=w(i),f=w(f),p[r+60>>2]=f,p[r+56>>2]=i,p[r+52>>2]=e,p[r+48>>2]=n},function(r,n,e,i,f,t){r|=0,n|=0,e=w(e),i=w(i),f=w(f),t=w(t);var u=0;u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),da[0|u](n,e,i,f,t)},zu,yf,zu,yf,function(r){v[28+(r|=0)>>2]=0},If,$o,wf,function(r,n,e,i){r|=0,n|=0,e|=0,i=w(i),Pi(v[r+16>>2],n,e,i)},pe,Uu,yf,function(r,n,e){r|=0,n|=0,v[8+(e|=0)>>2]||(v[r+40>>2]=n,v[e+12>>2]=0,v[e+8>>2]=r,v[e+168>>2]=0,v[e+164>>2]=394)},tf,function(r,n){n|=0,f[46+(r|=0)|0]=n},Uo,function(r,n,e,i,t){r|=0,n|=0,e|=0,i|=0,t|=0;var u=w(0),o=w(0),a=0,c=0;if((r=v[r+8>>2])&&(r=tu(r,n))&&(n=v[r+60>>2]))if(t&&af(r,n=0|da[v[v[n>>2]+12>>2]](n)),lu(0|da[v[v[n>>2]+8>>2]](n),21008))for(u=w(e>>>0),p[n+68>>2]=u,o=w(i>>>0),p[n+72>>2]=o,p[n+76>>2]=u,p[n+80>>2]=o,p[n+52>>2]=u,p[n+56>>2]=o,r=0,Zn(n,w(0),w(0),w(1),w(1),0),zr(n),i=v[n+24>>2],t&&(i=Rf(i),(t=v[n+28>>2])&&(!(e=v[n+24>>2])|(0|e)==(0|i)||da[0|t](e)),v[n+28>>2]=0,v[n+24>>2]=i),e=v[n+112>>2],i=v[v[i+4>>2]>>2],n=0;t=i+m(r,24)|0,a=n<<2,p[t+12>>2]=p[a+e>>2],p[t+16>>2]=p[e+(4|a)>>2],n=n+2|0,4!=(0|(r=r+1|0)););else if(lu(0|da[v[v[n>>2]+8>>2]](n),20912)&&(u=w(e>>>0),p[n+84>>2]=u,o=w(i>>>0),p[n+88>>2]=o,p[n+92>>2]=u,p[n+96>>2]=o,p[n+196>>2]=u,p[n+200>>2]=o,p[n+180>>2]=0,p[n+184>>2]=0,p[n+188>>2]=1,p[n+192>>2]=1,f[n+228|0]=1,v[n+232>>2]=0,sr(n),r=v[n+68>>2],t&&(r=Rf(r),(i=v[n+72>>2])&&(!(e=v[n+68>>2])|(0|r)==(0|e)||da[0|i](e)),v[n+72>>2]=0,v[n+68>>2]=r),e=v[n+52>>2]))for(i=v[v[r+4>>2]>>2],t=e-1>>>1|0,e=v[n+116>>2],n=0,r=0;a=i+m(r,24)|0,c=n<<2,p[a+12>>2]=p[e+c>>2],p[a+16>>2]=p[e+(4|c)>>2],n=n+2|0,a=(0|r)!=(0|t),r=r+1|0,a;);},function(r,n,e,i,f,t){n|=0,e|=0,i|=0,f|=0,t|=0;var u,o=0;D=u=D-16|0,n=((o=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&o?v[v[n>>2]+r>>2]:r,o=n,n=Ae(u+4|0,e+4|0,v[e>>2],0),da[0|r](o,n,i,f,t),lf(n),D=u+16|0},function(r){ce(r|=0),or(r)},function(r,n,e){var i;if(r|=0,n|=0,e|=0,D=i=D-16|0,v[i+12>>2]=e,(e=v[r+8>>2])&&(n=tu(e,n),v[i+8>>2]=n,n)){f[r+47|0]=1;r:{n:{if(n=v[r+84>>2])for(e=v[i+8>>2];;){if((0|e)==v[n+4>>2])break n;if(!(n=v[n+12>>2]))break}n=Xf(20),v[n+12>>2]=0,v[n+16>>2]=0,v[n>>2]=11224,v[n+4>>2]=v[i+8>>2],v[n+8>>2]=v[i+12>>2],(e=v[r+84>>2])&&(v[e+16>>2]=n,v[n+12>>2]=e),v[r+84>>2]=n,v[r+88>>2]=v[r+88>>2]+1;break r}v[n+4>>2]=e,v[n+8>>2]=v[i+12>>2]}}D=i+16|0},function(r,n,e,i){n|=0,e|=0,i|=0;var f,t=0;D=f=D-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=Ae(f+4|0,e+4|0,v[e>>2],0),da[0|r](t,n,i),lf(n),D=f+16|0},function(r,n,e,i){var f;r|=0,n|=0,e|=0,i=w(i),D=f=D-16|0,r=v[r>>2],p[f+12>>2]=i,da[0|r](n,e,f+12|0),D=f+16|0},bt,hf,hf,bt,hf,function(r,n,e,i){var f;n|=0,e|=0,i|=0,D=f=D-16|0,r=v[(r|=0)>>2],s[f+14>>1]=i,da[0|r](n,e,f+14|0),D=f+16|0},function(r,n,e,i){var f,t;n|=0,e|=0,i|=0,D=f=D-16|0,r=v[(r|=0)>>2],t=n,n=Ae(f+4|0,i+4|0,v[i>>2],0),da[0|r](t,e,n),lf(n),D=f+16|0},hf,Kt,Kt,function(r,n,e,i){return r|=0,n|=0,e=w(e),i=w(i),0|da[v[r>>2]](n,e,i)},function(r,n,e,i,f,t){return r|=0,n|=0,e=w(e),i=w(i),f=w(f),t=w(t),0|da[v[r>>2]](n,e,i,f,t)},Kt,Kt,Kt,Kt,Kt,function(r,n,e,i,f){r|=0,n|=0,e|=0,i=w(i),f=w(f),da[v[r>>2]](n,e,i,f)},Kt,Ti,Ti,Kt,Kt,Kt,Kt,Kt,function(r,n,e){n|=0,e|=0,da[v[(r|=0)>>2]](n,e)},Kt,Kt,Kt,Kt,function(r,n,e){var i;return n|=0,e|=0,D=i=D-16|0,da[v[(r|=0)>>2]](i,n,e),r=Hn(Xf(16),i),ae(i),D=i+16|0,0|r},function(r,n){var e;return n|=0,D=e=D-16|0,da[v[(r|=0)>>2]](e,n),r=Ro(Xf(16),e),Ru(e),D=e+16|0,0|r},function(r,n,e){var i;return n|=0,e|=0,D=i=D-16|0,da[v[(r|=0)>>2]](i,n,e),r=Ro(Xf(16),i),Ru(i),D=i+16|0,0|r},Ti,Kt,Kt,Kt,Kt,Kt,Kt,Kt,Kt,Kt,Kt,Kt,Kt,Kt,Kt,Kt,function(r,n,e,i,f){var t;r|=0,n|=0,e|=0,i=w(i),f|=0,D=t=D-16|0,r=v[r>>2],v[t+12>>2]=f,da[0|r](n,e,i,t+12|0),R(v[t+12>>2]),D=t+16|0},Kt,Kt,Kt,Kt,Kt,function(r,n,e,i,f){return n|=0,e|=0,i|=0,f|=0,0|da[v[(r|=0)>>2]](n,e,i,f)},Kt,Kt,Kt,Kt,Kt,Kt,function(r,n,e,i){r|=0,n|=0,e=w(e),i=w(i),da[v[r>>2]](n,e,i)},Kt,function(){return 21457},qo,function(r){da[0|(r|=0)]()},function(){var r=0,n=0;r=Tt(4),v[r>>2]=9520,v[r>>2]=16928,v[5208]=r,v[5304]||(n=Y(3669960),v[5304]=n,r=Y(1048560),v[5305]=n,v[5306]=r,v[5307]=r)},function(){var r=0;(r=v[5208])&&da[v[v[r>>2]+4>>2]](r),(r=v[5371])&&(or(r),v[5371]=0),v[5372]=0,or(v[5304]),v[5304]=0,or(v[5306]),v[5306]=0},function(r,n){return n|=0,0|da[0|(r|=0)](n)},function(r){r|=0;var n=0;r:{if(n=v[5371]){if(d[5372]>=r>>>0)break r;or(n)}n=Tt(r),v[5372]=r,v[5371]=n}return 0|n},vf,function(r){r|=0;var n=0,e=0;r:if(n=v[5366]){for(;;){if(!qi(n+4|0,r)){if(n=v[n+20>>2])continue;break r}break}if(n=v[5366]){for(;;){if(!qi(n+4|0,r)){if(n=v[n+20>>2])continue;break r}break}e=v[n+16>>2]}}return 0|e},function(r,n,e){var i;return r|=0,e|=0,D=i=D-32|0,n=Ae(i+20|0,4+(n|=0)|0,v[n>>2],0),e=Ae(i+8|0,e+4|0,v[e>>2],0),r=0|da[0|r](n,e),lf(e),lf(n),D=i+32|0,0|r},function(r,n){r|=0,n|=0;var e,i,t,u,o=0,a=0,c=0,b=0,k=0,h=0,y=0,g=0,F=0,A=w(0),T=0,$=0,I=0,C=w(0),P=0,E=0,O=0,R=0,S=0,W=0,G=0,U=0,j=0,H=0,L=0,M=w(0),_=0,z=w(0),x=0;D=u=D-48|0,n=oi(Xf(40),v[n+8>>2],v[n+4>>2]),n=pt(Xf(12),n),f[28+(i=u+4|0)|0]=0,v[i+4>>2]=n,v[i+20>>2]=0,v[i+24>>2]=1065353216,v[i+12>>2]=0,v[i+16>>2]=0,v[i+8>>2]=10280,v[i>>2]=10600,ht(n=i+32|0),v[i+24>>2]=1065353216,r=v[r+8>>2],D=e=D-32|0,ke(n,8524),v[i+12>>2]=0,t=He(Tt(32),r),b=Hr(Xf(232)),(r=In(t,3809))&&(ke(b+176|0,Ct(r,4799,0)),ke(b+164|0,Ct(r,5340,0)),_=b,z=Pt(r,1374,w(0)),p[_+148>>2]=z,_=b,z=Pt(r,1200,w(0)),p[_+152>>2]=z,_=b,z=Pt(r,4705,w(0)),p[_+156>>2]=z,_=b,z=Pt(r,2166,w(0)),p[_+160>>2]=z,_=b,z=Pt(r,2596,w(30)),p[_+204>>2]=z,ke(b+220|0,Ct(r,3787,0)),ke(b+208|0,Ct(r,2857,0))),n=In(t,2745),r=v[n+12>>2],v[e+20>>2]=0,Fn(b+16|0,r,e+20|0);r:{n:{if(r=v[n+4>>2])for(;;){if(k=0,(o=Ct(r,1817,0))&&(k=ru(b,n=gu(e+20|0,o,0)),lf(n),!k))break n;n=Xn(n=Xf(64),T,o=gu(e+20|0,Ct(r,5744,0),0),k),lf(o),_=n,z=w(Pt(r,4667,w(0))*p[i+24>>2]),p[_+24>>2]=z,_=n,z=w(Pt(r,1374,w(0))*p[i+24>>2]),p[_+28>>2]=z,_=n,z=w(Pt(r,1200,w(0))*p[i+24>>2]),p[_+32>>2]=z,_=n,z=Pt(r,4027,w(0)),p[_+36>>2]=z,_=n,z=Pt(r,6697,w(1)),p[_+40>>2]=z,_=n,z=Pt(r,6604,w(1)),p[_+44>>2]=z,_=n,z=Pt(r,6671,w(0)),p[_+48>>2]=z,_=n,z=Pt(r,6565,w(0)),p[_+52>>2]=z,a=Ct(r,4434,4552),v[n+56>>2]=0;e:if(ri(a,4552)){if(o=1,ri(a,4237)&&(o=2,ri(a,3983)&&(o=3,ri(a,5827)))){if(ri(a,4006))break e;o=4}v[n+56>>2]=o}if(_=n,x=Ce(r),f[_+60|0]=x,v[v[b+28>>2]+(T<<2)>>2]=n,T=T+1|0,!(r=v[r>>2]))break}e:{if((o=In(t,2422))&&(ca(n=b+32|0,v[o+12>>2]),r=v[o+12>>2],v[e+20>>2]=0,Fn(n,r,e+20|0),r=v[o+4>>2]))for(T=0;;){if(a=ru(b,n=gu(e+20|0,o=Ct(r,5314,0),0)),lf(n),!a)break e;n=Bn(n=Xf(84),T,o=gu(e+20|0,Ct(r,5744,0),0),a),lf(o),(a=Ct(r,3491,0))&&(_=o=Ko(n),z=de(a,0),p[_+4>>2]=z,_=o,z=de(a,1),p[_+8>>2]=z,_=o,z=de(a,2),p[_+12>>2]=z,_=o,z=de(a,3),p[_+16>>2]=z),(a=Ct(r,4578,0))&&(_=o=fa(n),z=de(a,0),p[_+4>>2]=z,_=o,z=de(a,1),p[_+8>>2]=z,A=de(a,2),v[o+16>>2]=1065353216,p[o+12>>2]=A,oa(n)),(o=In(r,1824))&&(ee(n+68|0,o=gu(e+20|0,v[o+16>>2],0)),lf(o));i:if(o=In(r,6086)){if(a=1,ri(o=v[o+16>>2],4945)&&(a=2,ri(o,1187))){if(ri(o,4427))break i;a=3}v[n+80>>2]=a}if(v[v[b+44>>2]+(T<<2)>>2]=n,T=T+1|0,!(r=v[r>>2]))break}if((o=In(t,4583))&&(ca(n=b+100|0,v[o+12>>2]),r=v[o+12>>2],v[e+20>>2]=0,Fn(n,r,e+20|0),n=v[o+4>>2]))for(;;){g=Ve(o=Xf(60),r=gu(a=e+20|0,Ct(n,5744,0),0)),lf(r),ea(g,$t(n,3604,0)),ua(g,Ce(n)),o=g+24|0,$=In(n,2745),ca(o,v[$+12>>2]),r=v[$+12>>2],v[e+20>>2]=0,Fn(o,r,a);i:if(r=v[$+4>>2]){for(k=0;;){if(a=ru(b,$=gu(e+20|0,v[r+16>>2],0)),v[(o=k<<2)+v[g+36>>2]>>2]=a,lf($),v[o+v[g+36>>2]>>2]){if(k=k+1|0,r=v[r>>2])continue;break i}break}da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8488,0),r=gu(e+8|0,v[r+16>>2],0)),lf(r),lf(n);break r}if(_=g,x=ru(b,r=gu(e+20|0,o=Ct(n,2229,0),0)),v[_+40>>2]=x,lf(r),!v[g+40>>2]){b&&da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8393,0),r=gu(e+8|0,o,0)),lf(r),lf(n);break r}if(_=g,z=Pt(n,1214,w(1)),p[_+52>>2]=z,_=g,z=w(Pt(n,2573,w(0))*p[i+24>>2]),p[_+56>>2]=z,_=g,x=$t(n,4932,1)?1:-1,v[_+44>>2]=x,_=g,x=!!(0|$t(n,2564,0)),f[_+48|0]=x,_=g,x=!!(0|$t(n,4804,0)),f[_+49|0]=x,_=g,x=!!(0|$t(n,4465,0)),f[_+50|0]=x,v[v[b+112>>2]+(I<<2)>>2]=g,I=I+1|0,!(n=v[n>>2]))break}if((o=In(t,4434))&&(ca(n=b+116|0,v[o+12>>2]),r=v[o+12>>2],v[e+20>>2]=0,Fn(n,r,e+20|0),n=v[o+4>>2]))for(I=0;;){g=Zf(o=Xf(88),r=gu(a=e+20|0,Ct(n,5744,0),0)),lf(r),ea(g,$t(n,3604,0)),ua(g,Ce(n)),o=g+24|0,$=In(n,2745),ca(o,v[$+12>>2]),r=v[$+12>>2],v[e+20>>2]=0,Fn(o,r,a);i:if(k=v[$+4>>2]){for(T=0;;){if(o=ru(b,a=gu(e+20|0,v[k+16>>2],0)),v[(r=T<<2)+v[g+36>>2]>>2]=o,lf(a),v[r+v[g+36>>2]>>2]){if(T=T+1|0,k=v[k>>2])continue;break i}break}b&&da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8439,0),r=gu(e+8|0,v[k+16>>2],0)),lf(r),lf(n);break r}if(_=g,x=ru(b,r=gu(e+20|0,o=Ct(n,2229,0),0)),v[_+40>>2]=x,lf(r),!v[g+40>>2]){b&&da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8393,0),r=gu(e+8|0,o,0)),lf(r),lf(n);break r}if(_=g,x=!!(0|$t(n,4559,0)),f[_+85|0]=x,_=g,x=!!(0|$t(n,4970,0)),f[_+84|0]=x,_=g,z=Pt(n,4027,w(0)),p[_+60>>2]=z,_=g,z=w(Pt(n,1374,w(0))*p[i+24>>2]),p[_+64>>2]=z,_=g,z=w(Pt(n,1200,w(0))*p[i+24>>2]),p[_+68>>2]=z,_=g,z=Pt(n,6697,w(0)),p[_+72>>2]=z,_=g,z=Pt(n,6604,w(0)),p[_+76>>2]=z,_=g,z=Pt(n,6565,w(0)),p[_+80>>2]=z,_=g,z=Pt(n,1252,w(1)),p[_+44>>2]=z,_=g,z=Pt(n,1262,w(1)),p[_+48>>2]=z,_=g,z=Pt(n,1275,w(1)),p[_+52>>2]=z,_=g,z=Pt(n,1243,w(1)),p[_+56>>2]=z,v[v[b+128>>2]+(I<<2)>>2]=g,I=I+1|0,!(n=v[n>>2]))break}if((o=In(t,4720))&&(ca(n=b+132|0,v[o+12>>2]),r=v[o+12>>2],v[e+20>>2]=0,Fn(n,r,e+20|0),n=v[o+4>>2]))for(I=0;;){r=Yf(r=Xf(76),o=gu($=e+20|0,Ct(n,5744,0),0)),lf(o),ea(r,$t(n,3604,0)),ua(r,Ce(n)),a=r+24|0,g=In(n,2745),ca(a,v[g+12>>2]),o=v[g+12>>2],v[e+20>>2]=0,Fn(a,o,$);i:if(k=v[g+4>>2]){for(T=0;;){if(a=ru(b,$=gu(e+20|0,v[k+16>>2],0)),v[(o=T<<2)+v[r+36>>2]>>2]=a,lf($),v[o+v[r+36>>2]>>2]){if(T=T+1|0,k=v[k>>2])continue;break i}break}b&&da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8466,0),r=gu(e+8|0,v[k+16>>2],0)),lf(r),lf(n);break r}if(_=r,x=ou(b,o=gu(e+20|0,a=Ct(n,2229,0),0)),v[_+40>>2]=x,lf(o),!v[r+40>>2]){b&&da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8094,0),r=gu(e+8|0,a,0)),lf(r),lf(n);break r}i:{if(ri(o=Ct(n,5893,2084),6244)){if(ri(o,2084))break i;o=1}else o=0;v[r+44>>2]=o}a=0;i:{if(ri(o=Ct(n,5974,4667),4667)&&(a=1,ri(o,6244))){if(ri(o,2084))break i;a=2}v[r+48>>2]=a}a=0;i:{if(ri(o=Ct(n,6005,2076),2076)&&(a=1,ri(o,4409))){if(ri(o,5835))break i;a=2}v[r+52>>2]=a}if(_=r,z=Pt(n,4027,w(0)),p[_+56>>2]=z,A=Pt(n,3919,w(0)),p[r+60>>2]=A,v[r+44>>2]||(p[r+60>>2]=A*p[i+24>>2]),A=Pt(n,4892,w(0)),p[r+64>>2]=A,d[r+48>>2]<=1&&(p[r+64>>2]=A*p[i+24>>2]),_=r,z=Pt(n,1252,w(1)),p[_+68>>2]=z,_=r,z=Pt(n,1262,w(1)),p[_+72>>2]=z,v[v[b+144>>2]+(I<<2)>>2]=r,I=I+1|0,!(n=v[n>>2]))break}if(G=i+8|0,(o=In(t,2668))&&(ca(n=b+48|0,v[o+12>>2]),r=v[o+12>>2],v[e+20>>2]=0,Fn(n,r,e+20|0),h=v[o+4>>2]))for(;;){if(k=Ct(h,5744,8524),l[0|k]||(k=v[h+28>>2]),P=ge(n=Xf(68),r=gu(e+20|0,k,0)),lf(r),(r=In(h,2745))&&(r=v[r+4>>2]))for(o=P+36|0;;){if(a=ru(b,n=gu(e+20|0,v[r+16>>2],0)),lf(n),v[e+4>>2]=a,!a){b&&da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8417,0),r=gu(e+8|0,v[r+16>>2],0)),lf(r),lf(n);break r}if(Sn(o,e+4|0),!(r=v[r>>2]))break}if((r=In(h,4583))&&(r=v[r+4>>2]))for(o=P+52|0;;){if(a=Vt(b,n=gu(e+20|0,v[r+16>>2],0)),lf(n),!a){b&&da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8234,0),r=gu(e+8|0,v[r+16>>2],0)),lf(r),lf(n);break r}if(v[e+20>>2]=a,Sn(o,e+20|0),!(r=v[r>>2]))break}if((r=In(h,4434))&&(r=v[r+4>>2]))for(o=P+52|0;;){if(a=Zt(b,n=gu(e+20|0,v[r+16>>2],0)),lf(n),!a){b&&da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8135,0),r=gu(e+8|0,v[r+16>>2],0)),lf(r),lf(n);break r}if(v[e+20>>2]=a,Sn(o,e+20|0),!(r=v[r>>2]))break}if((r=In(h,4720))&&(r=v[r+4>>2]))for(o=P+52|0;;){if(a=Yt(b,n=gu(e+20|0,v[r+16>>2],0)),lf(n),!a){b&&da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8173,0),r=gu(e+8|0,v[r+16>>2],0)),lf(r),lf(n);break r}if(v[e+20>>2]=a,Sn(o,e+20|0),!(r=v[r>>2]))break}if(v[v[b+60>>2]+(E<<2)>>2]=P,ri(k,2138)||(v[b+64>>2]=P),r=In(h,2529),y=v[(r||h)+4>>2])for(;;){if(I=ou(b,r=gu(e+20|0,v[y+28>>2],0)),lf(r),r=v[y+4>>2])for(;;){n=Ct(r,5744,g=v[r+28>>2]),$=Ct(r,4720,n);i:{f:{t:{u:{o:{a:{c:{if(ri(o=Ct(r,5177,4298),4298)){if(!ri(o,4752))break c;if(!ri(o,4746))break c;if(!ri(o,1202))break a;if(!ri(o,4720))break o;if(!ri(o,4855))break t;if(!ri(o,1645))break u;b&&da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8033,0),r=gu(e+8|0,o,0)),lf(r),lf(n);break r}if(a=v[i+4>>2],o=gu(e+20|0,n,0),n=gu(e+8|0,$,0),k=0|da[v[v[a>>2]+12>>2]](a,P,o,n),lf(n),lf(o),!k)break i;ke(k+116|0,$),_=k,z=w(Pt(r,1374,w(0))*p[i+24>>2]),p[_+32>>2]=z,_=k,z=w(Pt(r,1200,w(0))*p[i+24>>2]),p[_+36>>2]=z,_=k,z=Pt(r,6697,w(1)),p[_+44>>2]=z,_=k,z=Pt(r,6604,w(1)),p[_+48>>2]=z,_=k,z=Pt(r,4027,w(0)),p[_+40>>2]=z,_=k,z=w(Pt(r,4705,w(32))*p[i+24>>2]),p[_+52>>2]=z,_=k,z=w(Pt(r,2166,w(32))*p[i+24>>2]),p[_+56>>2]=z,(n=Ct(r,3491,0))&&(A=de(n,0),_=ia(k),z=A,p[_+4>>2]=z,A=de(n,1),_=ia(k),z=A,p[_+8>>2]=z,A=de(n,2),_=ia(k),z=A,p[_+12>>2]=z,A=de(n,3),_=ia(k),z=A,p[_+16>>2]=z),zr(k),n=v[i+4>>2],da[v[v[n>>2]+36>>2]](n,k);break f}if(a=v[i+4>>2],o=gu(e+20|0,n,0),n=gu(e+8|0,$,0),k=0|da[v[v[a>>2]+16>>2]](a,P,o,n),lf(n),lf(o),!k)break i;if(ke(k+168|0,$),(n=Ct(r,3491,0))&&(A=de(n,0),_=ra(k),z=A,p[_+4>>2]=z,A=de(n,1),_=ra(k),z=A,p[_+8>>2]=z,A=de(n,2),_=ra(k),z=A,p[_+12>>2]=z,A=de(n,3),_=ra(k),z=A,p[_+16>>2]=z),_=k,z=w(Pt(r,4705,w(32))*p[i+24>>2]),p[_+196>>2]=z,_=k,z=w(Pt(r,2166,w(32))*p[i+24>>2]),p[_+200>>2]=z,!(o=In(r,1817))){if(o=k+136|0,a=In(r,2802),ba(o,v[a+12>>2]),n=v[a+12>>2],s[e+20>>1]=0,pn(o,n,e+20|0),T=v[a+4>>2])for(o=v[k+148>>2],n=0;s[o+(n<<1)>>1]=v[T+20>>2],n=n+1|0,T=v[T>>2];);if(o=k+120|0,n=In(r,2398),ca(o,a=v[n+12>>2]),v[e+20>>2]=0,yn(o,a,e+20|0),T=v[n+4>>2])for(o=v[k+132>>2],n=0;p[o+(n<<2)>>2]=p[T+24>>2],n=n+1|0,T=v[T>>2];);if(dr(i,r,k,a),sr(k),_=k,x=$t(r,4506,0),v[_+224>>2]=x,(a=In(r,2842))&&(ba(o=k+152|0,v[a+12>>2]),n=v[a+12>>2],s[e+20>>1]=0,pn(o,n,e+20|0),T=v[a+4>>2]))for(o=v[k+164>>2],n=0;s[o+(n<<1)>>1]=v[T+20>>2],n=n+1|0,T=v[T>>2];);n=v[i+4>>2],da[v[v[n>>2]+36>>2]](n,k);break f}n=$t(r,4473,1),n=Cf(c=Xf(40),k,$=gu(e+20|0,Ct(r,4352,0),0),F=v[I+4>>2],o=gu(a=e+8|0,v[o+16>>2],0),!!(0|n)),lf(o),lf($),v[e+8>>2]=n,Sn(G,a);break f}o=v[i+4>>2],n=gu(e+20|0,n,0),k=0|da[v[v[o>>2]+20>>2]](o,P,n),lf(n),dr(i,r,k,$t(r,1605,0)<<1),n=v[i+4>>2],da[v[v[n>>2]+36>>2]](n,k);break f}if(o=v[i+4>>2],n=gu(a=e+20|0,n,0),k=0|da[v[v[o>>2]+24>>2]](o,P,n),lf(n),_=k,x=!!(0|$t(r,6250,0)),f[_+80|0]=x,_=k,x=!!(0|$t(r,6289,1)),f[_+81|0]=x,dr(i,r,k,(n=$t(r,1605,0))<<1),ca(o=k- -64|0,n=(0|n)/3|0),v[e+20>>2]=0,yn(o,n,a),T=v[In(r,2705)+4>>2])for(o=v[k+76>>2],n=0;p[o+(n<<2)>>2]=p[T+24>>2]*p[i+24>>2],n=n+1|0,T=v[T>>2];);n=v[i+4>>2],da[v[v[n>>2]+36>>2]](n,k);break f}o=v[i+4>>2],n=gu(e+20|0,n,0),k=0|da[v[v[o>>2]+28>>2]](o,P,n),lf(n),_=k,z=w(Pt(r,1374,w(0))*p[i+24>>2]),p[_+20>>2]=z,_=k,z=w(Pt(r,1200,w(0))*p[i+24>>2]),p[_+24>>2]=z,_=k,z=Pt(r,4027,w(0)),p[_+28>>2]=z,n=v[i+4>>2],da[v[v[n>>2]+36>>2]](n,k);break f}o=v[i+4>>2],n=gu(e+20|0,n,0),k=0|da[v[v[o>>2]+32>>2]](o,P,n),lf(n),(n=Ct(r,6113,0))&&(_=k,x=ou(b,n=gu(e+20|0,n,0)),v[_+64>>2]=x,lf(n)),dr(i,r,k,$t(r,1605,0)<<1),n=v[i+4>>2],da[v[v[n>>2]+36>>2]](n,k)}yt(P,o=v[I+4>>2],n=gu(e+20|0,g,0),k),lf(n)}if(!(r=v[r>>2]))break}if(!(y=v[y>>2]))break}if(E=E+1|0,!(h=v[h>>2]))break}if((0|(n=v[i+12>>2]))>0)for(k=0;a=v[v[i+20>>2]+(k<<2)>>2],(r=v[a+12>>2]?iu(b,a+8|0):v[b+64>>2])&&(o=gt(r,v[a+20>>2],a+24|0))&&(r=v[a+4>>2],v[r+56>>2]=l[a+36|0]?o:r,zn(r,o),sr(v[a+4>>2]),r=v[i+4>>2],da[v[v[r>>2]+36>>2]](r,v[a+4>>2])),(0|n)!=(0|(k=k+1|0)););if(Pn(G),v[i+12>>2]=0,(o=In(t,2512))&&(ca(n=b+68|0,v[o+12>>2]),r=v[o+12>>2],v[e+20>>2]=0,Fn(n,r,e+20|0),r=v[o+4>>2]))for(T=0;o=rf(o=Xf(56),n=gu(e+20|0,v[r+28>>2],0)),lf(n),_=o,x=$t(r,1776,0),v[_+16>>2]=x,_=o,z=Pt(r,2334,w(0)),p[_+20>>2]=z,ke(o+24|0,Ct(r,4831,0)),ke(o+36|0,n=Ct(r,3787,0)),n&&(_=o,z=Pt(r,5652,w(1)),p[_+48>>2]=z,_=o,z=Pt(r,6078,w(0)),p[_+52>>2]=z),v[v[b+80>>2]+(T<<2)>>2]=o,T=T+1|0,r=v[r>>2];);if((o=In(t,2624))&&(ca(n=b+84|0,v[o+12>>2]),r=v[o+12>>2],k=0,v[e+20>>2]=0,Fn(n,r,e+20|0),r=v[o+4>>2]))for(;;){A=w(0),D=c=D-80|0,v[c+76>>2]=0,v[c+68>>2]=0,v[c+72>>2]=0,v[c+64>>2]=1048,U=In(r,2745),n=In(r,2422),o=r,T=In(r,4583),P=In(r,4434),(R=In(r,4720))||(R=In(o,2724)),G=In(o,4473),j=In(o,3610),L=In(o,2512),j||(j=In(o,3600));i:{f:{if(n&&(F=v[n+4>>2]))for(I=v[2413],g=v[2317],$=v[2738];;){if(S=eu(b,r=gu(c+48|0,v[F+28>>2],0)),lf(r),-1==(0|S)){Pn(c- -64|0),h=0,Vn(i,0,n=gu(c+48|0,8118,0),r=gu(c+32|0,v[F+28>>2],0)),lf(r),lf(n);break i}if(y=v[F+4>>2])for(;;){if(ri(r=v[y+28>>2],1824))if(ri(r,3491)){if(ri(r,3506))break f;if(W=Le(Xf(40),v[y+12>>2]),v[W+36>>2]=S,n=0,r=v[y+4>>2])for(;O=Ct(r,2160,0),a=Ct(r,4578,0),Oe(W,n,Pt(r,5659,w(0)),de(O,0),de(O,1),de(O,2),de(O,3),de(a,0),de(a,1),de(a,2)),_e(r,W,n),n=n+1|0,r=v[r>>2];);v[c+48>>2]=W,Sn(c- -64|0,c+48|0),n=m($,v[y+12>>2]-1|0),r=W+32|0}else{if(O=Xe(Xf(40),v[y+12>>2]),v[O+20>>2]=S,n=0,r=v[y+4>>2])for(;a=Ct(r,3491,0),Ri(O,n,Pt(r,5659,w(0)),de(a,0),de(a,1),de(a,2),de(a,3)),_e(r,O,n),n=n+1|0,r=v[r>>2];);v[c+48>>2]=O,Sn(c- -64|0,c+48|0),n=m(g,v[y+12>>2]-1|0),r=O+36|0}else{if(O=sn(Xf(40),v[y+12>>2]),v[O+4>>2]=S,h=0,r=v[y+4>>2])for(;a=c+48|0,n=In(r,5744),n=gu(a,n=(0|I)!=v[n+8>>2]?v[n+16>>2]:8524,0),Wf(O,h,Pt(r,5659,w(0)),n),lf(n),h=h+1|0,r=v[r>>2];);v[c+48>>2]=O,Sn(c- -64|0,c+48|0),n=v[y+12>>2]-1|0,r=O+20|0}if(A=A>(C=p[v[r>>2]+(n<<2)>>2])?A:C,!(y=v[y>>2]))break}if(!(F=v[F>>2]))break}t:{if(U&&(y=v[U+4>>2]))for(a=v[2721];;){if(g=Xt(b,r=gu(c+48|0,v[y+28>>2],0)),lf(r),-1==(0|g)){Pn(c- -64|0),h=0,Vn(i,0,n=gu(c+48|0,8508,0),r=gu(c+32|0,v[y+28>>2],0)),lf(r),lf(n);break i}if(F=v[y+4>>2])for(;;){if(ri(r=v[F+28>>2],5052)){if(n=ri(r,5821),$=ri(r,5077),(r=ri(r,3732))&&!(!n|!$))break t;if(C=p[i+24>>2],n?$?(M=w(0),n=0,r||(n=bu(Xf(40),v[F+12>>2]))):(M=w(0),n=Ue(Xf(40),v[F+12>>2])):(M=w(1),n=cu(Xf(40),v[F+12>>2])),v[n+36>>2]=g,r=v[F+4>>2])for(C=$?w(1):C,h=0;Af(n,h,Pt(r,5659,w(0)),w(C*Pt(r,1374,M)),w(C*Pt(r,1200,M))),_e(r,n,h),h=h+1|0,r=v[r>>2];);v[c+48>>2]=n,Sn(c- -64|0,c+48|0),h=m(a,v[F+12>>2]-1|0),r=n+32|0}else{if(n=Qe(Xf(40),v[F+12>>2]),v[n+20>>2]=g,h=0,r=v[F+4>>2])for(;Mf(n,h,Pt(r,5659,w(0)),Pt(r,5791,w(0))),_e(r,n,h),h=h+1|0,r=v[r>>2];);v[c+48>>2]=n,Sn(c- -64|0,c+48|0),h=(v[F+12>>2]<<1)-2|0,r=n+36|0}if(A=A>(C=p[v[r>>2]+(h<<2)>>2])?A:C,!(F=v[F>>2]))break}if(!(y=v[y>>2]))break}if(T&&(y=v[T+4>>2]))for(g=v[2405];;){$=Vt(b,r=gu(c+48|0,v[y+28>>2],0)),lf(r),I=Co(Xf(40),v[y+12>>2]);u:if(a=v[b+104>>2])for(n=v[b+112>>2],r=0;;){if((0|$)==v[n+(r<<2)>>2]){v[I+36>>2]=r;break u}if((0|a)==(0|(r=r+1|0)))break}if(h=0,r=v[y+4>>2])for(;ei(I,h,Pt(r,5659,w(0)),Pt(r,1214,w(1)),w(Pt(r,2573,w(0))*p[i+24>>2]),$t(r,4932,1)?1:-1,!!(0|$t(r,2564,0)),!!(0|$t(r,4804,0))),_e(r,I,h),h=h+1|0,r=v[r>>2];);if(v[c+48>>2]=I,Sn(c- -64|0,c+48|0),A=A>(C=p[v[I+32>>2]+(m(g,v[y+12>>2]-1|0)<<2)>>2])?A:C,!(y=v[y>>2]))break}if(P&&(y=v[P+4>>2]))for(g=v[2713];;){$=Zt(b,r=gu(c+48|0,v[y+28>>2],0)),lf(r),I=mo(Xf(40),v[y+12>>2]);u:if(a=v[b+120>>2])for(n=v[b+128>>2],r=0;;){if((0|$)==v[n+(r<<2)>>2]){v[I+36>>2]=r;break u}if((0|a)==(0|(r=r+1|0)))break}if(h=0,r=v[y+4>>2])for(;Si(I,h,Pt(r,5659,w(0)),Pt(r,1252,w(1)),Pt(r,1262,w(1)),Pt(r,1275,w(1)),Pt(r,1243,w(1))),_e(r,I,h),h=h+1|0,r=v[r>>2];);if(v[c+48>>2]=I,Sn(c- -64|0,c+48|0),A=A>(C=p[v[I+32>>2]+(m(g,v[y+12>>2]-1|0)<<2)>>2])?A:C,!(y=v[y>>2]))break}u:{if(R&&(E=v[R+4>>2]))for($=v[2477],a=v[2469];;){if(I=Yt(b,r=gu(c+48|0,v[E+28>>2],0)),lf(r),!I)break u;o:{if(n=v[b+136>>2])for(r=v[b+144>>2],y=0;;){if((0|I)==v[r+(y<<2)>>2])break o;if((0|n)==(0|(y=y+1|0)))break}y=0}if(F=v[E+4>>2])for(;;){if(ri(g=v[F+28>>2],3919)&&ri(g,4892)){if(!ri(g,1214)){if(n=wo(Xf(40),v[F+12>>2]),v[n+36>>2]=y,h=0,r=v[F+4>>2])for(;Af(n,h,Pt(r,5659,w(0)),Pt(r,1252,w(1)),Pt(r,1262,w(1))),_e(r,n,h),h=h+1|0,r=v[r>>2];);v[c+48>>2]=n,Sn(c- -64|0,c+48|0),A=A>(C=p[v[n+32>>2]+(m(a,v[F+12>>2]-1|0)<<2)>>2])?A:C}}else{if(r=ri(g,4892),P=Xf(40),n=v[F+12>>2],r?(ni(P,n),n=!v[I+44>>2]):(Wt(P,n),n=d[I+48>>2]<2),C=p[i+24>>2],v[P+36>>2]=y,r=v[F+4>>2])for(C=n?C:w(1),h=0;Lf(P,h,Pt(r,5659,w(0)),w(C*Pt(r,g,w(0)))),_e(r,P,h),h=h+1|0,r=v[r>>2];);v[c+48>>2]=P,Sn(c- -64|0,c+48|0),A=A>(C=p[v[P+32>>2]+(m($,v[F+12>>2]-1|0)<<2)>>2])?A:C}if(!(F=v[F>>2]))break}if(!(E=v[E>>2]))break}if(G&&(R=v[G+4>>2]))for(;;){if(T=iu(b,r=gu(c+48|0,v[R+28>>2],0)),lf(r),S=v[R+4>>2])for(;;){if(O=eu(b,r=gu(c+48|0,v[S+28>>2],0)),lf(r),n=v[S+4>>2])for(;;){if(H=gt(T,O,r=gu(c+48|0,v[n+28>>2],0)),lf(r),H){if(r=v[H+40>>2],W=(U=v[H+24>>2])?(r>>>0)/3<<1:r,F=Or(Xf(60),v[n+12>>2]),v[F+56>>2]=H,v[F+20>>2]=O,y=v[n+4>>2])for(P=H+36|0,E=0,G=!!(0|U)|(0|W)<=0;;){r=In(y,2872),v[c+60>>2]=0,v[c+52>>2]=0,v[c+56>>2]=0,v[c+48>>2]=8712;o:if(r){h=$t(y,2183,0),v[c+32>>2]=0,yn(c+48|0,W,c+32|0),r=v[r+4>>2];a:if(p[i+24>>2]!=w(1)){if(r)for(a=v[c+60>>2];p[a+(h<<2)>>2]=p[r+24>>2]*p[i+24>>2],h=h+1|0,r=v[r>>2];);}else{if(!r)break a;for(a=v[c+60>>2];p[a+(h<<2)>>2]=p[r+24>>2],h=h+1|0,r=v[r>>2];);}if(!G)for(I=v[H+48>>2],r=0,g=v[c+60>>2];p[(a=g+($=r<<2)|0)>>2]=p[$+I>>2]+p[a>>2],(0|W)!=(0|(r=r+1|0)););}else{if(U){v[c+32>>2]=0,yn(c+48|0,W,c+32|0);break o}_i(c+48|0,P)}if(r=c+48|0,_f(F,E,Pt(y,5659,w(0)),r),_e(y,F,E),E=E+1|0,vo(r),!(y=v[y>>2]))break}v[c+48>>2]=F,Sn(c- -64|0,c+48|0),A=A>(C=p[(v[F+36>>2]+(v[n+12>>2]<<2)|0)-4>>2])?A:C}else Pn(c- -64|0);if(!(n=v[n>>2]))break}if(!(S=v[S>>2]))break}if(!(R=v[R>>2]))break}if(j){if(I=Ur(Xf(36),v[j+12>>2]),y=v[j+4>>2])for(E=0;;){if(v[c+60>>2]=0,v[c+52>>2]=0,v[c+56>>2]=0,v[c+48>>2]=8680,a=In(y,2556)){v[c+44>>2]=0,v[c+36>>2]=0,v[c+40>>2]=0,v[c+32>>2]=8680,(0|(r=v[b+36>>2]))!=(0|(n=v[a+12>>2]))?(n=r-n|0,v[c+40>>2]=n,r=v[5208],_=c,x=0|da[v[v[r>>2]+16>>2]](r,0,n<<2,8524,97),v[_+44>>2]=x,r=v[a+12>>2],n=v[b+36>>2]):n=r,v[c+20>>2]=0,mn(c+32|0,n-r|0,c+20|0),(r=v[b+36>>2])>>>0>d[c+56>>2]&&(v[c+56>>2]=r,n=v[5208],_=c,x=0|da[v[v[n>>2]+16>>2]](n,v[c+60>>2],r<<2,8524,97),v[_+60>>2]=x,r=v[b+36>>2]),v[c+20>>2]=0,mn(c+48|0,r,c+20|0),(0|(r=v[b+36>>2]))>0&&Bf(v[c+60>>2],255,r<<2),r=0,h=0;o:if(F=v[a+4>>2]){for(;;){if(n=eu(b,a=gu(c+20|0,Ct(F,1528,0),0)),lf(a),-1!=(0|n)){if((0|r)!=(0|n)){for(a=v[c+44>>2];v[a+(h<<2)>>2]=r,h=h+1|0,(0|n)!=(0|(r=r+1|0)););r=n}if(n=$t(F,2183,0),v[v[c+60>>2]+(r+n<<2)>>2]=r,r=r+1|0,F=v[F>>2])continue;break o}break}Pn(c- -64|0),h=0,Vn(i,0,n=gu(c+20|0,8118,0),r=gu(c+8|0,Ct(F,1528,0),0)),lf(r),lf(n),Qu(c+32|0),Qu(c+48|0);break i}if((n=v[b+36>>2])>>>0>r>>>0)for(a=v[c+44>>2];v[a+(h<<2)>>2]=r,h=h+1|0,(0|n)!=(0|(r=r+1|0)););if((0|n)>0)for(g=v[c+44>>2],$=v[c+60>>2];-1==v[(a=$+((r=n-1|0)<<2)|0)>>2]&&(h=h-1|0,v[a>>2]=v[g+(h<<2)>>2]),a=n>>>0>1,n=r,a;);Qu(c+32|0)}if(r=c+48|0,$e(I,E,Pt(y,5659,w(0)),r),E=E+1|0,Qu(r),!(y=v[y>>2]))break}v[c+48>>2]=I,Sn(c- -64|0,c+48|0),A=A>(C=p[(v[I+16>>2]+(v[j+12>>2]<<2)|0)-4>>2])?A:C}o:{if(L){if(a=le(Xf(36),v[L+12>>2]),r=v[L+4>>2])for(F=0;;){if($=Qt(b,n=gu(c+48|0,Ct(r,5744,0),0)),lf(n),!$)break o;if(_=n=Vi(Xf(40),Pt(r,5659,w(0)),$),x=$t(r,1776,v[$+16>>2]),v[_+12>>2]=x,_=n,z=Pt(r,2334,p[$+20>>2]),p[_+16>>2]=z,ke(n+20|0,Ct(r,4831,v[$+32>>2])),v[$+40>>2]&&(_=n,z=Pt(r,5652,w(1)),p[_+32>>2]=z,_=n,z=Pt(r,6078,w(0)),p[_+36>>2]=z),Hf(a,F,n),F=F+1|0,!(r=v[r>>2]))break}v[c+48>>2]=a,Sn(c- -64|0,c+48|0),A=A>(C=p[(v[a+16>>2]+(v[L+12>>2]<<2)|0)-4>>2])?A:C}h=Kr(n=Xf(48),r=gu(c+48|0,v[o+28>>2],0),c- -64|0,A),lf(r);break i}Pn(c- -64|0),h=0,Vn(i,0,n=gu(c+48|0,8265,0),r=gu(c+32|0,Ct(r,5744,0),0)),lf(r),lf(n);break i}Pn(c- -64|0),h=0,Vn(i,0,n=gu(c+48|0,8206,0),r=gu(c+32|0,v[E+28>>2],0)),lf(r),lf(n);break i}Pn(c- -64|0),h=0,Vn(i,0,n=gu(c+48|0,8059,0),r=gu(c+32|0,v[F+28>>2],0)),lf(r),lf(n);break i}Pn(c- -64|0),h=0,Vn(i,0,n=gu(c+48|0,7998,0),r=gu(c+32|0,v[y+28>>2],0)),lf(r),lf(n)}if(fo(c- -64|0),D=c+80|0,h&&(v[v[b+96>>2]+(k<<2)>>2]=h,k=k+1|0),!(r=v[o>>2]))break}or(we(t));break r}da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8347,0),r=gu(e+8|0,o,0)),lf(r),lf(n);break r}da[v[v[b>>2]+4>>2]](b),b=0,Vn(i,t,n=gu(e+20|0,8369,0),r=gu(e+8|0,o,0)),lf(r),lf(n)}return D=e+32|0,$i(i),D=u+48|0,0|b},function(r,n,e){var i,f;return r|=0,D=i=D-16|0,f=n|=0,n=Ae(i+4|0,4+(e|=0)|0,v[e>>2],0),r=0|da[0|r](f,n),lf(n),D=i+16|0,0|r},function(r,n){r|=0,n|=0;var e,i,t,o,a=0,c=0,b=0,s=0,h=0,y=0,g=0,F=w(0),A=0,T=0,$=0,I=0,C=w(0),P=0,E=0,O=0,R=0,S=0,W=0,G=0,U=0,j=0,H=0,L=0,M=0,_=0,z=0,x=0,J=0,K=w(0),B=0,N=0,q=0,V=0,Z=0;D=o=D-48|0,n=oi(Xf(40),v[n+8>>2],v[n+4>>2]),V=t=o+4|0,Z=pt(Xf(12),n),v[V+4>>2]=Z,v[t+20>>2]=0,v[t+12>>2]=0,v[t+16>>2]=0,v[t+8>>2]=10280,v[t>>2]=10248,ht(t+24|0),f[t+40|0]=0,v[t+36>>2]=1065353216,v[t+36>>2]=1065353216,n=v[5371],D=i=D-16|0,e=Xf(12),v[e+8>>2]=r+n,v[e+4>>2]=n,v[e>>2]=10264,v[t+12>>2]=0,Ai((g=Hr(Xf(232)))+176|0,mi(e)),Ai(r=g+164|0,mi(e)),r=qi(n=gu(i+4|0,6888,0),r),lf(n);r:if(r)da[v[v[e>>2]+4>>2]](e),g&&da[v[v[g>>2]+4>>2]](g),fe(t,6942,8524),g=0;else{if(r=v[e+4>>2],v[e+4>>2]=r+1,n=l[0|r],v[e+4>>2]=r+2,a=l[r+1|0],v[e+4>>2]=r+3,c=l[r+2|0],v[e+4>>2]=r+4,v[g+148>>2]=l[r+3|0]|(c|a<<8|n<<16)<<8,v[e+4>>2]=r+5,n=l[r+4|0],v[e+4>>2]=r+6,a=l[r+5|0],v[e+4>>2]=r+7,c=l[r+6|0],v[e+4>>2]=r+8,v[g+152>>2]=l[r+7|0]|(c|a<<8|n<<16)<<8,v[e+4>>2]=r+9,n=l[r+8|0],v[e+4>>2]=r+10,a=l[r+9|0],v[e+4>>2]=r+11,c=l[r+10|0],v[e+4>>2]=r+12,v[g+156>>2]=l[r+11|0]|(c|a<<8|n<<16)<<8,v[e+4>>2]=r+13,n=l[r+12|0],v[e+4>>2]=r+14,a=l[r+13|0],v[e+4>>2]=r+15,c=l[r+14|0],v[e+4>>2]=r+16,v[g+160>>2]=l[r+15|0]|(c|a<<8|n<<16)<<8,v[e+4>>2]=r+17,(s=l[r+16|0])&&(v[e+4>>2]=r+18,n=l[r+17|0],v[e+4>>2]=r+19,a=l[r+18|0],v[e+4>>2]=r+20,c=l[r+19|0],v[e+4>>2]=r+21,v[g+204>>2]=l[r+20|0]|(c|a<<8|n<<16)<<8,Ai(g+208|0,mi(e)),Ai(g+220|0,mi(e))),(0|(r=Wn(e,1)))>0)for(n=g+188|0;V=i,Z=mi(e),v[V+4>>2]=Z,Sn(n,i+4|0),(0|r)!=(0|(y=y+1|0)););if(a=Wn(e,1),v[i+4>>2]=0,Fn(g+16|0,a,i+4|0),(0|a)>0)for(c=0;n=mi(e),c?(r=Wn(e,1),r=v[v[g+28>>2]+(r<<2)>>2]):r=0,n=Xn(h=Xf(64),c,b=gu(i+4|0,n,1),r),lf(b),r=v[e+4>>2],v[e+4>>2]=r+1,b=l[0|r],v[e+4>>2]=r+2,y=l[r+1|0],v[e+4>>2]=r+3,h=l[r+2|0],v[e+4>>2]=r+4,v[n+36>>2]=l[r+3|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=r+5,b=l[r+4|0],v[e+4>>2]=r+6,y=l[r+5|0],v[e+4>>2]=r+7,h=l[r+6|0],v[e+4>>2]=r+8,F=p[t+36>>2],p[n+28>>2]=F*(u(2,l[r+7|0]|(h|y<<8|b<<16)<<8),k()),v[e+4>>2]=r+9,b=l[r+8|0],v[e+4>>2]=r+10,y=l[r+9|0],v[e+4>>2]=r+11,h=l[r+10|0],v[e+4>>2]=r+12,p[n+32>>2]=F*(u(2,l[r+11|0]|(h|y<<8|b<<16)<<8),k()),v[e+4>>2]=r+13,b=l[r+12|0],v[e+4>>2]=r+14,y=l[r+13|0],v[e+4>>2]=r+15,h=l[r+14|0],v[e+4>>2]=r+16,v[n+40>>2]=l[r+15|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=r+17,b=l[r+16|0],v[e+4>>2]=r+18,y=l[r+17|0],v[e+4>>2]=r+19,h=l[r+18|0],v[e+4>>2]=r+20,v[n+44>>2]=l[r+19|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=r+21,b=l[r+20|0],v[e+4>>2]=r+22,y=l[r+21|0],v[e+4>>2]=r+23,h=l[r+22|0],v[e+4>>2]=r+24,v[n+48>>2]=l[r+23|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=r+25,b=l[r+24|0],v[e+4>>2]=r+26,y=l[r+25|0],v[e+4>>2]=r+27,h=l[r+26|0],v[e+4>>2]=r+28,v[n+52>>2]=l[r+27|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=r+29,b=l[r+28|0],v[e+4>>2]=r+30,y=l[r+29|0],v[e+4>>2]=r+31,h=l[r+30|0],v[e+4>>2]=r+32,p[n+24>>2]=F*(u(2,l[r+31|0]|(h|y<<8|b<<16)<<8),k()),V=n,Z=Wn(e,1),v[V+56>>2]=Z,r=v[e+4>>2],v[e+4>>2]=r+1,f[n+60|0]=0!=l[0|r],s&&(v[e+4>>2]=r+5),v[v[g+28>>2]+(c<<2)>>2]=n,(0|a)!=(0|(c=c+1|0)););if(b=Wn(e,1),v[i+4>>2]=0,Fn(g+32|0,b,i+4|0),(0|b)>0)for(r=0;n=mi(e),a=Wn(e,1),a=v[v[g+28>>2]+(a<<2)>>2],a=Bn(c=Xf(84),r,n=gu(i+4|0,n,1),a),lf(n),c=Ko(a),n=v[e+4>>2],v[e+4>>2]=n+1,p[c+4>>2]=w(l[0|n])/w(255),v[e+4>>2]=n+2,p[c+8>>2]=w(l[n+1|0])/w(255),v[e+4>>2]=n+3,p[c+12>>2]=w(l[n+2|0])/w(255),v[e+4>>2]=n+4,p[c+16>>2]=w(l[n+3|0])/w(255),v[e+4>>2]=n+5,c=l[n+4|0],v[e+4>>2]=n+6,y=l[n+5|0],v[e+4>>2]=n+7,h=l[n+6|0],v[e+4>>2]=n+8,255==(c&y)&255==(h&l[n+7|0])||(n=fa(a),v[n+16>>2]=1065353216,p[n+12>>2]=w(h>>>0)/w(255),p[n+8>>2]=w(y>>>0)/w(255),p[n+4>>2]=w(c>>>0)/w(255),ci(n),oa(a)),ke(c=a+68|0,n=(n=Wn(e,1))?v[(v[g+200>>2]+(n<<2)|0)-4>>2]:0),V=a,Z=Wn(e,1),v[V+80>>2]=Z,v[v[g+44>>2]+(r<<2)>>2]=a,(0|b)!=(0|(r=r+1|0)););if(c=Wn(e,1),v[i+4>>2]=0,Fn(g+100|0,c,i+4|0),(0|c)>0)for(r=0;;){if(n=mi(e),a=Ve(a=Xf(60),n=gu(b=i+4|0,n,1)),lf(n),ea(a,Wn(e,1)),n=v[e+4>>2],v[e+4>>2]=n+1,ua(a,0!=l[0|n]),n=Wn(e,1),v[i+4>>2]=0,Fn(a+24|0,n,b),(0|n)>0)for(y=0;b=Wn(e,1),v[v[a+36>>2]+(y<<2)>>2]=v[v[g+28>>2]+(b<<2)>>2],(0|n)!=(0|(y=y+1|0)););if(n=Wn(e,1),v[a+40>>2]=v[v[g+28>>2]+(n<<2)>>2],n=v[e+4>>2],v[e+4>>2]=n+1,b=l[0|n],v[e+4>>2]=n+2,y=l[n+1|0],v[e+4>>2]=n+3,h=l[n+2|0],v[e+4>>2]=n+4,v[a+52>>2]=l[n+3|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=n+5,b=l[n+4|0],v[e+4>>2]=n+6,y=l[n+5|0],v[e+4>>2]=n+7,h=l[n+6|0],v[e+4>>2]=n+8,p[a+56>>2]=p[t+36>>2]*(u(2,l[n+7|0]|(h|y<<8|b<<16)<<8),k()),v[e+4>>2]=n+9,v[a+44>>2]=f[n+8|0],v[e+4>>2]=n+10,f[a+48|0]=0!=l[n+9|0],v[e+4>>2]=n+11,f[a+49|0]=0!=l[n+10|0],v[e+4>>2]=n+12,f[a+50|0]=0!=l[n+11|0],v[v[g+112>>2]+(r<<2)>>2]=a,(0|c)==(0|(r=r+1|0)))break}if(c=Wn(e,1),v[i+4>>2]=0,Fn(g+116|0,c,i+4|0),(0|c)>0)for(r=0;;){if(n=mi(e),a=Zf(a=Xf(88),n=gu(b=i+4|0,n,1)),lf(n),ea(a,Wn(e,1)),n=v[e+4>>2],v[e+4>>2]=n+1,ua(a,0!=l[0|n]),n=Wn(e,1),v[i+4>>2]=0,Fn(a+24|0,n,b),(0|n)>0)for(y=0;b=Wn(e,1),v[v[a+36>>2]+(y<<2)>>2]=v[v[g+28>>2]+(b<<2)>>2],(0|n)!=(0|(y=y+1|0)););if(n=Wn(e,1),v[a+40>>2]=v[v[g+28>>2]+(n<<2)>>2],n=v[e+4>>2],v[e+4>>2]=n+1,f[a+85|0]=0!=l[0|n],v[e+4>>2]=n+2,f[a+84|0]=0!=l[n+1|0],v[e+4>>2]=n+3,b=l[n+2|0],v[e+4>>2]=n+4,y=l[n+3|0],v[e+4>>2]=n+5,h=l[n+4|0],v[e+4>>2]=n+6,v[a+60>>2]=l[n+5|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=n+7,b=l[n+6|0],v[e+4>>2]=n+8,y=l[n+7|0],v[e+4>>2]=n+9,h=l[n+8|0],v[e+4>>2]=n+10,F=p[t+36>>2],p[a+64>>2]=F*(u(2,l[n+9|0]|(h|y<<8|b<<16)<<8),k()),v[e+4>>2]=n+11,b=l[n+10|0],v[e+4>>2]=n+12,y=l[n+11|0],v[e+4>>2]=n+13,h=l[n+12|0],v[e+4>>2]=n+14,p[a+68>>2]=F*(u(2,l[n+13|0]|(h|y<<8|b<<16)<<8),k()),v[e+4>>2]=n+15,b=l[n+14|0],v[e+4>>2]=n+16,y=l[n+15|0],v[e+4>>2]=n+17,h=l[n+16|0],v[e+4>>2]=n+18,v[a+72>>2]=l[n+17|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=n+19,b=l[n+18|0],v[e+4>>2]=n+20,y=l[n+19|0],v[e+4>>2]=n+21,h=l[n+20|0],v[e+4>>2]=n+22,v[a+76>>2]=l[n+21|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=n+23,b=l[n+22|0],v[e+4>>2]=n+24,y=l[n+23|0],v[e+4>>2]=n+25,h=l[n+24|0],v[e+4>>2]=n+26,v[a+80>>2]=l[n+25|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=n+27,b=l[n+26|0],v[e+4>>2]=n+28,y=l[n+27|0],v[e+4>>2]=n+29,h=l[n+28|0],v[e+4>>2]=n+30,v[a+44>>2]=l[n+29|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=n+31,b=l[n+30|0],v[e+4>>2]=n+32,y=l[n+31|0],v[e+4>>2]=n+33,h=l[n+32|0],v[e+4>>2]=n+34,v[a+48>>2]=l[n+33|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=n+35,b=l[n+34|0],v[e+4>>2]=n+36,y=l[n+35|0],v[e+4>>2]=n+37,h=l[n+36|0],v[e+4>>2]=n+38,v[a+52>>2]=l[n+37|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=n+39,b=l[n+38|0],v[e+4>>2]=n+40,y=l[n+39|0],v[e+4>>2]=n+41,h=l[n+40|0],v[e+4>>2]=n+42,v[a+56>>2]=l[n+41|0]|(h|y<<8|b<<16)<<8,v[v[g+128>>2]+(r<<2)>>2]=a,(0|c)==(0|(r=r+1|0)))break}if(c=Wn(e,1),v[i+4>>2]=0,Fn(g+132|0,c,i+4|0),(0|c)>0)for(r=0;;){if(n=mi(e),a=Yf(a=Xf(76),n=gu(b=i+4|0,n,1)),lf(n),ea(a,Wn(e,1)),n=v[e+4>>2],v[e+4>>2]=n+1,ua(a,0!=l[0|n]),n=Wn(e,1),v[i+4>>2]=0,Fn(a+24|0,n,b),(0|n)>0)for(y=0;b=Wn(e,1),v[v[a+36>>2]+(y<<2)>>2]=v[v[g+28>>2]+(b<<2)>>2],(0|n)!=(0|(y=y+1|0)););if(n=Wn(e,1),v[a+40>>2]=v[v[g+44>>2]+(n<<2)>>2],V=a,Z=Wn(e,1),v[V+44>>2]=Z,V=a,Z=Wn(e,1),v[V+48>>2]=Z,V=a,Z=Wn(e,1),v[V+52>>2]=Z,n=v[e+4>>2],v[e+4>>2]=n+1,b=l[0|n],v[e+4>>2]=n+2,y=l[n+1|0],v[e+4>>2]=n+3,h=l[n+2|0],v[e+4>>2]=n+4,v[a+56>>2]=l[n+3|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=n+5,b=l[n+4|0],v[e+4>>2]=n+6,y=l[n+5|0],v[e+4>>2]=n+7,h=l[n+6|0],v[e+4>>2]=n+8,b=l[n+7|0]|(h|y<<8|b<<16)<<8,v[a+60>>2]=b,v[a+44>>2]||(p[a+60>>2]=p[t+36>>2]*(u(2,b),k())),v[e+4>>2]=n+9,b=l[n+8|0],v[e+4>>2]=n+10,y=l[n+9|0],v[e+4>>2]=n+11,h=l[n+10|0],v[e+4>>2]=n+12,b=l[n+11|0]|(h|y<<8|b<<16)<<8,v[a+64>>2]=b,d[a+48>>2]<=1&&(p[a+64>>2]=p[t+36>>2]*(u(2,b),k())),v[e+4>>2]=n+13,b=l[n+12|0],v[e+4>>2]=n+14,y=l[n+13|0],v[e+4>>2]=n+15,h=l[n+14|0],v[e+4>>2]=n+16,v[a+68>>2]=l[n+15|0]|(h|y<<8|b<<16)<<8,v[e+4>>2]=n+17,b=l[n+16|0],v[e+4>>2]=n+18,y=l[n+17|0],v[e+4>>2]=n+19,h=l[n+18|0],v[e+4>>2]=n+20,v[a+72>>2]=l[n+19|0]|(h|y<<8|b<<16)<<8,v[v[g+144>>2]+(r<<2)>>2]=a,(0|c)==(0|(r=r+1|0)))break}if(r=X(t,e,1,g,!!(0|s)),v[i>>2]=r,r&&(v[g+64>>2]=r,Sn(g+48|0,i)),r=Wn(e,1))for(n=g+48|0,y=0;V=i,Z=X(t,e,0,g,!!(0|s)),v[V+4>>2]=Z,Sn(n,i+4|0),(0|r)!=(0|(y=y+1|0)););if((0|(s=v[t+12>>2]))>0)for(r=0;;){if(a=v[v[t+20>>2]+(r<<2)>>2],!(n=v[a+12>>2]?iu(g,a+8|0):v[g+64>>2])){da[v[v[e>>2]+4>>2]](e),g&&da[v[v[g>>2]+4>>2]](g),fe(t,8306,v[a+16>>2]),g=0;break r}if(!(n=gt(n,v[a+20>>2],a+24|0))){da[v[v[e>>2]+4>>2]](e),g&&da[v[v[g>>2]+4>>2]](g),fe(t,8323,v[a+32>>2]),g=0;break r}if(c=v[a+4>>2],v[c+56>>2]=l[a+36|0]?n:c,zn(c,n),sr(v[a+4>>2]),n=v[t+4>>2],da[v[v[n>>2]+36>>2]](n,v[a+4>>2]),(0|s)==(0|(r=r+1|0)))break}if(Pn(t+8|0),v[t+12>>2]=0,a=Wn(e,1),v[i+4>>2]=0,Fn(g+68|0,a,i+4|0),(0|a)>0)for(c=0;r=(r=Wn(e,1))?v[(v[g+200>>2]+(r<<2)|0)-4>>2]:0,n=rf(n=Xf(56),r=gu(i+4|0,r,0)),lf(r),V=n,Z=Wn(e,0),v[V+16>>2]=Z,r=v[e+4>>2],v[e+4>>2]=r+1,s=l[0|r],v[e+4>>2]=r+2,b=l[r+1|0],v[e+4>>2]=r+3,y=l[r+2|0],v[e+4>>2]=r+4,v[n+20>>2]=l[r+3|0]|(y|b<<8|s<<16)<<8,Ai(n+24|0,mi(e)),Ai(n+36|0,mi(e)),v[n+40>>2]&&(r=v[e+4>>2],v[e+4>>2]=r+1,s=l[0|r],v[e+4>>2]=r+2,b=l[r+1|0],v[e+4>>2]=r+3,y=l[r+2|0],v[e+4>>2]=r+4,v[n+48>>2]=l[r+3|0]|(y|b<<8|s<<16)<<8,v[e+4>>2]=r+5,s=l[r+4|0],v[e+4>>2]=r+6,b=l[r+5|0],v[e+4>>2]=r+7,y=l[r+6|0],v[e+4>>2]=r+8,v[n+52>>2]=l[r+7|0]|(y|b<<8|s<<16)<<8),v[v[g+80>>2]+(c<<2)>>2]=n,(0|a)!=(0|(c=c+1|0)););if(B=Wn(e,1),v[i+4>>2]=0,Fn(g+84|0,B,i+4|0),(0|B)>0)for(y=0;;){N=gu(i+4|0,mi(e),1),b=0,C=w(0),D=s=D+-64|0,v[s+60>>2]=0,v[s+52>>2]=0,v[s+56>>2]=0,v[s+48>>2]=1048,K=p[t+36>>2];n:{e:if(!((0|($=Wn(e,1)))<=0))for(j=v[2317],O=v[2738],A=1;;){if(T=Wn(e,1),h=0,(0|(W=Wn(e,1)))>0)for(;;){r=v[e+4>>2],v[e+4>>2]=r+1,r=l[0|r],a=Wn(e,1);i:{f:{t:{u:{switch(0|r){case 0:if(r=sn(Xf(40),a),v[r+4>>2]=T,n=0,(0|a)<=0)break f;for(;c=v[e+4>>2],v[e+4>>2]=c+1,P=l[0|c],v[e+4>>2]=c+2,E=l[c+1|0],v[e+4>>2]=c+3,I=l[c+2|0],v[e+4>>2]=c+4,u(2,l[c+3|0]|(I|E<<8|P<<16)<<8),Wf(r,n,F=k(),c=gu(I=s+32|0,c=(c=Wn(e,1))?v[(v[g+200>>2]+(c<<2)|0)-4>>2]:0,0)),lf(c),(0|a)!=(0|(n=n+1|0)););break f;case 1:if(c=Xe(Xf(40),a),v[c+20>>2]=T,P=a-1|0,n=0,(0|a)<=0)break t;for(;r=v[e+4>>2],v[e+4>>2]=r+1,E=l[0|r],v[e+4>>2]=r+2,I=l[r+1|0],v[e+4>>2]=r+3,S=l[r+2|0],v[e+4>>2]=r+4,R=l[r+3|0],v[e+4>>2]=r+5,U=l[r+4|0],v[e+4>>2]=r+6,G=l[r+5|0],v[e+4>>2]=r+7,H=l[r+6|0],v[e+4>>2]=r+8,Ri(c,n,(u(2,R|(S|I<<8|E<<16)<<8),k()),w(w(U>>>0)/w(255)),w(w(G>>>0)/w(255)),w(w(H>>>0)/w(255)),w(w(l[r+7|0])/w(255))),(0|n)<(0|P)&&jr(e,n,c),(0|a)!=(0|(n=n+1|0)););break t;case 2:if(c=Le(Xf(40),a),v[c+36>>2]=T,P=a-1|0,n=0,(0|a)<=0)break u;for(;r=v[e+4>>2],v[e+4>>2]=r+1,E=l[0|r],v[e+4>>2]=r+2,I=l[r+1|0],v[e+4>>2]=r+3,S=l[r+2|0],v[e+4>>2]=r+4,R=l[r+3|0],v[e+4>>2]=r+5,U=l[r+4|0],v[e+4>>2]=r+6,G=l[r+5|0],v[e+4>>2]=r+7,H=l[r+6|0],v[e+4>>2]=r+8,M=l[r+7|0],v[e+4>>2]=r+10,_=l[r+9|0],v[e+4>>2]=r+11,L=l[r+10|0],v[e+4>>2]=r+12,Oe(c,n,(u(2,R|(S|I<<8|E<<16)<<8),k()),w(w(U>>>0)/w(255)),w(w(G>>>0)/w(255)),w(w(H>>>0)/w(255)),w(w(M>>>0)/w(255)),w(w(_>>>0)/w(255)),w(w(L>>>0)/w(255)),w(w(l[r+11|0])/w(255))),(0|n)<(0|P)&&jr(e,n,c),(0|a)!=(0|(n=n+1|0)););break u}if(Pn(s+48|0),fe(t,7998,v[v[v[g+44>>2]+(T<<2)>>2]+16>>2]),r=0,1&A)break n;break e}v[s+32>>2]=c,n=c+32|0,Sn(s+48|0,s+32|0),r=m(P,O);break i}v[s+32>>2]=c,n=c+36|0,Sn(s+48|0,s+32|0),r=m(P,j);break i}v[s+32>>2]=r,n=r+20|0,Sn(s+48|0,s+32|0),r=a-1|0}if(C=(F=p[v[n>>2]+(r<<2)>>2])<C?C:F,(0|W)==(0|(h=h+1|0)))break}if(A=(0|$)>(0|(b=b+1|0)),(0|b)==(0|$))break}e:if(!((0|($=Wn(e,1)))<=0))for(b=0,j=v[2721],A=1;;){if(T=Wn(e,1),h=0,(0|(O=Wn(e,1)))>0)for(;;){r=v[e+4>>2],v[e+4>>2]=r+1,r=l[0|r],a=Wn(e,1);i:{f:{t:{u:{o:switch(0|r){case 0:if(c=Qe(Xf(40),a),v[c+20>>2]=T,(0|a)>0)for(P=a-1|0,n=0;r=v[e+4>>2],v[e+4>>2]=r+1,W=l[0|r],v[e+4>>2]=r+2,E=l[r+1|0],v[e+4>>2]=r+3,I=l[r+2|0],v[e+4>>2]=r+4,S=l[r+3|0],v[e+4>>2]=r+5,R=l[r+4|0],v[e+4>>2]=r+6,U=l[r+5|0],v[e+4>>2]=r+7,G=l[r+6|0],v[e+4>>2]=r+8,Mf(c,n,(u(2,S|(I|E<<8|W<<16)<<8),k()),(u(2,l[r+7|0]|(G|U<<8|R<<16)<<8),k())),(0|n)<(0|P)&&jr(e,n,c),(0|a)!=(0|(n=n+1|0)););v[s+32>>2]=c,n=(a<<1)-2|0,Sn(s+48|0,s+32|0),r=c+36|0;break i;case 2:c=cu(Xf(40),a),F=w(1);break u;case 3:c=bu(Xf(40),a),F=w(1);break u;case 1:break o;default:break t}c=Ue(Xf(40),a),F=K}if(v[c+36>>2]=T,P=a-1|0,n=0,(0|a)<=0)break f;for(;r=v[e+4>>2],v[e+4>>2]=r+1,W=l[0|r],v[e+4>>2]=r+2,E=l[r+1|0],v[e+4>>2]=r+3,I=l[r+2|0],v[e+4>>2]=r+4,S=l[r+3|0],v[e+4>>2]=r+5,R=l[r+4|0],v[e+4>>2]=r+6,U=l[r+5|0],v[e+4>>2]=r+7,G=l[r+6|0],v[e+4>>2]=r+8,H=l[r+7|0],v[e+4>>2]=r+9,M=l[r+8|0],v[e+4>>2]=r+10,_=l[r+9|0],v[e+4>>2]=r+11,L=l[r+10|0],v[e+4>>2]=r+12,Af(c,n,(u(2,S|(I|E<<8|W<<16)<<8),k()),w(F*(u(2,H|(G|U<<8|R<<16)<<8),k())),w(F*(u(2,l[r+11|0]|(L|_<<8|M<<16)<<8),k()))),(0|n)<(0|P)&&jr(e,n,c),(0|a)!=(0|(n=n+1|0)););break f}if(Pn(s+48|0),fe(t,8059,v[v[v[g+28>>2]+(T<<2)>>2]+16>>2]),r=0,1&A)break n;break e}n=m(P,j),v[s+32>>2]=c,Sn(s+48|0,s+32|0),r=c+32|0}if(C=(F=p[v[r>>2]+(n<<2)>>2])<C?C:F,(0|O)==(0|(h=h+1|0)))break}if(A=(0|$)>(0|(b=b+1|0)),(0|b)==(0|$))break}if((0|(A=Wn(e,1)))>0)for(T=v[2405],h=0;;){if(r=Wn(e,1),a=Wn(e,1),c=Co(Xf(40),a),v[c+36>>2]=r,b=a-1|0,n=0,(0|a)>0)for(;r=v[e+4>>2],v[e+4>>2]=r+1,$=l[0|r],v[e+4>>2]=r+2,P=l[r+1|0],v[e+4>>2]=r+3,j=l[r+2|0],v[e+4>>2]=r+4,O=l[r+3|0],v[e+4>>2]=r+5,W=l[r+4|0],v[e+4>>2]=r+6,E=l[r+5|0],v[e+4>>2]=r+7,I=l[r+6|0],v[e+4>>2]=r+8,S=l[r+7|0],v[e+4>>2]=r+9,R=l[r+8|0],v[e+4>>2]=r+10,U=l[r+9|0],v[e+4>>2]=r+11,G=l[r+10|0],v[e+4>>2]=r+12,F=p[t+36>>2],H=l[r+11|0],v[e+4>>2]=r+13,M=f[r+12|0],v[e+4>>2]=r+14,_=l[r+13|0],v[e+4>>2]=r+15,ei(c,n,(u(2,O|(j|P<<8|$<<16)<<8),k()),(u(2,S|(I|E<<8|W<<16)<<8),k()),w(F*(u(2,H|(G|U<<8|R<<16)<<8),k())),M,!!(0|_),0!=l[r+14|0]),(0|n)<(0|b)&&jr(e,n,c),(0|a)!=(0|(n=n+1|0)););if(v[s+32>>2]=c,Sn(s+48|0,s+32|0),C=(F=p[v[c+32>>2]+(m(b,T)<<2)>>2])<C?C:F,(0|A)==(0|(h=h+1|0)))break}if((0|(A=Wn(e,1)))>0)for(T=v[2713],c=0;;){if(r=Wn(e,1),a=Wn(e,1),b=mo(Xf(40),a),v[b+36>>2]=r,h=a-1|0,n=0,(0|a)>0)for(;r=v[e+4>>2],v[e+4>>2]=r+1,$=l[0|r],v[e+4>>2]=r+2,P=l[r+1|0],v[e+4>>2]=r+3,j=l[r+2|0],v[e+4>>2]=r+4,O=l[r+3|0],v[e+4>>2]=r+5,W=l[r+4|0],v[e+4>>2]=r+6,E=l[r+5|0],v[e+4>>2]=r+7,I=l[r+6|0],v[e+4>>2]=r+8,S=l[r+7|0],v[e+4>>2]=r+9,R=l[r+8|0],v[e+4>>2]=r+10,U=l[r+9|0],v[e+4>>2]=r+11,G=l[r+10|0],v[e+4>>2]=r+12,H=l[r+11|0],v[e+4>>2]=r+13,M=l[r+12|0],v[e+4>>2]=r+14,_=l[r+13|0],v[e+4>>2]=r+15,L=l[r+14|0],v[e+4>>2]=r+16,z=l[r+15|0],v[e+4>>2]=r+17,x=l[r+16|0],v[e+4>>2]=r+18,J=l[r+17|0],v[e+4>>2]=r+19,q=l[r+18|0],v[e+4>>2]=r+20,Si(b,n,(u(2,O|(j|P<<8|$<<16)<<8),k()),(u(2,S|(I|E<<8|W<<16)<<8),k()),(u(2,H|(G|U<<8|R<<16)<<8),k()),(u(2,z|(L|_<<8|M<<16)<<8),k()),(u(2,l[r+19|0]|(J<<8|x<<16|q)<<8),k())),(0|n)<(0|h)&&jr(e,n,b),(0|a)!=(0|(n=n+1|0)););if(v[s+32>>2]=b,Sn(s+48|0,s+32|0),C=(F=p[v[b+32>>2]+(m(h,T)<<2)>>2])<C?C:F,(0|A)==(0|(c=c+1|0)))break}if((0|(P=Wn(e,1)))>0)for(A=0,j=v[2477],O=v[2469];;){if(b=Wn(e,1),T=v[v[g+144>>2]+(b<<2)>>2],(0|(W=Wn(e,1)))>0)for(h=0;;){r=v[e+4>>2],v[e+4>>2]=r+1,r=f[0|r],a=Wn(e,1);e:{i:switch(0|r){case 0:case 1:if(c=Xf(40),1!=(0|r)?(ni(c,a),r=!v[T+44>>2]):(Wt(c,a),r=d[T+48>>2]<2),v[c+36>>2]=b,$=a-1|0,n=0,(0|a)>0)for(F=r?K:w(1);r=v[e+4>>2],v[e+4>>2]=r+1,E=l[0|r],v[e+4>>2]=r+2,I=l[r+1|0],v[e+4>>2]=r+3,S=l[r+2|0],v[e+4>>2]=r+4,R=l[r+3|0],v[e+4>>2]=r+5,U=l[r+4|0],v[e+4>>2]=r+6,G=l[r+5|0],v[e+4>>2]=r+7,H=l[r+6|0],v[e+4>>2]=r+8,Lf(c,n,(u(2,R|(S|I<<8|E<<16)<<8),k()),w(F*(u(2,l[r+7|0]|(H|G<<8|U<<16)<<8),k()))),(0|n)<(0|$)&&jr(e,n,c),(0|a)!=(0|(n=n+1|0)););v[s+32>>2]=c,Sn(s+48|0,s+32|0),C=(F=p[v[c+32>>2]+(m($,j)<<2)>>2])<C?C:F;break e;case 2:break i;default:break e}if(c=wo(Xf(40),a),v[c+36>>2]=b,$=a-1|0,n=0,(0|a)>0)for(;r=v[e+4>>2],v[e+4>>2]=r+1,E=l[0|r],v[e+4>>2]=r+2,I=l[r+1|0],v[e+4>>2]=r+3,S=l[r+2|0],v[e+4>>2]=r+4,R=l[r+3|0],v[e+4>>2]=r+5,U=l[r+4|0],v[e+4>>2]=r+6,G=l[r+5|0],v[e+4>>2]=r+7,H=l[r+6|0],v[e+4>>2]=r+8,M=l[r+7|0],v[e+4>>2]=r+9,_=l[r+8|0],v[e+4>>2]=r+10,L=l[r+9|0],v[e+4>>2]=r+11,z=l[r+10|0],v[e+4>>2]=r+12,Af(c,n,(u(2,R|(S|I<<8|E<<16)<<8),k()),(u(2,M|(H|G<<8|U<<16)<<8),k()),(u(2,l[r+11|0]|(z|L<<8|_<<16)<<8),k())),(0|n)<(0|$)&&jr(e,n,c),(0|a)!=(0|(n=n+1|0)););v[s+32>>2]=c,Sn(s+48|0,s+32|0),C=(F=p[v[c+32>>2]+(m($,O)<<2)>>2])<C?C:F}if((0|W)==(0|(h=h+1|0)))break}if((0|P)==(0|(A=A+1|0)))break}e:if(!((0|(E=Wn(e,1)))<=0))for(I=1,c=0;;){if(r=Wn(e,1),U=v[v[g+60>>2]+(r<<2)>>2],A=0,(0|(G=Wn(e,1)))>0)for(;;){if(h=0,S=Wn(e,1),(0|(H=Wn(e,1)))>0)for(;;){if(r=0,(n=Wn(e,1))&&(r=v[(v[g+200>>2]+(n<<2)|0)-4>>2]),T=gt(U,S,n=gu(s+32|0,r,0)),lf(n),!T){if(Pn(s+48|0),fe(t,8283,r),r=0,I)break n;break e}if(r=v[T+40>>2],P=(R=v[T+24>>2])?(r>>>0)/3<<1:r,j=Wn(e,1),$=Or(Xf(60),j),v[$+56>>2]=T,v[$+20>>2]=S,j)for(M=T+36|0,_=P<<2,W=j-1|0,b=0;;){r=v[e+4>>2],v[e+4>>2]=r+1,n=l[0|r],v[e+4>>2]=r+2,a=l[r+1|0],v[e+4>>2]=r+3,O=l[r+2|0],v[e+4>>2]=r+4,r=l[r+3|0],v[s+44>>2]=0,v[s+36>>2]=0,v[s+40>>2]=0,v[s+32>>2]=8712,u(2,r|(O|a<<8|n<<16)<<8),F=k();i:if(r=Wn(e,1)){v[s+16>>2]=0,yn(s+32|0,P,s+16|0),O=r+(n=Wn(e,1))|0;f:if(K==w(1)){if(!(n>>>0>=O>>>0))for(r=v[e+4>>2],L=v[s+44>>2];v[e+4>>2]=r+1,z=l[0|r],v[e+4>>2]=r+2,x=l[r+1|0],v[e+4>>2]=r+3,J=l[r+2|0],a=r+4|0,v[e+4>>2]=a,v[L+(n<<2)>>2]=l[r+3|0]|(J|x<<8|z<<16)<<8,r=a,(0|O)!=(0|(n=n+1|0)););}else{if(n>>>0>=O>>>0)break f;for(r=v[e+4>>2],L=v[s+44>>2];v[e+4>>2]=r+1,z=l[0|r],v[e+4>>2]=r+2,x=l[r+1|0],v[e+4>>2]=r+3,J=l[r+2|0],a=r+4|0,v[e+4>>2]=a,p[L+(n<<2)>>2]=K*(u(2,l[r+3|0]|(J|x<<8|z<<16)<<8),k()),r=a,(0|O)!=(0|(n=n+1|0)););}if(!R&&(n=v[s+36>>2]))for(a=v[T+48>>2],r=0,O=v[s+44>>2];p[(z=(L=r<<2)+O|0)>>2]=p[a+L>>2]+p[z>>2],(0|n)!=(0|(r=r+1|0)););}else{if(R){if(v[s+16>>2]=0,yn(s+32|0,P,s+16|0),!P)break i;Bf(v[s+44>>2],0,_);break i}v[s+36>>2]=0,_i(s+32|0,M)}if(_f($,b,F,s+32|0),b>>>0<W>>>0&&jr(e,b,$),vo(s+32|0),(0|j)==(0|(b=b+1|0)))break}else W=-1;if(v[s+32>>2]=$,Sn(s+48|0,s+32|0),C=(F=p[v[$+36>>2]+(W<<2)>>2])<C?C:F,(0|H)==(0|(h=h+1|0)))break}if((0|G)==(0|(A=A+1|0)))break}if(I=(0|E)>(0|(c=c+1|0)),(0|c)==(0|E))break}if(A=Wn(e,1)){for(T=Ur(Xf(36),A),P=(a=v[g+36>>2])<<2,b=0;;){if(r=v[e+4>>2],v[e+4>>2]=r+1,j=l[0|r],v[e+4>>2]=r+2,O=l[r+1|0],v[e+4>>2]=r+3,W=l[r+2|0],v[e+4>>2]=r+4,E=l[r+3|0],$=Wn(e,1),v[s+44>>2]=0,v[s+36>>2]=0,v[s+40>>2]=0,v[s+32>>2]=8680,v[s+16>>2]=0,mn(s+32|0,a,s+16|0),(I=(0|a)<=0)||Bf(v[s+44>>2],255,P),r=0,v[s+28>>2]=0,v[s+20>>2]=0,v[s+24>>2]=0,v[s+16>>2]=8680,v[s+12>>2]=0,mn(s+16|0,a-$|0,s+12|0),n=0,$)for(h=0,S=v[s+44>>2],R=v[s+28>>2];;){if((0|(c=Wn(e,1)))!=(0|r)){for(;v[R+(n<<2)>>2]=r,n=n+1|0,(0|c)!=(0|(r=r+1|0)););r=c}if(V=S+(Wn(e,1)+r<<2)|0,Z=r,v[V>>2]=Z,r=r+1|0,(0|$)==(0|(h=h+1|0)))break}if(r>>>0<a>>>0)for(c=v[s+28>>2];v[c+(n<<2)>>2]=r,n=n+1|0,(0|a)!=(0|(r=r+1|0)););if(!I)for(h=v[s+28>>2],$=v[s+44>>2],r=a;-1==v[(I=$+((c=r-1|0)<<2)|0)>>2]&&(n=n-1|0,v[I>>2]=v[h+(n<<2)>>2]),I=r>>>0>1,r=c,I;);if(r=s+32|0,$e(T,b,(u(2,E|(W|O<<8|j<<16)<<8),k()),r),Qu(s+16|0),Qu(r),(0|A)==(0|(b=b+1|0)))break}v[s+32>>2]=T,Sn(s+48|0,s+32|0),C=(F=p[(v[T+16>>2]+(A<<2)|0)-4>>2])<C?C:F}if((0|(a=Wn(e,1)))>0){for(c=le(Xf(36),a),b=0;r=v[e+4>>2],v[e+4>>2]=r+1,n=l[0|r],v[e+4>>2]=r+2,A=l[r+1|0],v[e+4>>2]=r+3,T=l[r+2|0],v[e+4>>2]=r+4,r=l[r+3|0],h=Wn(e,1),h=v[v[g+80>>2]+(h<<2)>>2],V=n=Vi(Xf(40),(u(2,r|(T|A<<8|n<<16)<<8),k()),h),Z=Wn(e,0),v[V+12>>2]=Z,r=v[e+4>>2],v[e+4>>2]=r+1,A=l[0|r],v[e+4>>2]=r+2,T=l[r+1|0],v[e+4>>2]=r+3,$=l[r+2|0],v[e+4>>2]=r+4,v[n+16>>2]=l[r+3|0]|($|T<<8|A<<16)<<8,v[e+4>>2]=r+5,ee(T=n+20|0,A=gu(A=s+32|0,r=($=l[r+4|0])?mi(e):v[h+32>>2],0)),lf(A),$&&(A=v[5208],da[v[v[A>>2]+20>>2]](A,r,8524,1028)),v[h+40>>2]&&(r=v[e+4>>2],v[e+4>>2]=r+1,h=l[0|r],v[e+4>>2]=r+2,A=l[r+1|0],v[e+4>>2]=r+3,T=l[r+2|0],v[e+4>>2]=r+4,v[n+32>>2]=l[r+3|0]|(T|A<<8|h<<16)<<8,v[e+4>>2]=r+5,h=l[r+4|0],v[e+4>>2]=r+6,A=l[r+5|0],v[e+4>>2]=r+7,T=l[r+6|0],v[e+4>>2]=r+8,v[n+36>>2]=l[r+7|0]|(T|A<<8|h<<16)<<8),Hf(c,b,n),(0|a)!=(0|(b=b+1|0)););v[s+32>>2]=c,Sn(s+48|0,s+32|0),C=(F=p[(v[c+16>>2]+(a<<2)|0)-4>>2])<C?C:F}r=Kr(r=Xf(48),n=Re(s+32|0,N),s+48|0,C),lf(n)}if(fo(s+48|0),D=s- -64|0,!r){da[v[v[e>>2]+4>>2]](e),g&&da[v[v[g>>2]+4>>2]](g),lf(N),g=0;break r}if(v[v[g+96>>2]+(y<<2)>>2]=r,lf(N),(0|B)==(0|(y=y+1|0)))break}da[v[v[e>>2]+4>>2]](e)}return D=i+16|0,bi(t),D=o+48|0,0|g},function(r,n,e){var i,f;r|=0,D=i=D-16|0,f=n|=0,n=Ae(i+4|0,4+(e|=0)|0,v[e>>2],0),da[0|r](f,n),lf(n),D=i+16|0},function(r,n){r|=0,n|=0;var e,i=0;D=e=D-16|0,v[e+12>>2]=r;r:{if(r=v[5366])for(;;){if(qi(r+4|0,n))break r;if(!(r=v[r+20>>2]))break}n:{e:{if(r=v[5366])for(;;){if(qi(i=r+4|0,n))break e;if(!(r=v[r+20>>2]))break}r=Xf(28),v[r>>2]=16912,i=ht(r+4|0),v[r+20>>2]=0,v[r+24>>2]=0,ee(i,n),v[r+16>>2]=v[e+12>>2],(n=v[5366])&&(v[n+24>>2]=r,v[r+20>>2]=n),v[5366]=r,v[5367]=v[5367]+1;break n}ee(i,n),v[r+16>>2]=v[e+12>>2]}}D=e+16|0},function(r,n){var e;r|=0,D=e=D-16|0,n=Ae(e+4|0,4+(n|=0)|0,v[n>>2],0),da[0|r](n),lf(n),D=e+16|0},function(r){r|=0;var n=0,e=0;r:if(n=v[5366]){for(;;){if(!qi(n+4|0,r)){if(n=v[n+20>>2])continue;break r}break}n:if(n=v[5366]){for(;;){if(!qi(n+4|0,r)){if(n=v[n+20>>2])continue;break n}break}(n=v[n+16>>2])&&da[v[v[n>>2]+4>>2]](n)}n:if(n=v[5366]){for(;;){if(!qi(n+4|0,r)){if(n=v[n+20>>2])continue;break n}break}r=v[n+24>>2],e=v[n+20>>2],v[(r?r+20:21464)>>2]=e,e&&(v[e+24>>2]=r),da[v[v[n>>2]+4>>2]](n),v[5367]=v[5367]-1}}},function(r,n){n|=0,da[0|(r|=0)](n)},uu,yo,function(){return v[5368]},yo,function(){return v[5145]},function(){return v[5369]},yo,function(){return v[5370]},Z,Dr,Ru,function(r){at(Ru(r|=0))},Au,function(r){at(Au(r|=0))},function(){Su(21460)},Su,function(r){at(Su(r|=0))},function(r){return v[(r|=0)>>2]=16912,lf(r+4|0),0|r},function(r){v[(r|=0)>>2]=16912,lf(r+4|0),at(r)},Yo,sa,function(r,n){return(n|=0)?0|Y(n):0},function(r,n){return r|=0,(n|=0)?(!(r=Y(n))|!(3&l[r-4|0])||Bf(r,0,n),0|r):0},function(r,n,e,i,f){r|=0,n|=0,i|=0,f|=0;var t=0,u=0,o=0,a=0,c=0,b=0,k=0,s=0,l=0,h=0;if(!(e|=0))return or(n),0;if(n)if(e>>>0>=4294967232)v[5373]=48,f=0;else{t=e>>>0<11?16:e+11&-8,r=0,f=-8&(a=v[4+(i=n-8|0)>>2]);r:if(3&a){u=i+f|0;n:if(f>>>0>=t>>>0){if((r=f-t|0)>>>0<16)break n;v[i+4>>2]=1&a|t|2,v[4+(f=i+t|0)>>2]=3|r,v[u+4>>2]=1|v[u+4>>2],ar(f,r)}else if(v[5380]!=(0|u))if(v[5379]!=(0|u)){if(2&(o=v[u+4>>2]))break r;if((c=f+(-8&o)|0)>>>0<t>>>0)break r;k=c-t|0;e:if(o>>>0<=255){if((0|(r=v[u+12>>2]))==(0|(f=v[u+8>>2]))){l=21496,h=v[5374]&Qf(o>>>3|0),v[l>>2]=h;break e}v[f+12>>2]=r,v[r+8>>2]=f}else{b=v[u+24>>2];i:if((0|u)==(0|(f=v[u+12>>2]))){f:{if(!(o=v[(r=u+20|0)>>2])){if(!(o=v[u+16>>2]))break f;r=u+16|0}for(;s=r,(o=v[(r=(f=o)+20|0)>>2])||(r=f+16|0,o=v[f+16>>2]););v[s>>2]=0;break i}f=0}else r=v[u+8>>2],v[r+12>>2]=f,v[f+8>>2]=r;if(b){r=v[u+28>>2];i:{if(v[(o=21800+(r<<2)|0)>>2]==(0|u)){if(v[o>>2]=f,f)break i;l=21500,h=v[5375]&Qf(r),v[l>>2]=h;break e}if(v[(v[b+16>>2]==(0|u)?16:20)+b>>2]=f,!f)break e}v[f+24>>2]=b,(r=v[u+16>>2])&&(v[f+16>>2]=r,v[r+24>>2]=f),(r=v[u+20>>2])&&(v[f+20>>2]=r,v[r+24>>2]=f)}}k>>>0<=15?(v[i+4>>2]=1&a|c|2,v[4+(r=i+c|0)>>2]=1|v[r+4>>2]):(v[i+4>>2]=1&a|t|2,v[4+(r=i+t|0)>>2]=3|k,v[4+(f=i+c|0)>>2]=1|v[f+4>>2],ar(r,k))}else{if((f=f+v[5376]|0)>>>0<t>>>0)break r;(r=f-t|0)>>>0>=16?(v[i+4>>2]=1&a|t|2,v[4+(o=i+t|0)>>2]=1|r,v[(f=i+f|0)>>2]=r,v[f+4>>2]=-2&v[f+4>>2]):(v[i+4>>2]=f|1&a|2,v[4+(r=i+f|0)>>2]=1|v[r+4>>2],r=0),v[5379]=o,v[5376]=r}else{if((f=f+v[5377]|0)>>>0<=t>>>0)break r;v[i+4>>2]=1&a|t|2,f=f-t|0,v[4+(r=i+t|0)>>2]=1|f,v[5377]=f,v[5380]=r}r=i}else{if(t>>>0<256)break r;if(f>>>0>=t+4>>>0&&(r=i,f-t>>>0<=v[5494]<<1>>>0))break r;r=0}f=r+8|0,r||(f=0,(i=Y(e))&&(Ff(i,n,(r=(3&(r=v[n-4>>2])?-4:-8)+(-8&r)|0)>>>0<e>>>0?r:e),or(n),f=i))}else f=Y(e);return 0|f},function(r,n){or(n|=0)},function(){return 0},An],ha.set=function(r,n){this[r]=n},ha.get=function(r){return this[r]},ha);function pa(){return i.byteLength/65536|0}return{y:function(){It(20664,3633,20688),Ht(20676,2025),Ht(20688,3638),It(20700,5435,21080),It(20712,5335,21152),It(20724,1838,21164),It(20736,2e3,21164),It(20748,5469,20784),It(20760,1732,21152),Ht(20772,6384),It(20784,5591,21080),It(20796,5546,20784),It(20808,5483,21080),It(20820,5421,21080),It(20836,1711,21152),It(20848,6363,20772),It(20860,5400,20784),Ht(20876,4276),It(20888,4253,20876),It(20900,4270,20876),It(20912,1982,21164),It(20924,1964,21164),It(20936,1728,21152),It(20948,6380,20772),It(20960,5346,20784),It(20972,5515,20784),It(20984,5561,20972),It(20996,1880,20676),It(21008,1927,20676),It(21020,5605,20784),It(21032,5638,21128),It(21044,5501,21128),Ht(21080,5643),It(21092,1687,21152),It(21104,6339,20772),It(21116,5372,20784),It(21128,5620,20784),It(21140,5466,20784),Ht(21152,5804),It(21164,1860,20676),Ht(21180,2278),It(21192,2254,21180),It(21204,2273,21180),v[5314]=1701,v[5315]=0,Z(),mu(21256),v[5316]=1702,v[5317]=0,Dr(),mu(21264),v[5366]=0,v[5367]=0,v[5365]=16896,v[5499]=1719,v[5500]=0,An(),v[5500]=v[5498],v[5498]=21996},z:da,A:Y,B:or,C:function(r){r|=0;var n,e=0,i=0,t=0,u=0;for(D=n=D-32|0,v[n+16>>2]=v[4336],e=v[4335],v[n+8>>2]=v[4334],v[n+12>>2]=e,e=v[4333],v[n>>2]=v[4332],v[n+4>>2]=e,i=7,t=2,u=1;(u&=!(i=r>>>((e=i)<<2)&15)&!!(0|e))||(f[n+t|0]=l[i+17296|0],t=t+1|0),i=e-1|0,e;);return f[n+t|0]=0,r=(e=Y(r=Qn(n)+1|0))?Ff(e,n,r):0,D=n+32|0,0|r},D:function(){var r=0;if(r=v[5498])for(;da[v[r>>2]](),r=v[r+4>>2];);}}}(r)}(r)}function s(r){return{then:function(n){n({instance:new k(r)})}}}Object.assign(e,u),u=null,e.wasmBinary&&(c=e.wasmBinary),e.noExitRuntime;var v=Error,l={};c=[],"object"!=typeof l&&H("no native wasm support detected");var h,d,p,y,m,w,g,F,A,T=!1;function $(){var r=h.buffer;e.HEAP8=d=new Int8Array(r),e.HEAP16=y=new Int16Array(r),e.HEAP32=w=new Int32Array(r),e.HEAPU8=p=new Uint8Array(r),e.HEAPU16=m=new Uint16Array(r),e.HEAPU32=g=new Uint32Array(r),e.HEAPF32=F=new Float32Array(r),e.HEAPF64=A=new Float64Array(r)}var I=e.INITIAL_MEMORY||33554432;65536<=I||H("INITIAL_MEMORY should be larger than STACK_SIZE, was "+I+"! (STACK_SIZE=65536)"),h=e.wasmMemory?e.wasmMemory:new function(){this.buffer=new ArrayBuffer(I/65536*65536)},$(),I=h.buffer.byteLength;var C,P=[],E=[],O=[];function R(){var r=e.preRun.shift();P.unshift(r)}var S,W=0,G=null;function U(){W++,e.monitorRunDependencies&&e.monitorRunDependencies(W)}function j(){if(W--,e.monitorRunDependencies&&e.monitorRunDependencies(W),0==W&&G){var r=G;G=null,r()}}function H(r){throw e.onAbort&&e.onAbort(r),b(r="Aborted("+r+")"),T=!0,r=new v(r+". Build with -sASSERTIONS for more info."),f(r),r}function L(r){return r.startsWith("data:application/octet-stream;base64,")}function M(r){try{if(r==S&&c)return new Uint8Array(c);throw"both async and sync fetching of the wasm failed"}catch(r){H(r)}}function _(r){return c||"function"!=typeof fetch?Promise.resolve().then((()=>M(r))):fetch(r,{credentials:"same-origin"}).then((n=>{if(!n.ok)throw"failed to load wasm binary file at '"+r+"'";return n.arrayBuffer()})).catch((()=>M(r)))}function z(r,n,e){return _(r).then((()=>s(n))).then((r=>r)).then(e,(r=>{b("failed to asynchronously prepare wasm: "+r),H(r)}))}function x(r,n){var e=S;return c||"function"!=typeof l.instantiateStreaming||L(e)||"function"!=typeof fetch?z(e,r,n):fetch(e,{credentials:"same-origin"}).then((i=>l.instantiateStreaming(i,r).then(n,(function(i){return b("wasm streaming compile failed: "+i),b("falling back to ArrayBuffer instantiation"),z(e,r,n)}))))}L(S="spine.wasm")||(S=a(S));var J="spine.js.mem";function K(){L(J)||(J=a(J)),U();var r=r=>{r.byteLength&&(r=new Uint8Array(r)),p.set(r,1024),e.memoryInitializerRequest&&delete e.memoryInitializerRequest.response,j()},n=()=>{t(r,(()=>{f(Error("could not load memory initializer "+J))}))};if(e.memoryInitializerRequest){var i=()=>{var i=e.memoryInitializerRequest,f=i.response;200!==i.status&&0!==i.status?(console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+i.status+", retrying "+J),n()):r(f)};e.memoryInitializerRequest.response?setTimeout(i,0):e.memoryInitializerRequest.addEventListener("load",i)}else n()}function B(r){for(;0<r.length;)r.shift()(e)}function N(r){switch(r){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${r}`)}}var q=void 0;function D(r){for(var n="";p[r];)n+=q[p[r++]];return n}var V={},Z={},Y={};function X(r){if(void 0===r)return"_unknown";var n=(r=r.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return 48<=n&&57>=n?`_${r}`:r}function Q(r,n){return r=X(r),{[r]:function(){return n.apply(this,arguments)}}[r]}function rr(r){var n=Error,e=Q(r,(function(n){this.name=r,this.message=n,void 0!==(n=Error(n).stack)&&(this.stack=this.toString()+"\n"+n.replace(/^Error(:[^\n]*)?\n/,""))}));return e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`},e}var nr=void 0;function er(r){throw new nr(r)}var ir=void 0;function fr(r){throw new ir(r)}function tr(r,n,e){function i(n){(n=e(n)).length!==r.length&&fr("Mismatched type converter count");for(var i=0;i<r.length;++i)ur(r[i],n[i])}r.forEach((function(r){Y[r]=n}));var f=Array(n.length),t=[],u=0;n.forEach(((r,n)=>{Z.hasOwnProperty(r)?f[n]=Z[r]:(t.push(r),V.hasOwnProperty(r)||(V[r]=[]),V[r].push((()=>{f[n]=Z[r],++u===t.length&&i(f)})))})),0===t.length&&i(f)}function ur(r,n){if(!("argPackAdvance"in n))throw new TypeError("registerType registeredInstance requires argPackAdvance");var e=n.name;if(r||er(`type "${e}" must have a positive integer typeid pointer`),Z.hasOwnProperty(r)){if({}.qa)return;er(`Cannot register type '${e}' twice`)}Z[r]=n,delete Y[r],V.hasOwnProperty(r)&&(n=V[r],delete V[r],n.forEach((r=>r())))}function or(r){er(r.F.I.G.name+" instance already deleted")}var ar=!1;function cr(){}function br(r){--r.count.value,0===r.count.value&&(r.L?r.M.S(r.L):r.I.G.S(r.H))}function kr(r,n,e){return n===e?r:void 0===e.J||null===(r=kr(r,n,e.J))?null:e.ha(r)}var sr={},vr=[];function lr(){for(;vr.length;){var r=vr.pop();r.F.U=!1,r.delete()}}var hr=void 0,dr={};function pr(r,n){for(void 0===n&&er("ptr should not be undefined");r.J;)n=r.W(n),r=r.J;return dr[n]}function yr(r,n){return n.I&&n.H||fr("makeClassHandle requires ptr and ptrType"),!!n.M!=!!n.L&&fr("Both smartPtrType and smartPtr must be specified"),n.count={value:1},mr(Object.create(r,{F:{value:n}}))}function mr(r){return"undefined"==typeof FinalizationRegistry?(mr=r=>r,r):(ar=new FinalizationRegistry((r=>{br(r.F)})),cr=r=>{ar.unregister(r)},(mr=r=>{var n=r.F;return n.L&&ar.register(r,{F:n},r),r})(r))}function wr(){}function gr(r,n,e){if(void 0===r[n].K){var i=r[n];r[n]=function(){return r[n].K.hasOwnProperty(arguments.length)||er(`Function '${e}' called with an invalid number of arguments (${arguments.length}) - expects one of (${r[n].K})!`),r[n].K[arguments.length].apply(this,arguments)},r[n].K=[],r[n].K[i.T]=i}}function Fr(r,n){e.hasOwnProperty(r)?(er(`Cannot register public name '${r}' twice`),gr(e,r,r),e.hasOwnProperty(void 0)&&er("Cannot register multiple overloads of a function with the same number of arguments (undefined)!"),e[r].K[void 0]=n):e[r]=n}function Ar(r,n,e,i,f,t,u,o){this.name=r,this.constructor=n,this.P=e,this.S=i,this.J=f,this.ia=t,this.W=u,this.ha=o,this.la=[]}function Tr(r,n,e){for(;n!==e;)n.W||er(`Expected null or instance of ${e.name}, got an instance of ${n.name}`),r=n.W(r),n=n.J;return r}function $r(r,n){return null===n?(this.$&&er(`null is not a valid ${this.name}`),0):(n.F||er(`Cannot pass "${Kr(n)}" as a ${this.name}`),n.F.H||er(`Cannot pass deleted object as a pointer of type ${this.name}`),Tr(n.F.H,n.F.I.G,this.G))}function Ir(r,n){if(null===n){if(this.$&&er(`null is not a valid ${this.name}`),this.Z){var e=this.ma();return null!==r&&r.push(this.S,e),e}return 0}if(n.F||er(`Cannot pass "${Kr(n)}" as a ${this.name}`),n.F.H||er(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.Y&&n.F.I.Y&&er(`Cannot convert argument of type ${n.F.M?n.F.M.name:n.F.I.name} to parameter type ${this.name}`),e=Tr(n.F.H,n.F.I.G,this.G),this.Z)switch(void 0===n.F.L&&er("Passing raw pointer to smart pointer is illegal"),this.pa){case 0:n.F.M===this?e=n.F.L:er(`Cannot convert argument of type ${n.F.M?n.F.M.name:n.F.I.name} to parameter type ${this.name}`);break;case 1:e=n.F.L;break;case 2:if(n.F.M===this)e=n.F.L;else{var i=n.clone();e=this.na(e,Jr((function(){i.delete()}))),null!==r&&r.push(this.S,e)}break;default:er("Unsupporting sharing policy")}return e}function Cr(r,n){return null===n?(this.$&&er(`null is not a valid ${this.name}`),0):(n.F||er(`Cannot pass "${Kr(n)}" as a ${this.name}`),n.F.H||er(`Cannot pass deleted object as a pointer of type ${this.name}`),n.F.I.Y&&er(`Cannot convert argument of type ${n.F.I.name} to parameter type ${this.name}`),Tr(n.F.H,n.F.I.G,this.G))}function Pr(r){return this.fromWireType(w[r>>2])}function Er(r,n,e,i){this.name=r,this.G=n,this.$=e,this.Y=i,this.Z=!1,this.S=this.na=this.ma=this.da=this.pa=this.ka=void 0,void 0!==n.J?this.toWireType=Ir:(this.toWireType=i?$r:Cr,this.O=null)}function Or(r,n){e.hasOwnProperty(r)||fr("Replacing nonexistant public symbol"),e[r]=n,e[r].T=void 0}function Rr(r,n){var i=[];return function(){if(i.length=0,Object.assign(i,arguments),r.includes("j")){var f=e["dynCall_"+r];f=i&&i.length?f.apply(null,[n].concat(i)):f.call(null,n)}else f=C.get(n).apply(null,i);return f}}function Sr(r,n){var e=(r=D(r)).includes("j")?Rr(r,n):C.get(n);return"function"!=typeof e&&er(`unknown function pointer with signature ${r}: ${n}`),e}var Wr=void 0;function Gr(r){var n=D(r=tn(r));return fn(r),n}function Ur(r,n){var e=[],i={};throw n.forEach((function r(n){i[n]||Z[n]||(Y[n]?Y[n].forEach(r):(e.push(n),i[n]=!0))})),new Wr(`${r}: `+e.map(Gr).join([", "]))}function jr(r){for(;r.length;){var n=r.pop();r.pop()(n)}}function Hr(r,n,e,i,f){var t=n.length;2>t&&er("argTypes array size mismatch! Must at least get return value and 'this' types!");var u=null!==n[1]&&null!==e,o=!1;for(e=1;e<n.length;++e)if(null!==n[e]&&void 0===n[e].O){o=!0;break}var a="void"!==n[0].name,c=t-2,b=Array(c),k=[],s=[];return function(){if(arguments.length!==c&&er(`function ${r} called with ${arguments.length} arguments, expected ${c} args!`),s.length=0,k.length=u?2:1,k[0]=f,u){var e=n[1].toWireType(s,this);k[1]=e}for(var t=0;t<c;++t)b[t]=n[t+2].toWireType(s,arguments[t]),k.push(b[t]);if(t=i.apply(null,k),o)jr(s);else for(var v=u?1:2;v<n.length;v++){var l=1===v?e:b[v-2];null!==n[v].O&&n[v].O(l)}return a?n[0].fromWireType(t):void 0}}function Lr(r,n){for(var e=[],i=0;i<r;i++)e.push(g[n+4*i>>2]);return e}function Mr(r,n,e){return r instanceof Object||er(`${e} with invalid "this": ${r}`),r instanceof n.G.constructor||er(`${e} incompatible with "this" of type ${r.constructor.name}`),r.F.H||er(`cannot call emscripten binding method ${e} on deleted object`),Tr(r.F.H,r.F.I.G,n.G)}var _r=new function(){this.N=[void 0],this.aa=[],this.get=function(r){return this.N[r]},this.has=function(r){return void 0!==this.N[r]},this.ea=function(r){var n=this.aa.pop()||this.N.length;return this.N[n]=r,n},this.fa=function(r){this.N[r]=void 0,this.aa.push(r)}};function zr(r){r>=_r.ba&&0==--_r.get(r).oa&&_r.fa(r)}var xr=r=>(r||er("Cannot use deleted val. handle = "+r),_r.get(r).value),Jr=r=>{switch(r){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return _r.ea({oa:1,value:r})}};function Kr(r){if(null===r)return"null";var n=typeof r;return"object"===n||"array"===n||"function"===n?r.toString():""+r}function Br(r,n){switch(n){case 2:return function(r){return this.fromWireType(F[r>>2])};case 3:return function(r){return this.fromWireType(A[r>>3])};default:throw new TypeError("Unknown float type: "+r)}}function Nr(r,n,e){switch(n){case 0:return e?function(r){return d[r]}:function(r){return p[r]};case 1:return e?function(r){return y[r>>1]}:function(r){return m[r>>1]};case 2:return e?function(r){return w[r>>2]}:function(r){return g[r>>2]};default:throw new TypeError("Unknown integer type: "+r)}}var qr="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function Dr(r,n){var e=Z[r];return void 0===e&&er(n+" has unknown type "+Gr(r)),e}var Vr={};function Zr(){var r=e.SpineWasmUtil,n=r.getCurrentListenerID(),i=r.getCurrentTrackEntry(),f=r.getCurrentEvent();r=r.getCurrentEventType(),globalThis.TrackEntryListeners.emitListener(n,i,f,r)}function Yr(){var r=e.SpineWasmUtil,n=r.getCurrentListenerID(),i=r.getCurrentEventType(),f=r.getCurrentTrackEntry();r=r.getCurrentEvent(),globalThis.TrackEntryListeners.emitTrackEntryListener(n,f,r,i)}e._spineListenerCallBackFromJS=Zr,e._spineTrackListenerCallback=Yr;for(var Xr=Array(256),Qr=0;256>Qr;++Qr)Xr[Qr]=String.fromCharCode(Qr);q=Xr,nr=e.BindingError=rr("BindingError"),ir=e.InternalError=rr("InternalError"),wr.prototype.isAliasOf=function(r){if(!(this instanceof wr&&r instanceof wr))return!1;var n=this.F.I.G,e=this.F.H,i=r.F.I.G;for(r=r.F.H;n.J;)e=n.W(e),n=n.J;for(;i.J;)r=i.W(r),i=i.J;return n===i&&e===r},wr.prototype.clone=function(){if(this.F.H||or(this),this.F.V)return this.F.count.value+=1,this;var r=mr,n=Object,e=n.create,i=Object.getPrototypeOf(this),f=this.F;return(r=r(e.call(n,i,{F:{value:{count:f.count,U:f.U,V:f.V,H:f.H,I:f.I,L:f.L,M:f.M}}}))).F.count.value+=1,r.F.U=!1,r},wr.prototype.delete=function(){this.F.H||or(this),this.F.U&&!this.F.V&&er("Object already scheduled for deletion"),cr(this),br(this.F),this.F.V||(this.F.L=void 0,this.F.H=void 0)},wr.prototype.isDeleted=function(){return!this.F.H},wr.prototype.deleteLater=function(){return this.F.H||or(this),this.F.U&&!this.F.V&&er("Object already scheduled for deletion"),vr.push(this),1===vr.length&&hr&&hr(lr),this.F.U=!0,this},e.getInheritedInstanceCount=function(){return Object.keys(dr).length},e.getLiveInheritedInstances=function(){var r,n=[];for(r in dr)dr.hasOwnProperty(r)&&n.push(dr[r]);return n},e.flushPendingDeletes=lr,e.setDelayFunction=function(r){hr=r,vr.length&&hr&&hr(lr)},Er.prototype.ja=function(r){return this.da&&(r=this.da(r)),r},Er.prototype.ca=function(r){this.S&&this.S(r)},Er.prototype.argPackAdvance=8,Er.prototype.readValueFromPointer=Pr,Er.prototype.deleteObject=function(r){null!==r&&r.delete()},Er.prototype.fromWireType=function(r){function n(){return this.Z?yr(this.G.P,{I:this.ka,H:e,M:this,L:r}):yr(this.G.P,{I:this,H:r})}var e=this.ja(r);if(!e)return this.ca(r),null;var i=pr(this.G,e);if(void 0!==i)return 0===i.F.count.value?(i.F.H=e,i.F.L=r,i.clone()):(i=i.clone(),this.ca(r),i);if(i=this.G.ia(e),!(i=sr[i]))return n.call(this);i=this.Y?i.ga:i.pointerType;var f=kr(e,this.G,i.G);return null===f?n.call(this):this.Z?yr(i.G.P,{I:i,H:f,M:this,L:r}):yr(i.G.P,{I:i,H:f})},Wr=e.UnboundTypeError=rr("UnboundTypeError"),_r.N.push({value:void 0},{value:null},{value:!0},{value:!1}),_r.ba=_r.N.length,e.count_emval_handles=function(){for(var r=0,n=_r.ba;n<_r.N.length;++n)void 0!==_r.N[n]&&++r;return r};var rn,nn={q:function(){},u:function(r,n,e,i,f){var t=N(e);ur(r,{name:n=D(n),fromWireType:function(r){return!!r},toWireType:function(r,n){return n?i:f},argPackAdvance:8,readValueFromPointer:function(r){if(1===e)var i=d;else if(2===e)i=y;else{if(4!==e)throw new TypeError("Unknown boolean type size: "+n);i=w}return this.fromWireType(i[r>>t])},O:null})},d:function(r,n,e,i,f,t,u,o,a,c,b,k,s){b=D(b),t=Sr(f,t),o&&(o=Sr(u,o)),c&&(c=Sr(a,c)),s=Sr(k,s);var v=X(b);Fr(v,(function(){Ur(`Cannot construct ${b} due to unbound types`,[i])})),tr([r,n,e],i?[i]:[],(function(n){if(n=n[0],i)var e=n.G,f=e.P;else f=wr.prototype;n=Q(v,(function(){if(Object.getPrototypeOf(this)!==u)throw new nr("Use 'new' to construct "+b);if(void 0===a.R)throw new nr(b+" has no accessible constructor");var r=a.R[arguments.length];if(void 0===r)throw new nr(`Tried to invoke ctor of ${b} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(a.R).toString()}) parameters instead!`);return r.apply(this,arguments)}));var u=Object.create(f,{constructor:{value:n}});n.prototype=u;var a=new Ar(b,n,u,s,e,t,o,c);a.J&&(void 0===a.J.X&&(a.J.X=[]),a.J.X.push(a)),e=new Er(b,a,!0,!1),f=new Er(b+"*",a,!1,!1);var k=new Er(b+" const*",a,!1,!0);return sr[r]={pointerType:f,ga:k},Or(v,n),[e,f,k]}))},g:function(r,n,e,i,f,t,u){var o=Lr(e,i);n=D(n),t=Sr(f,t),tr([],[r],(function(r){function i(){Ur(`Cannot call ${f} due to unbound types`,o)}var f=`${(r=r[0]).name}.${n}`;n.startsWith("@@")&&(n=Symbol[n.substring(2)]);var a=r.G.constructor;return void 0===a[n]?(i.T=e-1,a[n]=i):(gr(a,n,f),a[n].K[e-1]=i),tr([],o,(function(i){if(i=Hr(f,[i[0],null].concat(i.slice(1)),null,t,u),void 0===a[n].K?(i.T=e-1,a[n]=i):a[n].K[e-1]=i,r.G.X)for(const e of r.G.X)e.constructor.hasOwnProperty(n)||(e.constructor[n]=i);return[]})),[]}))},i:function(r,n,e,i,f,t,u,o){n=D(n),t=Sr(f,t),tr([],[r],(function(r){var f=`${(r=r[0]).name}.${n}`,a={get:function(){Ur(`Cannot access ${f} due to unbound types`,[e])},enumerable:!0,configurable:!0};return a.set=o?()=>{Ur(`Cannot access ${f} due to unbound types`,[e])}:()=>{er(`${f} is a read-only property`)},Object.defineProperty(r.G.constructor,n,a),tr([],[e],(function(e){e=e[0];var f={get:function(){return e.fromWireType(t(i))},enumerable:!0};return o&&(o=Sr(u,o),f.set=r=>{var n=[];o(i,e.toWireType(n,r)),jr(n)}),Object.defineProperty(r.G.constructor,n,f),[]})),[]}))},e:function(r,n,e,i,f,t){0<n||H();var u=Lr(n,e);f=Sr(i,f),tr([],[r],(function(r){var e=`constructor ${(r=r[0]).name}`;if(void 0===r.G.R&&(r.G.R=[]),void 0!==r.G.R[n-1])throw new nr(`Cannot register multiple constructors with identical number of parameters (${n-1}) for class '${r.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return r.G.R[n-1]=()=>{Ur(`Cannot construct ${r.name} due to unbound types`,u)},tr([],u,(function(i){return i.splice(1,0,null),r.G.R[n-1]=Hr(e,i,null,f,t),[]})),[]}))},b:function(r,n,e,i,f,t,u,o){var a=Lr(e,i);n=D(n),t=Sr(f,t),tr([],[r],(function(r){function i(){Ur(`Cannot call ${f} due to unbound types`,a)}var f=`${(r=r[0]).name}.${n}`;n.startsWith("@@")&&(n=Symbol[n.substring(2)]),o&&r.G.la.push(n);var c=r.G.P,b=c[n];return void 0===b||void 0===b.K&&b.className!==r.name&&b.T===e-2?(i.T=e-2,i.className=r.name,c[n]=i):(gr(c,n,f),c[n].K[e-2]=i),tr([],a,(function(i){return i=Hr(f,i,r,t,u),void 0===c[n].K?(i.T=e-2,c[n]=i):c[n].K[e-2]=i,[]})),[]}))},c:function(r,n,e,i,f,t,u,o,a,c){n=D(n),f=Sr(i,f),tr([],[r],(function(r){var i=`${(r=r[0]).name}.${n}`,b={get:function(){Ur(`Cannot access ${i} due to unbound types`,[e,u])},enumerable:!0,configurable:!0};return b.set=a?()=>{Ur(`Cannot access ${i} due to unbound types`,[e,u])}:()=>{er(i+" is a read-only property")},Object.defineProperty(r.G.P,n,b),tr([],a?[e,u]:[e],(function(e){var u=e[0],b={get:function(){var n=Mr(this,r,i+" getter");return u.fromWireType(f(t,n))},enumerable:!0};if(a){a=Sr(o,a);var k=e[1];b.set=function(n){var e=Mr(this,r,i+" setter"),f=[];a(c,e,k.toWireType(f,n)),jr(f)}}return Object.defineProperty(r.G.P,n,b),[]})),[]}))},t:function(r,n){ur(r,{name:n=D(n),fromWireType:function(r){var n=xr(r);return zr(r),n},toWireType:function(r,n){return Jr(n)},argPackAdvance:8,readValueFromPointer:Pr,O:null})},n:function(r,n,e){e=N(e),ur(r,{name:n=D(n),fromWireType:function(r){return r},toWireType:function(r,n){return n},argPackAdvance:8,readValueFromPointer:Br(n,e),O:null})},f:function(r,n,e,i,f){n=D(n),-1===f&&(f=4294967295),f=N(e);var t=r=>r;if(0===i){var u=32-8*e;t=r=>r<<u>>>u}e=n.includes("unsigned")?function(r,n){return n>>>0}:function(r,n){return n},ur(r,{name:n,fromWireType:t,toWireType:e,argPackAdvance:8,readValueFromPointer:Nr(n,f,0!==i),O:null})},s:function(r,n){var e="std::string"===(n=D(n));ur(r,{name:n,fromWireType:function(r){var n=g[r>>2],i=r+4;if(e)for(var f=i,t=0;t<=n;++t){var u=i+t;if(t==n||0==p[u]){if(f){var o=f,a=p,c=o+(u-f);for(f=o;a[f]&&!(f>=c);)++f;if(16<f-o&&a.buffer&&qr)o=qr.decode(a.subarray(o,f));else{for(c="";o<f;){var b=a[o++];if(128&b){var k=63&a[o++];if(192==(224&b))c+=String.fromCharCode((31&b)<<6|k);else{var s=63&a[o++];65536>(b=224==(240&b)?(15&b)<<12|k<<6|s:(7&b)<<18|k<<12|s<<6|63&a[o++])?c+=String.fromCharCode(b):(b-=65536,c+=String.fromCharCode(55296|b>>10,56320|1023&b))}}else c+=String.fromCharCode(b)}o=c}}else o="";if(void 0===v)var v=o;else v+=String.fromCharCode(0),v+=o;f=u+1}}else{for(v=Array(n),t=0;t<n;++t)v[t]=String.fromCharCode(p[i+t]);v=v.join("")}return fn(r),v},toWireType:function(r,n){n instanceof ArrayBuffer&&(n=new Uint8Array(n));var i,f,t="string"==typeof n;if(t||n instanceof Uint8Array||n instanceof Uint8ClampedArray||n instanceof Int8Array||er("Cannot pass non-string to std::string"),e&&t)for(i=f=0;i<n.length;++i){var u=n.charCodeAt(i);127>=u?f++:2047>=u?f+=2:55296<=u&&57343>=u?(f+=4,++i):f+=3}else f=n.length;if(u=(f=en(4+(i=f)+1))+4,g[f>>2]=i,e&&t){if(t=u,u=i+1,i=p,0<u){u=t+u-1;for(var o=0;o<n.length;++o){var a=n.charCodeAt(o);if(55296<=a&&57343>=a&&(a=65536+((1023&a)<<10)|1023&n.charCodeAt(++o)),127>=a){if(t>=u)break;i[t++]=a}else{if(2047>=a){if(t+1>=u)break;i[t++]=192|a>>6}else{if(65535>=a){if(t+2>=u)break;i[t++]=224|a>>12}else{if(t+3>=u)break;i[t++]=240|a>>18,i[t++]=128|a>>12&63}i[t++]=128|a>>6&63}i[t++]=128|63&a}}i[t]=0}}else if(t)for(t=0;t<i;++t)255<(o=n.charCodeAt(t))&&(fn(u),er("String has UTF-16 code units that do not fit in 8 bits")),p[u+t]=o;else for(t=0;t<i;++t)p[u+t]=n[t];return null!==r&&r.push(fn,f),f},argPackAdvance:8,readValueFromPointer:Pr,O:function(r){fn(r)}})},v:function(r,n){ur(r,{ra:!0,name:n=D(n),argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},k:function(r,n,e){r=xr(r),n=Dr(n,"emval::as");var i=[],f=Jr(i);return g[e>>2]=f,n.toWireType(i,r)},h:zr,l:function(r,n){return r=xr(r),n=xr(n),Jr(r[n])},p:function(r){var n=Vr[r];return Jr(void 0===n?D(r):n)},j:function(r){jr(xr(r)),zr(r)},o:function(r,n){return r=(r=Dr(r,"_emval_take_value")).readValueFromPointer(n),Jr(r)},m:function(){H("")},r:function(r){var n=p.length;if(2147483648<(r>>>=0))return!1;for(var e=1;4>=e;e*=2){var i=n*(1+.2/e);i=Math.min(i,r+100663296);var f=Math;i=Math.max(r,i);r:{f=f.min.call(f,2147483648,i+(65536-i%65536)%65536)-h.buffer.byteLength+65535>>>16;try{h.grow(f),$();var t=1;break r}catch(r){}t=void 0}if(t)return!0}return!1},a:h,x:Zr,w:Yr};function en(){return(en=e.asm.A).apply(null,arguments)}function fn(){return(fn=e.asm.B).apply(null,arguments)}function tn(){return(tn=e.asm.C).apply(null,arguments)}function un(){function r(){if(!rn&&(rn=!0,e.calledRun=!0,!T)){if(B(E),i(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;){var r=e.postRun.shift();O.unshift(r)}B(O)}}if(!(0<W)){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)R();B(P),0<W||(e.setStatus?(e.setStatus("Running..."),setTimeout((function(){setTimeout((function(){e.setStatus("")}),1),r()}),1)):r())}}if(function(){function r(r){return r=r.exports,e.asm=r,K(),C=e.asm.z,E.unshift(e.asm.y),j(),r}var n={a:nn};if(U(),e.instantiateWasm)try{return e.instantiateWasm(n,r)}catch(r){b("Module.instantiateWasm callback failed with error: "+r),f(r)}x(n,(function(n){r(n.instance)})).catch(f)}(),e.__embind_initialize_bindings=function(){return(e.__embind_initialize_bindings=e.asm.D).apply(null,arguments)},G=function r(){rn||un(),rn||(G=r)},e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);0<e.preInit.length;)e.preInit.pop()();return un(),r.ready}))}}}));
