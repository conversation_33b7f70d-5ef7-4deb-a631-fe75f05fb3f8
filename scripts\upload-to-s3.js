/**
 * 上传构建包到S3 OSS的脚本
 *
 * 使用方法:
 * node scripts/upload-to-s3.js <游戏名称> <版本号> [自定义zip文件名(不含扩展名)]
 *
 * 脚本会上传当前目录下的zip文件，如果提供了自定义文件名，则使用自定义文件名，
 * 否则使用<游戏名称>-<版本号>.zip作为文件名
 *
 * 环境变量:
 * S3_ACCESS_KEY_ID - S3的访问密钥ID
 * S3_SECRET_ACCESS_KEY - S3的秘密访问密钥
 * S3_ENDPOINT - S3的终端节点URL
 * S3_REGION - S3的区域
 * S3_BUCKET - S3的存储桶名称
 */

const AWS = require('aws-sdk/global')
const S3 = require('aws-sdk/clients/s3')
const path = require('path')
const fs = require('fs')
const { promisify } = require('util')
const readdir = promisify(fs.readdir)
const stat = promisify(fs.stat)

// 从环境变量获取S3配置
const accessKeyId = process.env.S3_ACCESS_KEY_ID
const secretAccessKey = process.env.S3_SECRET_ACCESS_KEY
const endpoint = process.env.S3_ENDPOINT
const region = process.env.S3_REGION
const bucket = process.env.S3_BUCKET

// 检查必要的环境变量
if (!accessKeyId || !secretAccessKey || !endpoint || !region || !bucket) {
    console.error(
        '错误: 缺少必要的环境变量 (S3_ACCESS_KEY_ID, S3_SECRET_ACCESS_KEY, S3_ENDPOINT, S3_REGION, S3_BUCKET)'
    )
    process.exit(1)
}

// 检查命令行参数
const [, , gameCode, version, customZipName] = process.argv
if (!gameCode || !version) {
    console.error(
        '用法: node scripts/upload-to-s3.js <游戏名称> <版本号> [自定义zip文件名(不含扩展名)]'
    )
    process.exit(1)
}

// 构建zip文件路径
const zipFileName = customZipName
    ? `${customZipName}.zip`
    : `${gameCode}-${version}.zip`
const zipFilePath = path.join(process.cwd(), zipFileName)

// 初始化S3客户端
const s3 = new S3({
    accessKeyId: accessKeyId,
    secretAccessKey: secretAccessKey,
    endpoint: endpoint,
    region: region,
    s3ForcePathStyle: true,
    signatureVersion: 'v4',
})

// 不再需要递归获取目录下所有文件的函数，因为我们只上传单个zip文件

// 上传单个文件到S3
async function uploadFile(filePath, s3Key) {
    return new Promise((resolve, reject) => {
        const fileStream = fs.createReadStream(filePath)
        const fileSize = fs.statSync(filePath).size

        const params = {
            Bucket: bucket,
            Key: s3Key,
            Body: fileStream,
        }

        // 分片上传配置 (8MB分片)
        const options = {
            partSize: 8 * 1024 * 1024,
            queueSize: 4,
        }

        const uploadTask = s3
            .upload(params, options, (err, data) => {
                if (err) {
                    reject(err)
                } else {
                    resolve(data)
                }
            })
            .on('httpUploadProgress', ({ loaded, total }) => {
                console.log(
                    `上传进度 [${s3Key}]: ${loaded}/${total} (${Math.round(
                        (100 * loaded) / total
                    )}%)`
                )
            })
    })
}

// 主函数
async function main() {
    try {
        // 检查zip文件是否存在
        if (!fs.existsSync(zipFilePath)) {
            console.error(`错误: ZIP文件 "${zipFilePath}" 不存在`)
            process.exit(1)
        }

        console.log(`准备上传ZIP文件: ${zipFileName}`)

        // 构建S3 Key
        const s3Key = `games/${gameCode}/${zipFileName}`

        console.log(`上传: ${zipFileName} -> ${s3Key}`)

        let baseUrl
        try {
            const data = await uploadFile(zipFilePath, s3Key)
            console.log(`ZIP文件上传成功!`, data)
            baseUrl = data['Location']
        } catch (err) {
            console.error(`上传ZIP文件失败:`, err)
            process.exit(1)
        }

        // 构建基础URL
        // 注意：如果S3返回的Location为空，则自行构建URL
        baseUrl =
            baseUrl || `${endpoint}/${bucket}/games/${gameCode}/${zipFileName}`
        console.log('上传完成!')
        console.log(`基础URL: ${baseUrl}`)

        // 输出结果到文件，供后续脚本使用
        const resultFile = path.join(process.cwd(), 'upload-result.json')
        fs.writeFileSync(
            resultFile,
            JSON.stringify({
                baseUrl,
                gameCode,
                version,
                timestamp: new Date().toISOString(),
            })
        )

        console.log(`结果已保存到: ${resultFile}`)

        // 返回成功
        process.exit(0)
    } catch (err) {
        console.error('上传过程中发生错误:', err)
        process.exit(1)
    }
}

main()
