System.register("chunks:///_virtual/AbsAdapter.ts",["cc"],(function(t){var e;return{setters:[function(t){e=t.cclegacy}],execute:function(){e._RF.push({},"52a1aGDTghFY53yQFqoEfJU","AbsAdapter",void 0);t("AbsAdapter",function(){function t(){this.onItemClickListener=void 0,this.dataSet=[]}var e=t.prototype;return e.setDataSet=function(t){this.dataSet=t||[]},e.getCount=function(){return this.dataSet?this.dataSet.length:0},e.getItem=function(t){return this.dataSet[t]},e._getView=function(t,e){return this.updateView(t,e,this.dataSet[e]),t},e.setOnItemClickListener=function(t,e){this.onItemClickListener=e?t.bind(e):t},e.onItemClicked=function(t,e){this.onItemClickListener&&this.onItemClickListener(this,e)},t}());e._RF.pop()}}}));

System.register("chunks:///_virtual/account_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./file.js","./message.js"],(function(c){var e,n,o;return{setters:[function(c){e=c.cclegacy},null,null,null,function(c){n=c.fileDesc},function(c){o=c.messageDesc}],execute:function(){e._RF.push({},"e37e3BQJvxEv54/wlX62hFn","account_pb",void 0);var b=c("file_account",n("Cg1hY2NvdW50LnByb3RvEgZwcm90b3MimwEKE0JhbGFuY2VOb3RpZmljYXRpb24SDwoHYmFsYW5jZRgBIAEoCRINCgVwb2ludBgCIAEoBBINCgVsZXZlbBgDIAEoBBIUCgxQbGF5VGltZVBhaWQYBCABKAQSFAoMUGxheVRpbWVGcmVlGAUgASgEEhoKEkZhc3RFbnRlckV4cGlyZXNJbhgGIAEoBBINCgVZb29kbxgHIAEoCUIwCiRjb20uc3RudHMuY2xvdWQuc3VpbGV5b28uZWNoby5wcm90b3NaCC4vcHJvdG9zYgZwcm90bzM"));c("BalanceNotificationSchema",o(b,0));e._RF.pop()}}}));

System.register("chunks:///_virtual/ActionEventConstant.ts",["cc"],(function(t){var n;return{setters:[function(t){n=t.cclegacy}],execute:function(){n._RF.push({},"4e88872tKZJXKFnBl8K3PZp","ActionEventConstant",void 0);t("ActionEventConstant",function(t){return{}}());n._RF.pop()}}}));

System.register("chunks:///_virtual/ActiveChoosePlayer.ts",["./rollupPluginModLoBabelHelpers.js","cc","./CommonActionUI.ts","./index21.ts","./index40.ts","./Decorator.ts","./msg_pb.ts","./PlayerItem.ts","./create.js","./descriptors.js","./reflect.js","./descriptor_pb.js","./binary-encoding.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./index23.ts","./GameEventConstant.b.ts"],(function(e){var t,n,o,r,a,s,i,l,c,p,u,y,d,f,h,v,m,P,g,_,b,S;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,o=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){a=e.cclegacy,s=e._decorator,i=e.Prefab,l=e.instantiate,c=e.Layers,p=e.v3},function(e){u=e.CommonActionUI},function(e){y=e.cat},null,function(e){d=e.buttonLock,f=e.watchUser},function(e){h=e.SelectTargetRequestSchema,v=e.SelectTargetResponseSchema},function(e){m=e.PlayerItem,P=e.PlayerState},function(e){g=e.create},null,function(e){_=e.reflect},null,null,null,null,null,null,function(e){b=e.default},function(e){S=e.GameEventConstant}],execute:function(){var C,E,A,T,L,j,R;a._RF.push({},"8f26110B9FFyq1vlNGJhaR0","ActiveChoosePlayer",void 0);var w=s.ccclass,x=s.property;e("ActiveChoosePlayer",(C=w("ActiveChoosePlayer"),E=x({type:i,tooltip:"玩家预制体"}),A=f(),T=d(),C((R=t((j=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return t=e.call.apply(e,[this].concat(a))||this,o(t,"player_prefab",R,r(t)),t.props={players:[]},t}n(t,e);var a=t.prototype;return a.onEventListener=function(){e.prototype.onEventListener.call(this),y.event.on(S.SELECT_PLAYER,this.onSelectedPlayerHandler,this).on("EVENT_SELECT_TARGET_BROADCAST",this.onSelectTargetBroadcast,this)},a.start=function(){var t=this;e.prototype.start.call(this),this.props.players.forEach((function(e,n){var o=l(t.player_prefab),r=b.game.playersPos.get(e.index),a=r.x,s=r.y;window.ccLog("重新实例玩家",n,e),o.getComponent(m).setNodeAndChildrenLayer(c.Enum.UI_2D).setPosition(p(a,s,0)).addToParent(t.node,{props:{player:e,showCurseCountImmediately:!0},data:{state:P.NORMAL,show_select:!0}})}))},a.close=function(){e.prototype.close.call(this)},a.onSelectedPlayerHandler=function(e){var t=this;y.ws.Request("SelectTarget",_(h,g(h,{index:e.index})),v).then((function(){t.close()}))},a.onSelectTargetBroadcast=function(e){this.close()},t}(u)).prototype,"player_prefab",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),t(j.prototype,"onSelectedPlayerHandler",[A,T],Object.getOwnPropertyDescriptor(j.prototype,"onSelectedPlayerHandler"),j.prototype),L=j))||L));a._RF.pop()}}}));

System.register("chunks:///_virtual/ActiveRemoveMine.ts",["./rollupPluginModLoBabelHelpers.js","cc","./AudioSourceUILayer.ts","./index21.ts","./index40.ts","./index23.ts","./Decorator.ts","./msg_pb.ts","./reflect.js","./descriptors.js","./create.js","./descriptor_pb.js","./binary-encoding.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./GetTimeDifferenceFromServer.ts","./GameEventConstant.b.ts"],(function(e){var t,n,o,i,r,s,a,u,c,l,p,d,f,m,_,v,h,b,y,g,w,C,I,R,A,L;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,o=e.initializerDefineProperty,i=e.assertThisInitialized},function(e){r=e.cclegacy,s=e._decorator,a=e.Button,u=e.Label,c=e.Node,l=e.math,p=e.tween,d=e.UIOpacity,f=e.Tween},function(e){m=e.default},function(e){_=e.cat},null,function(e){v=e.default},function(e){h=e.audioEffect,b=e.buttonLock,y=e.watchUser},function(e){g=e.Card,w=e.PostCardRequestSchema,C=e.PostCardResponseSchema},function(e){I=e.reflect},null,function(e){R=e.create},null,null,null,null,null,null,function(e){A=e.default},function(e){L=e.GameEventConstant}],execute:function(){var E,M,D,O,j,T,H,U,N,P,S,z;r._RF.push({},"79b1bo3W1JOBIXRe2xOBLc5","ActiveRemoveMine",void 0);var B=s.ccclass,x=s.property;e("ActiveRemoveMine",(E=B("ActiveRemoveMine"),M=x({type:a,tooltip:"逃生按钮"}),D=x({type:u,tooltip:"逃生剩余时间"}),O=x({type:c,tooltip:"警告节点"}),j=y(),T=h(),H=b(),E((P=t((N=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),s=0;s<n;s++)r[s]=arguments[s];return t=e.call.apply(e,[this].concat(r))||this,o(t,"btn_run",P,i(t)),o(t,"run_time",S,i(t)),o(t,"warning_node",z,i(t)),t.timeId=void 0,t.time_count=3,t.props={is_has_remove:!1,timeout_sec:0},t}n(t,e);var r=t.prototype;return r.onLoad=function(){this.btn_run.node.on(a.EventType.CLICK,this.onRunHandler,this)},r.onEventListener=function(){_.event.on(L.CLOSE_COMMON_UI,this.onCloseCommonUIHandler,this)},r.onAutoObserver=function(){var e=this;this.addAutorun([function(){e.props.is_has_remove||(_.util.nodeUtils.setNodeGray(e.btn_run),e.run_time.outlineColor=new l.Color("#585858")),window.ccLog("ActiveRemoveMine按钮------------",e.props.is_has_remove&&!v.user.isAudience),e.btn_run.interactable=e.props.is_has_remove&&!v.user.isAudience}])},r.start=function(){var e=this,t=A(this.props.timeout_sec),n=Date.now();window.ccLog("时间倒计时",t,n-t);var o=function(){var n=Date.now();if(e.time_count-=1,n>=t)return clearInterval(e.timeId),void(e.run_time.string="0s");e.run_time.string=e.time_count+"s"};this.timeId=setInterval(o,1e3),o(),this.showWarningTween(),_.event.dispatchEvent(L.CLOSE_TEAM_DROP_MINE)},r.onRunHandler=function(){var e=this;_.ws.Request("PostCard",I(w,R(w,{card:g.REMOVE_LANDMINE})),C).then((function(){e.close()}))},r.showWarningTween=function(){p(this.warning_node.getComponent(d)).to(.1,{opacity:0}).to(.1,{opacity:255}).union().repeatForever().start()},r.hideWarningTween=function(){f.stopAllByTarget(this.warning_node)},r.onDestroy=function(){this.hideWarningTween(),clearInterval(this.timeId)},r.onGamePostBroadcastHandler=function(e){e.post==g.LANDMINE&&this.onCloseCommonUIHandler()},r.onCloseCommonUIHandler=function(){this.close()},r.close=function(){_.gui.closeUI(this,{isMotion:!1})},t}(m)).prototype,"btn_run",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),S=t(N.prototype,"run_time",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),z=t(N.prototype,"warning_node",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),t(N.prototype,"onRunHandler",[j,T,H],Object.getOwnPropertyDescriptor(N.prototype,"onRunHandler"),N.prototype),U=N))||U));r._RF.pop()}}}));

System.register("chunks:///_virtual/ActiveScout.ts",["./rollupPluginModLoBabelHelpers.js","cc","./AudioSourceCommonActionUI.ts","./msg_pb.ts","./Decorator.ts","./index21.ts","./reflect.js","./descriptors.js","./create.js","./descriptor_pb.js","./binary-encoding.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./index23.ts","./LightCardItem.ts","./index40.ts","./TimeCount.ts","./Shark.ts","./Tooth.ts","./JSBridge.ts","./GameEventConstant.b.ts","./AudioEffectConstant.b.ts"],(function(t){var e,n,o,i,a,r,c,l,s,u,p,d,f,_,h,g,m,b,S,T,y,v,E,O,A,C,D,w,k,U,R,I,L,z;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,o=t.initializerDefineProperty,i=t.assertThisInitialized},function(t){a=t.cclegacy,r=t._decorator,c=t.Prefab,l=t.Node,s=t.Label,u=t.v3,p=t.Button,d=t.isValid,f=t.instantiate,_=t.Vec3,h=t.tween},function(t){g=t.AudioSourceCommonActionUI},function(t){m=t.DeskState,b=t.SelectDiagnoseRequestSchema,S=t.SelectDiagnoseResponseSchema,T=t.Card,y=t.CardState},function(t){v=t.audioEffect,E=t.watchUser},function(t){O=t.cat},function(t){A=t.reflect},null,function(t){C=t.create},null,null,null,null,null,null,function(t){D=t.default},function(t){w=t.LightCardItem},null,function(t){k=t.TimeCount},function(t){U=t.ZoomState},function(t){R=t.Tooth},function(t){I=t.JSBridgeClient},function(t){L=t.GameEventConstant},function(t){z=t.AudioEffectConstant}],execute:function(){var H,j,B,P,N,x,K,G,M,V,F,W,Z,q,J,Q,X,Y,$,tt,et,nt,ot;a._RF.push({},"f8540FmrDVHiIBAF53gPEpM","ActiveScout",void 0);var it=r.ccclass,at=r.property,rt=function(t){return t[t.TO_BE_SCOUT=0]="TO_BE_SCOUT",t[t.SCOUTED=1]="SCOUTED",t}(rt||{});t("ActiveScout",(H=it("ActiveScout"),j=at({type:c,tooltip:"卡牌预制体"}),B=at({type:l,tooltip:"标题"}),P=at({type:l,tooltip:"鲨鱼模型节点"}),N=at({type:l,tooltip:"确定按钮"}),x=at({type:s,tooltip:"确定倒计时"}),K=at({type:l,tooltip:"诊断按钮"}),G=at({type:s,tooltip:"选择诊断的牙齿数量"}),M=at({type:k,tooltip:"倒计时时间"}),V=at({type:l,tooltip:"主节点"}),F=E(),W=v(),H((J=e((q=function(t){function e(){for(var e,n=arguments.length,a=new Array(n),r=0;r<n;r++)a[r]=arguments[r];return e=t.call.apply(t,[this].concat(a))||this,o(e,"card_prefab",J,i(e)),o(e,"title",Q,i(e)),o(e,"shark",X,i(e)),o(e,"btn_ok",Y,i(e)),o(e,"ok_time_out",$,i(e)),o(e,"btn_scout",tt,i(e)),o(e,"scout_count",et,i(e)),o(e,"time_count",nt,i(e)),o(e,"main",ot,i(e)),e.pos=[[u(0,444,0)],[u(-145,444,0),u(145,444,0)]],e.props={},e.data={stage:rt.TO_BE_SCOUT},e}n(e,t);var a=e.prototype;return a.onLoad=function(){I.disableTouch(!0),this.btn_scout.on(p.EventType.CLICK,this.onScoutHandler,this),this.btn_ok.on(p.EventType.CLICK,this.onOkHandler,this)},a.onEventListener=function(){var t=this;O.event.on("EVENT_SELECT_DIAGNOSE",this.onScoutAction,this).on(L.DRAW_SCOUT_TIME_OUT,(function(e){var n=e.time;t.ok_time_out.string=n+"s"}),this)},a.start=function(){D.game.selected_tooth=[],O.event.dispatchEvent(L.SHARK_ZOOM,U.NEAR),D.game.click_tooth_type=2,D.game.is_allow_click_tooth=!0,this.playAudio()},a.onAutoObserver=function(){var t=this;this.addAutorun([function(){t.time_count.node.active=t.btn_scout.active=t.title.active=t.data.stage===rt.TO_BE_SCOUT},function(){var e=D.game.getUnusedTeeth.length<2?D.game.getUnusedTeeth.length:2;t.scout_count.string=D.game.selected_tooth.length+"/"+e},function(){D.game.roomData.state,m.DESK_DIAGNOSE_RESULT}]),this.addReaction((function(){return D.game.roomData.state}),(function(e){e===m.DESK_STATE_DRAW_OR_POST&&(window.ccLog("关闭 侦查",e,e===m.DESK_STATE_DRAW_OR_POST),t.close())}))},a.onDestroy=function(){I.disableTouch(!1),D.game.click_tooth_type=0},a.close=function(){d(this,!0)&&(O.event.dispatchEvent(L.SHARK_ZOOM,U.FAR),O.gui.closeUI(this))},a.onScoutHandler=function(){var t=D.game.getUnusedTeeth.length<2?D.game.getUnusedTeeth.length:2;if(D.game.selected_tooth.length===t){var e=[];D.game.selected_tooth.forEach((function(t){var n;e.push(null==(n=t.getComponent(R))?void 0:n.props.index)})),O.ws.Request("SelectDiagnose",A(b,C(b,{cardIndexs:e})),S)}else O.gui.showToast({title:"请选择牙齿"})},a.onScoutAction=function(t){var e=this;D.game.is_allow_click_tooth=!1,this.data.stage=rt.SCOUTED;var n=[],o=t.cardValues.length,i=this.pos[o-1];t.cardValues.forEach((function(o,a){var r=f(e.card_prefab);r.getComponent(w).setNodeAndChildrenLayer(e.node.layer).setScale(_.ZERO).setPosition(e.shark.position).addToParent(e.main,{props:{card:o}});n.push((function(){return new Promise((function(e){var n=o===T.LANDMINE?y.BAD:y.GOOD;O.event.dispatchEvent(L.TAG_TOOTH_STATE,{index:t.cardIndexs[a],state:n}),h(r).to(.2,{position:i[a],scale:_.ONE}).call((function(){O.audio.playEffect(z.SHOW),e()})).start()}))}))}));!function t(){var e=n.shift();e&&e().finally(t)}()},a.onOkHandler=function(){this.close()},e}(g)).prototype,"card_prefab",[j],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Q=e(q.prototype,"title",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),X=e(q.prototype,"shark",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Y=e(q.prototype,"btn_ok",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),$=e(q.prototype,"ok_time_out",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),tt=e(q.prototype,"btn_scout",[K],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),et=e(q.prototype,"scout_count",[G],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),nt=e(q.prototype,"time_count",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),ot=e(q.prototype,"main",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),e(q.prototype,"onScoutHandler",[F,W],Object.getOwnPropertyDescriptor(q.prototype,"onScoutHandler"),q.prototype),Z=q))||Z));a._RF.pop()}}}));

System.register("chunks:///_virtual/ArrayUtils.ts",["cc"],(function(t){var r;return{setters:[function(t){r=t.cclegacy}],execute:function(){r._RF.push({},"61222JjzVNOXbB3/shq6KyW","ArrayUtils",void 0);t("arraySortByField",(function(t,r,n){void 0===n&&(n=1),t.sort((function(t,c){return(t[r]-c[r])*n}))}));r._RF.pop()}}}));

System.register("chunks:///_virtual/Arrow.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts"],(function(o){var t,n,e,r,i,s,a;return{setters:[function(o){t=o.inheritsLoose},function(o){n=o.cclegacy,e=o._decorator,r=o.UITransform,i=o.misc,s=o.tween},function(o){a=o.BaseComponent}],execute:function(){var c;n._RF.push({},"436a189xp9CKLTq/1G2ZJ5Y","Arrow",void 0);var p=e.ccclass;e.property,o("Arrow",p("Arrow")(c=function(o){function n(){return o.apply(this,arguments)||this}return t(n,o),n.prototype.start=function(){var o=this,t=this.props,n=t.from,e=t.to,a=Math.atan2(e.worldPosition.y-n.worldPosition.y,e.worldPosition.x-n.worldPosition.x),c=n.parent.getComponent(r).convertToNodeSpaceAR(n.worldPosition),p=e.parent.getComponent(r).convertToNodeSpaceAR(e.worldPosition);this.node.setPosition(c),this.node.angle=i.radiansToDegrees(a),s(this.node).to(.5,{position:p}).delay(.5).call((function(){o.node.destroy()})).start()},n}(a))||c);n._RF.pop()}}}));

System.register("chunks:///_virtual/AudioEffect.ts",["./rollupPluginModLoBabelHelpers.js","cc","./CommonAudio.ts","./index40.ts","./index23.ts","./JSBridge.ts","./AudioEffectConstant.b.ts"],(function(t){var e,n,o,r,i,c,a,u,s,f;return{setters:[function(t){e=t.inheritsLoose,n=t.asyncToGenerator,o=t.regeneratorRuntime},function(t){r=t.cclegacy,i=t._decorator,c=t.AudioClip},function(t){a=t.CommonAudio},null,function(t){u=t.default},function(t){s=t.JSBridgeClient},function(t){f=t.AudioEffectConstant}],execute:function(){var l;r._RF.push({},"8642a58c2BOtrCxvbVMIx2p","AudioEffect",void 0);var d=i.ccclass;i.menu,t("AudioEffect",d("AudioEffect")(l=function(t){function r(){for(var e,n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return(e=t.call.apply(t,[this].concat(o))||this).effects=new Map,e}e(r,t);var i=r.prototype;return i.preloadAll=function(){var t=this;return window.ccLog("preload all audio effect"),Object.values(f).forEach((function(e){return t.load(e)})),this},i.load=function(t){var e=this;return window.ccLog("开始加载音效",t),new Promise((function(n,o){e.effects.has(t)&&n(e.effects.get(t)),e.cat.res.load(t,c,(function(r,i){if(r)return window.ccLog("加载音效失败",t),o(r);e.effects.set(t,i),window.ccLog("加载音效成功",t),n(i)}))}))},i.release=function(){for(var t in this.effects)this.cat.res.release(t);this.effects.clear()},i.loadAndPlay=function(){var t=n(o().mark((function t(e){var n;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.load(e);case 2:n=t.sent,u.global.isSupportNativePlayAudio?s.playAudio(n.nativeUrl):this.playOneShot(n,this.volume);case 4:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),r}(a))||l);r._RF.pop()}}}));

System.register("chunks:///_virtual/AudioEffectConstant.b.ts",["cc"],(function(a){var S;return{setters:[function(a){S=a.cclegacy}],execute:function(){S._RF.push({},"926bbAk65ZJB5LHxC37bAkU","AudioEffectConstant.b",void 0);a("AudioEffectConstant",function(a){return a.BGM="audio/Shark_Bgm",a.SILENT="audio/silent",a.CLICK="audio/Shark_Click",a.DEAL_CARD="audio/Shark_DealCards",a.DRAW_POST_CARD="audio/Shark_GiveCard",a.PRESS_TOOTH="audio/Shark_PressTooth",a.SHOW="audio/Shark_Show",a.SCOUT="audio/Shark_Inspect",a.REQUEST="audio/Shark_Request",a.REQUESTED="audio/Shark_Requested",a.REQUEST_FINISHED="audio/Shark_GiveCard",a.THROW_BOMB="audio/Shark_ThrowBomb",a.THROWN_BOMB="audio/Shark_ThrownBomb",a.THROW_SUCCESS="audio/Shark_ThrowSuccess",a.RESISTANCE="audio/Shark_Resistance",a.CURSE="audio/Shark_Curse",a.BLESSING="audio/Shark_Blessing",a.CLAMP="audio/Shark_Clamp_2",a.FIRE_BOMB="audio/Shark_FireBomb",a.BITE="audio/Shark_Bite",a.RUN="audio/Shark_Escape",a.PASS="audio/Shark_Pass",a.PASS_DOUBLE="audio/Shark_Pass",a.EXCHANGE="audio/Shark_Change",a.EXCHANGE_SELECTED="audio/Shark_BeChanged",a.EXCHANGE_FINISH="audio/Shark_Changed",a.TURN_ROUND="audio/Shark_TurnRound",a.YOUR_TURN="audio/Shark_YourTurn",a.OUT="audio/Shark_PlayOut",a.GAME_OVER="audio/Shark_GameOver",a}({}));S._RF.pop()}}}));

System.register("chunks:///_virtual/AudioEffectConstant.ts",["cc","./AudioEffectConstant.b.ts"],(function(t){var n;return{setters:[function(t){n=t.cclegacy},function(n){t("AudioEffectConstant",n.AudioEffectConstant)}],execute:function(){n._RF.push({},"d2812JWL6lK4b7ODwBZ63jL","AudioEffectConstant",void 0),n._RF.pop()}}}));

System.register("chunks:///_virtual/AudioEventConstant.ts",["cc"],(function(t){var n;return{setters:[function(t){n=t.cclegacy}],execute:function(){n._RF.push({},"4dd4fFn3itH5rPGafM9ch8T","AudioEventConstant",void 0);t("AudioEventConstant",function(t){return t.MUSIC_ON="AudioEventConstant/MUSIC_ON",t.MUSIC_OFF="AudioEventConstant/MUSIC_OFF",t.EFFECT_ON="AudioEventConstant/EFFECT_ON",t.EFFECT_OFF="AudioEventConstant/EFFECT_OFF",t.PAUSE_AUDIO="AudioEventConstant/PAUSE_AUDIO",t.RESUME_AUDIO="AudioEventConstant/RESUME_AUDIO",t}({}));n._RF.pop()}}}));

System.register("chunks:///_virtual/AudioMusic.ts",["./rollupPluginModLoBabelHelpers.js","cc","./CommonAudio.ts"],(function(t){var i,s,e,r,n,o,u;return{setters:[function(t){i=t.inheritsLoose,s=t.createClass},function(t){e=t.cclegacy,r=t._decorator,n=t.AudioClip,o=t.error},function(t){u=t.CommonAudio}],execute:function(){var l;e._RF.push({},"d7090+iptVJ2b0aiJnw/lQR","AudioMusic",void 0);var a=r.ccclass;r.menu,t("AudioMusic",a("AudioMusic")(l=function(t){function e(){for(var i,s=arguments.length,e=new Array(s),r=0;r<s;r++)e[r]=arguments[r];return(i=t.call.apply(t,[this].concat(e))||this).onComplete=null,i._progress=0,i._url=null,i._isPlay=!1,i}i(e,t);var r=e.prototype;return r.load=function(t,i){var s=this;this.cat.res.load(t,n,(function(e,r){e&&o(e),s.playing&&(s._isPlay=!1,s.stop(),s.cat.res.release(s._url)),s.playOnAwake=!1,s.enabled=!0,s.clip=r,s._url=t,i&&i()}))},r.update=function(t){this.currentTime>0&&(this._isPlay=!0),this._isPlay&&0==this.playing&&(this._isPlay=!1,this.enabled=!1,this.onComplete&&this.onComplete())},r.release=function(){this._url&&(this.cat.res.release(this._url),this._url=null)},s(e,[{key:"progress",get:function(){return this.duration>0&&(this._progress=this.currentTime/this.duration),this._progress},set:function(t){this._progress=t,this.currentTime=t*this.duration}}]),e}(u))||l);e._RF.pop()}}}));

System.register("chunks:///_virtual/AudioSourceBaseComponent.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index40.ts","./index23.ts","./BaseComponent.ts","./index21.ts","./JSBridge.ts","./AudioEventConstant.ts"],(function(o){var t,i,e,n,u,r,a,l,c,p,s,d,f,h;return{setters:[function(o){t=o.applyDecoratedDescriptor,i=o.inheritsLoose,e=o.initializerDefineProperty,n=o.assertThisInitialized},function(o){u=o.cclegacy,r=o._decorator,a=o.Enum,l=o.AudioClip,c=o.AudioSource},null,function(o){p=o.default},function(o){s=o.BaseComponent},function(o){d=o.cat},function(o){f=o.JSBridgeClient},function(o){h=o.AudioEventConstant}],execute:function(){var y,S,E,v,m,b,A,F,C,M,w;u._RF.push({},"9863ak3GPdP5ovIDlwLYdNz","AudioSourceBaseComponent",void 0);r.ccclass;var _=r.property,g=o("AudioTypeEnum",function(o){return o[o.EFFECT=0]="EFFECT",o[o.BGM=1]="BGM",o}({}));o("AudioSourceBaseComponent",(y=_({tooltip:"类型:\n        EFFECT 音效\n        BGM    音乐",type:a(g)}),S=_({tooltip:"音源",type:l}),E=_({tooltip:"循环"}),v=_({tooltip:"音量"}),m=_({tooltip:"是否启用自动播放"}),A=t((b=function(o){function t(){for(var t,i=arguments.length,u=new Array(i),r=0;r<i;r++)u[r]=arguments[r];return t=o.call.apply(o,[this].concat(u))||this,e(t,"type",A,n(t)),e(t,"clip",F,n(t)),e(t,"loop",C,n(t)),e(t,"volume",M,n(t)),e(t,"playOnAwake",w,n(t)),t.audioSource=new c,t}i(t,o);var u=t.prototype;return u.onEnable=function(){o.prototype.onEnable.call(this),this.clip&&(this.audioSource.clip=this.clip),this.audioSource.loop=this.loop,this.audioSource.volume=this.volume,this.audioSource.playOnAwake=this.playOnAwake,d.event.on(h.EFFECT_ON,this.onPlayEffectHandler,this).on(h.EFFECT_OFF,this.onStopEffectHandler,this).on(h.MUSIC_ON,this.onPlayMusicHandler,this).on(h.MUSIC_OFF,this.onStopMusicHandler,this)},u.__preload=function(){o.prototype.__preload.call(this);var t=d.audio,i=t.volumeEffect,e=t.volumeMusic;this.audioSource.volume=this.type===g.BGM?e:i},u.start=function(){},u.stopAudio=function(){this.audioSource.stop()},u.playAudio=function(){var o=d.audio,t=o.switchEffect,i=o.switchMusic;!this.audioSource.playing&&(this.type===g.BGM?i:t)&&(p.global.isSupportNativePlayAudio&&this.audioSource.clip?f.playAudio(this.audioSource.clip.nativeUrl):this.audioSource.play())},u.onPlayEffectHandler=function(){},u.onStopEffectHandler=function(){this.stopAudio()},u.onPlayMusicHandler=function(){},u.onStopMusicHandler=function(){this.stopAudio()},u._onPreDestroy=function(){this.onStopMusicHandler(),o.prototype._onPreDestroy.call(this)},t}(s)).prototype,"type",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return g.EFFECT}}),F=t(b.prototype,"clip",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=t(b.prototype,"loop",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),M=t(b.prototype,"volume",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1}}),w=t(b.prototype,"playOnAwake",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),b));u._RF.pop()}}}));

System.register("chunks:///_virtual/AudioSourceCommonActionUI.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts","./index40.ts","./AudioSourceUILayer.ts","./GameEventConstant.b.ts"],(function(o){var n,t,e,i,c,u,r;return{setters:[function(o){n=o.inheritsLoose},function(o){t=o.cclegacy,e=o._decorator,i=o.isValid},function(o){c=o.cat},null,function(o){u=o.default},function(o){r=o.GameEventConstant}],execute:function(){var s;t._RF.push({},"5e0560LxUpCDJqTgAjY7UXY","AudioSourceCommonActionUI",void 0);var a=e.ccclass;e.property,o("AudioSourceCommonActionUI",a("AudioSourceCommonActionUI")(s=function(o){function t(){return o.apply(this,arguments)||this}n(t,o);var e=t.prototype;return e.start=function(){c.event.dispatchEvent(r.CLOSE_TEAM_DROP_MINE)},e.onUpdateStateHandler=function(){window.ccLog("UPDATE_STATE"),this.onCloseCommonUIHandler()},e.onCloseCommonUIHandler=function(){i(this)&&(window.ccLog("onCloseCommonUIHandler--\x3e>",this.name),c.gui.closeUI(this,{isMotion:!1}))},t}(u))||s);t._RF.pop()}}}));

System.register("chunks:///_virtual/AudioSourceUILayer.ts",["./rollupPluginModLoBabelHelpers.js","cc","./AudioSourceBaseComponent.ts","./index21.ts","./index40.ts","./UILayer.ts","./index23.ts","./JSBridge.ts","./AudioEventConstant.ts"],(function(t){var o,i,e,n,u,r,l,a,c,p,s,d,f,y,h;return{setters:[function(t){o=t.applyDecoratedDescriptor,i=t.inheritsLoose,e=t.initializerDefineProperty,n=t.assertThisInitialized},function(t){u=t.cclegacy,r=t._decorator,l=t.Enum,a=t.AudioClip,c=t.AudioSource},function(t){p=t.AudioTypeEnum},function(t){s=t.cat},null,function(t){d=t.default},function(t){f=t.default},function(t){y=t.JSBridgeClient},function(t){h=t.AudioEventConstant}],execute:function(){var S,v,b,E,A,m,w,F,_,g,M;u._RF.push({},"1f6406vxTROb4tjx8PupwpC","AudioSourceUILayer",void 0);r.ccclass;var C=r.property;t("default",(S=C({tooltip:"类型:\n        EFFECT 音效\n        BGM    音乐",type:l(p)}),v=C({tooltip:"音源",type:a}),b=C({tooltip:"循环"}),E=C({tooltip:"音量"}),A=C({tooltip:"是否启用自动播放"}),w=o((m=function(t){function o(){for(var o,i=arguments.length,u=new Array(i),r=0;r<i;r++)u[r]=arguments[r];return o=t.call.apply(t,[this].concat(u))||this,e(o,"type",w,n(o)),e(o,"clip",F,n(o)),e(o,"loop",_,n(o)),e(o,"volume",g,n(o)),e(o,"playOnAwake",M,n(o)),o.audioSource=new c,o}i(o,t);var u=o.prototype;return u.onEnable=function(){t.prototype.onEnable.call(this),s.event.on(h.EFFECT_ON,this.onPlayEffectHandler,this).on(h.EFFECT_OFF,this.onStopEffectHandler,this).on(h.MUSIC_ON,this.onPlayMusicHandler,this).on(h.MUSIC_OFF,this.onStopMusicHandler,this)},u.__preload=function(){this.clip&&(this.audioSource.clip=this.clip),this.audioSource.loop=this.loop,this.audioSource.volume=this.volume,this.audioSource.playOnAwake=this.playOnAwake,t.prototype.__preload.call(this);var o=s.audio,i=o.volumeEffect,e=o.volumeMusic;this.audioSource.volume=this.type===p.BGM?e:i},u.stopAudio=function(){this.audioSource.stop()},u.playAudio=function(){var t=s.audio,o=t.switchEffect,i=t.switchMusic;!this.audioSource.playing&&(this.type===p.BGM?i:o)&&(f.global.isSupportNativePlayAudio&&this.audioSource.clip?y.playAudio(this.audioSource.clip.nativeUrl):this.audioSource.play())},u.onPlayEffectHandler=function(){},u.onStopEffectHandler=function(){this.stopAudio()},u.onPlayMusicHandler=function(){},u.onStopMusicHandler=function(){this.stopAudio()},u._onPreDestroy=function(){this.onStopMusicHandler(),t.prototype._onPreDestroy.call(this)},o}(d)).prototype,"type",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return p.EFFECT}}),F=o(m.prototype,"clip",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=o(m.prototype,"loop",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),g=o(m.prototype,"volume",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1}}),M=o(m.prototype,"playOnAwake",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),m));u._RF.pop()}}}));

System.register("chunks:///_virtual/authorization_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./enum.js","./file.js","./message.js","./any_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js"],(function(n){var i,t,o,c,u;return{setters:[function(n){i=n.cclegacy},null,null,null,function(n){t=n.enumDesc},function(n){o=n.fileDesc},function(n){c=n.messageDesc},function(n){u=n.file_google_protobuf_any},null,null,null],execute:function(){i._RF.push({},"d0badfwTCBLzIoWa7qDcEQ0","authorization_pb",void 0);var R=n("file_authorization",o("ChNhdXRob3JpemF0aW9uLnByb3RvEgZwcm90b3Mi3QEKGUF1dGhvcml6YXRpb25Ob3RpZmljYXRpb24SOAoGc3RhdHVzGAEgASgOMigucHJvdG9zLkF1dGhvcml6YXRpb25Ob3RpZmljYXRpb24uU3RhdHVzEiIKBGRhdGEYAiABKAsyFC5nb29nbGUucHJvdG9idWYuQW55ImIKBlN0YXR1cxILCgdSRVNFUlZFEAASCwoHU1VDQ0VTUxABEgsKB0VYUElSRUQQAhIMCghLSUNLX09VVBADEgsKB0lOVkFMSUQQBBINCglERVNUUk9ZRUQQBRIHCgNCQU4QByKhAQoUQXV0aG9yaXphdGlvbkJhbkRhdGESDgoGc3RhdHVzGAEgASgFEhAKCHRlbXBsYXRlGAIgASgJEjgKBnZhbHVlcxgDIAMoCzIoLnByb3Rvcy5BdXRob3JpemF0aW9uQmFuRGF0YS5WYWx1ZXNFbnRyeRotCgtWYWx1ZXNFbnRyeRILCgNrZXkYASABKAkSDQoFdmFsdWUYAiABKAk6AjgBQjAKJGNvbS5zdG50cy5jbG91ZC5zdWlsZXlvby5lY2hvLnByb3Rvc1oILi9wcm90b3NiBnByb3RvMw",[u]));n("AuthorizationNotificationSchema",c(R,0)),n("AuthorizationNotification_Status",function(n){return n[n.RESERVE=0]="RESERVE",n[n.SUCCESS=1]="SUCCESS",n[n.EXPIRED=2]="EXPIRED",n[n.KICK_OUT=3]="KICK_OUT",n[n.INVALID=4]="INVALID",n[n.DESTROYED=5]="DESTROYED",n[n.BAN=7]="BAN",n}({})),n("AuthorizationNotification_StatusSchema",t(R,0,0)),n("AuthorizationBanDataSchema",c(R,1));i._RF.pop()}}}));

System.register("chunks:///_virtual/BaseAPI.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index.js","./SuileyooSign.ts"],(function(e){var t,r,n,s,a;return{setters:[function(e){t=e.asyncToGenerator,r=e.regeneratorRuntime},function(e){n=e.cclegacy},function(e){s=e.default},function(e){a=e.attachSign}],execute:function(){n._RF.push({},"052c49WksVHKJIB/TWbF6sL","BaseAPI",void 0);e("BaseAPI",function(){function e(e){var n=this;this.cat=void 0,this.base=void 0,this.cat=e,this.base=s.create({prefixUrl:this.cat.env.http_base_url,retry:{limit:10,backoffLimit:3e3},hooks:{beforeRequest:[t(r().mark((function e(t){var n,s;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.json();case 2:return n=e.sent,s=a(null!=n?n:{}),t.headers.set("Content-type","application/x-www-form-urlencoded"),window.ccLog("%c HTTP请求地址 %c "+t.method+" %c "+t.url,"background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff","background:#41b883 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff","background:transparent"),e.abrupt("return",new Request(t,{body:new URLSearchParams(s)}));case 7:case"end":return e.stop()}}),e)})))],afterResponse:[t(r().mark((function e(t,s,a){var o;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(window.ccLog(t,s,a),!a.ok){e.next=6;break}return e.next=4,a.json();case 4:0!==(o=e.sent).code&&null!=o&&o.message&&n.cat.gui.showToast({title:""+(null==o?void 0:o.message)});case 6:case"end":return e.stop()}}),e)})))],beforeError:[function(e){var t,r=e.response;r&&r.body&&(window.ccLog("err",r),n.cat.gui.showToast({title:"REQUEST ERROR:"+r.status+(null!=(t=r.statusText)?t:"")}));return e}]}})}return e.prototype.api=function(){var e=t(r().mark((function e(t,n){var s;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.base.extend(n)(t).json();case 2:return s=e.sent,e.abrupt("return",s.data);case 4:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}(),e}());n._RF.pop()}}}));

System.register("chunks:///_virtual/BaseComponent.ts",["./rollupPluginModLoBabelHelpers.js","cc","./bundle.js","./index21.ts"],(function(t){var n,o,e,s,i,r,a,u,c,h,d,p;return{setters:[function(t){n=t.inheritsLoose,o=t.assertThisInitialized},function(t){e=t.cclegacy,s=t.Prefab,i=t.instantiate,r=t.Component,a=t.isValid},function(t){u=t.autorun,c=t.reaction,h=t.makeObservable,d=t.observable},function(t){p=t.cat}],execute:function(){e._RF.push({},"dc1baUlDRpIHbyRE7vdBfoF","BaseComponent",void 0);t("BaseComponent",function(t){n(f,t);var e=f.prototype;function f(){var n;return(n=t.call(this)||this).props={},n.data={},n.autorunDisposers=[],n.reactionDisposers=[],n.hook={destroyed:function(){},started:function(){}},h(o(n),{props:d,data:d}),n}return e.initUI=function(){},e.__preload=function(){this.initUI(),this.onAutoObserver()},e.onAutoObserver=function(){},e.addAutorun=function(t){var n=this;return(Array.isArray(t)?t:[t]).forEach((function(t){var o=u(t);n.autorunDisposers.push(o)})),this},e.addReaction=function(t,n,o){var e=c(t,n,o);return this.reactionDisposers.push(e),this},e._onPreDestroy=function(){this.autorunDisposers.forEach((function(t){t()})),this.reactionDisposers.forEach((function(t){t()})),this.autorunDisposers=[],this.reactionDisposers=[],t.prototype._onPreDestroy.call(this)},e.onDisable=function(){this.onHide(),p.event.deleteEventByComponent(this),this.removeListener()},e.onEnable=function(){this.onShow(),this.addListener(),this.onEventListener()},e.onEventListener=function(){},e.addListener=function(){},e.removeListener=function(){},e.addToParent=function(t,n){var o=t instanceof s?i(t):t instanceof r?t.node:t;return this.setOptions(n),o.addChild(this.node),this},e.setOptions=function(t){if(t)for(var n in t)switch(n){case"hook":t.hook&&(this.hook=t.hook);break;case"props":null!==t.props&&"object"==typeof t.props&&(this.props=t.props);break;case"data":null!==t.data&&"object"==typeof t.data&&(this.data=t.data)}},e.setUpdateData=function(t){return this.data&&t&&Object.assign(this.data,t),this},e.setUpdateProps=function(t){return this.props?(t&&Object.assign(this.props,t),this):(window.ccLog("this.props属性为空"),this)},e.removeAndDestroy=function(){a(null==this?void 0:this.node)&&(this.node.removeFromParent(),this.node.destroy())},e.setPosition=function(t){return this.node.setPosition(t),this},e.setScale=function(t){return this.node.setScale(t),this},e.setAngle=function(t){return this.node.angle=t,this},e.setRotation=function(t){return this.node.setRotation(t),this},e.onShow=function(){},e.onHide=function(){},e.setNodeAndChildrenLayer=function(t){return p.util.nodeUtils.setNodeAndChildrenLayer(this.node,t),this},f}(r));e._RF.pop()}}}));

System.register("chunks:///_virtual/BaseHandCard.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index23.ts","./LightCardItem.ts","./index21.ts","./index40.ts","./GameEventConstant.b.ts"],(function(t){var n,e,i,r,a,o,d,s,c,l,u,p,h,f,C,m,g;return{setters:[function(t){n=t.applyDecoratedDescriptor,e=t.inheritsLoose,i=t.initializerDefineProperty,r=t.assertThisInitialized},function(t){a=t.cclegacy,o=t._decorator,d=t.Node,s=t.Prefab,c=t.UITransform,l=t.Layout,u=t.instantiate,p=t.error},function(t){h=t.BaseComponent},function(t){f=t.default},function(t){C=t.LightCardItem},function(t){m=t.cat},null,function(t){g=t.GameEventConstant}],execute:function(){var v,y,w,L,H,b,A;a._RF.push({},"e5940FDge1EM7VV/Y8ApTqW","BaseHandCard",void 0);var _=o.ccclass,D=o.property;t("BaseHandCard",(v=_("BaseHandCard"),y=D({type:d,tooltip:"卡牌列表"}),w=D({type:s,tooltip:"卡牌预制体"}),v((b=n((H=function(t){function n(){for(var n,e=arguments.length,a=new Array(e),o=0;o<e;o++)a[o]=arguments[o];return n=t.call.apply(t,[this].concat(a))||this,i(n,"list",b,r(n)),i(n,"card_prefab",A,r(n)),n.limit=0,n.gap=-80,n}e(n,t);var a=n.prototype;return a.onLoad=function(){this.limit=this.getComponent(c).width-200},a.onEventListener=function(){m.event.on(g.UPDATE_HANDCARD_LAYOUT,this.onUpdateHandCardLayout,this)},a.onAutoObserver=function(){var t=this;this.addReaction((function(){return f.game.roomData.cursor}),(function(n){t.updateColor(n)}),{fireImmediately:!0})},a.onUpdateHandCardLayout=function(){this.list.getComponent(l).updateLayout();var t=this.list.getComponent(c);if(window.ccLog("------更新手牌布局------",t.width>=this.limit,t.width,this.limit),t.width>=this.limit){var n,e=(null==(n=this.list.children[0])?void 0:n.getComponent(c).width)||0,i=this.list.children.length;this.gap=(this.limit-e*i)/i-1}return this.list.getComponent(l).spacingX=this.gap,this.updateColor(),this},a.updateColor=function(t){var n,e=void 0===t?null==(n=f.game.roomData)?void 0:n.cursor:t;window.ccLog("更新手牌颜色",e,f.user.userIndex),this.setHandCardGray(e!=f.user.userIndex)},a.setHandCardGray=function(t){void 0===t&&(t=!0),this.list.children.forEach((function(n){var e=n.getComponent(C);e.isHandCard&&e.setUpdateData({is_gray:t})}))},a.onceAddCard=function(t){var n=this;this.list.active=!0,t.forEach((function(t){u(n.card_prefab).getComponent(C).setHandCard(!0).addToParent(n.list,{props:{card:t}}),n.onUpdateHandCardLayout()}))},a.clearCard=function(){return this.list.removeAllChildren(),this.list.destroyAllChildren(),this},a.addCard=function(t){var n=null;return t instanceof d?(this.list.addChild(t),t.getComponent(C).setHandCard(!0),n=[t.getComponent(C).props.card],this.onUpdateHandCardLayout()):"number"==typeof t?(this.onceAddCard([t]),n=[t]):Array.isArray(t)?(this.onceAddCard(t),n=t):p("Invalid argument type"),n&&f.game.addCard(n),this},a.deleteCard=function(t){var n=this.list.children.find((function(n){return n.getComponent(C).props.card===t}));window.ccLog("删除手牌元素位置:",n),null==n||n.removeFromParent(),f.game.removeCard(t)},a.getCardSelected=function(){return this.list.children.find((function(t){var n=t.getComponent(C);return n.is_card_selected&&n.isValidCard}))},a.getCardById=function(t){return window.ccLog("获取卡牌根据id",this.list),this.list.children.filter((function(n){return window.ccLog("card",n.getComponent(C).props.card),n.getComponent(C).props.card==t}))},n}(h)).prototype,"list",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=n(H.prototype,"card_prefab",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=H))||L));a._RF.pop()}}}));

System.register("chunks:///_virtual/BaseLoading.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts","./UILayer.ts","./index20.ts","./index10.ts","./index4.ts","./index24.ts","./index47.ts","./SuileyooWS.ts","./index.b2.ts"],(function(n){var t,i,e,o,r,c,a,s,u,f,d,l;return{setters:[function(n){t=n.inheritsLoose,i=n.asyncToGenerator,e=n.regeneratorRuntime},function(n){o=n.cclegacy},function(n){r=n.cat},function(n){c=n.default},function(n){a=n.API},function(n){s=n.Environment},function(n){u=n.Platform},function(n){f=n.Tracking},null,function(n){d=n.createProxySuileyooSocket},function(n){l=n.createProxySocket}],execute:function(){o._RF.push({},"ca8df2FrvZBJJgKUbvgKKao","BaseLoading",void 0);n("default",function(n){function o(){return n.apply(this,arguments)||this}t(o,n);var c=o.prototype;return c.start=function(){var n=i(e().mark((function n(){return e().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,this.businessInit();case 2:this.init();case 3:case"end":return n.stop()}}),n,this)})));return function(){return n.apply(this,arguments)}}(),c.businessInit=function(){var n=i(e().mark((function n(){return e().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r.platform=new u(r).platform,window.ccLog("platform initializationed"),n.next=4,(new s).init();case 4:r.env=n.sent,window.ccLog("env initializationed"),r.tracking=new f(r),window.ccLog("tracking initializationed"),r.api=new a(r),window.ccLog("api initializationed"),r.ws=l(r),window.ccLog("ws initializationed"),r.suiLeYooSocket=d(r),window.ccLog("suiLeYooSocket initializationed");case 13:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}(),c.init=function(){},o}(c));o._RF.pop()}}}));

System.register("chunks:///_virtual/BaseManager.ts",["cc"],(function(e){var a;return{setters:[function(e){a=e.cclegacy}],execute:function(){a._RF.push({},"eca9a3cm7RCbYuqiWF7PB4m","BaseManager",void 0);e("BaseManager",(function(e){this.cat=void 0,this.cat=e}));a._RF.pop()}}}));

System.register("chunks:///_virtual/BasePlayerOutOver.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Decorator.ts","./index21.ts","./UILayer.ts"],(function(t){var e,n,r,o,i,a,c,u,l,s,p,f;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,r=t.initializerDefineProperty,o=t.assertThisInitialized},function(t){i=t.cclegacy,a=t._decorator,c=t.Node,u=t.Button},function(t){l=t.audioEffect,s=t.watchUser},function(t){p=t.cat},function(t){f=t.default}],execute:function(){var h,y,g,d,v,O,_;i._RF.push({},"1355c1frJZKxoIpkfIuW8dF","BasePlayerOutOver",void 0);var b=a.ccclass,w=a.property;t("BasePlayerOutOver",(h=b("BasePlayerOutOver"),y=w({type:c,tooltip:"观战按钮节点"}),g=s(),d=l(),h((_=e((O=function(t){function e(){for(var e,n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=t.call.apply(t,[this].concat(i))||this,r(e,"btn_continue_watching",_,o(e)),e}n(e,t);var i=e.prototype;return i.onLoad=function(){this.btn_continue_watching.on(u.EventType.CLICK,this.onContinueWatchingHandler,this)},i.onContinueWatchingHandler=function(){p.gui.closeUI(this),p.tracking.game.battleWatch()},e}(f)).prototype,"btn_continue_watching",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),e(O.prototype,"onContinueWatchingHandler",[g,d],Object.getOwnPropertyDescriptor(O.prototype,"onContinueWatchingHandler"),O.prototype),v=O))||v));i._RF.pop()}}}));

System.register("chunks:///_virtual/BasePool__.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var e,i,o,n,s,a;return{setters:[function(t){e=t.inheritsLoose,i=t.createClass},function(t){o=t.cclegacy,n=t.instantiate,s=t.Vec3,a=t.NodePool}],execute:function(){o._RF.push({},"66eb3T5bhdHVJX/h5XcO5IQ","BasePool__",void 0);t("BasePool",function(t){function o(e,i,o){var n;return void 0===i&&(i=10),void 0===o&&(o=5),(n=t.call(this)||this)._getCount=0,n._putCount=0,n._autoExpandTimes=0,n._prefab=void 0,n.expandSize=void 0,n._prefab=e,n.expandSize=o,n.preinit(i),n}e(o,t);var a=o.prototype;return a.preinit=function(t){for(var e=0;e<t;e++)this.put(n(this._prefab))},a.get=function(){var e;window.ccLog("获取对象池对象");var i=t.prototype.get.call(this);return 0!==this.size()&&i||(this.preinit(this.expandSize),this._autoExpandTimes++,i=t.prototype.get.call(this)),null==(e=i)||e.setScale(s.ONE),this._getCount++,i.emit("onEnable"),i},a.put=function(e){window.ccLog("回收对象池对象"),e.emit("onDisable"),t.prototype.put.call(this,e),this._putCount++},a.clear=function(){t.prototype.clear.call(this),this._getCount=0,this._putCount=0,this._autoExpandTimes=0},i(o,[{key:"totalCreated",get:function(){return this._getCount-this._putCount+this.size()}},{key:"usageStatistics",get:function(){return{currentAvailable:this.size(),totalCapacity:this.totalCreated,getOperations:this._getCount,putOperations:this._putCount,autoExpandTimes:this._autoExpandTimes}}}]),o}(a));o._RF.pop()}}}));

System.register("chunks:///_virtual/BasePool.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var t,n,r,s,o,i,a,u;return{setters:[function(e){t=e.asyncToGenerator,n=e.regeneratorRuntime,r=e.createClass},function(e){s=e.cclegacy,o=e.Node,i=e.instantiate,a=e.Vec3,u=e.director}],execute:function(){s._RF.push({},"d1aeev/pP9OCbCs9hBPOfYo","BasePool",void 0);e("BasePool",function(){function e(e,t){this.prefab=void 0,this.component=void 0,this.gener=void 0,this._caches=void 0,this._uses=void 0,this.prefab=e,this._caches=[],this._uses=[]}var s=e.prototype;return s.total=function(e,r,s){var o=this;return void 0===s&&(s=10),new Promise(t(n().mark((function t(i,a){var u,c,h;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(null==e||null==e||"number"==typeof e&&isNaN(e))){t.next=2;break}return t.abrupt("return",i());case 2:return null==(u=o.gener)||u.return(""),c=e instanceof Array?e.length:e,h=e instanceof Array?e:null,o.gener=o.getGenerator(c,o.task.bind(o),h,r),t.next=8,o.exeGenerator(o.gener,s);case 8:o.surplus(c),i();case 10:case"end":return t.stop()}}),t)}))))},s.push=function(e){e(this.get(),this._uses.length-1)},s.put=function(e){if(!e)return!1;var t=e,n=this._uses.findIndex((function(e){return e instanceof o?e.uuid==t.uuid:e.node.uuid==t.uuid}));if(-1==n)return!1;var r=this._uses.splice(n,1)[0];return r instanceof o?r.removeFromParent():r.node.removeFromParent(),this._caches.push(r),e.emit("onDisable"),!0},s.recycle=function(){try{for(;this._uses.length>0;){var e=this._uses.pop();e instanceof o?e.removeFromParent():e.node.removeFromParent(),this._caches.push(e)}}catch(e){console.error(e)}},s.clear=function(){for(var e=[].concat(this._uses,this._caches),t=0;t<e.length;t++){var n=e[t];n instanceof o?n.destroy():n.node.destroy()}this._caches=[],this._uses=[]},s.task=function(e,t,n){var r=(null==t?void 0:t[e])||e;n(this.get(),r,e)},s.surplus=function(e){try{for(;this._uses.length-e;){var t=this._uses.pop();t instanceof o?t.removeFromParent():t.node.removeFromParent(),this._caches.push(t)}}catch(e){console.error(e)}},s.get=function(){if(0==this._caches.length){var e=i(this.prefab);this._caches.push(e)}var t=this._caches.pop();return null==t||t.setScale(a.ONE),t.emit("onEnable"),this._uses.push(t),t},s.getGenerator=n().mark((function e(t,r){var s,o,i,a,u=arguments;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(s=u.length,o=new Array(s>2?s-2:0),i=2;i<s;i++)o[i-2]=u[i];a=0;case 2:if(!(a<t)){e.next=8;break}return e.next=5,r.apply(void 0,[a].concat(o));case 5:a++,e.next=2;break;case 8:case"end":return e.stop()}}),e)})),s.exeGenerator=function(e,t){return new Promise((function(n,r){var s=e;!function e(){for(var r=(new Date).getTime(),o=s.next();;o=s.next()){if(null==o||o.done)return void n(null);if((new Date).getTime()-r>t)return void setTimeout((function(){e()}),1e3*u.getDeltaTime())}}()}))},r(e,[{key:"caches",get:function(){return Object.assign([],this._caches)}},{key:"uses",get:function(){return Object.assign([],this._uses)}}]),e}());s._RF.pop()}}}));

System.register("chunks:///_virtual/BaseStore.ts",["cc"],(function(t){var e;return{setters:[function(t){e=t.cclegacy}],execute:function(){e._RF.push({},"d05642PAwBE872zLY7GEuum","BaseStore",void 0);t("BaseStore",(function(t){this.rootStore=void 0,this.rootStore=t}));e._RF.pop()}}}));

System.register("chunks:///_virtual/BaseTracking.ts",["cc"],(function(c){var n;return{setters:[function(c){n=c.cclegacy}],execute:function(){n._RF.push({},"cf826DVqKNHQoZabp6BbxXn","BaseTracking",void 0);c("BaseTracking",(function(c){this.tracking=void 0,this.tracking=c}));n._RF.pop()}}}));

System.register("chunks:///_virtual/BaseWebSocket.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseManager.ts"],(function(e){var n,t,o,s,u;return{setters:[function(e){n=e.inheritsLoose},function(e){t=e.cclegacy,o=e.game,s=e.Game},function(e){u=e.BaseManager}],execute:function(){t._RF.push({},"16409qhHuFAdqEo2ruXAQ23","BaseWebSocket",void 0);e("BaseWebSocket",function(e){function t(n){var t;return(t=e.call(this,n)||this).ins=null,t.destroy=function(){var e;window.ccLog("销毁ws"),null==(e=t.ins)||e.destroy(),t.ins=null},o.on(s.EVENT_CLOSE,(function(){t.destroy()})),t}return n(t,e),t}(u));t._RF.pop()}}}));

System.register("chunks:///_virtual/BlackMask.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts"],(function(e){var t,n,r,o,a,i,c,l;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,r=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){a=e.cclegacy,i=e._decorator,c=e.Node},function(e){l=e.BaseComponent}],execute:function(){var s,p,u,f,h;a._RF.push({},"4b0b8ljdchPc6C3oh1gDtNZ","BlackMask",void 0);var k=i.ccclass,y=i.property;e("BlackMask",(s=k("BlackMask"),p=y({type:c,tooltip:"动画节点"}),s((h=t((f=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return t=e.call.apply(e,[this].concat(a))||this,r(t,"tween",h,o(t)),t}return n(t,e),t}(l)).prototype,"tween",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),u=f))||u));a._RF.pop()}}}));

System.register("chunks:///_virtual/BlessCardActiom.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts","./index40.ts","./msg_pb.ts","./CommonAction.ts","./BaseComponent.ts","./AudioEffectConstant.b.ts"],(function(t){var n,o,e,s,c,i,r,a;return{setters:[function(t){n=t.inheritsLoose},function(t){o=t.cclegacy,e=t._decorator},function(t){s=t.cat},null,function(t){c=t.Card},function(t){i=t.CommonAction},function(t){r=t.BaseComponent},function(t){a=t.AudioEffectConstant}],execute:function(){var u;o._RF.push({},"f10ecY/R+NCzI2N4SBnsGzJ","BlessCardActiom",void 0);var f=e.ccclass;e.property,t("BlessCardAction",f("BlessCardAction")(u=function(t){function o(){return t.apply(this,arguments)||this}n(o,t);var e=o.prototype;return e.onEventListener=function(){s.event.on("EVENT_POST",this.onGamePostHandler,this)},e.onGamePostHandler=function(t){c.BLESSING===t.post&&(s.audio.playEffect(a.BLESSING),this.getComponent(i).showCardEffect(c.BLESSING))},o}(r))||u);o._RF.pop()}}}));

System.register("chunks:///_virtual/BlobUtils.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var t,n,r,o,a,i,c,u;return{setters:[function(e){t=e.asyncToGenerator,n=e.regeneratorRuntime},function(e){r=e.cclegacy,o=e.UITransform,a=e.math,i=e.Camera,c=e.RenderTexture,u=e.SpriteFrame}],execute:function(){function s(e){return p.apply(this,arguments)}function p(){return(p=t(n().mark((function e(t){var r,s,p,d,l,f,m,h,v;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.getComponent(o),s=t.getWorldPosition(),p=a.rect(s.x,s.y,r.width,r.height),d=p.width,l=p.height,(f=t.addComponent(i)).orthoHeight=p.height/2,f.projection=i.ProjectionType.ORTHO,f.near=0,f.far=1e3,f.visibility=33554432,(m=new c).reset({width:p.width,height:p.height}),f.targetTexture=m,(h=document.createElement("canvas")).width=d,h.height=l,v=h.getContext("2d"),e.abrupt("return",new Promise((function(e){var t=setTimeout((function(){clearTimeout(t);var n=m.readPixels();if(n){for(var r=4*d,o=0;o<l;o++){for(var a=l-1-o,i=v.createImageData(d,1),c=a*d*4,s=0;s<r;s++)i.data[s]=n[c+s];v.putImageData(i,0,o)}f.destroy();var p=new u;p.texture=m,p.flipUVY=!0,e({canvas:h,spriteFrame:p})}}),16)})));case 19:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e){return new Promise((function(t,n){e.toBlob((function(e){t(e)}),"image/png",1)}))}function l(){return(l=t(n().mark((function e(t,r){var o,a,i,c;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===r&&(r=""),e.next=3,s(t);case 3:return o=e.sent,a=o.canvas,i=o.spriteFrame,e.next=8,d(a);case 8:return(c=e.sent)&&f(c,r),e.abrupt("return",Promise.resolve(i));case 11:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e,t){var n=document.createElement("a");n.download=t,n.href=URL.createObjectURL(e),document.body.appendChild(n),n.click();var r=setTimeout((function(){URL.revokeObjectURL(n.href),document.body.removeChild(n),clearTimeout(r)}),200)}e({blobSaveLocation:f,blobtoDataURL:function(e,t){var n=new FileReader;n.onload=function(e){window.ccLog(e),t&&t(e.target.result)},n.readAsDataURL(e)},canvasToDataURL:function(e){return e.toDataURL("image/jpg",10)},captureNode:s,captureNodeToLocation:function(e,t){return l.apply(this,arguments)},downloadIamge:function(e){return new Promise((function(t,n){var r=new XMLHttpRequest;r.open("get",e,!0),r.responseType="blob",r.onreadystatechange=function(){if(r.readyState===XMLHttpRequest.DONE)if(200==r.status){var e=r.response;t(e)}else n()},r.send(null)}))}}),r._RF.push({},"d7581MVm85JGKRimWemhA3r","BlobUtils",void 0),r._RF.pop()}}}));

System.register("chunks:///_virtual/BreakHosting.b.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts"],(function(t){var n,o,e,r,i;return{setters:[function(t){n=t.inheritsLoose},function(t){o=t.cclegacy,e=t._decorator,r=t.Node},function(t){i=t.BaseComponent}],execute:function(){var s;o._RF.push({},"5f69ePEWMdGDYRLXaI2gzi7","BreakHosting.b",void 0);var a=e.ccclass;e.property,t("BreakHosting",a("BreakHosting")(s=function(t){function o(){return t.apply(this,arguments)||this}n(o,t);var e=o.prototype;return e.onLoad=function(){this.node.on(r.EventType.TOUCH_START,this.onTouchStartHandler,this)},e.onTouchStartHandler=function(t){},o}(i))||s);o._RF.pop()}}}));

System.register("chunks:///_virtual/Bridge.ts",["cc"],(function(a){var e;return{setters:[function(a){e=a.cclegacy}],execute:function(){var o,t,i,E;a("Bridge",void 0),e._RF.push({},"3b117O6W1tKPKaCnwtyo+FF","Bridge",void 0),t=o||(o=a("Bridge",{})),function(a){var e=function(a){return a.LOAD_API_REQUEST="loadApiDataInfo",a.LOAD_BRIDGE_REQUEST="loadBridgeAction",a.LOAD_COMMON_REQUEST="loadCommonAction",a.LOAD_CHATROOM_REQUEST="loadChatRoomAction",a}({});a.CocosBridge=e;var o=function(a){return a.CALLBACK_API_RESPONSE="callBackApiData",a.CALLBACK_BRIDGE_RESPONSE="callBackBridgeData",a.CALLBACK_COMMON_RESPONSE="callBackCommonData",a.CALLBACK_CHATROOM_RESPONSE="callBackChatRoomData",a}({});a.NativeBridge=o}(i||(i=t.MethodName||(t.MethodName={}))),function(a){var e=function(a){return a.CREATE="japi/yoo/partyGame/create",a.SEARCH="japi/yoo/partyGame/search",a.MATCHING="japi/yoo/partyGame/matching",a.DETAIL="japi/yoo/game/party/detail",a.ASSETS="japi/yoo/partyGame/user/assets",a.ROOM_INFO="japi/yoo/partyGame/roomInfo",a.VIEW_LIST="japi/yoo/partyGame/mike/viewers",a.USER_LIST="japi/yoo/partyGame/viewers",a.MIKE_LIST="japi/yoo/partyGame/mike/list",a.MIKE_UP="japi/yoo/partyGame/mike/down",a.USER_GAME_DATA="japi/yoo/partyGame/user/gdInfo",a.CHECK_USER_PROFILES="japi/social/fw/profiles/v4",a.READY="japi/yoo/partyGame/mike/ready",a.UNREADY="japi/yoo/partyGame/mike/unReady",a.EXIT="japi/yoo/v2/room/party/game/exit",a.MIKE_CHANGE_JOIN="japi/yoo/partyGame/mike/up",a.READY_DOWN="japi/yoo/partyGame/mike/upReady",a.BAN="japi/yoo/blacklist/kick",a.MIKE_LOCK="japi/yoo/partyGame/mike/lock",a.MIKE_UNLOCK="japi/yoo/partyGame/mike/unlock",a.START_GAME="japi/yoo/partyGame/start",a.ENTER_PARAM="japi/yoo/partyGame/enterParam",a.FLOAT_BALL="japi/yoo/game/floatBall",a.SETTLE_INFO="japi/yoo/v2/room/party/game/settle/info",a.CHECK_FOLLOWED="japi/social/fw/followedUids",a.FOLLOW="japi/social/fw/follow",a.GET_BANNER="japi/msg/config/getPushConfig",a.ITEM_USE="japi/yoo/partyGame/op/useGameEvent",a.ITEM_CONFIG="/japi/yoo/partyGame/op/gameEvents",a}({});a.APIName=e;var o=function(a){return a.JOIN_ARENA="japi/yoo/partyGame/arena/join",a.EXIT_ARENA="japi/yoo/partyGame/arena/exit",a.TIER_RANK="japi/yoo/partyGame/data/tierRank",a.LEVEL_RANK="japi/yoo/partyGame/data/levelRank",a.ROOM_LIST="japi/yoo/partyGame/data/rooms",a.ROOM_EXPERT_LIST="japi/yoo/partyGame/data/gameExpert",a.QUICK_JOIN="japi/yoo/partyGame/quickJoin",a.CANCEL_QUICK_JOIN="japi/yoo/partyGame/cancelQuickJoin",a.SEASON_SETTLE="japi/yoo/partyGame/data/seasonSettle",a.LIKE="japi/yoo/partyGame/user/userUpvote",a.LONG_LINK="japi/yoo/partyGame/longLink",a.JOIN_WORLD_CHANNEL="japi/yoo/partyGame/joinWorldChannel",a.MODIFY_JOIN_STATE="japi/yoo/partyGame/modifyJoinState",a.SIT_DOWN="japi/yoo/partyGame/mike/sitDown",a.GAME_STATE_INFO="japi/yoo/partyGame/user/gameStateInfo",a.TASK="japi/social/tsk/partyGame/list",a.TASK_RECEIVE="japi/social/tsk/partyGame/receive",a.InviteAccept="/japi/yoo/mike/invite/accept",a.InviteRefuse="/japi/yoo/mike/invite/refuse",a}({});a.APIV2Name=o;var t=function(a){return a.BACK="back",a.TASK="task",a.MALL="mall",a.AUDIENCE="audience",a.black="black",a.REPORT="report",a.SHARE="shareAction",a.BANNER_SPECIAL="bannerSpecial",a.INVITE="invite",a.GIFT="gift",a.AVATAR_CARD="avatarCard",a.MORE_DIALOG="moreDialog",a.RULE="rule",a.EXIT="exit",a.PRIVATE_CHAT="chat",a.SHARE_IMG="shareImg",a.CLICK_EVENT="clickEvent",a.HANG_UP_GAME="hangUpGame",a.MALL_BUY="mallBuy",a.CLOSE_DIALOG="closeDialog",a}({});a.BridgeName=t;var i=function(a){return a.RES_COMPLETE="resComplete",a.RES_GAME_ID="resGameId",a.VOICE_STATE="voiceState",a.MUSIC_STATE="musicState",a.MIC_STATE="micState",a.UPDATE_MIKE="updateMikeData",a.COPY_TEXT="copyText",a.RECHARGE="recharge",a.UPDATE_INPUTMODE="updateInputMode",a.SOUND_WAVE_STATE="soundWaveState",a.UPDATE_BALANCE="updateBalance",a.ROOM_OPEN_TIME="roomOpenTime",a.NETWORK_AVAILABLE="networkAvailable",a.SPEECH_TO_TEXT="speechToText",a.ASRCONNECT="asrConnect",a.ASROPEN="asrOpen",a}({});a.CommonName=i;var E=function(a){return a.LOGIN_CHAT_ROOM="loginChatRoom",a.LOGOUT_CHAT_ROOM="logoutChatRoom",a.SEND_MESSAGE="sendMessage",a.MESSAGE="message",a.CUSTOM_MESSAGE="customMessage",a.APPEND_MESSAGE="appendMessage",a.KICKOUT_ROOM="kickOutRoom",a}({});a.ChatRoomName=E}(E||(E=t.EventName||(t.EventName={}))),e._RF.pop()}}}));

System.register("chunks:///_virtual/CardInfo.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./IGame.ts","./IGame.b.ts"],(function(e){var t,n,o,r,a,i,c,l,s,d,f,u;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,o=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){a=e.cclegacy,i=e._decorator,c=e.Label,l=e.tween,s=e.Vec3,d=e.Tween},function(e){f=e.BaseComponent},null,function(e){u=e.CardInfoMap}],execute:function(){var p,h,y,C,I;a._RF.push({},"4db0bDWKVdMrae24w+aCYpX","CardInfo",void 0);var b=i.ccclass,v=i.property;e("CardInfo",(p=b("CardInfo"),h=v({type:c,tooltip:"卡牌信息文本节点"}),p((I=t((C=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return t=e.call.apply(e,[this].concat(a))||this,o(t,"card_info_label",I,r(t)),t}n(t,e);var a=t.prototype;return a.showCardInfo=function(e){var t=this;this.hideCardInfo();var n=e;u[n]&&(this.card_info_label.string=u[n],this.node.active=!0,l(this.node).to(.2,{scale:s.ONE}).delay(2).call((function(){t.hideCardInfo()})).start())},a.hideCardInfo=function(){d.stopAllByTarget(this.node),this.node.scale=s.ZERO,this.node.active=!1},t}(f)).prototype,"card_info_label",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=C))||y));a._RF.pop()}}}));

System.register("chunks:///_virtual/CardItem.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts"],(function(e){var t,n,r,i,o,a,l,c,s,u,p,d;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,r=e.initializerDefineProperty,i=e.assertThisInitialized,o=e.createClass},function(e){a=e.cclegacy,l=e._decorator,c=e.Node,s=e.CCFloat,u=e.Tween,p=e.tween},function(e){d=e.BaseComponent}],execute:function(){var f,y,h,m,v,w,C,b,g;a._RF.push({},"55046CuZoVNyq+uy4JaZYuw","CardItem",void 0);var T=l.ccclass,_=l.property;e("CardItem",(f=T("CardItem"),y=_({type:c,tooltip:"卡牌节点"}),h=_({type:s,tooltip:"选中时放大倍率"}),m=_({type:s,tooltip:"选中动画持续时间(s)"}),f((C=t((w=function(e){function t(){for(var t,n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return t=e.call.apply(e,[this].concat(o))||this,r(t,"card",C,i(t)),r(t,"selected_scale",b,i(t)),r(t,"selected_duration",g,i(t)),t.isTween=!0,t.isValidCard=!0,t.isHandCard=!1,t}n(t,e);var a=t.prototype;return a.onDestroy=function(){u.stopAllByTarget(this.node)},a.moveTween=function(e,t,n){var r=this;return void 0===n&&(n=this.node),new Promise((function(i){r.isTween=!0,p(n).to(e,t).call((function(){u.stopAllByTarget(n),r.isTween=!1,i()})).start()}))},o(t,[{key:"currentIndex",get:function(){var e;return null==(e=this.node.parent)||null==(e=e.children)?void 0:e.indexOf(this.node)}}]),t}(d)).prototype,"card",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=t(w.prototype,"selected_scale",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1.1}}),g=t(w.prototype,"selected_duration",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.1}}),v=w))||v));a._RF.pop()}}}));

System.register("chunks:///_virtual/CardManager.b.ts",["cc"],(function(){var e;return{setters:[function(r){e=r.cclegacy}],execute:function(){e._RF.push({},"60be8VjbQxIPrcaiqaVUZ2r","CardManager.b",void 0),e._RF.pop()}}}));

System.register("chunks:///_virtual/Chat.ts",["cc"],(function(_){var T;return{setters:[function(_){T=_.cclegacy}],execute:function(){T._RF.push({},"4b2c7/nK7NC5JaCCcBnt3kv","Chat",void 0);_("RoomChangeType",function(_){return _[_.TYPE_1000=1e3]="TYPE_1000",_[_.TYPE_1001=1001]="TYPE_1001",_[_.TYPE_1002=1002]="TYPE_1002",_[_.TYPE_1004=1004]="TYPE_1004",_[_.TYPE_1005=1005]="TYPE_1005",_[_.TYPE_1009=1009]="TYPE_1009",_[_.TYPE_1010=1010]="TYPE_1010",_[_.TYPE_1011=1011]="TYPE_1011",_[_.TYPE_1020=1020]="TYPE_1020",_[_.TYPE_1021=1021]="TYPE_1021",_[_.TYPE_2001=2001]="TYPE_2001",_[_.TYPE_2002=2002]="TYPE_2002",_[_.TYPE_2012=2012]="TYPE_2012",_[_.TYPE_2013=2013]="TYPE_2013",_[_.TYPE_2015=2015]="TYPE_2015",_[_.TYPE_2031=2031]="TYPE_2031",_[_.TYPE_2033=2033]="TYPE_2033",_[_.TYPE_2043=2043]="TYPE_2043",_}({})),_("ChatRoomMsgType",function(_){return _[_.TYPE_UN_KNOW=-1]="TYPE_UN_KNOW",_[_.TYPE_TEXT_MSG=0]="TYPE_TEXT_MSG",_[_.TYPE_IMAGE_MSG=1]="TYPE_IMAGE_MSG",_[_.TYPE_SYSTEM_MSG=2]="TYPE_SYSTEM_MSG",_[_.TYPE_GIFT_MSG=3]="TYPE_GIFT_MSG",_}({}));T._RF.pop()}}}));

System.register("chunks:///_virtual/Clamp.ts",["./rollupPluginModLoBabelHelpers.js","cc","./AudioSourceCommonActionUI.ts","./msg_pb.ts","./Decorator.ts","./index21.ts","./reflect.js","./descriptors.js","./create.js","./descriptor_pb.js","./binary-encoding.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./index23.ts","./index40.ts","./TimeCount.ts","./Shark.ts","./Tooth.ts","./JSBridge.ts","./index26.ts","./GameEventConstant.b.ts","./index.b.ts"],(function(t){var e,n,o,i,a,l,r,c,s,u,p,d,m,f,_,h,b,C,y,g,T,S,v,A,E,D,O,R;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,o=t.initializerDefineProperty,i=t.assertThisInitialized},function(t){a=t.cclegacy,l=t._decorator,r=t.Prefab,c=t.Node,s=t.v3,u=t.Button,p=t.isValid},function(t){d=t.AudioSourceCommonActionUI},function(t){m=t.DeskState,f=t.ClampCardRequestSchema,_=t.ClampCardResponseSchema,h=t.CardState},function(t){b=t.audioEffect,C=t.watchUser},function(t){y=t.cat},function(t){g=t.reflect},null,function(t){T=t.create},null,null,null,null,null,null,function(t){S=t.default},null,function(t){v=t.TimeCount},function(t){A=t.ZoomState},function(t){E=t.Tooth},function(t){D=t.JSBridgeClient},null,function(t){O=t.GameEventConstant},function(t){R=t.ClickToothType}],execute:function(){var w,j,I,k,x,z,L,P,U,B,H,K,M,G,N,F,J;a._RF.push({},"f7472V58lJKtJzeBnAdG+6Q","Clamp",void 0);var V=l.ccclass,W=l.property;t("Clamp",(w=V("Clamp"),j=W({type:r,tooltip:"卡牌预制体"}),I=W({type:c,tooltip:"标题"}),k=W({type:c,tooltip:"鲨鱼模型节点"}),x=W({type:c,tooltip:"确定拔牙按钮"}),z=W({type:v,tooltip:"倒计时时间"}),L=W({type:c,tooltip:"主节点"}),P=C(),U=b(),w((K=e((H=function(t){function e(){for(var e,n=arguments.length,a=new Array(n),l=0;l<n;l++)a[l]=arguments[l];return e=t.call.apply(t,[this].concat(a))||this,o(e,"card_prefab",K,i(e)),o(e,"title",M,i(e)),o(e,"shark",G,i(e)),o(e,"btn_confirm_clamp",N,i(e)),o(e,"time_count",F,i(e)),o(e,"main",J,i(e)),e.pos=[[s(0,444,0)],[s(-145,444,0),s(145,444,0)]],e.props={},e}n(e,t);var a=e.prototype;return a.onLoad=function(){D.disableTouch(!0),this.btn_confirm_clamp.on(u.EventType.CLICK,this.onClampHandler,this)},a.onEventListener=function(){y.event.on("EVENT_CLAMP_BROADCAST",this.onClampBroadcast,this).on(O.DRAW_SCOUT_TIME_OUT,(function(t){t.time}),this)},a.start=function(){S.game.selected_tooth=[],y.event.dispatchEvent(O.SHARK_ZOOM,A.NEAR),S.game.click_tooth_type=R.CLAMP,S.game.is_allow_click_tooth=!0,this.playAudio()},a.onAutoObserver=function(){var t=this;this.addAutorun([function(){S.game.roomData.state,m.DESK_DIAGNOSE_RESULT}]),this.addReaction((function(){return S.game.roomData.state}),(function(e){e===m.DESK_STATE_DRAW_OR_POST&&(window.ccLog("关闭 侦查",e,e===m.DESK_STATE_DRAW_OR_POST),t.close())}))},a.onDestroy=function(){D.disableTouch(!1),S.game.click_tooth_type=R.UNSPECIFIED},a.close=function(){p(this,!0)&&(y.event.dispatchEvent(O.SHARK_ZOOM,A.FAR),y.gui.closeUI(this))},a.onClampHandler=function(){if(1===S.game.selected_tooth.length){var t=S.game.selected_tooth.map((function(t){var e;return null==(e=t.getComponent(E))?void 0:e.props.index}))[0];y.ws.Request("ClampCard",g(f,T(f,{cardIndex:t})),_)}else y.gui.showToast({title:"请选择牙齿"})},a.onClampBroadcast=function(t){this.scheduleOnce((function(){var e=h.USED;y.event.dispatchEvent(O.TAG_TOOTH_STATE,{index:t.cardIndex,state:e})}),.4),S.game.is_allow_click_tooth=!1,this.close()},e}(d)).prototype,"card_prefab",[j],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),M=e(H.prototype,"title",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),G=e(H.prototype,"shark",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),N=e(H.prototype,"btn_confirm_clamp",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),F=e(H.prototype,"time_count",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),J=e(H.prototype,"main",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),e(H.prototype,"onClampHandler",[P,U],Object.getOwnPropertyDescriptor(H.prototype,"onClampHandler"),H.prototype),B=H))||B));a._RF.pop()}}}));

System.register("chunks:///_virtual/ClampCardAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index21.ts","./index40.ts","./index23.ts","./msg_pb.ts","./CommonAction.ts","./AudioEffectConstant.b.ts"],(function(t){var n,e,o,a,i,r,c,s,u,l,f,p,d,m,C;return{setters:[function(t){n=t.applyDecoratedDescriptor,e=t.inheritsLoose,o=t.initializerDefineProperty,a=t.assertThisInitialized},function(t){i=t.cclegacy,r=t._decorator,c=t.Prefab,s=t.instantiate},function(t){u=t.BaseComponent},function(t){l=t.cat},null,function(t){f=t.default},function(t){p=t.DeskState,d=t.Card},function(t){m=t.CommonAction},function(t){C=t.AudioEffectConstant}],execute:function(){var _,A,h,v,g;i._RF.push({},"5388c1KVsFG7LLpTJWUjrXs","ClampCardAction",void 0);var y=r.ccclass,b=r.property;t("ClampCardAction",(_=y("ClampCardAction"),A=b({type:c,tooltip:"UI钳子选牌-预制体"}),_((g=n((v=function(t){function n(){for(var n,e=arguments.length,i=new Array(e),r=0;r<e;r++)i[r]=arguments[r];return n=t.call.apply(t,[this].concat(i))||this,o(n,"action_clamp_ui_prefab",g,a(n)),n}e(n,t);var i=n.prototype;return i.onEventListener=function(){l.event.on("EVENT_POST",this.onGamePostHandler,this)},i.onAutoObserver=function(){var t=this;this.addReaction((function(){var t;return f.game.roomData.state===p.DESK_STATE_CLAMP&&f.game.roomData.cursor===(null==(t=f.game.getUserPlayer)?void 0:t.relBattleOffset)}),(function(n){if(n){var e=s(t.action_clamp_ui_prefab);t.scheduleOnce((function(){l.gui.openUI(e)}),.8)}}))},i.onGamePostHandler=function(t){t.post==d.CLAMP&&(l.audio.playEffect(C.CLAMP),this.getComponent(m).showCardEffect(d.CLAMP))},n}(u)).prototype,"action_clamp_ui_prefab",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),h=v))||h));i._RF.pop()}}}));

System.register("chunks:///_virtual/Cocos.ts",["cc","./Bridge.ts","./index21.ts"],(function(t){var i,n,o,a,e;return{setters:[function(t){i=t.cclegacy,n=t.native,o=t.error},function(t){a=t.Bridge},function(t){e=t.cat}],execute:function(){i._RF.push({},"f17c9TUWuVKPr4pm8hFBmSw","Cocos",void 0);t("CocosAPI",function(){function t(){this.Aandroid_BRIDGE_CLASS_NAME="com.stnts.cocos.CocosApiManager",this.IOS_BRIDGE_CLASS_NAME="CocosManager"}var i=t.prototype;return i.iosCallStaticMethod=function(t,i,o){n.reflection.callStaticMethod(this.IOS_BRIDGE_CLASS_NAME,t+":",JSON.stringify({name:i,para:null!=o?o:""}))},i.androidCallStaticMethod=function(t,i,o){window.ccLog("androidCallStaticMethod",t,i,JSON.stringify(o));var c=n.reflection.callStaticMethod(this.Aandroid_BRIDGE_CLASS_NAME,t,"(Ljava/lang/String;)Ljava/lang/String;",JSON.stringify({name:i,para:null!=o?o:""}));t==a.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST&&"failure"==c&&i!=a.EventName.BridgeName.CLOSE_DIALOG&&e.gui.showToast({title:"功能未开放，敬请期待!"})},i.callStaticMethod=function(t,i,n){return o("非NATIVE平台,无法使用bridge"),this},t}());i._RF.pop()}}}));

System.register("chunks:///_virtual/CommonAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./msg_pb.ts"],(function(e){var n,t,o,i,a,r,l,s,c,u,p;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,o=e.initializerDefineProperty,i=e.assertThisInitialized},function(e){a=e.cclegacy,r=e._decorator,l=e.sp,s=e.error,c=e.isValid},function(e){u=e.BaseComponent},function(e){p=e.Card}],execute:function(){var m,d,h,b,y,f,_,g,k,v,A,w,S,L,C;a._RF.push({},"ce694qleeBP3Jesy2WLe0UF","CommonAction",void 0);var z=r.ccclass,M=r.property;e("CommonAction",(m=z("CommonAction"),d=M({type:l.Skeleton,tooltip:"scout动画节点"}),h=M({type:l.Skeleton,tooltip:"bypass动画节点"}),b=M({type:l.Skeleton,tooltip:"turn动画节点"}),y=M({type:l.Skeleton,tooltip:"remove动画节点"}),f=M({type:l.Skeleton,tooltip:"blessing动画节点"}),_=M({type:l.Skeleton,tooltip:"clamp动画节点"}),m((v=n((k=function(e){function n(){for(var n,t=arguments.length,a=new Array(t),r=0;r<t;r++)a[r]=arguments[r];return n=e.call.apply(e,[this].concat(a))||this,o(n,"scout_node",v,i(n)),o(n,"bypass_node",A,i(n)),o(n,"turn_node",w,i(n)),o(n,"remove_node",S,i(n)),o(n,"blessing_node",L,i(n)),o(n,"clamp_node",C,i(n)),n.animationMap=void 0,n.currentAnimation=null,n}t(n,e);var a=n.prototype;return a.onLoad=function(){this.animationMap=new Map([[p.SCOUT,{name:"idle",skeleton:this.scout_node}],[p.BYPASS,{name:"idle1",skeleton:this.bypass_node}],[p.DOUBLE_BYPASS,{name:"idle2",skeleton:this.bypass_node}],[p.TURN,{name:"08_zhuanxiang",skeleton:this.turn_node}],[p.REMOVE_LANDMINE,{name:"idle",skeleton:this.remove_node}],[p.BLESSING,{name:"idle",skeleton:this.blessing_node}],[p.CLAMP,{name:"idle",skeleton:this.clamp_node}]])},a.onEventListener=function(){},a.showCardEffect=function(e){this.animationMap.forEach((function(e,n){e.skeleton.node.active=!1})),this.currentAnimation=this.animationMap.get(e);var n=this.currentAnimation.skeleton,t=this.currentAnimation.name;window.ccLog("【type】:"+e+"  【播放动作】:"+t+"  【skeleton】:",n),this.doPlay(t)},a.hideTween=function(){this.currentAnimation&&(this.currentAnimation.skeleton.node.active=!1)},a.doPlay=function(e){if(!e.length)return s("指定播放name为空");var n=this.currentAnimation.skeleton;n.setCompleteListener((function(){window.ccLog("spice回调"),c(n)&&(window.ccLog("动画结束"),n.node.active=!1)})),this.currentAnimation.skeleton.node.active=!0,n.setAnimation(0,e,!1),n.timeScale=0,n.timeScale=1,window.ccLog("动画开始",this.currentAnimation.skeleton,this.currentAnimation.skeleton.node.active)},n}(u)).prototype,"scout_node",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),A=n(k.prototype,"bypass_node",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),w=n(k.prototype,"turn_node",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),S=n(k.prototype,"remove_node",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),L=n(k.prototype,"blessing_node",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),C=n(k.prototype,"clamp_node",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),g=k))||g));a._RF.pop()}}}));

System.register("chunks:///_virtual/CommonActionUI.ts",["./rollupPluginModLoBabelHelpers.js","cc","./UILayer.ts","./index21.ts","./index40.ts","./GameEventConstant.b.ts"],(function(n){var t,o,e,i,s,c;return{setters:[function(n){t=n.inheritsLoose},function(n){o=n.cclegacy,e=n._decorator},function(n){i=n.default},function(n){s=n.cat},null,function(n){c=n.GameEventConstant}],execute:function(){var r;o._RF.push({},"8239asWuWxAqqUoXyhuedlB","CommonActionUI",void 0);var u=e.ccclass;e.property,n("CommonActionUI",u("CommonActionUI")(r=function(n){function o(){return n.apply(this,arguments)||this}t(o,n);var e=o.prototype;return e.onEventListener=function(){s.event.on(c.CLOSE_COMMON_UI,this.onCloseCommonUIHandler,this)},e.start=function(){s.event.dispatchEvent(c.CLOSE_TEAM_DROP_MINE)},e.onCloseCommonUIHandler=function(){this.close()},e.close=function(){s.gui.closeUI(this,{isMotion:!1})},o}(i))||r);o._RF.pop()}}}));

System.register("chunks:///_virtual/CommonAudio.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(o){var t,n,i;return{setters:[function(o){t=o.inheritsLoose},function(o){n=o.cclegacy,i=o.AudioSource}],execute:function(){n._RF.push({},"c175b7qnQROFIuSEwGFd3et","CommonAudio",void 0);o("CommonAudio",function(o){function n(){for(var t,n=arguments.length,i=new Array(n),u=0;u<n;u++)i[u]=arguments[u];return(t=o.call.apply(o,[this].concat(i))||this).cat=void 0,t}return t(n,o),n.prototype.initAudio=function(o){return this.cat=o,this},n}(i));n._RF.pop()}}}));

System.register("chunks:///_virtual/CommonSign.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts","./index23.ts","./index2.js"],(function(t){var e,n,i,o,c;return{setters:[function(t){e=t.extends},function(t){n=t.cclegacy},function(t){i=t.cat},function(t){o=t.default},function(t){c=t.default}],execute:function(){n._RF.push({},"df5d8HOB2VGV7CIPxlmlOxI","CommonSign",void 0);t("getCommonSign",(function(t){void 0===t&&(t={});var n=(~~(Date.now()/1e3)+o.global.diffServerTimer).toString(),r=e({},t,{"X-Timestamp":n}),u=Object.keys(r).sort().reduce((function(t,e){return["paginate.limit","paginate.page"].includes(e)||["category_id","status"].includes(e)&&0==r[e]||"object"==typeof r[e]||(t+=(t.length?"&":"")+e+"="+r[e]),t}),"");return{sign:c.SHA1(c.MD5(u+i.env.secret).toString()).toString(),timestamp:n}}));n._RF.pop()}}}));

System.register("chunks:///_virtual/Commontils.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var r,t,n,c;return{setters:[function(e){r=e.asyncToGenerator,t=e.regeneratorRuntime},function(e){n=e.cclegacy,c=e.error}],execute:function(){n._RF.push({},"5f694HOaeRG6ZUFwZScKnC+","Commontils",void 0);e("retryRequest",function(){var e=r(t().mark((function e(n,o,u){var s,a;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===o&&(o=3),void 0===u&&(u=1e3),s=0,a=function(){var e=r(t().mark((function e(){return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,n();case 3:e.next=18;break;case 5:if(e.prev=5,e.t0=e.catch(0),console.error("Request failed. Retries left: "+(o-s)),!(s<o)){e.next=16;break}return s++,e.next=12,new Promise((function(e){return setTimeout(e,u)}));case 12:return e.next=14,a();case 14:e.next=18;break;case 16:throw c("Max retries reached. Unable to complete the request."),e.t0;case 18:case"end":return e.stop()}}),e,null,[[0,5]])})));return function(){return e.apply(this,arguments)}}(),e.next=6,a();case 6:case"end":return e.stop()}}),e)})));return function(r,t,n){return e.apply(this,arguments)}}());n._RF.pop()}}}));

System.register("chunks:///_virtual/connect_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./enum.js","./file.js","./message.js"],(function(E){var A,R,N,n;return{setters:[function(E){A=E.cclegacy},null,null,null,function(E){R=E.enumDesc},function(E){N=E.fileDesc},function(E){n=E.messageDesc}],execute:function(){A._RF.push({},"49669UZFzVMIZy6US3oJ9aQ","connect_pb",void 0);var U=E("file_connect",N("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"));E("ConnectAuthAccessTokenRequestSchema",n(U,0)),E("ConnectAuthAccessTokenResponseSchema",n(U,1)),E("ConnectParamsRequestSchema",n(U,2)),E("ConnectParamsResponseSchema",n(U,3)),E("ConnectParamsResponse_Code",function(E){return E[E.OK=0]="OK",E[E.ERROR=1]="ERROR",E[E.EXIST_ANOTHER_CONNECT=2]="EXIST_ANOTHER_CONNECT",E[E.EXIST_ANOTHER_WAIT_CONNECT=3]="EXIST_ANOTHER_WAIT_CONNECT",E[E.FREE_TIME_USE_UP=4]="FREE_TIME_USE_UP",E[E.FREE_NUMBER_USE_UP=5]="FREE_NUMBER_USE_UP",E[E.USER_BALANCE_NOT_ENOUGH=6]="USER_BALANCE_NOT_ENOUGH",E[E.USER_PLAY_TIME_NOT_ENOUGH=7]="USER_PLAY_TIME_NOT_ENOUGH",E[E.USER_NOT_BIND_PHONE=8]="USER_NOT_BIND_PHONE",E[E.NOT_ALLOWED_CROSS_CLIENT=9]="NOT_ALLOWED_CROSS_CLIENT",E}({})),E("ConnectParamsResponse_CodeSchema",R(U,3,0)),E("ConnectStatusRequestSchema",n(U,4)),E("ConnectStatusResponseSchema",n(U,5)),E("ConnectOfflineNotificationSchema",n(U,6)),E("ConnectOnlineNotificationSchema",n(U,7)),E("ConnectPendingNotificationSchema",n(U,8)),E("ConnectAction",function(E){return E[E.NEW=0]="NEW",E[E.CONTINUE=1]="CONTINUE",E}({})),E("ConnectActionSchema",R(U,0)),E("ConnectStatus",function(E){return E[E.UNKNOWN=0]="UNKNOWN",E[E.PENDING=1]="PENDING",E[E.WAITING_CONNECT=2]="WAITING_CONNECT",E[E.ONLINE=3]="ONLINE",E}({})),E("ConnectStatusSchema",R(U,1)),E("ConnectOfflineType",function(E){return E[E.RESERVE=0]="RESERVE",E[E.USER_ACTION=1]="USER_ACTION",E[E.DEDUCTION_FAILED=2]="DEDUCTION_FAILED",E[E.TIME_OUT=3]="TIME_OUT",E[E.EXCEPTION=4]="EXCEPTION",E[E.SWITCH_GAME=6]="SWITCH_GAME",E[E.BALANCE_NOT_ENOUGH=201]="BALANCE_NOT_ENOUGH",E[E.FREE_TIME_USED_UP=202]="FREE_TIME_USED_UP",E[E.FREE_NUMBER_USED_UP=203]="FREE_NUMBER_USED_UP",E[E.PLAY_TIME_NOT_ENOUGH=204]="PLAY_TIME_NOT_ENOUGH",E}({})),E("ConnectOfflineTypeSchema",R(U,2));A._RF.pop()}}}));

System.register("chunks:///_virtual/Curse.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./create.js","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./index23.ts","./msg_pb.ts"],(function(e){var t,r,n,o,i,u,s,c,a,l,p,d,f;return{setters:[function(e){t=e.applyDecoratedDescriptor,r=e.inheritsLoose,n=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){i=e.cclegacy,u=e._decorator,s=e.Node,c=e.Sprite,a=e.SpriteFrame},function(e){l=e.BaseComponent},function(e){p=e.create},null,null,null,null,null,null,null,function(e){d=e.default},function(e){f=e.PlayerSchema}],execute:function(){var y,_,m,h,b,C,v,g,j;i._RF.push({},"2b91eX856pEtaLpeBl3u+ic","Curse",void 0);var w=u.ccclass,F=u.property;e("Curse",(y=w("Curse"),_=F({type:s,tooltip:"诅咒次数容器节点"}),m=F({type:c,tooltip:"诅咒次数"}),h=F({type:[a],tooltip:"诅咒次数精灵图集"}),y((v=t((C=function(e){function t(){for(var t,r=arguments.length,i=new Array(r),u=0;u<r;u++)i[u]=arguments[u];return t=e.call.apply(e,[this].concat(i))||this,n(t,"curse_node",v,o(t)),n(t,"curse_count",g,o(t)),n(t,"curse_count_spriteFrame",j,o(t)),t.props={player:p(f),showCurseCountImmediately:!1},t}r(t,e);var i=t.prototype;return i.initUI=function(){},i.onLoad=function(){},i.onAutoObserver=function(){var e=this;this.addAutorun([function(){var t=d.game.roomData;t.throwLandmineTotal,t.cursor,t.state;e.props.player.curseCount>0?e.scheduleOnce((function(){e.updateCurseCount(e.props.player.curseCount)}),e.props.showCurseCountImmediately?0:2):e.curse_node.active=!1}])},i.onDestroy=function(){},i.updateCurseCount=function(e){void 0===e&&(e=0),e>0&&(this.curse_node.active=!0,this.curse_count.spriteFrame=this.curse_count_spriteFrame[e-1])},i.hide=function(){this.curse_node.active=!1},t}(l)).prototype,"curse_node",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),g=t(C.prototype,"curse_count",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),j=t(C.prototype,"curse_count_spriteFrame",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),b=C))||b));i._RF.pop()}}}));

System.register("chunks:///_virtual/CurseCardAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index40.ts","./index23.ts","./msg_pb.ts","./SelectPlayerAction.ts","./index21.ts","./AudioEffectConstant.b.ts"],(function(e){var t,n,o,r,i,s,a,c,l,u,d,p,f,y,_,v,C,h;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,o=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){i=e.cclegacy,s=e._decorator,a=e.sp,c=e.Node,l=e.find,u=e.UITransform,d=e.tween,p=e.isValid},null,function(e){f=e.default},function(e){y=e.DeskState,_=e.Card},function(e){v=e.SelectPlayerAction},function(e){C=e.cat},function(e){h=e.AudioEffectConstant}],execute:function(){var g,m,E,T,A,S,w,R,b;i._RF.push({},"4838ej85wRLS7CYVckxl+v8","CurseCardAction",void 0);var x=s.ccclass,L=s.property;e("CurseCardAction",(g=x("CurseCardAction"),m=L({type:a.Skeleton,tooltip:"被诅咒-动画-节点"}),E=L({type:c,tooltip:"玩家节点"}),T=L({type:c,tooltip:"诅咒动画节点"}),g((w=t((S=function(e){function t(){for(var t,n=arguments.length,i=new Array(n),s=0;s<n;s++)i[s]=arguments[s];return t=e.call.apply(e,[this].concat(i))||this,o(t,"passive_request_node",w,r(t)),o(t,"players",R,r(t)),o(t,"curse_node",b,r(t)),t.play_zone=void 0,t.carde_effect=void 0,t}n(t,e);var i=t.prototype;return i.onEventListener=function(){e.prototype.onEventListener.call(this),C.event.on("EVENT_CURSE_BROADCAST",this.onEventCurseBroadcastHandler,this)},i.start=function(){var e=l("Game UI"),t=null==e?void 0:e.getComponentInChildren("Desk").node;this.play_zone=null==t?void 0:t.getChildByName("play_zone"),this.carde_effect=null==t?void 0:t.getChildByName("carde_effect")},i.onAutoObserver=function(){var t=this;e.prototype.onAutoObserver,this.addReaction((function(){return f.game.roomData.state}),(function(e){e===y.DESK_STATE_SELECT_TARGET&&_.CURSE===f.game.getLastCardOnPostZone&&(window.ccLog("诅咒指定阶段",e===y.DESK_STATE_SELECT_TARGET),C.audio.playEffect(h.THROW_BOMB),t.onStateGameSelectTarget(f.game.roomData))}))},i.checkValidPlayer=function(){return f.game.roomData.players.filter((function(e){var t=e.die,n=e.nickname,o=e.index;return window.ccLog("诅咒 checkValidPlayer:",n,t),!t&&o!==f.user.userIndex}))},i.onEventCurseBroadcastHandler=function(e){C.audio.playEffect(h.CURSE),window.ccLog("诅咒广播!!",e);var t=f.game.getPlayerComponentByIndex(this.players,e.responseIndex),n=f.game.getPlayerComponentByIndex(this.players,e.requestIndex);e.responseIndex===f.user.userIndex?this.playCurseTween({to:t,isResponseUser:!0}):(this.playCurseTween({to:t,isResponseUser:!1}),this.selectTargetTween(n.node,t.node))},i.playCurseTween=function(e){var t=this,n=e.to;e.isResponseUser;this.passive_request_node.setCompleteListener((function(){p(t.passive_request_node.node)&&(t.passive_request_node.node.active=!1)})),this.passive_request_node.node.active=!0,this.passive_request_node.setAnimation(0,"idle",!1);var o=this.play_zone.parent.getComponent(u).convertToNodeSpaceAR(this.play_zone.worldPosition),r=n.node.parent.getComponent(u).convertToNodeSpaceAR(n.node.worldPosition);this.curse_node.setPosition(o),d(this.curse_node).delay(.3).to(.5,{position:r}).delay(.5).call((function(){})).start()},t}(v)).prototype,"passive_request_node",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=t(S.prototype,"players",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=t(S.prototype,"curse_node",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=S))||A));i._RF.pop()}}}));

System.register("chunks:///_virtual/DarkCardItem.ts",["./rollupPluginModLoBabelHelpers.js","cc","./create.js","./descriptors.js","./reflect.js","./descriptor_pb.js","./binary-encoding.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./CardItem.ts","./msg_pb.ts","./index21.ts","./index40.ts","./index23.ts","./Decorator.ts","./GameEventConstant.b.ts"],(function(e){var t,r,n,a,c,s,o,d,i,l,u,p,f,_,v,h,C,g,y;return{setters:[function(e){t=e.applyDecoratedDescriptor,r=e.inheritsLoose},function(e){n=e.cclegacy,a=e._decorator,c=e.Vec3,s=e.Node,o=e.tween,d=e.Tween,i=e.v3},function(e){l=e.create},null,function(e){u=e.reflect},null,null,null,null,null,null,function(e){p=e.CardItem},function(e){f=e.DataBroadcastRequestSchema,_=e.DataBroadcastResponseSchema},function(e){v=e.cat},null,function(e){h=e.default},function(e){C=e.audioEffect,g=e.watchUser},function(e){y=e.GameEventConstant}],execute:function(){var m,D,I,b,j;n._RF.push({},"5abc1xIeKxAnqeu/dT8rcko","DarkCardItem",void 0);var x=a.ccclass;a.property,e("DarkCardItem",(m=x("DarkCardItem"),D=g(),I=C(),m((t((j=function(e){function t(){for(var t,r=arguments.length,n=new Array(r),a=0;a<r;a++)n[a]=arguments[a];return(t=e.call.apply(e,[this].concat(n))||this).is_card_selected=!1,t.origin_scale=c.ONE,t}r(t,e);var n=t.prototype;return n.start=function(){this.node.on(s.EventType.TOUCH_END,this.onCardHandler,this)},n.onAutoObserver=function(){var e=this;this.addAutorun([function(){h.game.giveMeYourCardSelectedCardIndex===e.currentIndex?e.selectTween(!0):e.selectTween(!1)}])},n.onCardHandler=function(){var e=this.currentIndex;v.ws.Request("DataBroadcast",u(f,l(f,{data:JSON.stringify({selectedCardIndex:e})})),_),h.game.giveMeYourCardSelectedCardIndex=e},n.selectTween=function(e,t){var r=this;void 0===t&&(t=!1);var n=this.is_card_selected;return this.is_card_selected=!!e&&!this.is_card_selected,new Promise((function(a){n?o(r.card).to(r.selected_duration,{scale:r.origin_scale}).call((function(){d.stopAllByTarget(r.card),a()})).start():e&&(t||v.event.dispatchEvent(y.SHOW_CARD_INFO,{card:r.props.card}),o(r.card).to(r.selected_duration,{scale:i(r.selected_scale,r.selected_scale,1)}).call((function(){d.stopAllByTarget(r.card),a()})).start())}))},t}(p)).prototype,"onCardHandler",[D,I],Object.getOwnPropertyDescriptor(j.prototype,"onCardHandler"),j.prototype),b=j))||b));n._RF.pop()}}}));

System.register("chunks:///_virtual/DarkCardList.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index23.ts","./LightCardItem.ts","./DarkCardItem.ts"],(function(t){var i,e,r,n,o,a,s,l,d,c,p,u,h,f,m;return{setters:[function(t){i=t.applyDecoratedDescriptor,e=t.inheritsLoose,r=t.initializerDefineProperty,n=t.assertThisInitialized},function(t){o=t.cclegacy,a=t._decorator,s=t.Node,l=t.Prefab,d=t.UITransform,c=t.instantiate,p=t.Layout},function(t){u=t.BaseComponent},function(t){h=t.default},function(t){f=t.LightCardItem},function(t){m=t.DarkCardItem}],execute:function(){var g,C,v,y,L,w,b;o._RF.push({},"f7297dPrxVKXKdsUiwmPItn","DarkCardList",void 0);var D=a.ccclass,k=a.property;t("DarkCardList",(g=D("DarkCardList"),C=k({type:s,tooltip:"卡牌列表"}),v=k({type:l,tooltip:"卡牌预制体"}),g((w=i((L=function(t){function i(){for(var i,e=arguments.length,o=new Array(e),a=0;a<e;a++)o[a]=arguments[a];return i=t.call.apply(t,[this].concat(o))||this,r(i,"list",w,n(i)),r(i,"card_prefab",b,n(i)),i.limit=0,i.gap=-80,i}e(i,t);var o=i.prototype;return o.onLoad=function(){this.limit=this.getComponent(d).width-200},o.start=function(){var t=this.props.length;this.list.active=!0;for(var i=0;i<t;i++){c(this.card_prefab).getComponent(m).addToParent(this.list)}this.onUpdateHandCardLayout()},o.onUpdateHandCardLayout=function(){this.list.getComponent(p).updateLayout();var t=this.list.getComponent(d);if(window.ccLog("------更新手牌布局------",t.width>=this.limit,t.width,this.limit),t.width>=this.limit){var i,e=(null==(i=this.list.children[0])?void 0:i.getComponent(d).width)||0,r=this.list.children.length;this.gap=(this.limit-e*r)/r-1}return this.list.getComponent(p).spacingX=this.gap,this},o.clearCard=function(){return this.list.removeAllChildren(),this.list.destroyAllChildren(),this},o.deleteCard=function(t){var i=this.list.children.find((function(i){return i.getComponent(f).props.card===t}));window.ccLog("删除手牌元素位置:",i),null==i||i.removeFromParent(),h.game.removeCard(t)},i}(u)).prototype,"list",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=i(L.prototype,"card_prefab",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=L))||y));o._RF.pop()}}}));

System.register("chunks:///_virtual/Debugout.ts",["cc","./StringUtil.ts"],(function(t){var e,i;return{setters:[function(t){e=t.cclegacy},function(t){i=t.safeStringify}],execute:function(){e._RF.push({},"39d3dpmFo1Gy5tv88+3lF/x","Debugout",void 0);var o=console.log,n=console.error,r=console.info,s=console.warn,a={realTimeLoggingOn:!0,useTimestamps:!0,includeSessionMetadata:!0,useLocalStorage:!1,recordLogs:!0,autoTrim:!0,maxLines:1e6,tailNumLines:25,maxDepth:20,logFilename:"debugout.txt",localStorageKey:"debugout.js",indent:"  ",quoteStrings:!0};t("default",function(){function t(t){void 0===t&&(t={}),this.indent=void 0,this.tailNumLines=void 0,this.output=void 0,this.realTimeLoggingOn=void 0,this.useTimestamps=void 0,this.includeSessionMetadata=void 0,this.useLocalStorage=void 0,this.recordLogs=void 0,this.autoTrim=void 0,this.maxLines=void 0,this.maxDepth=void 0,this.logFilename=void 0,this.localStorageKey=void 0,this.quoteStrings=void 0,this.startTime=void 0,this.indent="  ",this.tailNumLines=25,this.output="";var e=Object.assign(a,t);for(var i in e)void 0!==e[i]&&(this[i]=e[i]);if(this.useLocalStorage&&window&&window.localStorage){var o=this.load();if(o){this.output=o.log,this.startTime=new Date(o.startTime);var n=new Date(o.lastLog);this.logMetadata("Last session end: "+o.lastLog),this.logMetadata("Last "+this.formatSessionDuration(this.startTime,n)),this.startLog()}else this.startLog()}else this.useLocalStorage=!1,this.startLog()}var e=t.prototype;return e.indentsForDepth=function(t){return this.indent.repeat(Math.max(t,0))},e.startLog=function(){this.startTime=new Date,this.logMetadata("Session started: "+this.formatDate(this.startTime))},e.recordLog=function(){var t=this;this.useTimestamps&&(this.output+=this.formatDate()+" ");for(var e=arguments.length,i=new Array(e),o=0;o<e;o++)i[o]=arguments[o];if(this.output+=i.map((function(e){return t.stringify(e)})).join(" "),this.output+="#end#\n",this.autoTrim&&(this.output=this.trimLog(this.maxLines)),this.useLocalStorage){var n={startTime:this.startTime,log:this.output,lastLog:new Date};window.localStorage.setItem(this.localStorageKey,JSON.stringify(n))}},e.logMetadata=function(t){this.includeSessionMetadata&&(this.output+="---- "+t+" ----\n")},e.log=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this.realTimeLoggingOn&&o.apply(console,e),this.recordLogs&&this.recordLog.apply(this,e)},e.info=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this.realTimeLoggingOn&&r.apply(console,e),this.recordLogs&&(this.output+="[INFO] ",this.recordLog.apply(this,e))},e.warn=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this.realTimeLoggingOn&&s.apply(console,e),this.recordLogs&&(this.output+="[WARN] ",this.recordLog.apply(this,e))},e.error=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this.realTimeLoggingOn&&n.apply(console,e),this.recordLogs&&(this.output+="[ERROR] ",this.recordLog.apply(this,e))},e.debug=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this.realTimeLoggingOn&&console.debug.apply(console,e),this.recordLogs&&(this.output+="[DEBUG] ",this.recordLog.apply(this,e))},e.getLog=function(){var t=new Date;if(this.recordLogs||this.info("Log recording is off"),this.useLocalStorage&&window&&window.localStorage){var e=this.load();e&&(this.startTime=new Date(e.startTime),this.output=e.log)}return this.includeSessionMetadata?this.output+"---- "+this.formatSessionDuration(this.startTime,t)+" ----\n":this.output},e.clear=function(){this.output="",this.logMetadata("Session started: "+this.formatDate(this.startTime)),this.logMetadata("Log cleared "+this.formatDate()),this.useLocalStorage&&this.save()},e.tail=function(t){var e=t||this.tailNumLines;return this.trimLog(e)},e.search=function(t){for(var e=new RegExp(t,"ig"),i=this.output.split("\n"),o=[],n=0;n<i.length;n++){var r="["+n+"] ";i[n].match(e)&&o.push(r+i[n].trim())}var s=o.join("\n");return s.length||(s='Nothing found for "'+t+'".'),s},e.slice=function(){var t;return(t=this.output.split("\n")).slice.apply(t,arguments).join("\n")},e.downloadLog=function(){if(window){var t=this.getLog(),e=new Blob([t],{type:"data:text/plain;charset=utf-8"}),i=document.createElement("a");i.href=window.URL.createObjectURL(e),i.target="_blank",i.download=this.logFilename,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(i.href)}else n("downloadLog only works in the browser")},e.save=function(){var t={startTime:this.startTime,log:this.output,lastLog:new Date};window.localStorage.setItem(this.localStorageKey,JSON.stringify(t))},e.load=function(){var t=window.localStorage.getItem(this.localStorageKey);return t?JSON.parse(t):null},e.determineType=function(e){if(null===e)return"null";if(void 0===e)return"undefined";var i=typeof e;return"object"===i&&(i=Array.isArray(e)?"Array":e instanceof Date?"Date":e instanceof RegExp?"RegExp":e instanceof t?"Debugout":"Object"),i},e.stringifyObject=function(t,e){if(void 0===e&&(e=0),t instanceof Error)return t.toString();try{return i(t,this.indent)}catch(t){return"[Circular Reference]"}var o="{",n=e;if(this.objectSize(t)>0){o+="\n",n++;var r=0;for(var s in t){o+=this.indentsForDepth(n),o+=s+": ";var a=this.stringify(t[s],n);a&&(o+=a),r<this.objectSize(t)-1&&(o+=","),o+="\n",r++}n--,o+=this.indentsForDepth(n)}return o+="}"},e.stringifyArray=function(t,e){void 0===e&&(e=0);var i="[",o=e,n=!1;if(t.length>0){o++;for(var r=0;r<t.length;r++){var s=this.determineType(t[r]),a=!1;"Object"===s&&this.objectSize(t[r])>0&&(a=!0),"Array"===s&&t[r].length>0&&(a=!0),!n&&a&&(i+="\n");var u=this.stringify(t[r],o);u&&(a&&(i+=this.indentsForDepth(o)),i+=u,r<t.length-1&&(i+=", "),a&&(i+="\n")),n=a}o--}return i+="]"},e.stringifyFunction=function(t,e){void 0===e&&(e=0);var i=this,o=e;return String(t).split("\n").map((function(t){t.match(/\}/)&&o--;var e=i.indentsForDepth(o)+t.trim();return t.match(/\{/)&&o++,e})).join("\n")},e.stringify=function(t,e){if(void 0===e&&(e=0),e>=this.maxDepth)return"... (max-depth reached)";var i=this.determineType(t);switch(i){case"Object":return this.stringifyObject(t,e);case"Array":return this.stringifyArray(t,e);case"function":return this.stringifyFunction(t,e);case"RegExp":return"/"+t.source+"/"+t.flags;case"Date":case"string":return this.quoteStrings?'"'+t+'"':t+"";case"boolean":return t?"true":"false";case"number":return t+"";case"null":case"undefined":return i;case"Debugout":return"... (Debugout)";default:return"?"}},e.trimLog=function(t){var e=this.output.split("\n");return e.pop(),e.length>t&&(e=e.slice(e.length-t)),e.join("\n")+"\n"},e.formatSessionDuration=function(t,e){var i=e.getTime()-t.getTime(),o=Math.floor(i/1e3/60/60),n=("0"+o).slice(-2);i-=1e3*o*60*60;var r=Math.floor(i/1e3/60),s=("0"+r).slice(-2);i-=1e3*r*60;var a=Math.floor(i/1e3);return i-=1e3*a,"Session duration: "+n+":"+s+":"+("0"+a).slice(-2)},e.formatDate=function(t){return void 0===t&&(t=new Date),"["+t.toISOString()+"]"},e.objectSize=function(t){var e=0;try{for(var i in t)t.hasOwnProperty(i)&&e++}catch(t){}return e},e.report=function(t){var e=this.getLog();fetch("https://cocos-game-dev.mityoo.com/log",{mode:"cors",headers:{Accept:"application/json","Content-Type":"application/json"},method:"POST",body:JSON.stringify({fileName:t,content:e})}).catch((function(t){})).finally((function(){}))},e.request=function(t,e,i){return new Promise((function(o){var n=new XMLHttpRequest;n.open(t,e,!0),n.timeout=1e4,n.send(JSON.stringify(i)),n.onreadystatechange=function(){if(4==n.readyState&&200==n.status){var t=n.responseText;o("ERROR_PUT_SUCCESS"==t?t:JSON.parse(t))}}}))},t}());e._RF.pop()}}}));

System.register("chunks:///_virtual/Decorator.ts",["cc","./index21.ts","./index40.ts","./index23.ts","./StringUtil.ts","./AudioEffectConstant.b.ts"],(function(n){var t,e,r,u,i;return{setters:[function(n){t=n.cclegacy},function(n){e=n.cat},null,function(n){r=n.default},function(n){u=n.safeStringify},function(n){i=n.AudioEffectConstant}],execute:function(){n("LogMethod",(function(n,t,e){var r=e.value;return e.value=function(){window.ccLog("Method: "+t);for(var n=arguments.length,e=new Array(n),i=0;i<n;i++)e[i]=arguments[i];window.ccLog("Arguments: "+u(e));var o=r.apply(this,e);return window.ccLog("Return: "+u(o)),o},e})),t._RF.push({},"450b2fvIZ9EZZzjfCgbz/Fb","Decorator",void 0);n("buttonLock",(function(n,t){return void 0===n&&(n=1),function(e,r,u){var i=u.value,o=!1;return u.value=function(){if(o)t&&t();else{o=!0,setTimeout((function(){o=!1}),1e3*n);for(var e=arguments.length,r=new Array(e),u=0;u<e;u++)r[u]=arguments[u];i.apply(this,r)}},u}})),n("audioEffect",(function(n,t){return void 0===n&&(n=i.CLICK),function(r,u,i){var o=i.value;return i.value=function(){e.audio.playEffect(n),t&&t();for(var r=arguments.length,u=new Array(r),i=0;i<r;i++)u[i]=arguments[i];o.apply(this,u)},i}})),n("watchUser",(function(n){return function(t,e,u){var i=u.value;return u.value=function(){if(n&&n(),window.ccLog("观战",r.user.isAudience),!r.user.isAudience){for(var t=arguments.length,e=new Array(t),u=0;u<t;u++)e[u]=arguments[u];i.apply(this,e)}},u}}));n("gameVisibleCheck",(function(n){return function(t,e,u){var i=u.value;return u.value=function(){if(r.global.isGameVisiable){for(var t=arguments.length,e=new Array(t),u=0;u<t;u++)e[u]=arguments[u];return i.apply(this,e)}n&&n()},u}}));t._RF.pop()}}}));

System.register("chunks:///_virtual/Decorator2.ts",["cc"],(function(){var t;return{setters:[function(c){t=c.cclegacy}],execute:function(){t._RF.push({},"d081drIH69Lc5E8tNkPjYo6","Decorator",void 0),t._RF.pop()}}}));

System.register("chunks:///_virtual/Desk.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index3.js","./index40.ts","./Decorator.ts","./index23.ts","./IGame.ts","./index21.ts","./BaseComponent.ts","./msg_pb.ts","./CardItem.ts","./LightCardItem.ts","./PlayerItem.ts","./HandCard.ts","./reflect.js","./descriptors.js","./create.js","./descriptor_pb.js","./from-binary.js","./binary-encoding.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./Shark.ts","./GameEventConstant.b.ts","./AudioEffectConstant.b.ts","./msg_pb.js","./IGame.b.ts"],(function(e){var t,n,a,r,o,i,s,l,c,u,d,p,f,_,m,h,y,g,w,b,C,v,D,P,A,S,E,T,O,R,L,I,k,z,B,U,N,x,H,j,M,F,G,V,Z,K,W,Y,q,J,Q;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,a=e.initializerDefineProperty,r=e.assertThisInitialized,o=e.asyncToGenerator,i=e.regeneratorRuntime},function(e){s=e.cclegacy,l=e._decorator,c=e.Node,u=e.Label,d=e.sp,p=e.Prefab,f=e.SpriteFrame,_=e.Camera,m=e.v3,h=e.Button,y=e.Sprite,g=e.UITransform,w=e.Vec3,b=e.error,C=e.instantiate,v=e.tween,D=e.Tween,P=e.isValid},null,null,function(e){A=e.audioEffect,S=e.buttonLock,E=e.watchUser},function(e){T=e.default},null,function(e){O=e.cat},function(e){R=e.BaseComponent},function(e){L=e.DeskState,I=e.CardState,k=e.PostCardRequestSchema,z=e.PostCardResponseSchema,B=e.Card,U=e.RefreshCardRequestSchema,N=e.RefreshCardResponseSchema},function(e){x=e.CardItem},function(e){H=e.LightCardItem},function(e){j=e.PlayerItem},function(e){M=e.HandCard},function(e){F=e.reflect},null,function(e){G=e.create},null,function(e){V=e.fromBinary},null,null,null,null,null,function(e){Z=e.Shark},function(e){K=e.GameEventConstant},function(e){W=e.AudioEffectConstant},function(e){Y=e.BattleMode},function(e){q=e.GameModelSinglePosMap,J=e.GameModelTeamPosMap,Q=e.CardChineseName}],execute:function(){var X,$,ee,te,ne,ae,re,oe,ie,se,le,ce,ue,de,pe,fe,_e,me,he,ye,ge,we,be,Ce,ve,De,Pe,Ae,Se,Ee,Te,Oe,Re,Le,Ie,ke,ze,Be,Ue,Ne,xe,He,je,Me,Fe,Ge,Ve;s._RF.push({},"2a834HhEk5HP6lhCS3BmHku","Desk",void 0);var Ze=l.ccclass,Ke=l.property;e("Desk",(X=Ze("Desk"),$=Ke({type:c,tooltip:"游戏区域节点"}),ee=Ke({type:c,tooltip:"抽牌区"}),te=Ke({type:c,tooltip:"玩家节点"}),ne=Ke({type:u,tooltip:"牌堆剩余张数"}),ae=Ke({type:u,tooltip:"炸弹(危险牙齿)数量"}),re=Ke({type:c,tooltip:"出牌区"}),oe=Ke({type:d.Skeleton,tooltip:"轮转顺序"}),ie=Ke({type:M,tooltip:"手牌组件"}),se=Ke({type:p,tooltip:"手牌预制体"}),le=Ke({type:p,tooltip:"玩家预制体"}),ce=Ke({type:c,tooltip:"操作按钮组"}),ue=Ke({type:c,tooltip:"抽牌按钮"}),de=Ke({type:[f],tooltip:"抽牌按钮精灵图集"}),pe=Ke({type:c,tooltip:"出牌按钮"}),fe=Ke({type:c,tooltip:"轮到你了节点"}),_e=Ke({type:Z,tooltip:"鲨鱼节点"}),me=Ke({type:_,tooltip:"3D摄像机节点"}),he=Ke({type:p,tooltip:"按钮UI预制体"}),ye=Ke({type:[f],tooltip:"轮到你了图集"}),ge=E(),we=A(),be=S(.5),Ce=E(),ve=A(),De=S(.5),X((Se=t((Ae=function(e){function t(){for(var t,n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return t=e.call.apply(e,[this].concat(o))||this,a(t,"card_move_tween",Se,r(t)),a(t,"draw_zone",Ee,r(t)),a(t,"players_node",Te,r(t)),a(t,"remaining_deck_cards",Oe,r(t)),a(t,"boom_count",Re,r(t)),a(t,"play_zone",Le,r(t)),a(t,"player_direction",Ie,r(t)),a(t,"hand_card",ke,r(t)),a(t,"hand_card_item_prefab",ze,r(t)),a(t,"players_prefab",Be,r(t)),a(t,"action_node",Ue,r(t)),a(t,"btn_action_draw",Ne,r(t)),a(t,"btn_action_draw_spriteFrames",xe,r(t)),a(t,"btn_action_play",He,r(t)),a(t,"you_turn_node",je,r(t)),a(t,"shark",Me,r(t)),a(t,"camera3D",Fe,r(t)),a(t,"ui_draw_prefab",Ge,r(t)),a(t,"you_turn_spriteFrame",Ve,r(t)),t.drawZonePos=m(),t.playZonePos=m(),t.handZonePos=m(),t.isDealCardLoading=!1,t.ui_draw=null,t.data={showActionButton:!1},t}n(t,e);var s=t.prototype;return s.initUI=function(){this.draw_zone.on(c.EventType.TOUCH_END,this.onActionDrawHandler,this),this.btn_action_draw.on(h.EventType.CLICK,this.onActionDrawHandler,this),this.btn_action_play.on(h.EventType.CLICK,this.onActionplayCardManual,this),this.you_turn_node.getComponent(y).spriteFrame=this.you_turn_spriteFrame[T.user.isAudience?1:0]},s.initDesk=function(){this.reset()},s.onLoad=function(){},s.start=function(){this.drawZonePos=this.card_move_tween.getComponent(g).convertToNodeSpaceAR(this.draw_zone.worldPosition),this.playZonePos=this.card_move_tween.getComponent(g).convertToNodeSpaceAR(this.play_zone.worldPosition),this.handZonePos=this.card_move_tween.getComponent(g).convertToNodeSpaceAR(this.hand_card.list.worldPosition),this.play_zone.removeAllChildren()},s.onEventListener=function(){O.event.on("EVENT_BROADCAST",this.onBroadcastSatateSocketHandler,this).on("RefreshCard",this.onWatchRefreshCard,this).on("EVENT_DRAW",this.onDrawBroadcastHandler,this)},s.onAutoObserver=function(){var e=this;this.addAutorun([function(){e.remaining_deck_cards.string=""+T.game.roomData.deskRemainingCards},function(){e.boom_count.string=""+T.game.roomData.badNumber},function(){e.player_direction.node.scale=w.ONE.clone().multiply3f(T.game.roomData.isForward?-1:1,1,1)},function(){e.btn_action_draw.active=e.btn_action_play.active=T.game.showActionButton},function(){e.btn_action_play.getComponent(h).interactable=!!T.game.playerCards.length,e.btn_action_play.getComponent(y).grayscale=!T.game.playerCards.length},function(){switch(T.game.roomData.state){case L.DESK_STATE_DRAW_OR_POST:e.onPhaseDrawOrPlay(T.game.roomData)}},function(){e.btn_action_draw.getComponent(y).spriteFrame=e.btn_action_draw_spriteFrames[T.game.getPlayerDrawCount-1]},function(){T.game.roomData.state===L.DESK_STATE_GAME_START&&(window.ccLog("DESK_STATE_GAME_START"),O.gui.hideLoading(),e.onPhaseInit())}]),this.addReaction((function(){return T.game.roomData.players}),(function(t){t.forEach((function(t){var n,a=e.players_node.children.find((function(e){var n;return(null==(n=e.getComponent(j))?void 0:n.props.player.index)===t.index}));a&&(null==(n=a.getComponent(j))||n.setUpdateProps({player:t}))}))})).addReaction((function(){var e;return T.game.roomData.state===L.DESK_STATE_DRAW_OR_POST_TIMEOUT&&T.game.roomData.cursor===(null==(e=T.game.getUserPlayer)?void 0:e.relBattleOffset)}),(function(t){var n;t&&(null!=(n=T.game.over_time_draw)&&n.isMe?T.game.over_time_draw=null:e.openDrawUI())}),{fireImmediately:!0}).addReaction((function(){var e;return T.game.roomData.state===L.DESK_STATE_BLESSING&&T.game.roomData.cursor===(null==(e=T.game.getUserPlayer)?void 0:e.relBattleOffset)}),(function(t){t&&e.scheduleOnce((function(){e.openDrawUI()}),1)}))},s.onDrawBroadcastHandler=function(e){e.index==T.user.userIndex&&this.openDrawUI()},s.onBroadcastSatateSocketHandler=function(e){var t;e.players=e.players.map((function(e){return delete e.team,e})),window.ccLog("onBroadcastSatateSocketHandler------------",e);var n,a=e.players.find((function(e){var t,n;return console.log(e.id,null==(t=T.game.getUserPlayer)?void 0:t.id),e.id===(null==(n=T.game.getUserPlayer)?void 0:n.id)}));(T.user.isAudience&&(a=e.players[0]).cardState.forEach((function(e,t,n){[I.BAD,I.GOOD].includes(e)&&(n[t]=I.UNUSED)})),null!=(t=a)&&t.cardState)&&(T.game.player_teeth=a.cardState,null==(n=this.shark.getComponent(Z))||n.setOptions({props:{card_state:a.cardState}}));if(window.ccLog("store.game.roomData 变化：",T.game.roomData,"---------------------------------\x3e",e),T.game.roomData=e,window.ccLog("当前信息",e),e.serviceTime){var r=Number(e.serviceTime)-Date.now();T.global.ws_diff_server_timer=r,window.ccLog("ws时间差(ms)",r)}},s.init=function(){this.initDesk()},s.onPhaseInit=function(){this.init(),window.ccLog("初始化-阶段"),this.dealCard(T.game.playerCards),O.tracking.game.roomEnter()},s.onPhaseDrawOrPlay=function(e){var t=this;e.cursor===T.user.userIndex?(O.event.dispatchEvent(K.CANCEL_SELECT_HAND_CARD),T.game.showActionButton=!0,e.preCursor==T.user.userIndex||T.game.userTurn||(T.game.userTurn=!0,O.audio.playEffect(W.YOUR_TURN),this.you_turn_node.active=!0,this.scheduleOnce((function(){t.you_turn_node.active=!1}),2))):(T.game.userTurn=!1,T.game.showActionButton=!1)},s.initPlayers=function(){var e=this,t=T.game,n=T.user;T.game.playersPos.clear();var a=n.auth.mode,r=n.userIndex,o=n.userTeamId;window.ccLog("初始化玩家位置信息",r,o);var i=t.battle,s=[].concat(t.basePlayers);if(s.sort((function(e,t){return e.relBattleOffset-t.relBattleOffset})),!s.length)return b("玩家列表不存在");if(a==Y.PVP_COOPERATION)for(var l=s.find((function(e){return e.teamId==o}));l&&s[0].teamId!=o;){var c=s.shift();c&&s.push(c)}else{if(a!=Y.PVP_SOLO)return b("错误模式:"+a);for(var u=s.find((function(e){return e.relBattleOffset===r}));u&&s[0].relBattleOffset!=r;){var d=s.shift();d&&s.push(d)}}var p=a==Y.PVP_SOLO?q[i.teams]:a==Y.PVP_COOPERATION?J[i.teams]:[];s.forEach((function(e,t){T.game.playersPos.set(e.relBattleOffset,p[t])})),window.ccLog("位置信息是否为空",p),this.players_node.destroyAllChildren(),T.game.roomData.players.forEach((function(t,n){var a=C(e.players_prefab),r=T.game.playersPos.get(t.index),o=r.x,i=r.y;window.ccLog("重新实例玩家",n,t),a.getComponent(j).setPosition(m(o,i,0)).addToParent(e.players_node,{props:{player:t}})}))},s.oncePlayAnimation=function(e,t){var n=this;void 0===t&&(t=.2),e.getComponent(x).isValidCard=!1,e.setParent(this.card_move_tween,!0),v(e.getComponent(x).card).to(t,{scale:w.ONE}).start(),v(e).to(t,{position:this.playZonePos}).call((function(){D.stopAllByTarget(e),e.setParent(n.play_zone,!0)})).start()},s.syncHandCard=function(e){window.ccLog("----------同步手牌"),this.dealCard(e,!1)},s.dealCard=function(){var e=o(i().mark((function e(t,n){var a,r,o,s=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===n&&(n=!0),!this.isDealCardLoading&&t.length){e.next=3;break}return e.abrupt("return");case 3:if(this.isDealCardLoading=!0,window.ccLog("发牌",[].concat(t)),this.hand_card.clearCard(),a=.2,r=[],!n){e.next=11;break}return e.next=11,O.audio.playEffect(W.DEAL_CARD);case 11:o=[],t.forEach((function(e,t){o.push(Q[e]),r.push(s.onceDealAnimation({card:e,duration:a+.05*t,is_tween:n}))})),Promise.all(r).finally((function(){O.tracking.game.cardsFinish(o),s.isDealCardLoading=!1}));case 14:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),s.onceDealAnimation=function(){var e=o(i().mark((function e(t){var n,a,r,o,s,l;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.card,a=t.duration,r=void 0===a?.2:a,o=t.is_tween,s=C(this.hand_card_item_prefab),(l=s.getComponent(H)).addToParent(this.card_move_tween,{props:{card:n}}).node.setPosition(this.drawZonePos),!o){e.next=7;break}return e.next=7,l.moveTween(r,{position:this.handZonePos});case 7:l.setHandCard(!0),s.setParent(this.hand_card.list,!0),O.event.dispatchEvent(K.UPDATE_HANDCARD_LAYOUT);case 10:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),s.onActionDrawHandler=function(){T.game.showActionButton&&this.openDrawUI()},s.openDrawUI=function(){window.ccLog("------------打开抽牌UI",!P(this.ui_draw)),P(this.ui_draw)||(this.ui_draw=C(this.ui_draw_prefab),O.gui.openUI(this.ui_draw,{props:{shark:this.shark,hand_card:this.hand_card,camera3D:this.camera3D}}))},s.onActionplayCardManual=function(){var e=this,t=this.hand_card.getCardSelected();if(t){var n=t.getComponent(H);window.ccLog("获取选中的牌",n.props.card),O.ws.Request("PostCard",F(k,G(k,{card:n.props.card})),z).then((function(t){e.handleByCard(n.props.card,t)}))}else O.gui.showToast({title:"请选择出牌"})},s.handleByCard=function(e,t){switch(O.event.dispatchEvent(K.STOP_PLAYER_Timer,T.user.userIndex),e){case B.SCOUT:O.event.dispatchEvent(K.SCOUNT_CARD_RESPONSE,t);break;case B.BYPASS:O.event.dispatchEvent(K.BYPASS_CARD_RESPONSE,t);break;case B.TURN:O.event.dispatchEvent(K.TURN_CARD_RESPONSE,t)}},s.onWatchRefreshCard=function(e){var t=e.response,n=e.request,a=V(U,n),r=V(N,t);window.ccLog("观战视角刷新手牌通知",a,r),this.dealCard(T.game.playerCards),T.game.playerCards=r.handCard},s.addPlayCard=function(e){C(this.hand_card_item_prefab).getComponent(x).addToParent(this.play_zone,{props:{card:e}})},s.reset=function(){this.card_move_tween.destroyAllChildren(),this.play_zone.destroyAllChildren()},t}(R)).prototype,"card_move_tween",[$],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ee=t(Ae.prototype,"draw_zone",[ee],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Te=t(Ae.prototype,"players_node",[te],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Oe=t(Ae.prototype,"remaining_deck_cards",[ne],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Re=t(Ae.prototype,"boom_count",[ae],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Le=t(Ae.prototype,"play_zone",[re],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ie=t(Ae.prototype,"player_direction",[oe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ke=t(Ae.prototype,"hand_card",[ie],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ze=t(Ae.prototype,"hand_card_item_prefab",[se],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Be=t(Ae.prototype,"players_prefab",[le],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ue=t(Ae.prototype,"action_node",[ce],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ne=t(Ae.prototype,"btn_action_draw",[ue],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),xe=t(Ae.prototype,"btn_action_draw_spriteFrames",[de],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),He=t(Ae.prototype,"btn_action_play",[pe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),je=t(Ae.prototype,"you_turn_node",[fe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Me=t(Ae.prototype,"shark",[_e],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Fe=t(Ae.prototype,"camera3D",[me],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ge=t(Ae.prototype,"ui_draw_prefab",[he],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ve=t(Ae.prototype,"you_turn_spriteFrame",[ye],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),t(Ae.prototype,"onActionDrawHandler",[ge,we,be],Object.getOwnPropertyDescriptor(Ae.prototype,"onActionDrawHandler"),Ae.prototype),t(Ae.prototype,"onActionplayCardManual",[Ce,ve,De],Object.getOwnPropertyDescriptor(Ae.prototype,"onActionplayCardManual"),Ae.prototype),Pe=Ae))||Pe));s._RF.pop()}}}));

System.register("chunks:///_virtual/DoublePassCardAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./PassCardAction.ts","./msg_pb.ts","./index21.ts","./index40.ts","./AudioEffectConstant.b.ts"],(function(t){var n,o,s,c,e,i,a;return{setters:[function(t){n=t.inheritsLoose},function(t){o=t.cclegacy,s=t._decorator},function(t){c=t.PassCardAction},function(t){e=t.Card},function(t){i=t.cat},null,function(t){a=t.AudioEffectConstant}],execute:function(){var r;o._RF.push({},"5cc51Y2WbFL76hyCe5ayC+k","DoublePassCardAction",void 0);var u=s.ccclass;s.property,t("DoublePassCardAction",u("DoublePassCardAction")(r=function(t){function o(){return t.apply(this,arguments)||this}return n(o,t),o.prototype.onGamePostHandler=function(t){e.DOUBLE_BYPASS==t.post&&(i.audio.playEffect(a.PASS_DOUBLE),window.ccLog("绕过x2"))},o}(c))||r);o._RF.pop()}}}));

System.register("chunks:///_virtual/EffectSwitch.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Switch.ts","./index21.ts"],(function(t){var c,i,n,e,o;return{setters:[function(t){c=t.inheritsLoose},function(t){i=t.cclegacy,n=t._decorator},function(t){e=t.Switch},function(t){o=t.cat}],execute:function(){var f;i._RF.push({},"68d200VjJFNE5Ta1v41lthG","EffectSwitch",void 0);var s=n.ccclass;n.property,t("EffectSwitch",s("EffectSwitch")(f=function(t){function i(){return t.apply(this,arguments)||this}c(i,t);var n=i.prototype;return n.start=function(){this.data.is_on=o.audio.switchEffect},n.onSwitchHandler=function(){o.audio.switchEffect=!o.audio.switchEffect,this.data.is_on=o.audio.switchEffect},i}(e))||f);i._RF.pop()}}}));

System.register("chunks:///_virtual/EncryptUtil.ts",["cc","./index2.js"],(function(t){var n,r;return{setters:[function(t){n=t.cclegacy},function(t){r=t.default}],execute:function(){n._RF.push({},"4ef2b2yWj9Kn6bWST+n9Iv8","EncryptUtil",void 0);var e=null,i=(t("md5",(function(t){return r.MD5(t).toString()})),t("initCrypto",(function(t,n){e=r.enc.Hex.parse(n)})),t("aesEncrypt",(function(t,n,c){return r.AES.encrypt(t,n,{iv:e,format:i}).toString()})),t("aesDecrypt",(function(t,n,c){return r.AES.decrypt(t,n,{iv:e,format:i}).toString(r.enc.Utf8)})),t("JsonFormatter",{stringify:function(t){var n={ct:t.ciphertext.toString(r.enc.Base64)};return t.iv&&(n.iv=t.iv.toString()),t.salt&&(n.s=t.salt.toString()),JSON.stringify(n)},parse:function(t){var n=JSON.parse(t),e=r.lib.CipherParams.create({ciphertext:r.enc.Base64.parse(n.ct)});return n.iv&&(e.iv=r.enc.Hex.parse(n.iv)),n.s&&(e.salt=r.enc.Hex.parse(n.s)),e}}));n._RF.pop()}}}));

System.register("chunks:///_virtual/EnterGame.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index3.js","./index23.ts","./index21.ts","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./from-json.js","./msg_pb.js"],(function(e){var n,t,r,s,u,i,a,o;return{setters:[function(e){n=e.asyncToGenerator,t=e.regeneratorRuntime},function(e){r=e.cclegacy},null,function(e){s=e.default},function(e){u=e.cat},null,null,null,null,null,null,null,function(e){i=e.fromJsonString,a=e.fromJson},function(e){o=e.ClientBootParamSchema}],execute:function(){e("default",(function(e){return l.apply(this,arguments)})),r._RF.push({},"56068PJxuVDlZ0dE7MQMc3a","EnterGame",void 0);var c=function(){var e=s.user;if(!e.auth.battleId)return u.gui.showToast({title:"缺少battleId"});var n=e.auth,t=n.battleId,r=n.token,i=void 0===r?"":r,a=n.serverId,o=void 0===a?"":a;return u.ws.create({battle_id:t,serverId:o,token:i},{url:e.isAudience?u.env.audience_ws_url:u.env.player_ws_url,reconnectMaxAttempts:1/0}).connectRequest(e.isAudience)};function l(){return(l=n(t().mark((function e(n){var r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!n){e.next=6;break}r="string"==typeof n?i(o,n,{ignoreUnknownFields:!0}):a(o,n,{ignoreUnknownFields:!0}),s.user.auth=r,e.next=8;break;case 6:return e.next=8,u.platform.serverLogin();case 8:window.ccLog("EnterGame authParams",JSON.stringify(s.user.auth)),e.next=15;break;case 11:throw e.prev=11,e.t0=e.catch(0),u.gui.showToast({title:"登录参数错误"}),new Error("登录参数错误");case 15:return e.next=17,c();case 17:case"end":return e.stop()}}),e,null,[[0,11]])})))).apply(this,arguments)}r._RF.pop()}}}));

System.register("chunks:///_virtual/ExchangeCardAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./SelectPlayerAction.ts","./DarkCardItem.ts","./LightCardItem.ts","./HandCard.ts","./index21.ts","./msg_pb.ts","./index40.ts","./index23.ts","./Decorator.ts","./AudioEffectConstant.b.ts","./GameEventConstant.b.ts"],(function(e){var n,t,o,r,a,i,s,c,d,p,l,u,f,g,m,h,E,y,C,_,v,T,x,A;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,o=e.initializerDefineProperty,r=e.assertThisInitialized,a=e.asyncToGenerator,i=e.regeneratorRuntime},function(e){s=e.cclegacy,c=e._decorator,d=e.Prefab,p=e.Node,l=e.UITransform,u=e.v3,f=e.instantiate},function(e){g=e.SelectPlayerAction},function(e){m=e.DarkCardItem},function(e){h=e.LightCardItem},function(e){E=e.HandCard},function(e){y=e.cat},function(e){C=e.DeskState,_=e.Card},null,function(e){v=e.default},function(e){T=e.audioEffect},function(e){x=e.AudioEffectConstant},function(e){A=e.GameEventConstant}],execute:function(){var w,b,I,P,S,R,D,H,L,N,O,B;s._RF.push({},"78854jwiglFSpFYv5ihK50g","ExchangeCardAction",void 0);var G=c.ccclass,k=c.property;e("ExchangeCardAction",(w=G("ExchangeCardAction"),b=k({type:d,tooltip:"暗牌预制体"}),I=k({type:d,tooltip:"明牌预制体"}),P=k({type:E,tooltip:"手牌组件"}),S=k({type:p,tooltip:"玩家节点"}),R=T(x.EXCHANGE_FINISH),w((L=n((H=function(e){function n(){for(var n,t=arguments.length,a=new Array(t),i=0;i<t;i++)a[i]=arguments[i];return n=e.call.apply(e,[this].concat(a))||this,o(n,"dark_card_prefab",L,r(n)),o(n,"light_card_prefab",N,r(n)),o(n,"hand_card",O,r(n)),o(n,"players",B,r(n)),n}t(n,e);var s=n.prototype;return s.onEventListener=function(){e.prototype.onEventListener.call(this),y.event.on("EVENT_RESPONSE_EXCHANGE_BROADCAST",this.ononResponseExchangeBroadcastHandler,this)},s.onAutoObserver=function(){var n=this;e.prototype.onAutoObserver,this.addReaction((function(){return v.game.roomData.state}),(function(e){e===C.DESK_STATE_SELECT_TARGET&&_.EXCHANGE===v.game.getLastCardOnPostZone&&(window.ccLog("调包阶段",e===C.DESK_STATE_SELECT_TARGET),y.audio.playEffect(x.EXCHANGE),n.onStateGameSelectTarget(v.game.roomData))}))},s.checkValidPlayer=function(){return v.game.roomData.players.filter((function(e){var n=e.die,t=e.index;return!n&&t!==v.user.userIndex}))},s.onSelectTargetBroadcast=function(e){},s.ononResponseExchangeBroadcastHandler=function(e){var n=this;this.scheduleOnce((function(){n.onResponseExchangeBroadcast(e)}),.2)},s.onResponseExchangeBroadcast=function(e){var n=this;if(_.EXCHANGE===v.game.getLastCardOnPostZone)if(window.ccLog(e.requestIndex+"向"+e.responseIndex+"发起指向"),y.audio.playEffect(x.EXCHANGE_SELECTED),e.responseIndex===v.user.userIndex);else if(e.requestIndex!==v.user.userIndex);else{var t=v.game.getPlayerComponentByIndex(this.players,e.requestIndex).node,o=v.game.getPlayerComponentByIndex(this.players,e.responseIndex).node;this.selectTargetTween(t,o),window.ccLog("指向 : 其他人 显示指向动画")}var r=v.game.getPlayerComponentByIndex(this.players,e.requestIndex),s=v.game.getPlayerComponentByIndex(this.players,e.responseIndex);this.hand_card.node.getComponent(l).convertToNodeSpaceAR(s.node.worldPosition),this.hand_card.getComponent(l).convertToNodeSpaceAR(r.node.worldPosition);if(v.game.isUserOrWatchTeammate(e.responseIndex)||v.game.isUserOrWatchTeammate(e.requestIndex)){var c=v.game.isUserOrWatchTeammate(e.requestIndex)?s.node.position:r.node.position,d=v.game.isUserOrWatchTeammate(e.requestIndex)?e.requestHandCard:e.responseHandCard,p=[].concat(this.hand_card.list.children),g=p.map((function(e){return e.setParent(n.card_move_tween,!0),window.ccLog(""+e.getComponent(h).props.card),e.getComponent(h).moveTween(.5,{position:c,scale:u(.5,.5,1)})}));Promise.all(g).finally((function(){p.forEach((function(e){return e.destroy()}))}));var E=d.map(a(i().mark((function e(t){var o,r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=f(n.light_card_prefab),(r=o.getComponent(h)).addToParent(n.card_move_tween,{props:{card:t}}).setPosition(c),e.next=5,r.moveTween(.5,{position:n.hand_card.node.position});case 5:r.setParent(n.hand_card.list,!0).setHandCard(!0);case 6:case"end":return e.stop()}}),e)}))));Promise.all(E).then((function(){v.game.clearCard().addCard(d),y.event.dispatchEvent(A.UPDATE_HANDCARD_LAYOUT)}))}else{for(var C=r.getComponent(l).convertToNodeSpaceAR(s.node.worldPosition),T=s.getComponent(l).convertToNodeSpaceAR(r.node.worldPosition),w=function(){var e=f(n.dark_card_prefab),t=e.getComponent(m);t.addToParent(r.node),e.setScale(.5,.5),t.moveTween(.29+.01*b,{position:C}).finally((function(){e.destroy()}))},b=0;b<e.requestHandCount;b++)w();for(var I=function(){var e=f(n.dark_card_prefab),t=e.getComponent(m);t.addToParent(s.node),e.setScale(.5,.5),t.moveTween(.29+.01*P,{position:T}).finally((function(){e.destroy()}))},P=0;P<e.responseHandCount;P++)I();window.ccLog("其他人 显示指向动画")}},n}(g)).prototype,"dark_card_prefab",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=n(H.prototype,"light_card_prefab",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=n(H.prototype,"hand_card",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=n(H.prototype,"players",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),n(H.prototype,"onResponseExchangeBroadcast",[R],Object.getOwnPropertyDescriptor(H.prototype,"onResponseExchangeBroadcast"),H.prototype),D=H))||D));s._RF.pop()}}}));

System.register("chunks:///_virtual/exportLog.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index23.ts","./index21.ts","./BaseComponent.ts"],(function(o){var t,e,n,i,r,a,l,p,c,u,s,g,f;return{setters:[function(o){t=o.applyDecoratedDescriptor,e=o.inheritsLoose,n=o.initializerDefineProperty,i=o.assertThisInitialized},function(o){r=o.cclegacy,a=o._decorator,l=o.Node,p=o.Prefab,c=o.Button,u=o.instantiate},function(o){s=o.default},function(o){g=o.cat},function(o){f=o.BaseComponent}],execute:function(){var _,d,L,b,x,y,h;r._RF.push({},"79ec6VXAGVLdZnVVnHRMA9B","exportLog",void 0);var v=a.ccclass,E=a.property;o("exportLog",(_=v("exportLog"),d=E({type:l,tooltip:"导出日志"}),L=E({type:p,tooltip:"上传日志预制体"}),_((y=t((x=function(o){function t(){for(var t,e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return t=o.call.apply(o,[this].concat(r))||this,n(t,"export_log",y,i(t)),n(t,"uploadLogPrefab",h,i(t)),t}e(t,o);var r=t.prototype;return r.onLoad=function(){s.global.isDebugOrTestEnv&&(this.export_log.active=!0,this.export_log.on(c.EventType.CLICK,this.onExportLogClick,this))},r.onExportLogClick=function(){var o=u(this.uploadLogPrefab);g.gui.openUI(o,{props:{confrim:function(t){window.__DEBUGOUT_INSTENCE__&&window.__DEBUGOUT_INSTENCE__.report(t),g.gui.closeUI(o)},cancel:function(){g.gui.closeUI(o)}}})},t}(f)).prototype,"export_log",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),h=t(x.prototype,"uploadLogPrefab",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),b=x))||b));r._RF.pop()}}}));

System.register("chunks:///_virtual/FitWidthCamera.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts"],(function(t){var e,a,o,i,n,r,c;return{setters:[function(t){e=t.inheritsLoose},function(t){a=t.cclegacy,o=t._decorator,i=t.CameraComponent,n=t.view,r=t.Component},function(t){c=t.cat}],execute:function(){var s;a._RF.push({},"14375FfVRxBNosESRoeI+SX","FitWidthCamera",void 0);var u=o.ccclass,h=(o.property,o.requireComponent),v=t("UPDATE_FOV","update-fov");t("FitWidthCamera",u(s=h(i)(s=function(t){function a(){for(var e,a=arguments.length,o=new Array(a),i=0;i<a;i++)o[i]=arguments[i];return(e=t.call.apply(t,[this].concat(o))||this)._camera=void 0,e._defaultTanHalfFov=void 0,e.updateFov=function(){var t=n.getVisibleSize().height/n.getDesignResolutionSize().height*e._defaultTanHalfFov;window.ccLog("--------tanHalfFov2",t),e._camera.fov=Math.atan(t)/Math.PI*180*2,window.ccLog("canvas-resize",n.getVisibleSize().height,n.getDesignResolutionSize().height,e._camera.fov),c.event.dispatchEvent(v)},e}e(a,t);var o=a.prototype;return o.onLoad=function(){this._camera=this.getComponent(i),this._defaultTanHalfFov=Math.tan(.5*this._camera.fov/180*Math.PI),this.updateFov(),n.on("canvas-resize",this.updateFov)},o.onDestroy=function(){n.off("canvas-resize",this.updateFov)},a}(r))||s)||s);a._RF.pop()}}}));

System.register("chunks:///_virtual/Flex.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var n,t,r,o,i,a,l,u,p,c;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,r=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){i=e.cclegacy,a=e._decorator,l=e.Enum,u=e.CCFloat,p=e.CCInteger,c=e.Component}],execute:function(){var d,f,s,g,b,m,y,w,x,_,S,F,v,N,h,C,z,R,O,P,D,E,B;i._RF.push({},"1bdf9uStclAEJ+Si+kKvWZn","Flex",void 0);var A=a.ccclass,T=a.property,W=function(e){return e[e.None=0]="None",e[e.Row=1]="Row",e[e.Row_Reverse=2]="Row_Reverse",e[e.Column=3]="Column",e[e.Column_Reverse=4]="Column_Reverse",e}(W||{}),j=function(e){return e[e.None=0]="None",e[e.Nowrap=1]="Nowrap",e[e.Wrap=2]="Wrap",e[e.WrapReverse=3]="WrapReverse",e}(j||{}),L=function(e){return e[e.None=0]="None",e[e.FlexStart=1]="FlexStart",e[e.FlexEnd=2]="FlexEnd",e[e.Center=3]="Center",e[e.SpaceBetween=4]="SpaceBetween",e[e.SpaceAround=5]="SpaceAround",e}(L||{}),k=function(e){return e[e.None=0]="None",e[e.FlexStart=1]="FlexStart",e[e.FlexEnd=2]="FlexEnd",e[e.Center=3]="Center",e[e.SpaceBetween=4]="SpaceBetween",e[e.SpaceAround=5]="SpaceAround",e[e.Stretch=6]="Stretch",e}(k||{}),I=function(e){return e[e.None=0]="None",e[e.FlexStart=1]="FlexStart",e[e.FlexEnd=2]="FlexEnd",e[e.Center=3]="Center",e[e.Baseline=4]="Baseline",e[e.Stretch=5]="Stretch",e}(I||{});e("Flex",(d=A("Flex"),f=T({group:{name:"container",id:"1",displayOrder:1,style:"section"},tooltip:"[TODO] 属性决定主轴的方向（即项目的排列方向）。\n        row（默认值）：主轴为水平方向，起点在左端。\n        row- reverse：主轴为水平方向，起点在右端。\n        column：主轴为垂直方向，起点在上沿。\n        column - reverse：主轴为垂直方向，起点在下沿。",type:l(W)}),s=T({group:{name:"container",id:"1"},tooltip:'[TODO] 默认情况下，项目都排在一条线（又称"轴线"）上。flex-wrap属性定义，如果一条轴线排不下，如何换行。\n        nowrap（默认）：不换行。\n        wrap：换行，第一行在上方。\n        wrap-reverse：换行，第一行在下方。',type:l(j)}),g=T({group:{name:"container",id:"1"},tooltip:"[TODO]属性定义了项目在主轴上的对齐方式。\n         flex-start（默认值）：左对齐\n         flex-end：右对齐\n         center： 居中\n         space-between：两端对齐，项目之间的间隔都相等。\n         space-around：每个项目两侧的间隔相等。所以，项目之间的间隔比项目与边框的间隔大一倍。",type:l(L)}),b=T({group:{name:"container",id:"1"},tooltip:"[TODO] 属性定义了多根轴线的对齐方式。如果项目只有一根轴线，该属性不起作用。\n            flex-start：与交叉轴的起点对齐。\n            flex-end：与交叉轴的终点对齐。\n            center：与交叉轴的中点对齐。\n            space-between：与交叉轴两端对齐，轴线之间的间隔平均分布。\n            space-around：每根轴线两侧的间隔都相等。所以，轴线之间的间隔比轴线与边框的间隔大一倍。\n            stretch（默认值）：轴线占满整个交叉轴。",type:l(k)}),m=T({group:{name:"container",id:"1"},tooltip:"[TODO] 属性定义了多根轴线的对齐方式。如果项目只有一根轴线，该属性不起作用。\n            flex-start：交叉轴的起点对齐。\n            flex-end：交叉轴的终点对齐。\n            center：交叉轴的中点对齐。\n            baseline: 项目的第一行文字的基线对齐。\n            stretch（默认值）：如果项目未设置高度或设为auto，将占满整个容器的高度。",type:l(I)}),y=T({group:{name:"container",id:"1"},tooltip:"上内边距",type:u}),w=T({group:{name:"container",id:"1"},tooltip:"下内边距",type:u}),x=T({group:{name:"container",id:"1"},tooltip:"左内边距",type:u}),_=T({group:{name:"container",id:"1"},tooltip:"右内边距",type:u}),S=T({group:{name:"item",id:"2",style:"section"},type:p}),d((N=n((v=function(e){function n(){for(var n,t=arguments.length,i=new Array(t),a=0;a<t;a++)i[a]=arguments[a];return n=e.call.apply(e,[this].concat(i))||this,r(n,"flex_direction",N,o(n)),r(n,"flex_wrap",h,o(n)),r(n,"justify_content",C,o(n)),r(n,"align_content",z,o(n)),r(n,"align_items",R,o(n)),r(n,"Padding_top",O,o(n)),r(n,"Padding_bottom",P,o(n)),r(n,"Padding_left",D,o(n)),r(n,"Padding_right",E,o(n)),r(n,"order",B,o(n)),n}t(n,e);var i=n.prototype;return i.onLoad=function(){},i.onDestroy=function(){},n}(c)).prototype,"flex_direction",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return W.None}}),h=n(v.prototype,"flex_wrap",[s],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return j.None}}),C=n(v.prototype,"justify_content",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return L.None}}),z=n(v.prototype,"align_content",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return k.None}}),R=n(v.prototype,"align_items",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return I.None}}),O=n(v.prototype,"Padding_top",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),P=n(v.prototype,"Padding_bottom",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),D=n(v.prototype,"Padding_left",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),E=n(v.prototype,"Padding_right",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),B=n(v.prototype,"order",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=v))||F));i._RF.pop()}}}));

System.register("chunks:///_virtual/FullScreenArea.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var i,t,n,o,r,c,s,h;return{setters:[function(e){i=e.inheritsLoose},function(e){t=e.cclegacy,n=e._decorator,o=e.screen,r=e.view,c=e.math,s=e.UITransform,h=e.Component}],execute:function(){var u;t._RF.push({},"9fc5fZAmB5IXYgKGGwMMyVt","FullScreenArea",void 0);var a=n.ccclass;n.property,e("FullScreenArea",a("FullScreenArea")(u=function(e){function t(){return e.apply(this,arguments)||this}i(t,e);var n=t.prototype;return n.onLoad=function(){o.on("window-resize",this.updateSize,this),this.updateSize()},n.onDestroy=function(){o.off("window-resize",this.updateSize,this)},n.updateSize=function(){var e=o.windowSize,i=e.width,t=e.height,n=r.getDesignResolutionSize(),h=null,u=i/t>n.width/n.height?"HEIGHT":"WIDTH";if("HEIGHT"==u){var a=n.width/(t/(n.height/n.width))*i;h=new c.Size(a,n.height)}else if("WIDTH"==u){var l=n.height/(i/(n.width/n.height)/t);h=new c.Size(n.width,l)}else h=n;window.ccLog("screen",i,t),window.ccLog("model",u),window.ccLog("designSize",n),window.ccLog("FullScreenArea: ",h),this.getComponent(s).setContentSize(h)},t}(h))||u);t._RF.pop()}}}));

System.register("chunks:///_virtual/game_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./file.js","./message.js"],(function(e){var g,s,n;return{setters:[function(e){g=e.cclegacy},null,null,null,function(e){s=e.fileDesc},function(e){n=e.messageDesc}],execute:function(){g._RF.push({},"6f845xxHd1Bx7b9bCkrKsXS","game_pb",void 0);var c=e("file_game_game",s("Cg9nYW1lL2dhbWUucHJvdG8SC2NsaWVudC5nYW1lIjQKBlJvdGF0ZRIJCgF4GAEgASgFEgkKAXkYAiABKAUSCQoBehgDIAEoBRIJCgF3GAQgASgFIpYBChREYXRhQnJvYWRjYXN0Q29udGVudBIoCgZyb3RhdGUYASABKAsyEy5jbGllbnQuZ2FtZS5Sb3RhdGVIAIgBARIQCghzZWxlY3RlZBgCIAMoBRIZChFzZWxlY3RlZENhcmRJbmRleBgDIAEoBRISCgVpbmRleBgEIAEoBUgBiAEBQgkKB19yb3RhdGVCCAoGX2luZGV4YgZwcm90bzM"));e("RotateSchema",n(c,0)),e("DataBroadcastContentSchema",n(c,1));g._RF.pop()}}}));

System.register("chunks:///_virtual/Game.ts",["./rollupPluginModLoBabelHelpers.js","cc","./SceneLayer.ts","./index40.ts","./JoinBattle.ts","./index23.ts","./index21.ts","./msg_pb.ts","./CardInfo.ts","./Desk.ts","./create.js","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./from-binary.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./Shark.ts","./JSBridge.ts","./EnterGame.ts","./index32.ts","./GameEventConstant.b.ts","./GlobalEventConstant.ts","./AudioEffectConstant.b.ts"],(function(e){var n,t,a,i,r,o,s,u,l,c,d,f,h,p,m,g,E,_,y,b,S,w,v,I,O,T,C,G,k,A,H,L,R,x,N;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,a=e.initializerDefineProperty,i=e.assertThisInitialized,r=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){s=e.cclegacy,u=e._decorator,l=e.Node,c=e.Prefab,d=e.Button,f=e.instantiate,h=e.error},function(e){p=e.default},null,function(e){m=e.default},function(e){g=e.default},function(e){E=e.cat},function(e){_=e.SyncResponseSchema,y=e.DeskState,b=e.MeOverBroadcastSchema,S=e.SelectTargetBroadcastSchema},function(e){w=e.CardInfo},function(e){v=e.Desk},function(e){I=e.create},null,null,null,function(e){O=e.fromBinary},null,null,null,null,function(e){T=e.Shark},function(e){C=e.JSBridgeWebView,G=e.EnumJSBridgeWebView,k=e.JSBridgeClient,A=e.GameCloseType},function(e){H=e.default},function(e){L=e.GameVisiable},function(e){R=e.GameEventConstant},function(e){x=e.GlobalEventConstant},function(e){N=e.AudioEffectConstant}],execute:function(){var D,P,M,F,U,B,V,z,j,J,W,K,Y,q,Q,X,Z,$,ee;s._RF.push({},"fe7e5tOFshN+byDvE4pS57Y","Game",void 0);var ne=u.ccclass,te=u.property;e("Game",(D=ne("Game"),P=te({type:l,tooltip:"卡牌信息节点"}),M=te({type:v,tooltip:"牌桌"}),F=te({type:c,tooltip:"出局UI预制体"}),U=te({type:l,tooltip:"观战节点"}),B=te({type:T,tooltip:"鲨鱼"}),V=te({type:d,tooltip:"规则按钮"}),z=te({type:c,tooltip:"规则ui预制体"}),j=te({type:l,tooltip:"玩家节点"}),D((K=n((W=function(e){function n(){for(var n,t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];return n=e.call.apply(e,[this].concat(r))||this,a(n,"card_info_node",K,i(n)),a(n,"desk",Y,i(n)),a(n,"player_out_ui_prefab",q,i(n)),a(n,"watch_node",Q,i(n)),a(n,"shark",X,i(n)),a(n,"btn_rule",Z,i(n)),a(n,"ui_rule_prefab",$,i(n)),a(n,"players_node",ee,i(n)),n.needCleanSceneOnGameStart=!1,n.needRestartGameOnEventShow=!1,n.gameStartParams="",n}t(n,e);var s=n.prototype;return s.start=function(){var e=r(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:g.game.reset(),E.platform.inputMode(1),g.game.isParamsFromUrl?m(this.isReload):g.game.gameLoadFinished?this.isReload&&m(this.isReload):(window.ccLog("正在登录中"),E.gui.showLoading({title:"正在登录中"}),g.game.gameLoadFinished=!0,k.gameLoadFinished());case 3:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),s.onLoad=function(){this.btn_rule.node.on(d.EventType.CLICK,this.onRuleHandler,this)},s.onAutoObserver=function(){var e=this;this.addAutorun((function(){e.watch_node.active=g.user.isAudience}))},s.onDestroy=function(){E.audio.stopMusic(),E.platform.inputMode(0),g.game.reset()},s.onRuleHandler=function(){var e=f(this.ui_rule_prefab);E.gui.openUI(e)},s.onEventListener=function(){E.event.on("BattleInitialize",this.onBattleInitializeHandler,this).on("EVENT_USERINFO",this.onGameUserInfoHandler,this).on("EVENT_FINISHED_ONE",this.onGameFinishedOneHandler,this).on("EVENT_RANKING",this.onGameRankingHandler,this).on("EVENT_MEOVER",this.onGameOutHandler,this).on("EVENT_PLAYER_OVER",this.onPlayerOverHandler,this).on("PlayerStatusChanged",this.onPlayerStatusChangedHandler,this).on("EVENT_FINISHED_TWO",this.onEventFinishedHandler,this).on(R.SHOW_CARD_INFO,this.onShowCardInfoHandler,this).on(x.EVENT_SHOW,this.onShowHandler,this)},s.addListener=function(){C.on(G.GAME_START,this.onGameStart,this),C.on(G.MIKE_USERS_INFO,this.updataMikeUsersInfo,this)},s.removeListener=function(){C.off(G.GAME_START,this.onGameStart,this),C.off(G.MIKE_USERS_INFO,this.updataMikeUsersInfo,this)},s.onShowHandler=function(){this.needRestartGameOnEventShow&&(this.onGameStart(this.gameStartParams),this.needRestartGameOnEventShow=!1,this.gameStartParams="")},s.onGameStart=function(){var e=r(o().mark((function e(n){var t;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(g.global.isGameVisiable){e.next=4;break}return this.gameStartParams=n,this.needRestartGameOnEventShow=!0,e.abrupt("return");case 4:if(!this.needCleanSceneOnGameStart){e.next=8;break}return this.needCleanSceneOnGameStart=!1,e.next=8,E.gui.cleanScene();case 8:return window.ccLog("游戏开始 启动参数",n),e.prev=9,E.gui.showLoading({title:"连接服务器"}),e.next=13,H(n);case 13:g.game.gameLoadFinished=!0,m(),e.next=23;break;case 17:e.prev=17,e.t0=e.catch(9),h("监听游戏开始err",e.t0),t="未知错误...",e.t0 instanceof Error&&(t=e.t0.message),k.closeGame(A.JoinOverTime,JSON.stringify({err:e.t0,message:t,info:"游戏开始错误"}));case 23:case"end":return e.stop()}}),e,this,[[9,17]])})));return function(n){return e.apply(this,arguments)}}(),s.onBattleInitializeHandler=function(){var e=r(o().mark((function e(n){var t,a,i,r,s,u,l,c,d,f,h=this;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(E.audio.playMusic(N.BGM),t=n.data,a=n.players,i=n.started,r=n.battle,s=O(_,t),u=s.player,l=s.gameBroadcast,c=s.draw,window.ccLog("战局同步",s),window.ccLog("基础玩家",a),window.ccLog("战局初始化",i),window.ccLog("战局信息",r),d=g.game,f=new Map,d.basePlayers=a,g.game.over_time_draw=c,d.roomData=l,r&&(d.battle=r),a.forEach((function(e){f.has(e.teamId)||f.set(e.teamId,[]);var n=f.get(e.teamId);null==n||n.push(e)})),d.teams=f,window.ccLog(f),this.shark.initTooth(null==u?void 0:u.cardState),this.desk.initPlayers(),!i){e.next=24;break}return e.next=22,this.syncHandle(s);case 22:e.next=26;break;case 24:E.tracking.game.roomWait(),E.gui.showLoading({title:"等待其他玩家加入..."});case 26:k.getMikeUsersInfo().then((function(e){console.log("平台用户装扮信息========",JSON.stringify(e)),h.updataMikeUsersInfo(e)}));case 27:case"end":return e.stop()}}),e,this)})));return function(n){return e.apply(this,arguments)}}(),s.onGameUserInfoHandler=function(e){window.ccLog("------用户信息-广播",e),E.gui.hideLoading(),g.game.playerCards=[].concat(e.onHand)},s.onGameFinishedOneHandler=function(e){g.game.showActionButton=!1,E.event.dispatchEvent(R.CLOSE_COMMON_UI).dispatchEvent(R.UPDATE_PLAYER_STATE),E.audio.playEffect(N.GAME_OVER),E.gui.showLoading({title:"结算中..."}),g.global.isChannelWeb&&window.history.back()},s.onGameRankingHandler=function(e){if(!g.user.isAudience){E.event.dispatchEvent(R.CLOSE_COMMON_UI),E.audio.playEffect(N.OUT);f(this.player_out_ui_prefab)}},s.onGameOutHandler=function(e){var n;E.audio.playEffect(N.OUT),E.event.dispatchEvent(R.UPDATE_PLAYER_STATE);var t=g.user.auth.mode,a=g.user,i=a.userIndex,r=a.userTeamId;if(i==e.index&&(null==(n=g.game.getTeammateByTeamId(r))||!n.die)&&2==t){f(this.player_out_ui_prefab);E.tracking.game.gameOut()}},s.onPlayerOverHandler=function(e){this.shark.playBite()},s.playOutAnimation=function(){},s.onPlayerStatusChangedHandler=function(e){var n=g.game.getBasePlayerByUid(e.id);n&&(n.online=e.online)},s.onShowCardInfoHandler=function(e){var n=e.card;g.game.roomData.cursor!=g.user.userIndex&&this.card_info_node.getComponent(w).showCardInfo(n)},s.syncHandle=function(){var e=r(o().mark((function e(n){var t=this;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,a){var i=n.player,r=n.gameBroadcast,o=r,s=o.knownCard,u=o.cursor,l=o.preCursor,c=o.state;g.user.isAudience||null!=i&&i.onHand&&(g.game.playerCards=i.onHand,window.ccLog("同步添加手牌",[].concat(i.onHand))),c!=y.DESK_STATE_UNSPECIFIED?(c===y.DESK_STATE_FINISHED?E.gui.hideLoading().showLoading({title:"游戏已结束"}):(t.desk.init(),s.length>=1&&t.desk.addPlayCard(s[s.length-1]),g.user.isAudience||(i.die?E.event.dispatchEvent(R.UPDATE_WATCH_HANDCARD,I(b,{index:i.index})):(t.desk.syncHandCard(i.onHand),window.ccLog("同步手牌结束"))),E.event.dispatchEvent("EVENT_BROADCAST",r),c===y.DESK_STATE_GIVE_ME_YOUR_CARD&&E.event.dispatchEvent("EVENT_SELECT_TARGET_BROADCAST",I(S,{responseIndex:u,requestIndex:l})),E.gui.hideLoading()),e()):a()})));case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),s.onEventFinishedHandler=function(){var e=r(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(window.ccLog("对局结束： onEventFinishedHandler"),!g.global.isChannelWeb){e.next=4;break}return window.history.back(),e.abrupt("return");case 4:if(E.ws.destroy(),!g.global.isGameVisiable){e.next=12;break}return g.reset(),g.game.gameLoadFinished=!0,e.next=10,E.gui.cleanScene();case 10:e.next=16;break;case 12:g.reset(),g.game.gameLoadFinished=!0,g.global.gameVisiable=L.HIDE,this.needCleanSceneOnGameStart=!0;case 16:k.closeGame();case 17:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),s.updataMikeUsersInfo=function(e){var n=e;"string"==typeof e&&(n=JSON.parse(e)),console.log("平台头像信息==========",JSON.stringify(n)),g.game.paltformPlayInfo=n,E.event.dispatchEvent(R.UPDATE_PLATFORM)},n}(p)).prototype,"card_info_node",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Y=n(W.prototype,"desk",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),q=n(W.prototype,"player_out_ui_prefab",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Q=n(W.prototype,"watch_node",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),X=n(W.prototype,"shark",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Z=n(W.prototype,"btn_rule",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),$=n(W.prototype,"ui_rule_prefab",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),ee=n(W.prototype,"players_node",[j],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),J=W))||J));s._RF.pop()}}}));

System.register("chunks:///_virtual/GameEventConstant.b.ts",["cc"],(function(E){var _;return{setters:[function(E){_=E.cclegacy}],execute:function(){_._RF.push({},"2f649TPFzRFuZI8xs34K//Z","GameEventConstant.b",void 0);E("GameEventConstant",function(E){return E.SELECT_HAND_CARD="GameEventConstant/SELECT_HAND_CARD",E.CANCEL_SELECT_HAND_CARD="GameEventConstant/CANCEL_SELECT_HAND_CARD",E.HAND_CARD_SELECT_STATE_CHANGE="GameEventConstant/HAND_CARD_SELECT_STATE_CHANGE",E.SHOW_CARD_INFO="GameEventConstant/SHOW_CARD_INFO",E.SELECT_DROP_MINE_POS="GameEventConstant/SELECT_DROP_MINE_POS",E.SELECT_PLAYER="GameEventConstant/SELECT_PLAYER",E.STOP_PLAYER_Timer="GameEventConstant/STOP_PLAYER_Timer",E.SCOUNT_CARD_RESPONSE="GameEventConstant/SCOUNT_CARD_RESPONSE",E.BYPASS_CARD_RESPONSE="GameEventConstant/BYPASS_CARD_RESPONSE",E.TURN_CARD_RESPONSE="GameEventConstant/TURN_CARD_RESPONSE",E.DRAW_BOTTOM_RESPONSE="GameEventConstant/DRAW_BOTTOM_RESPONSE",E.UPDATE_STATE="GameEventConstant/UPDATE_STATE",E.CLOSE_TEAM_DROP_MINE="GameEventConstant/CLOSE_TEAM_DROP_MINE",E.UPDATE_WATCH_HANDCARD="GameEventConstant/UPDATE_WATCH_HANDCARD",E.UPDATE_PLAYER_STATE="GameEventConstant/UPDATE_PLAYER_STATE",E.CLOSE_COMMON_UI="GameEventConstant/CLOSE_COMMON_UI",E.UPDATE_FOLLOW_STATE="GameEventConstant/UPDATE_FOLLOW_STATE",E.HAND_CARD_STATE="GameEventConstant/HAND_CARD_STATE",E.CLEAR_UPGRADE_UI_CLOSE_TIME_COUNT="GameEventConstant/CLEAR_UPGRADE_UI_CLOSE_TIME_COUNT",E.GAME_CLOSE_TIMEOUT="GameEventConstant/GAME_CLOSE_TIMEOUT",E.SELECTED_TOOTH="GameEventConstant/SELECTED_TOOTH",E.DRAW_SCOUT_TIME_OUT="GameEventConstant/DRAW_SCOUT_TIME_OUT",E.TAG_TOOTH_STATE="GameEventConstant/TAG_TOOTH_STATE",E.CLOSE_RULE_UI="GameEventConstant/CLOSE_RULE_UI",E.RATATE_SHARK="GameEventConstant/RATATE_SHARK",E.CLICK_TOOTH="GameEventConstant/CLICK_TOOTH",E.SHARK_ZOOM="GameEventConstant/SHARK_ZOOM",E.SET_SHARK_ROTATE_CENTER="GameEventConstant/SET_SHARK_ROTATE_CENTER",E.UPDATE_HANDCARD_LAYOUT="GameEventConstant/UPDATE_HANDCARD_LAYOUT",E.UPDATE_PLATFORM="GameEventConstant/UPDATE_PLATFORM",E}({}));_._RF.pop()}}}));

System.register("chunks:///_virtual/GameEventConstant.ts",["cc","./GameEventConstant.b.ts"],(function(t){var n;return{setters:[function(t){n=t.cclegacy},function(n){t("GameEventConstant",n.GameEventConstant)}],execute:function(){n._RF.push({},"7f445T0tvxIK7LS+P8r32+1","GameEventConstant",void 0),n._RF.pop()}}}));

System.register("chunks:///_virtual/GetTimeDifferenceFromServer.ts",["cc","./index23.ts"],(function(e){var r,t;return{setters:[function(e){r=e.cclegacy},function(e){t=e.default}],execute:function(){e("default",(function(e){return e-t.global.ws_diff_server_timer})),r._RF.push({},"fabe3NNdxpPpp7bULU5kVp+","GetTimeDifferenceFromServer",void 0),r._RF.pop()}}}));

System.register("chunks:///_virtual/GlobalEventConstant.ts",["cc"],(function(t){var n;return{setters:[function(t){n=t.cclegacy}],execute:function(){n._RF.push({},"98f23vp9eVBp4pc8EdJKOgw","GlobalEventConstant",void 0);t("GlobalEventConstant",function(t){return t.EVENT_SHOW="GlobalEventConstant/EVENT_SHOW",t.EVENT_HIDE="GlobalEventConstant/EVENT_HIDE",t.GAME_RESIZE="GlobalEventConstant/GAME_RESIZE",t.EVENT_CLOSE="GlobalEventConstant/EVENT_CLOSE",t.ONLINE="GlobalEventConstant/ONLINE",t.OFFLINE="GlobalEventConstant/OFFLINE",t}({}));n._RF.pop()}}}));

System.register("chunks:///_virtual/growth_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./file.js","./message.js"],(function(c){var n,e,s;return{setters:[function(c){n=c.cclegacy},null,null,null,function(c){e=c.fileDesc},function(c){s=c.messageDesc}],execute:function(){n._RF.push({},"1a924HprOZBuLdAN9nQJgiO","growth_pb",void 0);var t=c("file_growth",e("Cgxncm93dGgucHJvdG8SBnByb3RvcyJEChZHcm93dGhJbmZvTm90aWZpY2F0aW9uEhQKDGdyb3d0aF92YWx1ZRgBIAEoBBIUCgxncm93dGhfbGV2ZWwYAiABKA1CMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z"));c("GrowthInfoNotificationSchema",s(t,0));n._RF.pop()}}}));

System.register("chunks:///_virtual/Gui.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./SceneLayer.ts","./UILayer.ts","./Toast.ts","./index44.ts","./UIContainer.ts","./ShowLoading.ts","./Reconnection.ts","./Notice.ts","./index23.ts"],(function(t){var n,e,o,i,r,a,c,u,s,l,p,_,d,h,f,m,v,g,b,y,w,T,I,L,S,E,N;return{setters:[function(t){n=t.applyDecoratedDescriptor,e=t.inheritsLoose,o=t.initializerDefineProperty,i=t.assertThisInitialized,r=t.asyncToGenerator,a=t.regeneratorRuntime,c=t.extends},function(t){u=t.cclegacy,s=t._decorator,l=t.Prefab,p=t.Node,_=t.director,d=t.instantiate,h=t.Director,f=t.warn,m=t.error},function(t){v=t.BaseComponent},function(t){g=t.default},function(t){b=t.default},function(t){y=t.ToastType,w=t.Toast},function(t){T=t.GlobalEventConstant},function(t){I=t.UIContainer},function(t){L=t.ShowLoading},function(t){S=t.Reconnection},function(t){E=t.Notice},function(t){N=t.default}],execute:function(){var U,C,k,O,R,x,A,D,z,P,B,G,M,H,F,K,V,j,J,Z,q,Q,W;u._RF.push({},"a4eeaB3ILVI5ZHwShhAJmKw","Gui",void 0);var X=s.ccclass,Y=s.property;t("LayerType",function(t){return t[t.UI=0]="UI",t[t.LOADING=1]="LOADING",t[t.TOAST=2]="TOAST",t[t.RECONNECTTION=3]="RECONNECTTION",t[t.NOTICE=4]="NOTICE",t}({})),t("Gui",(U=X("Gui"),C=Y({type:l,tooltip:"断线重连UI预制体"}),k=Y({type:l,tooltip:"提示UI预制体"}),O=Y({type:l,tooltip:"加载UI预制体"}),R=Y({type:l,tooltip:"公告UI预制体"}),x=Y({type:l,tooltip:"UI层预制体"}),A=Y({type:l,tooltip:"导出日志预制体"}),D=Y({type:p,tooltip:"root-UI层"}),z=Y({type:p,tooltip:"root-组件层"}),P=Y({type:p,tooltip:"log调试组件层"}),B=Y({type:p,tooltip:"root-mask"}),U((H=n((M=function(t){function n(){for(var n,e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return n=t.call.apply(t,[this].concat(r))||this,o(n,"reconnection_ui_prefab",H,i(n)),o(n,"toast_ui_prefab",F,i(n)),o(n,"loading_ui_prefab",K,i(n)),o(n,"notice_ui_prefab",V,i(n)),o(n,"ui_prefab",j,i(n)),o(n,"exportLogEntry_prefab",J,i(n)),o(n,"root_ui",Z,i(n)),o(n,"root_toast",q,i(n)),o(n,"root_log",Q,i(n)),o(n,"root_mask",W,i(n)),n.cat=void 0,n.ui_container_component=void 0,n.reconnection_ui_component=void 0,n.notice_ui_component=void 0,n.loading_ui_component=void 0,n.toast_ui_component=void 0,n.currentScene=void 0,n}e(n,t);var u=n.prototype;return u.onEventListener=function(){this.cat.event.on(T.ROOT_MASK_UPDATE,this.onRootUpdate,this)},u.init=function(t){this.cat=t;var n=_.getScene();return this.root_log.active=N.global.isDebugOrTestEnv,window.ccLog("init scene"),n&&this.changeScene(n),this},u.start=function(){this.onRootUpdate()},u.showToast=function(t){var n=t.title,e=void 0===n?"":n,o=t.type,i=void 0===o?y.SLIDE:o,r=t.fixed_time,a=void 0===r?2:r,c=d(this.toast_ui_prefab);return this.toast_ui_component=c.getComponent(w).addToParent(this.root_toast,{props:{title:e,type:i,fixed_time:a}}),this},u.hideToast=function(){var t;return null==(t=this.toast_ui_component)||t.removeAndDestroy(),this},u.showLoading=function(t){var n=void 0===t?{}:t,e=n.title,o=void 0===e?"":e,i=n.mask,r=void 0===i||i,a=n.black,c=void 0===a||a;if(this.loading_ui_component)this.loading_ui_component.setOptions({props:{title:o,mask:r,black:c}});else{var u=d(this.loading_ui_prefab);this.loading_ui_component=u.getComponent(L).addToParent(this.root_ui,{props:{title:o,mask:r,black:c}})}return this},u.hideLoading=function(){var t;return null==(t=this.loading_ui_component)||t.removeAndDestroy(),this.loading_ui_component=null,this},u.showReconnect=function(t){if(this.reconnection_ui_component)this.reconnection_ui_component.setUpdateProps({content:t});else{var n=d(this.reconnection_ui_prefab);this.reconnection_ui_component=n.getComponent(S).addToParent(this.root_ui,{props:{content:t}})}return this},u.hideReconnect=function(){var t;return null==(t=this.reconnection_ui_component)||t.removeAndDestroy(),this.reconnection_ui_component=null,this},u.showNotice=function(t){var n=d(this.notice_ui_prefab);return this.notice_ui_component=n.getComponent(E).addToParent(this.root_ui,{props:t}),this},u.hideNotice=function(){var t;return null==(t=this.notice_ui_component)||t.removeAndDestroy(),this},u.loadScene=function(t,n,e){var o=this;return new Promise((function(i,r){var a,c;window.ccLog("加载场景",t,n,e);var u={},s=null!=e&&e;"boolean"==typeof n?s=n:u=null!=n?n:{},window.ccLog(null==(a=_.getScene())?void 0:a.uuid,null==(c=_.getScene())?void 0:c.name),_.once(h.EVENT_BEFORE_SCENE_LAUNCH,(function(t){window.ccLog("Director.EVENT_BEFORE_SCENE_LAUNCH",t),o.changeScene(t,u,s),i()})),_.loadScene(t,(function(t,n){!t&&n||r(t)}))}))},u.resetScene=function(t){return void 0===t&&(t=""),t=t||this.currentScene,this.loadScene(t)},u.cleanScene=function(){var t=r(a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.resetScene();case 2:this.hideLoading().hideNotice().hideReconnect().hideToast();case 3:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}(),u.changeScene=function(t,n,e){this.currentScene=t.name,this.createUILayer(t);var o=t.getComponentInChildren(g);o&&(o.isReload=null!=e&&e),null==o||o.setOptions(n)},u.createUILayer=function(t){var n;null==(n=this.ui_container_component)||n.removeAndDestroy(),this.ui_container_component=null;var e=d(this.ui_prefab);this.ui_container_component=e.getComponent(I).setGui(this).addToParent(t)},u.reloadScene=function(){this.loadScene(this.currentScene,!0)},u.closeUI=function(){var t=r(a().mark((function t(n,e){var o,i,r,u,s;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(window.ccLog("closeUI",n.name),o=this.findUIBaseLayer(n,!1),!(i=o.component)){t.next=7;break}return i.setOptions(c({},null!=(r=null==e?void 0:e.hook)?r:{},null!=(u=null==e?void 0:e.props)?u:{})),t.next=6,i.hideTween(e||{isMotion:!1});case 6:null==(s=this.ui_container_component)||s.subMask();case 7:case"end":return t.stop()}}),t,this)})));return function(n,e){return t.apply(this,arguments)}}(),u.openUI=function(){var t=r(a().mark((function t(n,e){var o,i,r,c,u,s;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(i=this.findUIBaseLayer(n,!0),r=i.rootNode,c=i.component,r.getComponent(b),null==c||c.setOptions(e),r&&(null==(u=this.ui_container_component)||u.addNodeToTween(r),null==(s=this.ui_container_component)||s.addMask(null==e?void 0:e.noMask)),!c){t.next=7;break}return t.next=7,c.showTween(e||{});case 7:(null==(o=this.ui_container_component)?void 0:o.ui_container)&&(null==c||c.addToParent(this.ui_container_component.ui_container));case 8:case"end":return t.stop()}}),t,this)})));return function(n,e){return t.apply(this,arguments)}}(),u.findUIBaseLayer=function(t,n){var e,o=null;if(t instanceof p){e=t;var i=t.components.filter((function(t){return t instanceof b}));if(!i.length)return m(t.name+"节点未找到继承自BaseLayer的组件"),{rootNode:e,component:o};1==i.length?o=i[0]:f(t.name+"节点存在多个继承自BaseLayer的组件")}else if(e=t.node,o=t,n){for(;e.parent;)e=e.parent;o.root=e}else e=t.root;return{rootNode:e,component:o}},u.onRootUpdate=function(){window.ccLog("onRootUpdate");var t=this.root_ui.children.length;if(this.root_mask.active=t>1,t>1){var n=this.root_ui.children[t-2];this.root_mask.setSiblingIndex(n.getSiblingIndex())}this.cat.event.dispatchEvent(T.ROOT_MASK_CHANGE)},n}(v)).prototype,"reconnection_ui_prefab",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),F=n(M.prototype,"toast_ui_prefab",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),K=n(M.prototype,"loading_ui_prefab",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),V=n(M.prototype,"notice_ui_prefab",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),j=n(M.prototype,"ui_prefab",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),J=n(M.prototype,"exportLogEntry_prefab",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Z=n(M.prototype,"root_ui",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),q=n(M.prototype,"root_toast",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Q=n(M.prototype,"root_log",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),W=n(M.prototype,"root_mask",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),G=M))||G));u._RF.pop()}}}));

System.register("chunks:///_virtual/HandCard.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseHandCard.ts","./index21.ts","./index40.ts","./msg_pb.ts","./index23.ts","./index3.js","./reflect.js","./descriptors.js","./create.js","./descriptor_pb.js","./binary-encoding.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./GameEventConstant.b.ts","./msg_pb.js"],(function(e){var t,n,a,r,s,i,o,d,l,u,c,p,_,m,h,y,f,C,v;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,a=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){s=e.cclegacy,i=e._decorator,o=e.Node,d=e.SpriteFrame,l=e.error,u=e.Sprite},function(e){c=e.BaseHandCard},function(e){p=e.cat},null,function(e){_=e.WatchRequestSchema,m=e.WatchResponseSchema},function(e){h=e.default},null,function(e){y=e.reflect},null,function(e){f=e.create},null,null,null,null,null,null,function(e){C=e.GameEventConstant},function(e){v=e.BattleMode}],execute:function(){var N,E,g,A,O,H,b;s._RF.push({},"a2bd0mOsSNBvrnca+kgEpNN","HandCard",void 0);var R=i.ccclass,T=i.property,k=e("PlayerHandCardState",function(e){return e[e.NONE=0]="NONE",e[e.NO_CARD=1]="NO_CARD",e[e.Team=2]="Team",e[e.Enemy=3]="Enemy",e}({}));e("HandCard",(N=R("HandCard"),E=T({type:o,tooltip:"玩家牌桌状态节点"}),g=T({type:[d],tooltip:"玩家牌桌状态精灵图"}),N((H=t((O=function(e){function t(){for(var t,n=arguments.length,s=new Array(n),i=0;i<n;i++)s[i]=arguments[i];return t=e.call.apply(e,[this].concat(s))||this,a(t,"player_desk_state_node",H,r(t)),a(t,"player_desk_state_spriteFrame",b,r(t)),t.data={state:k.NONE},t}n(t,e);var s=t.prototype;return s.onEventListener=function(){e.prototype.onEventListener.call(this),p.event.on("EVENT_RANKING",this.onGameRankingHandler,this).on("EVENT_MEOVER",this.onGameTeamOutHandler,this).on(C.UPDATE_WATCH_HANDCARD,this.onUpdateWatchHandCardHandler,this).on(C.HAND_CARD_STATE,this.onHandCardState,this)},s.onHandCardState=function(e){this.list.active=e,window.ccLog("显示手牌")},s.onGameRankingHandler=function(e){this.data.state=k.Enemy},s.onGameTeamOutHandler=function(e){var t=this,n=h.user.auth.mode,a=h.user,r=a.userTeamId,s=a.userIndex;if(n!=v.PVP_SOLO&&e.index==s){var i=h.game.getTeammateByTeamId(r);window.ccLog("观战",i),!i||null!=i&&i.die?this.data.state=k.Enemy:(this.data.state=k.Team,h.user.isAudience?p.ws.Watch(i.id):p.ws.Request("Watch",y(_,f(_)),m).then((function(e){t.clearCard().onceAddCard(e.handCard)})))}},s.onUpdateWatchHandCardHandler=function(e){var t=h.user.auth.mode;t==v.PVP_SOLO?this.data.state=k.Enemy:t==v.PVP_COOPERATION?this.onGameTeamOutHandler(e):l("Invalid mode")},s.onAutoObserver=function(){var t=this;e.prototype.onAutoObserver.call(this),h.user.isAudience||(this.addAutorun([function(){var e=h.game.getPlayerByIndex(h.user.userIndex);t.data.state=h.game.playerCards.length?null!=e&&e.die?k.Team:k.NONE:k.NO_CARD}]),this.addReaction((function(){return t.data.state}),(function(e){window.ccLog("设置牌桌状态",e),e===k.NONE?t.player_desk_state_node.active=!1:(t.player_desk_state_node.active=!0,e===k.Enemy?(t.player_desk_state_node.getComponent(u).spriteFrame=t.player_desk_state_spriteFrame[2],t.player_desk_state_node.active=!h.user.isAudience,t.list.removeAllChildren()):(t.player_desk_state_node.getComponent(u).spriteFrame=t.player_desk_state_spriteFrame[e===k.NO_CARD?0:1],t.player_desk_state_node.active=!(h.user.isAudience&&e!==k.NO_CARD)))}),{fireImmediately:!0}))},t}(c)).prototype,"player_desk_state_node",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=t(O.prototype,"player_desk_state_spriteFrame",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),A=O))||A));s._RF.pop()}}}));

System.register("chunks:///_virtual/handler_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./file.js","./service.js","./msg_pb.ts"],(function(c){var l,u,Z,b;return{setters:[function(c){l=c.cclegacy},null,null,null,function(c){u=c.fileDesc},function(c){Z=c.serviceDesc},function(c){b=c.file_shark_suileyoo_v1_msg}],execute:function(){l._RF.push({},"affffhZttxBPrNmSQBWHIUq","handler_pb",void 0);var V=c("file_shark_suileyoo_v1_handler",u("Ch9zaGFyay9zdWlsZXlvby92MS9oYW5kbGVyLnByb3RvEhFzaGFyay5zdWlsZXlvby52MTKGCAoMc2hhcmtTZXJ2aWNlElwKC1JlZnJlc2hDYXJkEiUuc2hhcmsuc3VpbGV5b28udjEuUmVmcmVzaENhcmRSZXF1ZXN0GiYuc2hhcmsuc3VpbGV5b28udjEuUmVmcmVzaENhcmRSZXNwb25zZRJTCghEcmF3Q2FyZBIiLnNoYXJrLnN1aWxleW9vLnYxLkRyYXdDYXJkUmVxdWVzdBojLnNoYXJrLnN1aWxleW9vLnYxLkRyYXdDYXJkUmVzcG9uc2USXAoLRm91bmRPbkhhbmQSJS5zaGFyay5zdWlsZXlvby52MS5Gb3VuZE9uSGFuZFJlcXVlc3QaJi5zaGFyay5zdWlsZXlvby52MS5Gb3VuZE9uSGFuZFJlc3BvbnNlElMKCFBvc3RDYXJkEiIuc2hhcmsuc3VpbGV5b28udjEuUG9zdENhcmRSZXF1ZXN0GiMuc2hhcmsuc3VpbGV5b28udjEuUG9zdENhcmRSZXNwb25zZRJfCgxTZWxlY3RUYXJnZXQSJi5zaGFyay5zdWlsZXlvby52MS5TZWxlY3RUYXJnZXRSZXF1ZXN0Gicuc2hhcmsuc3VpbGV5b28udjEuU2VsZWN0VGFyZ2V0UmVzcG9uc2USZQoOR2l2ZU1lWW91ckNhcmQSKC5zaGFyay5zdWlsZXlvby52MS5HaXZlTWVZb3VyQ2FyZFJlcXVlc3QaKS5zaGFyay5zdWlsZXlvby52MS5HaXZlTWVZb3VyQ2FyZFJlc3BvbnNlElYKCUNsYW1wQ2FyZBIjLnNoYXJrLnN1aWxleW9vLnYxLkNsYW1wQ2FyZFJlcXVlc3QaJC5zaGFyay5zdWlsZXlvby52MS5DbGFtcENhcmRSZXNwb25zZRJHCgRFeGl0Eh4uc2hhcmsuc3VpbGV5b28udjEuRXhpdFJlcXVlc3QaHy5zaGFyay5zdWlsZXlvby52MS5FeGl0UmVzcG9uc2USYgoNRGF0YUJyb2FkY2FzdBInLnNoYXJrLnN1aWxleW9vLnYxLkRhdGFCcm9hZGNhc3RSZXF1ZXN0Giguc2hhcmsuc3VpbGV5b28udjEuRGF0YUJyb2FkY2FzdFJlc3BvbnNlEmUKDlNlbGVjdERpYWdub3NlEiguc2hhcmsuc3VpbGV5b28udjEuU2VsZWN0RGlhZ25vc2VSZXF1ZXN0Gikuc2hhcmsuc3VpbGV5b28udjEuU2VsZWN0RGlhZ25vc2VSZXNwb25zZRJcCgtVaUJyb2FkY2FzdBIlLnNoYXJrLnN1aWxleW9vLnYxLlVpQnJvYWRjYXN0UmVxdWVzdBomLnNoYXJrLnN1aWxleW9vLnYxLlVpQnJvYWRjYXN0UmVzcG9uc2ViBnByb3RvMw",[b]));c("sharkService",Z(V,0));l._RF.pop()}}}));

System.register("chunks:///_virtual/IGame.b.ts",["cc","./msg_pb.ts"],(function(C){var R,A,_;return{setters:[function(C){R=C.cclegacy,A=C.v2},function(C){_=C.Card}],execute:function(){var E;R._RF.push({},"d26e1B6VEZHc7sbA7czottK","IGame.b",void 0);C("HandCardStatus",function(C){return C[C.SHOW=0]="SHOW",C[C.HIDE=1]="HIDE",C}({}));var D=C("CardInfo",function(C){return C.CARD_PREDICTION="",C.CARD_SHUFFLE="",C.CARD_SCOUT="选择2颗牙齿诊断安全or危险",C.CARD_GIVE_ME_YOUR_CARD="指定一名玩家抽取其一张手牌",C.CARD_EXCHANGE="指定一名玩家与其交换手牌",C.CARD_BYPASS="跳过你的回合",C.CARD_TURN="跳过你的回合并改变回合顺序",C.CARD_THROW_LANDMINE="跳过你的回合，将回合转移给被甩锅玩家",C.CARD_DOUBLE_BYPASS="跳过本回合，到后2位玩家",C.CARD_REMOVE_LANDMINE="被咬时使用可逃生",C.CARD_RESISTANCE="被其他玩家使用指向性卡牌时自动使用，不能主动打出",C.CARD_CURSE="指定一名玩家，让其下一次按牙操作多按一颗牙",C.CARD_BLESSING="使用后自动按下一颗好牙",C.CARD_CLAMP="指定一颗牙后拔除",C}({}));C("CardInfoMap",((E={})[_.PREDICTION]=D.CARD_PREDICTION,E[_.SCOUT]=D.CARD_SCOUT,E[_.GIVE_ME_YOUR_CARD]=D.CARD_GIVE_ME_YOUR_CARD,E[_.EXCHANGE]=D.CARD_EXCHANGE,E[_.SHUFFLE]=D.CARD_SHUFFLE,E[_.BYPASS]=D.CARD_BYPASS,E[_.TURN]=D.CARD_TURN,E[_.THROW_LANDMINE]=D.CARD_THROW_LANDMINE,E[_.DOUBLE_BYPASS]=D.CARD_DOUBLE_BYPASS,E[_.REMOVE_LANDMINE]=D.CARD_REMOVE_LANDMINE,E[_.RESISTANCE]=D.CARD_RESISTANCE,E[_.CURSE]=D.CARD_CURSE,E[_.BLESSING]=D.CARD_BLESSING,E[_.CLAMP]=D.CARD_CLAMP,E)),C("GameModelSinglePosMap",{2:[A(-416,-200),A(0,430)],3:[A(-416,-200),A(-200,430),A(200,430)],4:[A(-416,-200),A(-300,420),A(0,460),A(300,420)],5:[A(-416,-200),A(-380,400),A(-130,490),A(130,490),A(380,390)],6:[A(-416,-200),A(-416,400),A(-220,490),A(0,520),A(220,490),A(416,400)]}),C("GameModelTeamPosMap",{2:[A(400,-280),A(-400,-280),A(-140,650),A(112,650)],3:[A(400,-280),A(-400,-280),A(-370,630),A(-120,700),A(122,700),A(370,630)]}),C("GameSingle",{2:"1V1",3:"1V1V1",4:"1V1V1V1",5:"1V1V1V1V1",6:"1V1V1V1V1V1"}),C("GameTeam",{2:"2v2",3:"2v2v2"}),C("CardChineseName",{0:"未知牌",1:"炸弹牌",2:"探雷牌",3:"侦查牌",4:"索取牌",5:"交换牌",6:"洗牌",7:"绕过牌",8:"调头牌",9:"抛雷牌",10:"绕过x2牌",12:"拆除牌"}),C("GameCloseType",function(C){return C.JoinOverTime="join_over_time",C.GameOver="game_over",C}({})),C("CardDistribution",{2:10,3:12,4:14,5:16,6:18});R._RF.pop()}}}));

System.register("chunks:///_virtual/IGame.ts",["cc","./IGame.b.ts"],(function(e){var a;return{setters:[function(e){a=e.cclegacy},function(a){var n={};n.CardChineseName=a.CardChineseName,n.CardDistribution=a.CardDistribution,n.CardInfo=a.CardInfo,n.CardInfoMap=a.CardInfoMap,n.GameCloseType=a.GameCloseType,n.GameModelSinglePosMap=a.GameModelSinglePosMap,n.GameModelTeamPosMap=a.GameModelTeamPosMap,n.GameSingle=a.GameSingle,n.GameTeam=a.GameTeam,n.HandCardStatus=a.HandCardStatus,e(n)}],execute:function(){a._RF.push({},"8e1d6dpwMtMpYSEKIXexpHw","IGame",void 0),a._RF.pop()}}}));

System.register("chunks:///_virtual/index.b.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index3.js","./BaseStore.ts","./msg_pb.ts","./create.js","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./PlayerItem.ts","./bundle.js","./msg_pb.js"],(function(e){var t,r,n,a,o,i,l,s,u,d,f,c,y,h;return{setters:[function(e){t=e.inheritsLoose,r=e.createClass,n=e.assertThisInitialized},function(e){a=e.cclegacy},null,function(e){o=e.BaseStore},function(e){i=e.Card,l=e.CardState,s=e.GameBroadcastSchema},function(e){u=e.create},null,null,null,null,null,null,null,function(e){d=e.PlayerItem},function(e){f=e.makeObservable,c=e.observable,y=e.observe},function(e){h=e.BattleSchema}],execute:function(){a._RF.push({},"05e18fdspdAurbkzU2bWa+L","index.b",void 0);var m=e("ClickToothType",function(e){return e[e.UNSPECIFIED=0]="UNSPECIFIED",e[e.DRAW=1]="DRAW",e[e.SCOUT=2]="SCOUT",e[e.CLAMP=3]="CLAMP",e}({}));e("default",function(e){function a(t){var r;return(r=e.call(this,t)||this).roomData=u(s),r.lastRoomData=u(s),r.playersPos=new Map,r.playerCards=[],r.result=null,r.over_finished=!1,r.player_teeth=[],r.userTurn=!1,r.over_time_draw=null,r.showActionButton=!1,r.is_allow_click_tooth=!1,r.basePlayers=[],r.battle=u(h),r.teams=new Map,r.click_tooth_type=m.UNSPECIFIED,r.selected_tooth=[],r.is_bite=!1,r.gameLoadFinished=!1,r.isParamsFromUrl=!1,r.giveMeYourCardSelectedCardIndex=void 0,r.paltformPlayInfo=[],r.teamCount=void 0,r.playerInPerTeam=void 0,r.isOpenChat=!1,f(n(r),{roomData:c,lastRoomData:c,showActionButton:c,selected_tooth:c,playerCards:c,is_bite:c,basePlayers:c,giveMeYourCardSelectedCardIndex:c}),y(n(r),"roomData",(function(e){e.newValue;var t=e.oldValue;r.lastRoomData=t})),r}t(a,e);var o=a.prototype;return o.getPlatUserWave=function(e){for(var t=0;t<this.paltformPlayInfo.length;t++)if(this.paltformPlayInfo[t].userId==Number(e))return this.paltformPlayInfo[t].mikeSoundWave;return""},o.getPlatUserAvatarFrame=function(e){for(var t=0;t<this.paltformPlayInfo.length;t++)if(this.paltformPlayInfo[t].userId==Number(e))return this.paltformPlayInfo[t].avatarFrame;return""},o.getPlatUserVipLevel=function(e){for(var t=0;t<this.paltformPlayInfo.length;t++)if(this.paltformPlayInfo[t].userId==Number(e))return this.paltformPlayInfo[t].mtyMemberLevel;return 0},o.getBasePlayerByUid=function(e){return this.basePlayers.find((function(t){return t.id===e}))},o.getPlayerComponentByIndex=function(e,t){var r=e.children.find((function(e){return e.getComponent(d).props.player.index==t}));return null==r?void 0:r.getComponent(d)},o.getPlayerByIndex=function(e){return this.roomData.players.find((function(t){return t.index==e}))},o.getTeammateByTeamId=function(e){var t=this;return this.roomData.players.find((function(r){return r.teamId===e&&r.index!==t.rootStore.user.userIndex}))},o.removeCard=function(e){var t=this.playerCards.findIndex((function(t){return e==t}));-1!==t&&this.playerCards.splice(t,1)},o.addCard=function(e){var t,r=e.filter((function(e){return e!=i.LANDMINE}));(t=this.playerCards).push.apply(t,r)},o.clearCard=function(){return this.playerCards=[],this},o.isUserOrWatchTeammate=function(e){var t=this.getPlayerByIndex(e),r=this.getPlayerByIndex(this.rootStore.user.userIndex);return e==(null==r?void 0:r.index)||(null==r?void 0:r.die)&&!(null!=t&&t.die)&&(null==t?void 0:t.teamId)===(null==r?void 0:r.teamId)},o.isWatchTeammate=function(e){var t=this.getPlayerByIndex(e),r=this.getPlayerByIndex(this.rootStore.user.userIndex);return(null==t?void 0:t.teamId)===(null==r?void 0:r.teamId)&&e!=(null==r?void 0:r.index)&&(null==r?void 0:r.die)&&!(null!=t&&t.die)},o.isUserOrTeammate=function(e){var t=this.getPlayerByIndex(e),r=this.getPlayerByIndex(this.rootStore.user.userIndex);return e==(null==r?void 0:r.index)||(null==t?void 0:t.teamId)===(null==r?void 0:r.teamId)},o.getPlayerByKey=function(e,t){return this.basePlayers.find((function(r){return r[e]===t}))},o.reset=function(){this.result=null,this.over_finished=!1,this.roomData=u(s)},r(a,[{key:"getUnusedTeeth",get:function(){return this.player_teeth.filter((function(e){return e!==l.USED}))}},{key:"getUserPlayer",get:function(){var e=this.rootStore.user;return e.isAudience?this.getPlayerByKey("relBattleOffset",-1):this.getPlayerByKey("uuid",e.auth.token)}},{key:"getPlayerDrawCount",get:function(){var e=this.getPlayerByIndex(this.roomData.cursor),t=this.roomData.deskRemainingCards;return Math.min(((null==e?void 0:e.curseCount)||0)+1,t)}},{key:"getLastCardOnPostZone",get:function(){var e=this.roomData.knownCard;return e[e.length-1]}}]),a}(o));a._RF.pop()}}}));

System.register("chunks:///_virtual/index.b2.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index3.js","./index27.ts","./index21.ts","./BaseWebSocket.ts","./msg_pb.ts","./report_battle_pb.ts","./msg_pb.js"],(function(e){var t,n,s,a,r,o,c,i,E,T,p,S,_,l,v,u,y,A,d,R,m,B,h,N,O,f,w,V,C,D,P,g,b;return{setters:[function(e){t=e.inheritsLoose,n=e.extends,s=e.classPrivateFieldLooseBase,a=e.assertThisInitialized,r=e.classPrivateFieldLooseKey},function(e){o=e.cclegacy,c=e.error},null,function(e){i=e.WrapperSocialGameClient},null,function(e){E=e.BaseWebSocket},function(e){T=e.PostBroadcastSchema,p=e.SelectTargetBroadcastSchema,S=e.TeamPredictionBroadcastSchema,_=e.TeamScoutBroadcastSchema,l=e.DrawBottomBroadcastSchema,v=e.GiveMeYourCardBroadcastSchema,u=e.ResponseExchangeBroadcastSchema,y=e.ThrowLandmineBroadcastSchema,A=e.CurseBroadcastSchema,d=e.ExitBroadcastSchema,R=e.GameBroadcastSchema,m=e.MeOverBroadcastSchema,B=e.PlayerSchema,h=e.DrawSchema,N=e.TeammateRemoveBroadcastSchema,O=e.selectDiagnoseBroadcastSchema,f=e.ClampBroadcastSchema,w=e.DataBroadcastRequestSchema,V=e.UiBroadcastSchema},function(e){C=e.ReportGameStateRequest_GameSettlePayloadSchema,D=e.ReportGameStateRequestSchema},function(e){P=e.BattleInitialDataSchema,g=e.PlayerSchema,b=e.WatchBroadcastMessageSchema}],execute:function(){o._RF.push({},"10091AsM/xO1q4Fr70vkXIS","index.b",void 0);var I=e("businessEventResponsePairs",[{event:"EVENT_POST",responseType:T},{event:"EVENT_SELECT_TARGET_BROADCAST",responseType:p},{event:"EVENT_TEAM_PREDICTION_BROADCAST",responseType:S},{event:"EVENT_TEAM_SCOUT_BROADCAST",responseType:_},{event:"EVENT_DRAW_BOTTOM_BROADCAST",responseType:l},{event:"EVENT_GIVE_ME_YOUR_CARD_BROADCAST",responseType:v},{event:"EVENT_RESPONSE_EXCHANGE_BROADCAST",responseType:u},{event:"EVENT_THROW_LANDMINE_BROADCAST",responseType:y},{event:"EVENT_CURSE_BROADCAST",responseType:A},{event:"EVENT_EXIT_BROADCAST",responseType:d},{event:"EVENT_BROADCAST",responseType:R},{event:"EVENT_FINISHED_ONE",responseType:C},{event:"EVENT_FINISHED_TWO",responseType:D},{event:"EVENT_MEOVER",responseType:m},{event:"EVENT_USERINFO",responseType:B},{event:"EVENT_DRAW",responseType:h},{event:"EVENT_TEAMMATE_REMOVE",responseType:N},{event:"EVENT_RANKING",responseType:C},{event:"EVENT_ME_OVER_MESSAGE",responseType:m},{event:"EVENT_SELECT_DIAGNOSE",responseType:O},{event:"EVENT_CLAMP_BROADCAST",responseType:f},{event:"EVENT_PLAYER_OVER",responseType:m},{event:"EVENT_DATA_BROADCAST",responseType:w},{event:"EVENT_UI_BROADCAST",responseType:V}]),M=r("registerServerListen"),L=r("baseEventResponsePairs"),x=r("businessEventResponsePairs"),G=e("SocialGameBusinessSocket",function(e){function r(t){var r;return(r=e.call(this,t)||this).create=function(e,t){if(null!==r.ins)throw new Error("[ws] is alreay exist! please invoke destroy first");return r.ins=new i(e,n({},t,{isAutoConnect:!1})),window.ccLog("------创建"),s(a(r),M)[M](),window.ccLog("------注册"),r.cat.ws},Object.defineProperty(a(r),M,{writable:!0,value:function(){[].concat(s(a(r),L)[L],s(a(r),x)[x]).forEach((function(e){var t;null==(t=r.ins)||t.listen(e.event,e.responseType,null==e?void 0:e.cb)}))}}),Object.defineProperty(a(r),L,{writable:!0,value:[{event:"BattleInitialize",responseType:P},{event:"PlayerStatusChanged",responseType:g},{event:"WatchBroadcast",responseType:b}]}),Object.defineProperty(a(r),x,{writable:!0,value:I}),r}return t(r,e),r}(E)),W={get:function(e,t,n){if(e.hasOwnProperty(t))return Reflect.get(e,t,n);if(e.ins)return function(){for(var s=arguments.length,a=new Array(s),r=0;r<s;r++)a[r]=arguments[r];return Reflect.get(e.ins,t,n).apply(e.ins,a)};var s=new Error("[ws] is not available, please invoke [create]");throw c(s),s},set:function(e,t,n){return"string"==typeof t&&(e[t]=n,window.ccLog("Setting property "+t+" to "+n),!0)}};e("createProxySocket",(function(e){var t=new G(e);window.ccLog("ws instance");var n=new Proxy(t,W);return window.ccLog("ws proxy"),n}));o._RF.pop()}}}));

System.register("chunks:///_virtual/index.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index3.js","./index48.ts","./index23.ts","./index45.ts","./Bridge.ts","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./from-json.js","./JSBridge.ts","./msg_pb.js"],(function(t){var n,e,r,s,i,u,a,o,c,p,f;return{setters:[function(t){n=t.inheritsLoose,e=t.asyncToGenerator,r=t.regeneratorRuntime},function(t){s=t.cclegacy},null,function(t){i=t.H5API},function(t){u=t.default},function(t){a=t.default},function(t){o=t.Bridge},null,null,null,null,null,null,null,function(t){c=t.fromJsonString},function(t){p=t.JSBridgeClient},function(t){f=t.ClientBootParamSchema}],execute:function(){s._RF.push({},"020eem/6AtC4q2hfWYcs07N","index",void 0);t("SuiLeYooH5API",function(t){function s(){return t.apply(this,arguments)||this}n(s,t);var i=s.prototype;return i.serverLogin=function(){var t=e(r().mark((function t(){var n,e;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=this.cat.util.stringUtil.getURLParameters(decodeURIComponent(window.location.href)),e=c(f,n.params),u.user.auth=e,window.ccLog("authParams",n,e);case 4:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}(),i.matchRoom=function(t){return this.cat.api.suileyoo.matchRoom(t)},i.getAssets=function(){var t=e(r().mark((function t(){var n;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.cat.api.suileyoo.getAssets();case 2:n=t.sent,u.user.assets=n.point;case 4:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}(),i.searchRoom=function(){var t=e(r().mark((function t(n){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",this.cat.api.suileyoo.searchRoom(n));case 1:case"end":return t.stop()}}),t,this)})));return function(n){return t.apply(this,arguments)}}(),i.getUserInfo=function(){var t=e(r().mark((function t(n){var e;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.cat.api.suileyoo.getUserInfo(n);case 2:e=t.sent,u.user.userGameData=e;case 4:case"end":return t.stop()}}),t,this)})));return function(n){return t.apply(this,arguments)}}(),i.getUserProfile=function(){var t=e(r().mark((function t(n){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.cat.api.suileyoo.getUserProfile(n);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t,this)})));return function(n){return t.apply(this,arguments)}}(),i.getMikeList=function(){var t=e(r().mark((function t(n){var s=this;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise(e(r().mark((function t(e,i){var u;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,s.cat.api.suileyoo.getMikeList(n);case 2:u=t.sent,a.nativeAPI.dispatchEvent(o.EventName.APIName.MIKE_LIST,{code:0,data:u}),e(u);case 5:case"end":return t.stop()}}),t)})))));case 1:case"end":return t.stop()}}),t)})));return function(n){return t.apply(this,arguments)}}(),i.joinMike=function(){var t=e(r().mark((function t(n){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})));return function(n){return t.apply(this,arguments)}}(),i.ready=function(){var t=e(r().mark((function t(n){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.cat.api.suileyoo.ready(n);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t,this)})));return function(n){return t.apply(this,arguments)}}(),i.unReady=function(){var t=e(r().mark((function t(n){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.cat.api.suileyoo.unReady(n);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t,this)})));return function(n){return t.apply(this,arguments)}}(),i.getRoomInfo=function(){var t=e(r().mark((function t(n){var s=this;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise(e(r().mark((function t(e,i){var u;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,s.cat.api.suileyoo.getRoomInfo(n);case 2:u=t.sent,a.nativeAPI.dispatchEvent(o.EventName.APIName.ROOM_INFO,{code:0,data:u}),e(u);case 5:case"end":return t.stop()}}),t)})))));case 1:case"end":return t.stop()}}),t)})));return function(n){return t.apply(this,arguments)}}(),i.exitRoom=function(t){return this.cat.api.suileyoo.exitRoom(t)},i.openProfile=function(t){p.openProfile(t)},s}(i));s._RF.pop()}}}));

System.register("chunks:///_virtual/index10.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index30.ts","./index16.ts","./index18.ts","./index21.ts"],(function(n){var t,e,r,i,o,c,u;return{setters:[function(n){t=n.asyncToGenerator,e=n.regeneratorRuntime},function(n){r=n.cclegacy},function(n){i=n.default},function(n){o=n.default},function(n){c=n.default},function(n){u=n.cat}],execute:function(){r._RF.push({},"421652OHjFMgIg88WvVvzlz","index",void 0);n("Environment",function(){function n(){this.env=void 0}return n.prototype.init=function(){var n=t(e().mark((function n(){var t,r;return e().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!((t=u.platform.getEnvironmentAsync())instanceof Promise)){n.next=7;break}return n.next=4,t;case 4:n.t0=n.sent,n.next=8;break;case 7:n.t0=t;case 8:return r=n.t0,this.env="release"===r?c:"trial"===r?i:o,window.ccLog("%c 获取应用环境: "+r,"background:#35495e; padding: 1px; border-radius: 3px 3px 3px 3px; color: #fff"),n.abrupt("return",this.env);case 12:case"end":return n.stop()}}),n,this)})));return function(){return n.apply(this,arguments)}}(),n}());r._RF.pop()}}}));

System.register("chunks:///_virtual/index11.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseTracking.ts"],(function(n){var t,e,a;return{setters:[function(n){t=n.inheritsLoose},function(n){e=n.cclegacy},function(n){a=n.BaseTracking}],execute:function(){e._RF.push({},"4499aa2NFxIhImwTA+W8HnN","index",void 0);var r=n("TrackingGameEvent",function(n){return n.ROOM_WAIT="room_wait",n.ROOM_ENTER="room_enter",n.CARDS_FINISH="cards_finish",n.CARDS_CHANGE="cards_change",n.GAME_OUT="game_out",n.BATTLE_WATCH="battle_watch",n.GAME_ENTRUST="game_entrust",n.GAME_OVER="game_over",n}({}));n("TrackingGame",function(n){function e(){for(var t,e=arguments.length,a=new Array(e),c=0;c<e;c++)a[c]=arguments[c];return(t=n.call.apply(n,[this].concat(a))||this).roomWait=function(){t.tracking.upload({event_type:r.ROOM_WAIT})},t.roomEnter=function(){t.tracking.upload({event_type:r.ROOM_ENTER})},t.cardsFinish=function(n){t.tracking.upload({event_type:r.CARDS_FINISH,value:n})},t.cardsChange=function(n){t.tracking.upload({event_type:r.CARDS_CHANGE,value:n})},t.gameOut=function(){t.tracking.upload({event_type:r.GAME_OUT})},t.battleWatch=function(){t.tracking.upload({event_type:r.BATTLE_WATCH})},t.gameEntrust=function(n){t.tracking.upload({event_type:r.GAME_ENTRUST,value:{entrust:n}})},t.gameOver=function(n,e){t.tracking.upload({event_type:r.GAME_OVER,value:{ranking:n,score:e}})},t}return t(e,n),e}(a));e._RF.pop()}}}));

System.register("chunks:///_virtual/index12.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseTracking.ts"],(function(e){var t,n,a;return{setters:[function(e){t=e.inheritsLoose},function(e){n=e.cclegacy},function(e){a=e.BaseTracking}],execute:function(){n._RF.push({},"47833RzHeNMi6gQgK2GQiqp","index",void 0);var i=e("TrackingSuileyooEvent",function(e){return e.RETURN_CLICK="return_click",e.SEARCH_ROOM="SearchRoom",e.CONFIRM_SEARCH="ConfirmSearch",e.RULE_CLICK="rule_click",e.COMPETITIVE_MODE="CompetitiveMode",e.CANCELCOMPETITIVE="CancelCompetitive",e.RANKING_CLICK="Ranking_Click",e.TASK_CLICK="task_click",e.MALL_CLICK="mall_click",e.HOME_LEISUREMODE="Home_LeisureMode",e.RANKING_LV_CLICK="Ranking_Lv_Click",e.CLAIM_REWARD="CLAIM_REWARD",e.CLICK_ON_THE_ROOM_LIST="ClickOnTheRoomList",e.JOIN_LEISUREMODE="Join_LeisureMode",e.CREATE_ROOM="CreateRoom",e.SUCCESSFULLY_CREATED_ROOM="SuccessfullyCreatedRoom",e.GAME_PREPARE="game_prepare",e.GAME_SIT_DOWN="game_sit_down",e.GAME_STAND="game_stand",e.GAME_INVITE="game_invite",e.ENTER_THE_BATTLE="EnterTheBattle",e.SMALLSETTLEMENT="SmallSettlement",e.GAME_OVER="game_over",e.ANOTHER_ROUND="AnotherRound",e.Leave="leave",e.ADD_FRIEND="add_friend",e.MESSAGE_SEND="message_send",e.GIFT_TOUCH="gift_touch",e.MIKE_STATE="mike_state",e}({}));e("TrackingSuileyoo",function(e){function n(){for(var t,n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return(t=e.call.apply(e,[this].concat(a))||this).returnClick=function(){t.tracking.upload({event_type:i.RETURN_CLICK})},t.searchRoom=function(){t.tracking.upload({event_type:i.SEARCH_ROOM})},t.confirmSearch=function(e){t.tracking.upload({event_type:i.CONFIRM_SEARCH,value:{roomid:e}})},t.ruleClick=function(){t.tracking.upload({event_type:i.RULE_CLICK})},t.competitiveMode=function(){t.tracking.upload({event_type:i.COMPETITIVE_MODE})},t.cancelCompetitive=function(){t.tracking.upload({event_type:i.CANCELCOMPETITIVE})},t.rankingClick=function(){t.tracking.upload({event_type:i.RANKING_CLICK})},t.taskClick=function(){t.tracking.upload({event_type:i.TASK_CLICK})},t.mallClick=function(){t.tracking.upload({event_type:i.MALL_CLICK})},t.homeLeisureMode=function(){t.tracking.upload({event_type:i.HOME_LEISUREMODE})},t.rankingLvClick=function(){t.tracking.upload({event_type:i.RANKING_LV_CLICK})},t.claimReward=function(e){t.tracking.upload({event_type:i.CLAIM_REWARD,value:{taskname:e}})},t.clickOnTheRoomList=function(e){t.tracking.upload({event_type:i.CLICK_ON_THE_ROOM_LIST,value:{roomid:e}})},t.joinLeisureMode=function(){t.tracking.upload({event_type:i.JOIN_LEISUREMODE})},t.createRoom=function(){t.tracking.upload({event_type:i.CREATE_ROOM})},t.successfullyCreatedRoom=function(e){t.tracking.upload({event_type:i.SUCCESSFULLY_CREATED_ROOM,value:{mode:e}})},t.gamePrepare=function(){t.tracking.upload({event_type:i.GAME_PREPARE})},t.gameSitDown=function(){t.tracking.upload({event_type:i.GAME_SIT_DOWN})},t.gameStand=function(){t.tracking.upload({event_type:i.GAME_STAND})},t.gameInvite=function(){t.tracking.upload({event_type:i.GAME_INVITE})},t.enterBattle=function(){t.tracking.upload({event_type:i.ENTER_THE_BATTLE})},t.smallSettlement=function(e){t.tracking.upload({event_type:i.SMALLSETTLEMENT,value:{rank:e}})},t.gameOver=function(e){t.tracking.upload({event_type:i.GAME_OVER,value:{rank:e}})},t.anotherRound=function(){t.tracking.upload({event_type:i.ANOTHER_ROUND})},t.leave=function(){t.tracking.upload({event_type:i.Leave})},t.addFriend=function(){t.tracking.upload({event_type:i.ADD_FRIEND})},t.messageSend=function(){t.tracking.upload({event_type:i.MESSAGE_SEND})},t.giftTouch=function(){t.tracking.upload({event_type:i.GIFT_TOUCH})},t.mikeState=function(e){t.tracking.upload({event_type:i.MIKE_STATE,value:{type:e}})},t}return t(n,e),n}(a));n._RF.pop()}}}));

System.register("chunks:///_virtual/index13.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index3.js","./BaseStore.ts","./bundle.js","./create.js","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./msg_pb.js"],(function(e){var t,n,i,a,r,s,u,o,l,c,d;return{setters:[function(e){t=e.inheritsLoose,n=e.createClass,i=e.assertThisInitialized},function(e){a=e.cclegacy},null,function(e){r=e.BaseStore},function(e){s=e.makeObservable,u=e.observable,o=e.computed},function(e){l=e.create},null,null,null,null,null,null,null,function(e){c=e.BattleMode,d=e.ClientBootParamSchema}],execute:function(){a._RF.push({},"47b7dU5zfZFL47QUZlq1ZoF","index",void 0);var p=e("GameMode",function(e){return e[e.NONE=0]="NONE",e[e.SOLO=1]="SOLO",e[e.TEAM=2]="TEAM",e}({}));e("default",function(e){function a(t){var n;return(n=e.call(this,t)||this).auth=l(d),n.assets=void 0,n.pointD10=void 0,n.userGameData={playTime:0,playCount:0,winCount:0,winRatio:"0%",userId:0,gameLevel:1,exp:1,seasonTier:null,seasonExp:0},n.profile={displayName:"",avatar:"",avatarFrame:"",gender:0,intro:"",nameplate:"",badge:"",bizCard:"",region:"",id:void 0,charms:0},s(i(n),{auth:u,assets:u,pointD10:u,userGameData:u,profile:u,isAudience:o}),n}return t(a,e),n(a,[{key:"isAudience",get:function(){var e=!this.auth.token;return window.ccLog("isAudience",e,this.auth),e}},{key:"gameMode",get:function(){return this.auth.mode==c.PVP_COOPERATION?p.TEAM:this.auth.mode==c.PVP_SOLO?p.SOLO:p.NONE}},{key:"userTeamId",get:function(){var e,t;return null!=(e=null==(t=this.rootStore.game.getUserPlayer)?void 0:t.teamId)?e:""}},{key:"userIndex",get:function(){var e,t;return null!=(e=null==(t=this.rootStore.game.getUserPlayer)?void 0:t.relBattleOffset)?e:-1}}]),a}(r));a._RF.pop()}}}));

System.register("chunks:///_virtual/index14.ts",["cc","./index23.ts","./index21.ts"],(function(e){var n,t,o,i;return{setters:[function(e){n=e.error,t=e.cclegacy},function(e){o=e.default},function(e){i=e.cat}],execute:function(){e("default",(function(){var e=o.global,t=e.social_game_feature,c=e.social_game_id;i.res.loadRemote(i.env.stop_service_url+"/social_games/stop_service/admin/"+t+"-"+c+".json?"+Date.now(),{ext:".json"},(function(e,t){if(e)return n(e);if(t.json){var o=t.json;i.gui.showNotice({text:o.notice,confrim:function(){window.ccLog("获取停服公告退出"),i.platform.back()}})}}))})),t._RF.push({},"50c5e6EGiJM94wspOm7OgZ6","index",void 0),t._RF.pop()}}}));

System.register("chunks:///_virtual/index15.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index32.ts","./index23.ts","./index.js","./CommonSign.ts","./SuileyooSign.ts"],(function(e){var t,n,r,o,a,s,i,u;return{setters:[function(e){t=e.asyncToGenerator,n=e.regeneratorRuntime},function(e){r=e.cclegacy},function(e){o=e.CustomPlatform},function(e){a=e.default},function(e){s=e.default},function(e){i=e.getCommonSign},function(e){u=e.attachSign}],execute:function(){r._RF.push({},"555f0r1kjFJh4+le9wuKxkz","index",void 0);e("http",function(){var e=t(n().mark((function e(t,r){var c,l;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return c=(null!=r?r:s.create()).extend({hooks:{beforeRequest:[function(e){if(a.global.customPlatform==o.SuiLeYoo)t.data=u(null==t?void 0:t.data),e.headers.set("Content-type","application/x-www-form-urlencoded");else{var n=i(null==t?void 0:t.data),r=n.sign,s=n.timestamp;e.headers.set("X-Signature",r),e.headers.set("X-Timestam",s),e.headers.set("Content-type","application/json; charset=utf-8")}}],beforeError:[function(e){var t=e.response;return t&&t.body&&(e.name="GitHubError",e.message="("+t.status+")"),e}]},headers:{Authorization:"Bearer "+a.login.token},body:JSON.stringify(t.data)}),e.next=3,c(t.url).json();case 3:return l=e.sent,window.ccLog("response",l),e.abrupt("return",l);case 6:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}());r._RF.pop()}}}));

System.register("chunks:///_virtual/index16.ts",["cc","./util.ts"],(function(e){var t,s;return{setters:[function(e){t=e.cclegacy},function(e){s=e.queryStringToObject}],execute:function(){t._RF.push({},"55f4asE9IlIba2MJXymvzMN","index",void 0);var r=s(location.search.slice(1));e("default",{static_base_url:"",http_base_url:"https://dev-cloud-game-api.stnts.com/",event_tracking_url:"https://dssp-test.stnts.com/?opt=put&type=json",event_tracking_key:"alibabatutudodo@",bucket:"",region:"",secret:"5bozaf6by&w3^4i$mll1uj0yzyfhx8t_",player_ws_url:r.player_ws_url||"wss://game-server-dev.mityoo.com/ws/conn",audience_ws_url:r.audience_ws_url||"wss://game-server-dev.mityoo.com/ws/aud",stop_service_url:""});t._RF.pop()}}}));

System.register("chunks:///_virtual/index17.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseTracking.ts"],(function(c){var n,e,t;return{setters:[function(c){n=c.inheritsLoose},function(c){e=c.cclegacy},function(c){t=c.BaseTracking}],execute:function(){e._RF.push({},"6356ch1FxtHNbc7HzeNfkIx","index",void 0);var i=c("TrackingSystemEvent",function(c){return c.GUIDE_CLICK="guide_click",c.SOUND_CLICK="sound_click",c.CHAT_CLICK="chat_click",c.GIFT_CLICK="gift_click",c.MESSAGE_CLICK="message_click",c.SETTING_CLICK="setting_click",c.BUSINESS_CARD="business_card",c}({}));c("TrackingSystem",function(c){function e(){for(var n,e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return(n=c.call.apply(c,[this].concat(t))||this).guideClick=function(){n.tracking.upload({event_type:i.GUIDE_CLICK})},n.soundClick=function(c,e){n.tracking.upload({event_type:i.SOUND_CLICK,value:{bgm:c,effect:e}})},n}return n(e,c),e}(t));e._RF.pop()}}}));

System.register("chunks:///_virtual/index18.ts",["cc","./util.ts"],(function(t){var e,s;return{setters:[function(t){e=t.cclegacy},function(t){s=t.queryStringToObject}],execute:function(){e._RF.push({},"69b0el/tBFGSaO3rpwuwi92","index",void 0);var c=s(location.search.slice(1));t("default",{static_base_url:"",http_base_url:"https://draw-something-test.yiqiyoo.com",event_tracking_url:"https://dssp.stnts.com/?opt=put&type=json",event_tracking_key:"alibabatutudodo@",bucket:"",region:"",secret:"",player_ws_url:c.player_ws_url||"wss://game-ws.mityoo.com/ws/connector",audience_ws_url:c.audience_ws_url||"wss://game-ws.mityoo.com/ws/audience",stop_service_url:""});e._RF.pop()}}}));

System.register("chunks:///_virtual/index19.ts",["./rollupPluginModLoBabelHelpers.js","cc","./ArrayUtils.ts","./BlobUtils.ts","./EncryptUtil.ts","./StringUtil.ts","./TimeUtils.ts","./NodeUtils.ts","./Commontils.ts","./BaseManager.ts"],(function(t){var n,i,s,e,o,l,r,c,u,a;return{setters:[function(t){n=t.inheritsLoose},function(t){i=t.cclegacy},function(t){s=t},function(t){e=t},function(t){o=t},function(t){l=t},function(t){r=t},function(t){c=t},function(t){u=t},function(t){a=t.BaseManager}],execute:function(){i._RF.push({},"6d9e8WEpzdCLaUvz1UHgOF1","index",void 0);t("Util",function(t){function i(){for(var n,i=arguments.length,a=new Array(i),f=0;f<i;f++)a[f]=arguments[f];return(n=t.call.apply(t,[this].concat(a))||this).arrayUtils=s,n.blobUtils=e,n.encryptUtil=o,n.stringUtil=l,n.timeUtils=r,n.nodeUtils=c,n.commontils=u,n}return n(i,t),i}(a));i._RF.pop()}}}));

System.register("chunks:///_virtual/index2.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index23.ts","./index48.ts"],(function(n){var e,t,o,r,i,c,u;return{setters:[function(n){e=n.inheritsLoose,t=n.asyncToGenerator,o=n.regeneratorRuntime},function(n){r=n.cclegacy,i=n.error},function(n){c=n.default},function(n){u=n.H5API}],execute:function(){r._RF.push({},"0650bD7XUVPSotUNv7yEfr0","index",void 0);n("WXAPI",function(n){function r(){return n.apply(this,arguments)||this}e(r,n);var u=r.prototype;return u.netWorkStatusListener=function(){var n=this;wx.onNetworkStatusChange((function(e){e.isConnected?n.online():n.offline()}))},u.getEnvironmentAsync=function(){return wx.getAccountInfoSync().miniProgram.envVersion},u.getLaunchOptionsSync=function(){return wx.getLaunchOptionsSync()},u.getSetting=function(){return new Promise((function(n,e){wx.getPrivacySetting({success:function(t){t.needAuthorization?(e(),window.ccLog("用户隐私已授权")):(window.ccLog("用户隐私未授权"),n(t.needAuthorization))}})}))},u.getSystemInfo=function(){return new Promise((function(n,e){wx.getSystemInfo({success:function(e){var t=c.global;t.pixelRatio=e.pixelRatio,t.titleBarHeight=e.statusBarHeight,n(e)},fail:function(n){i(n),e(n)}})}))},u.getLoginCode=function(){return new Promise((function(n,e){wx.login({provider:"weixin",success:function(e){c.login.code=e.code,n(e.code)}})}))},u.serverLogin=function(){var n=t(o().mark((function n(){return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=3,this.getLoginCode();case 3:case"end":return n.stop()}}),n,this)})));return function(){return n.apply(this,arguments)}}(),u.getUserInfo=function(){var n=this;return new Promise((function(e,t){wx.getUserInfo({success:function(n){window.ccLog(n.userInfo),e()},fail:function(e){n.cat.gui.showToast({title:e.errMsg}),i(e.errMsg),t()}})}))},u.navigateToMiniProgram=function(){return console.error("暂未实现方法"),Promise.reject()},u.getAppId=function(){return console.error("暂未实现方法"),wx.getAccountInfoSync().miniProgram.appId},u.authorize=function(){return new Promise((function(n,e){var t=wx.getSystemInfoSync(),o=t.screenWidth,r=t.screenHeight,i=wx.createUserInfoButton({type:"text",text:"",style:{left:0,top:0,width:o,height:r,lineHeight:40,backgroundColor:"#********",color:"#ffffff",textAlign:"center",fontSize:16,borderRadius:4,borderColor:"#********",borderWidth:0},withCredentials:!1});i.onTap((function(e){e.userInfo?(window.ccLog("用户授权进入游戏",e),i.destroy(),n()):window.ccLog("用户拒绝授权")}))}))},u.showShareMenu=function(){wx.showShareMenu({withShareTicket:!0,menus:["shareAppMessage","shareTimeline"]})},u.onShareAppMessage=function(n){return wx.onShareAppMessage((function(){return n}))},u.onShareTimeline=function(n){return wx.onShareTimeline((function(){return n}))},r}(u));r._RF.pop()}}}));

System.register("chunks:///_virtual/index20.ts",["cc","./index21.ts","./index31.ts","./index9.ts","./index39.ts","./index.mjs_cjs=&original=.js"],(function(i){var n,o,e,s;return{setters:[function(i){n=i.cclegacy},null,function(i){o=i.LoginAPI},function(i){e=i.SuileyooAPI},function(i){s=i.UserAPI},null],execute:function(){n._RF.push({},"6e281orfjJJBrwkFhV5jRcn","index",void 0);i("API",(function(i){this.login=void 0,this.user=void 0,this.suileyoo=void 0,window.ccLog("api cos"),this.login=new o(i),window.ccLog("api login"),this.user=new s(i),window.ccLog("api user"),this.suileyoo=new e(i),window.ccLog("api suileyoo")}));n._RF.pop()}}}));

System.register("chunks:///_virtual/index21.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index5.ts","./index41.ts","./index22.ts","./index15.ts","./index6.ts","./index43.ts","./index7.ts","./index19.ts","./Debugout.ts"],(function(n){var t,e,i,o,s,r,a,u,c,_,d,l,w,h,f,E,g,p;return{setters:[function(n){t=n.createForOfIteratorHelperLoose,e=n.createClass,i=n.classPrivateFieldLooseKey,o=n.classPrivateFieldLooseBase,s=n.asyncToGenerator,r=n.regeneratorRuntime},function(n){a=n.cclegacy,u=n.log,c=n.game},function(n){_=n.AudioManager},function(n){d=n.StorageManager},function(n){l=n.GuiManager},function(n){w=n.http},function(n){h=n.ResLoader},function(n){f=n.MessageManager},function(n){E=n.WatchSystem},function(n){g=n.Util},function(n){p=n.default}],execute:function(){a._RF.push({},"71aa1ZLfOdHN4RqYDgSJYFZ","index",void 0),location.href.includes("debug=1")&&(window.__DEBUGOUT_INSTENCE__=new p,console.log=function(){window.__DEBUGOUT_INSTENCE__.log.apply(window.__DEBUGOUT_INSTENCE__,arguments);for(var n,e=t(arguments);!(n=e()).done;){n.value;null}},console.error=function(){window.__DEBUGOUT_INSTENCE__.error.apply(window.__DEBUGOUT_INSTENCE__,arguments)},console.warn=function(){window.__DEBUGOUT_INSTENCE__.warn.apply(window.__DEBUGOUT_INSTENCE__,arguments)},console.info=function(){window.__DEBUGOUT_INSTENCE__.info.apply(window.__DEBUGOUT_INSTENCE__,arguments)}),window.ccLog=window.__DEBUGOUT_INSTENCE__?console.log:u;var v=i("ins"),N=n("Manager",function(){function n(){this.mount=void 0,this.audio=void 0,this.event=void 0,this.gui=void 0,this.storage=void 0,this.res=void 0,this.http=w,this.watch=void 0,this.util=void 0}return n.prototype.boot=function(){var n=s(r().mark((function n(){return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return c.setFrameRate(30),this.res=new h,this.storage=new d(this),this.event=new f,this.audio=new _(this),this.util=new g(this),n.next=8,new l(this).init();case 8:this.gui=n.sent.gui,this.watch=new E(this);case 10:case"end":return n.stop()}}),n,this)})));return function(){return n.apply(this,arguments)}}(),e(n,null,[{key:"instance",get:function(){return o(this,v)[v]||(o(this,v)[v]=new n),o(this,v)[v]}}]),n}());Object.defineProperty(N,v,{writable:!0,value:void 0});n("cat",N.instance);a._RF.pop()}}}));

System.register("chunks:///_virtual/index22.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Gui.ts","./BaseManager.ts"],(function(e){var t,n,r,o,a,i,s,c,u;return{setters:[function(e){t=e.inheritsLoose,n=e.createClass,r=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){a=e.cclegacy,i=e.instantiate,s=e.director},function(e){c=e.Gui},function(e){u=e.BaseManager}],execute:function(){a._RF.push({},"771415bfKRGa5w8ZQEIXKCw","index",void 0);var f=e("BasePrefab",function(e){return e.Root="prefabs/core/base/root",e.Toast="prefabs/core/base/toast",e.BlackMask="prefabs/core/base/black-mask",e.Loading="prefabs/core/base/loading",e.Notice="prefabs/core/base/notice",e.Reconnection="prefabs/core/base/reconnection",e}({}));e("GuiManager",function(e){function a(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(t=e.call.apply(e,[this].concat(r))||this)._gui=void 0,t.getGuiPrefabByType=function(e){return new Promise((function(n,r){t.cat.res.load(e,(function(e,t){e?r(e):n(t)}))}))},t}return t(a,e),a.prototype.init=function(){var e=r(o().mark((function e(){var t,n;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.getGuiPrefabByType(f.Root);case 2:return t=e.sent,n=i(t),this.gui=n.getComponent(c).init(this.cat),s.addPersistRootNode(n),e.abrupt("return",this);case 7:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),n(a,[{key:"gui",get:function(){return this._gui},set:function(e){this._gui=e}}]),a}(u));a._RF.pop()}}}));

System.register("chunks:///_virtual/index23.ts",["cc","./index13.ts","./index32.ts","./index26.ts","./index38.ts","./index35.ts","./index.b.ts"],(function(t){var n,e,i,s,u,h;return{setters:[function(t){n=t.cclegacy},function(t){e=t.default},function(t){i=t.default},null,function(t){s=t.default},function(t){u=t.default},function(t){h=t.default}],execute:function(){n._RF.push({},"7a1cf7YrgFL1rzpBR49uKSb","index",void 0);var o=t("Store",function(){function t(){this.user=new e(this),this.global=new i(this),this.game=new h(this),this.login=new s(this),this.lobby=new u(this)}return t.prototype.reset=function(){this.user=new e(this),this.global=new i(this),this.game=new h(this),this.login=new s(this),this.lobby=new u(this)},t}());t("default",new o);n._RF.pop()}}}));

System.register("chunks:///_virtual/index24.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index40.ts","./index2.js","./index23.ts","./index3.js","./index34.ts","./index11.ts","./index33.ts","./index12.ts","./index17.ts","./index21.ts","./BaseManager.ts","./index.js","./GlobalEventConstant.ts","./msg_pb.js"],(function(e){var n,t,i,a,o,r,s,c,u,d,l,f,v,p,g,m,y;return{setters:[function(e){n=e.inheritsLoose,t=e.asyncToGenerator,i=e.regeneratorRuntime,a=e.extends,o=e.assertThisInitialized},function(e){r=e.cclegacy},null,function(e){s=e.default},function(e){c=e.default},null,function(e){u=e.TrackingLoading},function(e){d=e.TrackingGame},function(e){l=e.TrackingNetwork},function(e){f=e.TrackingSuileyoo},function(e){v=e.TrackingSystem},null,function(e){p=e.BaseManager},function(e){g=e.default},function(e){m=e.GlobalEventConstant},function(e){y=e.BattleMode}],execute:function(){r._RF.push({},"7cf87iawYBBVIlu4OwcyyZK","index",void 0);e("Tracking",function(e){function r(n){var r;return(r=e.call(this,n)||this).game=void 0,r.loading=void 0,r.network=void 0,r.suileyoo=void 0,r.system=void 0,r.ecbkey=void 0,r.ecbiv=void 0,r.sign=function(e){var n={type:"object",para:[]},t=[],i=r.cat.env.event_tracking_key;return t.push({mq:"social_games",data:i?[e]:r.encodeECB("["+JSON.stringify(e)+"]")}),n.para=t,n},r.encodeECB=function(e){var n=s.enc.Utf8.parse(e),t=s.AES.encrypt(n,r.ecbkey,{iv:r.ecbiv,mode:s.mode.ECB,padding:s.pad.ZeroPadding}),i=s.enc.Hex.parse((t.ciphertext||new s.lib.WordArray).toString().toUpperCase());return s.enc.Base64.stringify(i)},r.http=t(i().mark((function e(n){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.sign(n),e.next=3,g.post(r.cat.env.event_tracking_url,{body:JSON.stringify(t),headers:{"Content-Type":"application/json; charset=utf-8"}});case 3:case"end":return e.stop()}}),e)}))),r.upload=function(e){var n,t=e.event_type,i=e.value,o=c.user.userTeamId,r=c.game.battle,s=c.user.auth,u=s.mode,d=s.battleId,l=c.user.auth.token,f={uid:""+(null!=l?l:""),event_type:t,event_properties:JSON.stringify(i),gamename:"鲨鱼",channel_name:"带带",battle_mode:null!=u?u:0,battle_id:null!=d?d:"",room_id:null!=(n=c.lobby.matchRoom.ddRid)?n:""};a({},f,r.mode!=y.UNSPECIFIED?{team_num:r.teams,perteam_num:r.teamPlayers}:{},o?{team_id:o}:{})},r.init(),r.game=new d(o(r)),r.loading=new u(o(r)),r.network=new l(o(r)),r.suileyoo=new f(o(r)),r.system=new v(o(r)),r}n(r,e);var p=r.prototype;return p.init=function(){this.cat.event.on(m.EVENT_SHOW,this.onShowHandler,this),this.cat.event.on(m.EVENT_HIDE,this.onHideHandler,this),this.ecbkey=s.enc.Utf8.parse(this.cat.env.event_tracking_key),this.ecbiv=s.enc.Utf8.parse(this.cat.env.event_tracking_key)},p.onShowHandler=function(){},p.onHideHandler=function(){},p.onHeartBeatHandler=function(){},r}(p));r._RF.pop()}}}));

System.register("chunks:///_virtual/index25.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index44.ts","./BaseManager.ts"],(function(e){var i,t,n,a,r,s,o;return{setters:[function(e){i=e.inheritsLoose,t=e.createForOfIteratorHelperLoose,n=e.assertThisInitialized},function(e){a=e.cclegacy,r=e.error},function(e){s=e.GlobalEventConstant},function(e){o=e.BaseManager}],execute:function(){a._RF.push({},"7e368838PVDc5QiEC1jvqvR","index",void 0);var l=function(e){return e[e.Delay=0]="Delay",e[e.Interval=1]="Interval",e}(l||{}),u=function(e,i,t,n){this.tag=void 0,this.type=void 0,this.callback=void 0,this.remainingTime=void 0,this.executionTime=0,this.initialTime=void 0,this.tag=e,this.type=i,this.remainingTime=t,this.initialTime=t,this.callback=n};e("TimerManager",function(e){function a(i){var t;return(t=e.call(this,i)||this).timers=new Map,t.lastUpdateTime=Date.now(),i.event.on(s.EVENT_HIDE,t.onHandleAppBackground,n(t)),i.event.on(s.EVENT_SHOW,t.onHandleAppForeground,n(t)),setInterval(t.update.bind(n(t)),1e3),t}i(a,e);var o=a.prototype;return o.onHandleAppBackground=function(){this.lastUpdateTime=Date.now()},o.onHandleAppForeground=function(){var e=Date.now(),i=e-this.lastUpdateTime;window.ccLog("elapsedTime",i);for(var n,a=t(this.timers.values());!(n=a()).done;){var r=n.value;r.remainingTime>0&&(r.remainingTime-=i,r.executionTime+=i)}this.lastUpdateTime=e},o.registerInterval=function(e,i,t){if(this.has(e))return r(e+"定时器已存在，请勿重复注册");var n=new u(e,l.Interval,i,t);this.timers.set(e,n)},o.registerDelay=function(e,i,t){if(this.has(e))return r(e+"延时器已存在，请勿重复注册");var n=new u(e,l.Delay,i,t);this.timers.set(e,n)},o.unregister=function(e){this.has(e)&&this.timers.delete(e)},o.has=function(e){return this.timers.has(e)},o.update=function(){for(var e,i=[],n=t(this.timers.entries());!(e=n()).done;){var a=e.value,r=a[0],s=a[1];s.remainingTime-=1e3,s.remainingTime<=0&&(s.type===l.Interval&&(s.executionTime+=s.initialTime),s.callback(s.executionTime),s.type===l.Interval?s.remainingTime=s.initialTime:i.push(r))}for(var o=0,u=i;o<u.length;o++){var c=u[o];this.timers.delete(c)}},a}(o));a._RF.pop()}}}));

System.register("chunks:///_virtual/index26.ts",["cc","./index.b.ts"],(function(t){var e;return{setters:[function(t){e=t.cclegacy},function(e){var c={};c.ClickToothType=e.ClickToothType,c.default=e.default,t(c)}],execute:function(){e._RF.push({},"8348dZ3T0ZGbIsx13m0ZTtw","index",void 0),e._RF.pop()}}}));

System.register("chunks:///_virtual/index27.ts",["./rollupPluginModLoBabelHelpers.js","cc","./create.js","./clone.js","./descriptors.js","./reflect.js","./descriptor_pb.js","./from-binary.js","./binary-encoding.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./index3.js","./index44.ts","./index21.ts","./Reconnection.ts","./index23.ts","./msg_pb.js"],(function(e){var n,t,r,o,i,c,s,a,u,d,h,f,p,l,w;return{setters:[function(e){n=e.inheritsLoose,t=e.asyncToGenerator,r=e.regeneratorRuntime,o=e.assertThisInitialized},function(e){i=e.cclegacy},function(e){c=e.create},function(e){s=e.clone},null,function(e){a=e.reflect},null,function(e){u=e.fromBinary},null,null,null,null,null,function(e){d=e.SocialGameClient},function(e){h=e.GlobalEventConstant},function(e){f=e.cat},function(e){p=e.ReconnectPrompt},function(e){l=e.default},function(e){w=e.AudienceOptionsSchema}],execute:function(){i._RF.push({},"855edwhaiNDI6uXDBVl2vfN","index",void 0);e("WrapperSocialGameClient",function(e){function i(n,i){var s;return(s=e.call(this,n,i)||this).isNeedReconnect=!1,s.isOnline=!0,s.isInBackground=!1,s.running=!1,s.index=0,s.GameNotify=t(r().mark((function n(t,i,c){return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:n.prev=0,e.prototype.GameNotify.call(o(s),t,i,c),n.next=8;break;case 4:throw n.prev=4,n.t0=n.catch(0),console.error("Error in connectRequest for route "+t+": ",n.t0),n.t0;case 8:case"end":return n.stop()}}),n,null,[[0,4]])}))),s.Notify=t(r().mark((function e(n,t,o){var i,a,u;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=(i=void 0===o?{}:o).audOptions,u=void 0===a?c(w,{forwardReq:!1,forwardResp:!1}):a,i.show_log,s.isSocketConnected){e.next=3;break}return e.abrupt("return");case 3:s.GameNotify(n,t,u);case 5:case"end":return e.stop()}}),e)}))),s.running=!0,s.addEvent(),s}n(i,e);var d=i.prototype;return d.addEvent=function(){var e=this;this.on("connected",(function(){return e.handleEvent({type:"connected"})})).on("reconnected",(function(){return e.handleEvent({type:"reconnected"})})).on("disconnect",(function(n){return e.handleEvent({type:"disconnect",ev:n})})).on("paramError",(function(n){return e.handleEvent({type:"paramError",ev:n})})).on("kick",(function(){return e.handleEvent({type:"kick"})})).on("reconnecting",(function(){return e.handleEvent({type:"reconnecting"})})).on("maxReconnect",(function(){return e.handleEvent({type:"maxReconnect"})})).on("error",(function(n){return e.handleEvent({type:"error",ev:n})})),f.event.on(h.EVENT_SHOW,this.onShowHandler,this).on(h.EVENT_HIDE,this.onHideHandler,this).on(h.ONLINE,this.onOnlineHandler,this).on(h.OFFLINE,this.onOfflineHandler,this)},d.removeEvent=function(){this.removeAllListeners(),f.event.off(h.EVENT_SHOW,this.onShowHandler,this).off(h.EVENT_HIDE,this.onHideHandler,this).off(h.ONLINE,this.onOnlineHandler,this).off(h.OFFLINE,this.onOfflineHandler,this)},d.onShowHandler=function(){window.ccLog("Application moved to foreground"),this.isInBackground=!1,this.isNeedReconnect&&this.isOnline&&(window.ccLog("Attempting to reconnect after moving to foreground"),this.handleEvent({type:"show"}))},d.onHideHandler=function(){window.ccLog("Application moved to background"),this.isInBackground=!0,this.handleEvent({type:"hide"})},d.onOnlineHandler=function(){this.isOnline=!0,window.ccLog("正在检查网络状态:"+(this.isOnline?"在线":"断开")),this.handleEvent({type:"online"})},d.onOfflineHandler=function(){this.isOnline=!1,window.ccLog("正在检查网络状态:"+(this.isOnline?"在线":"断开")),this.handleEvent({type:"offline"})},d.handleEvent=function(){var e=t(r().mark((function e(n){var t;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.running){e.next=2;break}return e.abrupt("return");case 2:e.t0=n.type,e.next="init"===e.t0?5:"connected"===e.t0?6:"reconnected"===e.t0?9:"disconnect"===e.t0?28:"paramError"===e.t0?33:"kick"===e.t0?37:"reconnecting"===e.t0?42:"maxReconnect"===e.t0?45:"error"===e.t0?48:"online"===e.t0?50:"offline"===e.t0?54:"show"===e.t0?59:"hide"===e.t0?62:"reconnect"===e.t0?64:"destroy"===e.t0?67:70;break;case 5:return e.abrupt("break",71);case 6:return window.ccLog("ws连接成功状态 connected"),this.isNeedReconnect=!1,e.abrupt("break",71);case 9:return window.ccLog("ws重连成功状态 reconnected"),this.isNeedReconnect=!1,window.ccLog(this.isSocketConnected),window.ccLog("%c 重连成功状态","background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff",this.index),e.prev=13,e.next=16,this.connectRequest(l.user.isAudience);case 16:t=e.sent,window.ccLog("-------",t),f.gui.showToast({title:p.RECONNECTED}),f.gui.hideReconnect(),f.gui.reloadScene(),e.next=27;break;case 23:e.prev=23,e.t1=e.catch(13),console.error(e.t1),f.gui.showReconnect(p.GAME_ERROR);case 27:return e.abrupt("break",71);case 28:return window.ccLog("断开连接状态 disconnect",n.ev),window.ccLog("%c 断开连接状态","background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff",this.index),this.isNeedReconnect=!0,this.handleEvent({type:"reconnect"}),e.abrupt("break",71);case 33:return window.ccLog("参数错误状态 paramError",JSON.stringify(n.ev)),f.gui.showReconnect(p.CONNECT_PARAM_ERROR),this.handleEvent({type:"destroy"}),e.abrupt("break",71);case 37:return window.ccLog("%c 被踢出状态","background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff",this.index),window.ccLog("被踢出状态 kick"),f.gui.showReconnect(p.KICK),this.handleEvent({type:"destroy"}),e.abrupt("break",71);case 42:return window.ccLog("正在重连状态 reconnecting"),this.isOnline&&this.isInBackground,e.abrupt("break",71);case 45:return window.ccLog("超出最大重连状态 maxReconnect"),f.gui.showReconnect(p.MAX_RECONNECT),e.abrupt("break",71);case 48:return this.isOnline&&(window.ccLog("ws报错状态 error",n.ev),"string"==typeof n.ev&&f.gui.showToast({title:n.ev}),f.gui.showReconnect(p.RECONNECTED_ERROR)),e.abrupt("break",71);case 50:return f.gui.showReconnect(p.ONLINE),window.ccLog("在线状态 online"),this.handleEvent({type:"reconnect"}),e.abrupt("break",71);case 54:return this.disconnect(!0),window.ccLog("离线状态 offline"),this.isNeedReconnect=!0,f.gui.showReconnect(p.OFFLINE),e.abrupt("break",71);case 59:return window.ccLog("前台状态 show"),this.handleEvent({type:"reconnect"}),e.abrupt("break",71);case 62:return window.ccLog("后台状态 hide"),e.abrupt("break",71);case 64:return console.log("重连状态 reconnect"),this.isNeedReconnect&&!this.isInBackground&&this.isOnline&&(this.reset(),this.index+=1,window.ccLog("%c ws重连次数","background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff",this.index),this.reconnectImmediately()),e.abrupt("break",71);case 67:return window.ccLog("销毁状态 destroy"),this.destroyStateMachine(),e.abrupt("break",71);case 70:window.ccLog("Unknown event:",n.type);case 71:case"end":return e.stop()}}),e,this,[[13,23]])})));return function(n){return e.apply(this,arguments)}}(),d.destroy=function(){this.handleEvent({type:"destroy"})},d.disconnect=function(n){void 0===n&&(n=!1),e.prototype.disconnect.call(this,n)},d.destroyStateMachine=function(){window.ccLog("Destroying state machine"),this.running=!1,this.removeEvent(),this.disconnect(!0)},d.Request=function(){var e=t(r().mark((function e(n,t,o,i){var s,a,u,d,h;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=(s=void 0===i?{}:i).audOptions,u=void 0===a?c(w,{forwardReq:!1,forwardResp:!1}):a,s.show_log,d=s.showToast,h=void 0===d||d,window.ccLog("GameNotifyType",this.isSocketConnected),this.isSocketConnected){e.next=4;break}throw this.isSocketConnected?"未加入战局/观战":"Socket未连接";case 4:return e.abrupt("return",this.GameRequest(n,t,o,u,h));case 6:case"end":return e.stop()}}),e,this)})));return function(n,t,r,o){return e.apply(this,arguments)}}(),d.GameRequest=function(){var n=t(r().mark((function n(t,o,i,c,s){var a=this;return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return void 0===s&&(s=!0),n.abrupt("return",new Promise((function(n,r){e.prototype.GameRequest.call(a,t,o,i,c).then((function(e){n(e)}),(function(e){s&&f.gui.showToast({title:e.msg}),console.error(e)}))})));case 2:case"end":return n.stop()}}),n)})));return function(e,t,r,o,i){return n.apply(this,arguments)}}(),d.gameRequest=function(){var n=t(r().mark((function n(t,o,i){return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",e.prototype.gameRequest.call(this,t,o,i));case 2:case"end":return n.stop()}}),n,this)})));return function(e,t,r){return n.apply(this,arguments)}}(),d.listen=function(e,n,t){return this.routes.has(e)||this.routes.set(e,(function(r){var o=u(n,r);window.ccLog("ws服务端消息:["+new Date+"] "+e),f.event.has(e)&&f.event.dispatchEvent(e,s(n,o)),t&&t(s(n,o))})),this},d.onData=function(n){e.prototype.onData.call(this,n)},d.registerService=function(e){var n=this;e.forEach((function(e){n.registerMethod(e)}))},d.registerMethod=function(e){var n,o=e.name,i=e.localName;o&&i&&Object.defineProperty(this,i,{enumerable:!0,configurable:!0,writable:!0,value:(n=t(r().mark((function n(t,i){return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",this.Request(o,a(e.input,c(e.input,t)),e.output,i));case 1:case"end":return n.stop()}}),n,this)}))),function(e,t){return n.apply(this,arguments)})})},i}(d));i._RF.pop()}}}));

System.register("chunks:///_virtual/index28.ts",["cc"],(function(){var e;return{setters:[function(t){e=t.cclegacy}],execute:function(){e._RF.push({},"87c185pSahNG5oE6lNjetuz","index",void 0),e._RF.pop()}}}));

System.register("chunks:///_virtual/index29.ts",["./rollupPluginModLoBabelHelpers.js","cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./from-json.js","./index3.js","./index23.ts","./index48.ts","./msg_pb.js"],(function(n){var e,r,o,t,i,u,s,c;return{setters:[function(n){e=n.inheritsLoose,r=n.asyncToGenerator,o=n.regeneratorRuntime},function(n){t=n.cclegacy},null,null,null,null,null,null,null,function(n){i=n.fromJsonString},null,function(n){u=n.default},function(n){s=n.H5API},function(n){c=n.ClientBootParamSchema}],execute:function(){t._RF.push({},"8b1964xE6xM+7Yd7wCoQZrG","index",void 0);n("DaiDaiAPI",function(n){function t(){return n.apply(this,arguments)||this}e(t,n);var s=t.prototype;return s.getEnvironmentAsync=function(){var n=""+window.location.origin+window.location.pathname;return window.ccLog("url",n),-1!==n.indexOf("dev")||-1!==n.indexOf("localhost")||-1!==n.indexOf("192.168")?"dev":-1!==n.indexOf("test")?"trial":"release"},s.getLaunchOptionsSync=function(){return console.error("暂未实现方法"),null},s.getSetting=function(){return console.error("暂未实现方法"),Promise.resolve(!0)},s.getSystemInfoSync=function(){return console.error("暂未实现方法"),null},s.getLoginCode=function(){var n=r(o().mark((function n(){return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",new Promise((function(n,e){n("0f3Ny4000ufYVQ1phi100myyjh3Ny40G")})));case 1:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}(),s.serverLogin=function(){var n=r(o().mark((function n(){var e,r;return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:e=this.cat.util.stringUtil.getURLParameters(decodeURIComponent(window.location.href)),r=i(c,e.params),u.user.auth=r,window.ccLog("authParams",e,r);case 4:case"end":return n.stop()}}),n,this)})));return function(){return n.apply(this,arguments)}}(),s.getUserInfo=function(){return new Promise((function(n,e){n()}))},s.navigateToMiniProgram=function(){return console.error("暂未实现方法"),Promise.reject()},s.getSystemInfo=function(){return console.error("暂未实现方法"),Promise.reject()},s.getAppId=function(){console.error("暂未实现方法")},s.authorize=function(){return console.error("暂未实现方法"),Promise.reject()},s.showShareMenu=function(){console.error("暂未实现方法")},s.onShareAppMessage=function(n){console.error("暂未实现方法")},s.onShareTimeline=function(n){console.error("暂未实现方法")},t}(s));t._RF.pop()}}}));

System.register("chunks:///_virtual/index3.ts",["cc","./index40.ts","./index23.ts","./index32.ts","./index21.ts","./GlobalEventConstant.ts","./AudioEventConstant.ts"],(function(){var n,t,e,i,a,o,l,s;return{setters:[function(i){n=i.cclegacy,t=i.game,e=i.Game},null,function(n){i=n.default},function(n){a=n.GameVisiable},function(n){o=n.cat},function(n){l=n.GlobalEventConstant},function(n){s=n.AudioEventConstant}],execute:function(){n._RF.push({},"07961g73+xBCZ7+2tZahRRu","index",void 0),t.on(e.EVENT_SHOW,(function(){var n;i.global.showTime=Date.now(),i.global.gameVisiable=a.SHOW,null==(n=o.event)||null==(n=n.dispatchEvent(l.EVENT_SHOW))||n.dispatchEvent(s.RESUME_AUDIO)})),t.on(e.EVENT_HIDE,(function(){var n;i.global.hideTime=Date.now(),i.global.gameVisiable=a.HIDE,null==(n=o.event)||null==(n=n.dispatchEvent(l.EVENT_HIDE))||n.dispatchEvent(s.PAUSE_AUDIO)})),n._RF.pop()}}}));

System.register("chunks:///_virtual/index30.ts",["cc","./util.ts"],(function(t){var e,s;return{setters:[function(t){e=t.cclegacy},function(t){s=t.queryStringToObject}],execute:function(){e._RF.push({},"8db1dPw7KNDhbrJF0pMGZzd","index",void 0);var c=s(location.search.slice(1));t("default",{static_base_url:"",http_base_url:"https://test-cloud-game-api.stnts.com/",event_tracking_url:"https://dssp-test.stnts.com/?opt=put&type=json",event_tracking_key:"alibabatutudodo@",bucket:"",region:"",secret:"",player_ws_url:c.player_ws_url||"wss://game-ws-testing.mityoo.com/ws/connector",audience_ws_url:c.audience_ws_url||"wss://game-ws-testing.mityoo.com/ws/audience",stop_service_url:""});e._RF.pop()}}}));

System.register("chunks:///_virtual/index31.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseAPI.ts"],(function(n){var e,t,i;return{setters:[function(n){e=n.inheritsLoose},function(n){t=n.cclegacy},function(n){i=n.BaseAPI}],execute:function(){t._RF.push({},"9d64cUbAhJHfLyXrUJP9pJy","index",void 0);n("LoginAPI",function(n){function t(){return n.apply(this,arguments)||this}return e(t,n),t}(i));t._RF.pop()}}}));

System.register("chunks:///_virtual/index32.ts",["./rollupPluginModLoBabelHelpers.js","cc","./JSBridge.ts","./StringUtil.ts","./BaseStore.ts"],(function(e){var t,r,i,n,a,o;return{setters:[function(e){t=e.inheritsLoose,r=e.createClass},function(e){i=e.cclegacy},function(e){n=e.PlatformType},function(e){a=e.getURLParameters},function(e){o=e.BaseStore}],execute:function(){i._RF.push({},"9db2ct/3vRA/pfmj14jKxia","index",void 0);var u=e("CustomPlatform",function(e){return e[e.DaiDaiH5=0]="DaiDaiH5",e[e.SuiLeYoo=1]="SuiLeYoo",e}({})),s=e("GameVisiable",function(e){return e[e.SHOW=0]="SHOW",e[e.HIDE=1]="HIDE",e}({})),l=(e("URLParamsKey",function(e){return e.params="params",e.platform="platform",e.safe_top="safe_top",e.safe_bottom="safe_bottom",e.channel="channel",e.env="env",e.voice_room_version_num="voiceRoomVersionNum",e}({})),e("EnvType",function(e){return e.DEV="dev",e.TRIAL="trial",e.RELEASE="release",e}({})),e("ChannelType",function(e){return e.WEB="web",e.APP="app",e}({})));e("default",function(e){function i(){for(var t,r=arguments.length,i=new Array(r),n=0;n<r;n++)i[n]=arguments[n];return(t=e.call.apply(e,[this].concat(i))||this).customPlatform=u.SuiLeYoo,t.channel="",t.social_game_feature="daidai",t.social_game_id="bombduck",t.diffServerTimer=0,t.ws_diff_server_timer=0,t.pixelRatio=1,t.titleBarHeight=0,t.appId="",t.hideTime=0,t.showTime=0,t.gameVisiable=s.SHOW,t.url_params=a(decodeURIComponent(window.location.href)),t}return t(i,e),r(i,[{key:"isFlutterPlatform",get:function(){return this.url_params.platform===n.FLUTTER}},{key:"isChannelWeb",get:function(){return this.url_params.channel===l.WEB}},{key:"isDebugOrTestEnv",get:function(){return location.href.includes("debug=1")}},{key:"isGameVisiable",get:function(){return this.gameVisiable===s.SHOW}},{key:"isSupportNativePlayAudio",get:function(){var e=Number(this.url_params.voiceRoomVersionNum||0);return this.url_params.platform===n.IOS&&e>=1}}]),i}(o));i._RF.pop()}}}));

System.register("chunks:///_virtual/index33.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index23.ts","./BaseTracking.ts"],(function(n){var e,t,r,c;return{setters:[function(n){e=n.inheritsLoose},function(n){t=n.cclegacy},function(n){r=n.default},function(n){c=n.BaseTracking}],execute:function(){t._RF.push({},"a56651qXi1MYLRkc7fQP7KF","index",void 0);var i=n("TrackingNetworkEvent",function(n){return n.NETWORK_CONNECT="network_connect",n.DURATION_CHECK="duration_check",n}({}));n("TrackingNetwork",function(n){function t(){for(var e,t=arguments.length,c=new Array(t),o=0;o<t;o++)c[o]=arguments[o];return(e=n.call.apply(n,[this].concat(c))||this).networkConnect=function(){e.tracking.upload({event_type:i.NETWORK_CONNECT,value:{type:r.user.isAudience?"player":"viewer"}})},e}return e(t,n),t}(c));t._RF.pop()}}}));

System.register("chunks:///_virtual/index34.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseTracking.ts"],(function(n){var e,r,t;return{setters:[function(n){e=n.inheritsLoose},function(n){r=n.cclegacy},function(n){t=n.BaseTracking}],execute:function(){r._RF.push({},"a975f60YpJO9K5BU/mfLi8A","index",void 0);var i=n("TrackingLoadEvent",function(n){return n.GAME_LOADING="game_loading",n}({}));n("TrackingLoading",function(n){function r(){for(var e,r=arguments.length,t=new Array(r),a=0;a<r;a++)t[a]=arguments[a];return(e=n.call.apply(n,[this].concat(t))||this).loadingProgress=function(n){e.tracking.upload({event_type:i.GAME_LOADING,value:{progress:100*n+"%"}})},e}return e(r,n),r}(t));r._RF.pop()}}}));

System.register("chunks:///_virtual/index35.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseStore.ts","./bundle.js"],(function(e){var n,s,t,r,i;return{setters:[function(e){n=e.inheritsLoose,s=e.assertThisInitialized},function(e){t=e.cclegacy},function(e){r=e.BaseStore},function(e){i=e.makeAutoObservable}],execute:function(){t._RF.push({},"b2d97OZRNlCg59stREY+E5T","index",void 0);e("default",function(e){function t(n){var t;return(t=e.call(this,n)||this).profiles=new Map,t.roomList=[],t.levelRank={rankList:[],userRank:null},t.tierRank={gameSeasons:[],rankList:[],userRank:null},t.matchRoom={ddRid:"",roomSerial:"",imUsername:"",impassword:"",swToken:"",userId:0,partyMode:1},i(s(t)),t}return n(t,e),t}(r));t._RF.pop()}}}));

System.register("chunks:///_virtual/index36.ts",["cc"],(function(){var c;return{setters:[function(e){c=e.cclegacy}],execute:function(){c._RF.push({},"b46fcnrwc9IKaKy+OPPUazA","index",void 0),c._RF.pop()}}}));

System.register("chunks:///_virtual/index37.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index45.ts","./Bridge.ts","./index23.ts","./index48.ts","./index3.js","./index40.ts","./index21.ts","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./from-json.js","./RoomEventConstant.ts","./msg_pb.js"],(function(e){var o,t,n,a,c,i,m,r,A,E,N,d;return{setters:[function(e){o=e.inheritsLoose,t=e.asyncToGenerator,n=e.regeneratorRuntime},function(e){a=e.cclegacy},function(e){c=e.default},function(e){i=e.Bridge},function(e){m=e.default},function(e){r=e.H5API},null,null,function(e){A=e.cat},null,null,null,null,null,null,null,function(e){E=e.fromJson},function(e){N=e.RoomEventConstant},function(e){d=e.ClientBootParamSchema}],execute:function(){a._RF.push({},"b533fAO7WNDpaMpLx08aPA2","index",void 0);e("SuiLeYooNativeAPI",function(e){function a(){return e.apply(this,arguments)||this}o(a,e);var r=a.prototype;return r.netWorkStatusListener=function(){var e=this;c.nativeAPI.on(i.EventName.CommonName.NETWORK_AVAILABLE,(function(o){o?e.online():e.offline()}),this)},r.getEnvironmentAsync=function(){return new Promise((function(e,o){c.nativeAPI.once(i.EventName.CommonName.RES_COMPLETE,(function(e){}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_COMMON_REQUEST,i.EventName.CommonName.RES_COMPLETE)}))},r.serverLogin=function(){var e=t(n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,o){c.nativeAPI.once(i.EventName.APIName.ENTER_PARAM,(function(o){var t=E(d,o.data);m.user.auth=t,window.ccLog("authParams",JSON.stringify(t)),e()}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.ENTER_PARAM)})));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),r.share=function(e){void 0===e&&(e={type:1}),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.SHARE,e)},r.report=function(){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.REPORT)},r.copyText=function(e){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_COMMON_REQUEST,i.EventName.CommonName.COPY_TEXT,e)},r.loginChatRoom=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.ChatRoomName.LOGIN_CHAT_ROOM,(function(){window.ccLog("登录聊天室成功"),o()}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_CHATROOM_REQUEST,i.EventName.ChatRoomName.LOGIN_CHAT_ROOM,e)}))},r.logoutChatRoom=function(){return c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_CHATROOM_REQUEST,i.EventName.ChatRoomName.LOGOUT_CHAT_ROOM),this},r.back=function(){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.BACK)},r.task=function(){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.TASK)},r.mall=function(){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.MALL)},r.audience=function(){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.AUDIENCE)},r.matchRoom=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.MATCHING,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.MATCHING,e)}))},r.getAssets=function(){return new Promise((function(e,o){c.nativeAPI.once(i.EventName.APIName.ASSETS,(function(t){"errorCode"in t?o():(m.user.assets=t.data.point,e())}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.ASSETS)}))},r.searchRoom=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.SEARCH,(function(e){"errorCode"in e?t():(m.lobby.matchRoom=e.data,o(e.data))}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.SEARCH,e)}))},r.createRoom=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.CREATE,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.CREATE,e)}))},r.recharge=function(){},r.getUserInfo=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.USER_GAME_DATA,(function(e){"errorCode"in e?t():(m.user.userGameData=e.data,o())}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.USER_GAME_DATA,e)}))},r.getUserProfile=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.CHECK_USER_PROFILES,(function(e){window.ccLog("res",JSON.stringify(e)),"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.CHECK_USER_PROFILES,e)}))},r.getMikeList=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.MIKE_LIST,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.MIKE_LIST,e)}))},r.joinChangeMike=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.MIKE_CHANGE_JOIN,(function(n){"errorCode"in n?t():(A.event.has(N.MIKE_CHANGE)&&A.event.dispatchEvent(N.MIKE_CHANGE,e.mikeId),o())}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.MIKE_CHANGE_JOIN,e)}))},r.openProfile=function(e){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.AVATAR_CARD,e)},r.invite=function(){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.INVITE)},r.ready=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.READY,(function(e){"errorCode"in e?t():o()}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.READY,e)}))},r.unReady=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.UNREADY,(function(e){"errorCode"in e?t():o()}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.UNREADY,e)}))},r.banner=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.BridgeName.BANNER_SPECIAL,(function(e){o()}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.BANNER_SPECIAL,e)}))},r.getRoomInfo=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.ROOM_INFO,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.ROOM_INFO,e)}))},r.openGift=function(){var e,o=this;null!=(e=m.lobby.matchRoom)&&e.ddRid&&this.getMikeList({ddRid:m.lobby.matchRoom.ddRid}).then((function(e){o.updateMike(e),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.GIFT)}))},r.voiceState=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.CommonName.VOICE_STATE,(function(e){o()}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_COMMON_REQUEST,i.EventName.CommonName.VOICE_STATE,e)}))},r.micState=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.CommonName.MIC_STATE,(function(e){o()}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_COMMON_REQUEST,i.EventName.CommonName.MIC_STATE,e)}))},r.musicState=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.CommonName.MUSIC_STATE,(function(e){o()}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_COMMON_REQUEST,i.EventName.CommonName.MIC_STATE,e)}))},r.sendMessage=function(e){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_CHATROOM_REQUEST,i.EventName.ChatRoomName.SEND_MESSAGE,e)},r.inputMode=function(e){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_COMMON_REQUEST,i.EventName.CommonName.UPDATE_INPUTMODE,e)},r.follow=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.FOLLOW,(function(e){"errorCode"in e?t():o()}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.FOLLOW,e)}))},r.checkFollowedUser=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.CHECK_FOLLOWED,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.CHECK_FOLLOWED,e)}))},r.exitRoom=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.EXIT,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.EXIT,e)}))},r.updateMike=function(e){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_COMMON_REQUEST,i.EventName.CommonName.UPDATE_MIKE,e)},r.moreDialog=function(){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.MORE_DIALOG)},r.rule=function(){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.RULE)},r.exit=function(){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.EXIT)},r.openPrivateChat=function(e){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.PRIVATE_CHAT,e)},r.roomOpenTime=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.CommonName.ROOM_OPEN_TIME,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_COMMON_REQUEST,i.EventName.CommonName.ROOM_OPEN_TIME,e)}))},r.getAudienceList=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.VIEW_LIST,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.VIEW_LIST,e)}))},r.mikeUp=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.MIKE_UP,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.MIKE_UP,{ddRid:e})}))},r.readyDown=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.READY_DOWN,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.READY_DOWN,{ddRid:e})}))},r.ban=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.BAN,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.BAN,e)}))},r.mikeLock=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.MIKE_LOCK,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.MIKE_LOCK,e)}))},r.mikeUnlock=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.MIKE_UNLOCK,(function(e){"errorCode"in e?t():o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.MIKE_UNLOCK,e)}))},r.startGame=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIName.START_GAME,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIName.START_GAME,{ddRid:e})}))},r.asrConnect=function(e){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_COMMON_REQUEST,i.EventName.CommonName.ASRCONNECT,e)},r.asrOpen=function(e){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_COMMON_REQUEST,i.EventName.CommonName.ASROPEN,e)},r.clickEvent=function(e){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.CLICK_EVENT,e)},r.joinArena=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.JOIN_ARENA,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.JOIN_ARENA,e)}))},r.cancelJoinArena=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.EXIT_ARENA,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.EXIT_ARENA,e)}))},r.tierRank=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.TIER_RANK,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.TIER_RANK,e)}))},r.levelRank=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.LEVEL_RANK,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.LEVEL_RANK,e)}))},r.roomList=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.ROOM_LIST,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.ROOM_LIST,e)}))},r.roomExpertList=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.ROOM_EXPERT_LIST,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.ROOM_EXPERT_LIST,e)}))},r.quickJoin=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.QUICK_JOIN,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.QUICK_JOIN,e)}))},r.cancelQuickJoin=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.CANCEL_QUICK_JOIN,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.CANCEL_QUICK_JOIN,e)}))},r.seasonSettle=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.SEASON_SETTLE,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.SEASON_SETTLE,e)}))},r.like=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.LIKE,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.LIKE,e)}))},r.hangUpGame=function(){c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_BRIDGE_REQUEST,i.EventName.BridgeName.HANG_UP_GAME)},r.longLink=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.LONG_LINK,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.LONG_LINK,e)}))},r.modifyJoinState=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.MODIFY_JOIN_STATE,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.MODIFY_JOIN_STATE,e)}))},r.sitDown=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.SIT_DOWN,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.SIT_DOWN,e)}))},r.gameStateInfo=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.GAME_STATE_INFO,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.GAME_STATE_INFO,e)}))},r.taskReceive=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.TASK_RECEIVE,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.TASK_RECEIVE,e)}))},r.taskList=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.TASK,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.TASK,e)}))},r.inviteAccept=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.InviteAccept,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.InviteAccept,e)}))},r.inviteRefuse=function(e){return new Promise((function(o,t){c.nativeAPI.once(i.EventName.APIV2Name.InviteRefuse,(function(e){"errorCode"in e?t(e):o(e.data)}),Symbol()),c.cocosAPI.callStaticMethod(i.MethodName.CocosBridge.LOAD_API_REQUEST,i.EventName.APIV2Name.InviteRefuse,e)}))},a}(r));a._RF.pop()}}}));

System.register("chunks:///_virtual/index38.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseStore.ts"],(function(e){var n,t,o;return{setters:[function(e){n=e.inheritsLoose},function(e){t=e.cclegacy},function(e){o=e.BaseStore}],execute:function(){t._RF.push({},"bef51C8MYhIP7OHD/r7cUwX","index",void 0);e("default",function(e){function t(){for(var n,t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];return(n=e.call.apply(e,[this].concat(o))||this).code="",n.token="",n.ws_url="",n.isLogin=!0,n.gameStateInfo=null,n}return n(t,e),t}(o));t._RF.pop()}}}));

System.register("chunks:///_virtual/index39.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index23.ts","./BaseAPI.ts"],(function(n){var e,t,r,u,c;return{setters:[function(n){e=n.inheritsLoose,t=n.asyncToGenerator,r=n.regeneratorRuntime},function(n){u=n.cclegacy},null,function(n){c=n.BaseAPI}],execute:function(){u._RF.push({},"cbb49PZAPlFUbMSABckBc9Y","index",void 0);n("UserAPI",function(n){function u(){return n.apply(this,arguments)||this}return e(u,n),u.prototype.get_user_info=function(){var n=t(r().mark((function n(){return r().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:case 1:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}(),u}(c));u._RF.pop()}}}));

System.register("chunks:///_virtual/index4.ts",["cc","./index48.ts","./index2.ts","./index23.ts","./index32.ts","./index29.ts","./index21.ts","./index.ts","./index37.ts"],(function(t){var n,i,o,e,s,u,l;return{setters:[function(t){n=t.cclegacy,i=t.error},function(t){o=t.H5API},null,function(t){e=t.default},function(t){s=t.CustomPlatform},function(t){u=t.DaiDaiAPI},null,function(t){l=t.SuiLeYooH5API},null],execute:function(){n._RF.push({},"0e8b9U96+ZPk7TsMfD3GFxf","index",void 0);t("Platform",(function(t){this.platform=void 0,e.global.customPlatform==s.DaiDaiH5?this.platform=new u(t):e.global.customPlatform==s.SuiLeYoo?this.platform=new l(t):(i("默认H5平台运行环境"),this.platform=new o(t))}));n._RF.pop()}}}));

System.register("chunks:///_virtual/index40.ts",["cc","./AudioEffectConstant.ts","./GlobalEventConstant.ts","./GameEventConstant.ts","./AudioEventConstant.ts","./RoomEventConstant.ts","./AudioEffectConstant.b.ts","./GameEventConstant.b.ts"],(function(t){var n;return{setters:[function(t){n=t.cclegacy},null,function(n){t("GlobalEventConstant",n.GlobalEventConstant)},null,function(n){t("AudioEventConstant",n.AudioEventConstant)},function(n){t("RoomEventConstant",n.RoomEventConstant)},function(n){t("AudioEffectConstant",n.AudioEffectConstant)},function(n){t("GameEventConstant",n.GameEventConstant)}],execute:function(){n._RF.push({},"ccbb4rRq29LjYgnpj92a1lX","index",void 0),n._RF.pop()}}}));

System.register("chunks:///_virtual/index41.ts",["./rollupPluginModLoBabelHelpers.js","cc","./env","./BaseManager.ts"],(function(t){var e,i,n,r,l;return{setters:[function(t){e=t.inheritsLoose},function(t){i=t.cclegacy,n=t.sys},function(t){r=t.PREVIEW},function(t){l=t.BaseManager}],execute:function(){i._RF.push({},"d8de4YAK85FrYaXdUFYgYqU","index",void 0);t("StorageManager",function(t){function i(){for(var e,i=arguments.length,n=new Array(i),r=0;r<i;r++)n[r]=arguments[r];return(e=t.call.apply(t,[this].concat(n))||this)._key=null,e._iv=null,e._id="",e}e(i,t);var l=i.prototype;return l.init=function(t,e){this.cat.util.encryptUtil.initCrypto(t,e),this._key=this.cat.util.encryptUtil.md5(t),this._iv=this.cat.util.encryptUtil.md5(e)},l.setUser=function(t){this._id=t},l.set=function(t,e){if(null!=(t=t+"_"+this._id)){if(t=this.cat.util.encryptUtil.md5(t),null==e)return console.warn("存储的值为空，则直接移除该存储"),void this.remove(t);if("function"!=typeof e){if("object"==typeof e)try{e=JSON.stringify(e)}catch(t){return void console.error("解析失败，str = "+e)}else"number"==typeof e&&(e+="");null!=this._key&&null!=this._iv&&(e=this.cat.util.encryptUtil.aesEncrypt(""+e,this._key,this._iv)),n.localStorage.setItem(t,e)}else console.error("储存的值不能为方法")}else console.error("存储的key不能为空")},l.get=function(t,e){if(null==t)return console.error("存储的key不能为空"),null;t=t+"_"+this._id,t=this.cat.util.encryptUtil.md5(t);var i=n.localStorage.getItem(t);return null==i||""===i||r||null==this._key||null==this._iv||(i=this.cat.util.encryptUtil.aesDecrypt(i,this._key,this._iv)),null===i?e:i},l.getNumber=function(t,e){void 0===e&&(e=0);var i=this.get(t);return Number(i)||e},l.getBoolean=function(t){var e=this.get(t);return Boolean(e)||!1},l.getJson=function(t,e){var i=this.get(t);return i&&JSON.parse(i)||e},l.remove=function(t){null!=t?(t=t+"_"+this._id,t=this.cat.util.encryptUtil.md5(t),n.localStorage.removeItem(t)):console.error("存储的key不能为空")},l.clear=function(){n.localStorage.clear()},i}(l));i._RF.pop()}}}));

System.register("chunks:///_virtual/index42.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts","./BaseStore.ts","./bundle.js"],(function(t){var e,i,o,n,r,s,c;return{setters:[function(t){e=t.inheritsLoose,i=t.createClass,o=t.assertThisInitialized},function(t){n=t.cclegacy},function(t){r=t.cat},function(t){s=t.BaseStore},function(t){c=t.makeAutoObservable}],execute:function(){n._RF.push({},"dc68aV2rldEp79v7tfFsiz7","index",void 0);t("default",function(t){function n(e){var i;return(i=t.call(this,e)||this).gameId=1017,i.micState=0,i._voiceState=0,i.giftState=0,i._musicState=0,i.invitePlay=!1,i.appType=0,i.gameState=0,i.banner=1,i.roomUserlist=new Map,i.fetchProfiles=new Map,i.mikeUserList=[],i.roomOwner=-1,i.roomInfo={ddRid:"",roomSerial:"",roomName:"",partyMode:0,announcement:"",readyStatus:0,duration:0,imUsername:"",impassword:"",swToken:"",openTime:"",joinState:0,superManager:0},c(o(i)),i}e(n,t);var s=n.prototype;return s.addList=function(t){window.ccLog("添加成员",t),t.id&&this.roomUserlist.set(t.id,t)},s.removeList=function(t){var e=this;window.ccLog("移除成员"),t.forEach((function(t){e.roomUserlist.delete(t)}))},s.clearList=function(){window.ccLog("清除成员"),this.roomUserlist.clear()},s.getRoomUserById=function(t){return this.roomUserlist.get(t)},s.hasRoomUser=function(t){return"number"==typeof t&&this.roomUserlist.has(t)},s.getMikeUserByKey=function(t,e){return this.mikeUserList.find((function(i){return i[t]===e}))},s.resetMatch=function(){this.roomOwner=-1,this.mikeUserList=[]},i(n,[{key:"voiceState",get:function(){return this._voiceState},set:function(t){this._voiceState=t,r.storage.set("crazy_watermelon_voiceState",t)}},{key:"musicState",get:function(){return this._musicState},set:function(t){this._musicState=t}},{key:"selfMick",get:function(){var t=this;return this.mikeUserList.find((function(e){return e.userId===t.rootStore.lobby.matchRoom.userId}))}},{key:"isRoomOwner",get:function(){return this.selfMick&&this.selfMick.userId===this.roomOwner}}]),n}(s));n._RF.pop()}}}));

System.register("chunks:///_virtual/index43.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var t,n,s,r;return{setters:[function(e){t=e.createForOfIteratorHelperLoose},function(e){n=e.cclegacy,s=e.warn,r=e.error}],execute:function(){n._RF.push({},"df149MUag5DNZb7Nfzv2i4p","index",void 0);e("MessageManager",function(){function e(){this.events=new Map}var n=e.prototype;return n.on=function(e,t,n){var r;if(!e||!t)return s("注册【"+e+"】事件的侦听器函数为空"),this;var i=null!=(r=this.events.get(e))?r:this.events.set(e,new Map).get(e);return i.has(n)?(s("名为【"+e+"】的事件重复注册侦听器"),this):(i.set(n,t),this)},n.once=function(e,t,n){var s=this,r=function(i,o){s.off(e,r,n),r=null,t.call(n,i,o)};this.on(e,r,n)},n.off=function(e,t,n){var s;if(null==(s=this.events.get(e))||!s.has(n))return this;var i=this.events.get(e);return i.get(n)!==t?(r(n+"注册事件和取消事件不一致"),this):(i.delete(n),this)},n.dispatchEvent=function(e,n){if(!this.events.has(e))return this;for(var s,r=this.events.get(e),i=t(r);!(s=i()).done;){var o=s.value,u=o[0];o[1].call(u,n,e)}return this},n.offAll=function(){this.events.clear()},n.has=function(e){return this.events.has(e)},n.deleteEventByComponent=function(e){for(var n,s=t(this.events);!(n=s()).done;){for(var r,i=n.value,o=i[0],u=i[1],a=t(u);!(r=a()).done;){var v=r.value[0];v===e&&u.delete(v)}0===u.size&&this.events.delete(o)}},n.getEvents=function(e){return this.events.get(e)},e}());n._RF.pop()}}}));

System.register("chunks:///_virtual/index44.ts",["cc"],(function(n){var t;return{setters:[function(n){t=n.cclegacy}],execute:function(){t._RF.push({},"dfff6f4fT1FooZ0l16+Cacd","index",void 0);n("GlobalEventConstant",function(n){return n.EVENT_SHOW="GlobalEventConstant/EVENT_SHOW",n.EVENT_HIDE="GlobalEventConstant/EVENT_HIDE",n.GAME_RESIZE="GlobalEventConstant/GAME_RESIZE",n.EVENT_CLOSE="GlobalEventConstant/EVENT_CLOSE",n.ONLINE="GlobalEventConstant/ONLINE",n.OFFLINE="GlobalEventConstant/OFFLINE",n.ROOT_MASK_UPDATE="GlobalEventConstant/ROOT_MASK_UPDATE",n.ROOT_MASK_CHANGE="GlobalEventConstant/ROOT_MASK_CHANGE",n}({}));t._RF.pop()}}}));

System.register("chunks:///_virtual/index45.ts",["cc","./Cocos.ts","./Native.ts"],(function(t){var e,n,c;return{setters:[function(t){e=t.cclegacy},function(t){n=t.CocosAPI},function(t){c=t.NativeAPI}],execute:function(){e._RF.push({},"f0e903aQ4RM+5kCYAdZiIvu","index",void 0);var o=new n,i=new c;t("default",{nativeAPI:i,cocosAPI:o});window.PlatformToTS=i,e._RF.pop()}}}));

System.register("chunks:///_virtual/index46.ts",["cc"],(function(e){var c;return{setters:[function(e){c=e.cclegacy}],execute:function(){e("default",(function(){return null})),c._RF.push({},"f551eRmzBZDrbgcccNXqNWZ","index",void 0),c._RF.pop()}}}));

System.register("chunks:///_virtual/index47.ts",["cc","./index.b2.ts"],(function(e){var s;return{setters:[function(e){s=e.cclegacy},function(s){var t={};t.SocialGameBusinessSocket=s.SocialGameBusinessSocket,t.businessEventResponsePairs=s.businessEventResponsePairs,t.createProxySocket=s.createProxySocket,e(t)}],execute:function(){s._RF.push({},"f8ef1OYtoVNKqkxz7Y6T+YN","index",void 0),s._RF.pop()}}}));

System.register("chunks:///_virtual/index48.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index23.ts","./index44.ts","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./from-json.js","./index3.js","./msg_pb.js"],(function(e){var o,n,t,r,i,c,s;return{setters:[function(e){o=e.asyncToGenerator,n=e.regeneratorRuntime},function(e){t=e.cclegacy},function(e){r=e.default},function(e){i=e.GlobalEventConstant},null,null,null,null,null,null,null,function(e){c=e.fromJsonString},null,function(e){s=e.ClientBootParamSchema}],execute:function(){t._RF.push({},"fd03433rvdE5oJQGoMv3ZPa","index",void 0);e("H5API",function(){function e(e){this.cat=void 0,this.cat=e,this.netWorkStatusListener()}var t=e.prototype;return t.inviteAccept=function(e){return console.error("Method not implemented."),Promise.reject()},t.inviteRefuse=function(e){return console.error("Method not implemented."),Promise.reject()},t.taskReceive=function(e){return console.error("Method not implemented."),Promise.reject()},t.taskList=function(e){return console.error("Method not implemented."),Promise.reject()},t.gameStateInfo=function(e){return console.error("Method not implemented."),Promise.reject()},t.sitDown=function(e){return console.error("Method not implemented."),Promise.reject()},t.hangUpGame=function(){console.error("Method not implemented.")},t.longLink=function(e){return console.error("Method not implemented."),Promise.reject()},t.modifyJoinState=function(e){return console.error("Method not implemented."),Promise.reject()},t.joinArena=function(e){return console.error("Method not implemented."),Promise.reject()},t.cancelJoinArena=function(e){return console.error("Method not implemented."),Promise.reject()},t.tierRank=function(e){return console.error("Method not implemented."),Promise.reject()},t.levelRank=function(e){return console.error("Method not implemented."),Promise.reject()},t.roomList=function(e){return console.error("Method not implemented."),Promise.reject()},t.roomExpertList=function(e){return console.error("Method not implemented."),Promise.reject()},t.quickJoin=function(e){return console.error("Method not implemented."),Promise.reject()},t.cancelQuickJoin=function(e){return console.error("Method not implemented."),Promise.reject()},t.seasonSettle=function(e){return console.error("Method not implemented."),Promise.reject()},t.like=function(e){return console.error("Method not implemented."),Promise.reject()},t.clickEvent=function(e){},t.asrConnect=function(e){console.error("Method not implemented.")},t.asrOpen=function(e){console.error("Method not implemented.")},t.startGame=function(e){return console.error("Method not implemented."),Promise.reject()},t.ban=function(e){return console.error("Method not implemented."),Promise.reject()},t.mikeLock=function(e){return console.error("Method not implemented."),Promise.reject()},t.mikeUnlock=function(e){return console.error("Method not implemented."),Promise.reject()},t.mikeUp=function(e){return console.error("Method not implemented."),Promise.reject()},t.readyDown=function(e){return console.error("Method not implemented."),Promise.reject()},t.getAudienceList=function(e){return Promise.reject()},t.netWorkStatusListener=function(){var e=this;window.addEventListener("online",(function(){e.online()})),window.addEventListener("offline",(function(){e.offline()}))},t.online=function(){window.ccLog("online"),this.cat.event.dispatchEvent(i.ONLINE)},t.offline=function(){window.ccLog("offline"),this.cat.event.dispatchEvent(i.OFFLINE)},t.createRoom=function(e){return console.error("Method not implemented."),Promise.reject()},t.roomOpenTime=function(e){return console.error("Method not implemented."),Promise.reject()},t.openPrivateChat=function(e){console.error("Method not implemented.")},t.moreDialog=function(){console.error("Method not implemented.")},t.updateMike=function(e){console.error("Method not implemented.")},t.exitRoom=function(e){return console.error("Method not implemented."),Promise.reject()},t.checkFollowedUser=function(e){return console.error("Method not implemented."),Promise.reject()},t.follow=function(e){return Promise.resolve()},t.inputMode=function(e){console.error("Method not implemented.")},t.loginChatRoom=function(e){return Promise.resolve()},t.sendMessage=function(e){console.error("Method not implemented.")},t.voiceState=function(e){return Promise.resolve()},t.micState=function(e){return Promise.resolve()},t.musicState=function(e){return Promise.resolve()},t.openGift=function(){console.error("Method not implemented.")},t.getRoomInfo=function(e){return console.error("Method not implemented."),Promise.reject()},t.banner=function(e){return Promise.resolve()},t.ready=function(e){return console.error("Method not implemented."),Promise.reject()},t.unReady=function(e){return console.error("Method not implemented."),Promise.reject()},t.invite=function(){window.ccLog("邀请好友")},t.openProfile=function(e){window.ccLog("打开"+e+"个人简介")},t.joinChangeMike=function(e){return console.error("Method not implemented."),Promise.reject()},t.getMikeList=function(e){return console.error("Method not implemented."),Promise.reject()},t.getUserProfile=function(e){return console.error("Method not implemented."),Promise.reject()},t.getEnvironmentAsync=function(){var e=""+window.location.origin+window.location.pathname+" ";return new Promise((function(o,n){setTimeout((function(){if(window.ccLog("获取环境"),r.global.url_params.env)o(r.global.url_params.env);else{var n=-1!==e.indexOf("dev")||-1!==e.indexOf("local")||-1!==e.indexOf("192.168")?"dev":-1!==e.indexOf("test")?"trial":"release";o(n)}}),1e3)}))},t.getLaunchOptionsSync=function(){return console.error("暂未实现方法"),null},t.getSetting=function(){return console.error("暂未实现方法"),Promise.resolve(!0)},t.getSystemInfoSync=function(){return console.error("暂未实现方法"),null},t.getLoginCode=function(){var e=o(n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,o){e("0f3Ny4000ufYVQ1phi100myyjh3Ny40G")})));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),t.serverLogin=function(){var e=o(n().mark((function e(){var o,t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:o=this.cat.util.stringUtil.getURLParameters(decodeURIComponent(window.location.href)),t=c(s,o.params),r.user.auth=t,window.ccLog("authParams",o,t);case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),t.getUserInfo=function(e){return new Promise((function(e,o){e()}))},t.navigateToMiniProgram=function(){return console.error("暂未实现方法"),Promise.resolve()},t.getSystemInfo=function(){return console.error("暂未实现方法"),Promise.resolve()},t.getAppId=function(){console.error("暂未实现方法")},t.authorize=function(){return console.error("暂未实现方法"),Promise.resolve()},t.showShareMenu=function(){console.error("暂未实现方法")},t.onShareAppMessage=function(e){console.error("暂未实现方法")},t.onShareTimeline=function(e){console.error("暂未实现方法")},t.share=function(e){console.error("Method not implemented.")},t.report=function(){console.error("Method not implemented.")},t.copyText=function(e){console.error("Method not implemented.")},t.logoutChatRoom=function(){return window.ccLog("logged out"),this},t.back=function(){console.error("Method not implemented.")},t.task=function(){console.error("Method not implemented.")},t.mall=function(){console.error("Method not implemented.")},t.audience=function(){console.error("Method not implemented.")},t.matchRoom=function(e){return console.error("Method not implemented."),Promise.reject()},t.getAssets=function(){return console.error("Method not implemented."),Promise.reject()},t.searchRoom=function(e){return console.error("Method not implemented."),Promise.reject()},t.recharge=function(){console.error("Method not implemented.")},e}());t._RF.pop()}}}));

System.register("chunks:///_virtual/index5.ts",["./rollupPluginModLoBabelHelpers.js","cc","./AudioEffect.ts","./AudioMusic.ts","./index40.ts","./index23.ts","./index32.ts","./BaseManager.ts","./JSBridge.ts","./index44.ts","./AudioEventConstant.ts"],(function(t){var i,e,s,c,u,o,n,a,l,f,h,r,m,_,d,v,p,w,g;return{setters:[function(t){i=t.inheritsLoose,e=t.createClass,s=t.assertThisInitialized,c=t.asyncToGenerator,u=t.regeneratorRuntime},function(t){o=t.cclegacy,n=t.Node,a=t.director},function(t){l=t.AudioEffect},function(t){f=t.AudioMusic},null,function(t){h=t.default},function(t){r=t.CustomPlatform},function(t){m=t.BaseManager},function(t){_=t.JSBridgeClient,d=t.AudioState,v=t.JSBridgeWebView,p=t.EnumJSBridgeWebView},function(t){w=t.GlobalEventConstant},function(t){g=t.AudioEventConstant}],execute:function(){o._RF.push({},"112917CYdBAuY6VVhJhCAmH","index",void 0);var M="game_audio";t("AudioManager",function(t){function o(i){var e;(e=t.call(this,i)||this).local_data={},e.music=void 0,e.effect=void 0,e._volume_music=.3,e._volume_effect=1,e._switch_music=!0,e._switch_effect=!0;var c=new n("UIAudioManager");a.addPersistRootNode(c);var u=new n("UIMusic");u.parent=c,e.music=u.addComponent(f).initAudio(i);var o=new n("UIEffect");return o.parent=c,e.effect=o.addComponent(l).initAudio(i).preloadAll(),h.global.customPlatform===r.SuiLeYoo&&v.on(p.CHANGE_AUDIO_STATE,(function(t){window.ccLog("SuiLeYoo音频状态改变",t),e.switchEffect=e.switchMusic=Number(t)===d.ON}),s(e)),i.event.on(w.EVENT_HIDE,e.onHandleAppBackground,s(e)),e.load(),e}i(o,t);var m=o.prototype;return m.onHandleAppBackground=function(){var t=this;setTimeout((function(){var i,e,s,c;h.global.isSupportNativePlayAudio||(t.switchMusic&&(null==(i=t.music)||!i.playing)&&(null==(e=t.music)||e.play()),t.switchEffect&&(null==(s=t.effect)||!s.playing)&&(null==(c=t.effect)||c.play()))}),20)},m.setMusicComplete=function(t){void 0===t&&(t=null),this.music.onComplete=t},m.playMusic=function(t,i){var e=this;this.music.loop=!0,t&&this.music.load(t,(function(){var t,s;e._switch_music&&e.music&&(h.global.isSupportNativePlayAudio&&e.music.clip?(window.ccLog("AudioManager--\x3eplayMusic(), data:",null==(t=e.music.clip)?void 0:t.nativeUrl),_.playMusic(e.music.clip.nativeUrl)):null==(s=e.music)||s.play());i&&i()}))},m.stopMusic=function(){var t;(window.ccLog("AudioManager--\x3estopMusic(),enter",this.music.state),h.global.isSupportNativePlayAudio)||0!=this.music.state&&(window.ccLog("AudioManager--\x3estopMusic(),music?.stop()"),null==(t=this.music)||t.stop())},m.pauseMusic=function(){var t;(window.ccLog("AudioManager--\x3epauseMusic(),enter",this.music.state),h.global.isSupportNativePlayAudio)||1==this.music.state&&(window.ccLog("AudioManager--\x3epauseMusic(),music?.pause()"),null==(t=this.music)||t.pause())},m.resumeMusic=function(){var t;(window.ccLog("AudioManager--\x3eresumeMusic(),enter",this.music.state),h.global.isSupportNativePlayAudio)?this.music.clip&&_.playMusic(this.music.clip.nativeUrl):[0,2].includes(this.music.state)&&(window.ccLog("AudioManager--\x3eresumeMusic(),music?.play()"),null==(t=this.music)||t.play())},m.playEffect=function(){var t=c(u().mark((function t(i){return u().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._switch_effect&&i){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,this.effect.loadAndPlay(i);case 4:case"end":return t.stop()}}),t,this)})));return function(i){return t.apply(this,arguments)}}(),m.stopEffect=function(){var t;null==(t=this.effect)||t.stop()},m.save=function(){if(this.local_data.volume_music=this._volume_music,this.local_data.volume_effect=this._volume_effect,this.local_data.switch_music=this._switch_music,this.local_data.switch_effect=this._switch_effect,h.global.customPlatform!==r.SuiLeYoo){var t=JSON.stringify(this.local_data);this.cat.storage.set(M,t)}},m.load=function(){var t=this;if(h.global.customPlatform===r.SuiLeYoo)_.getAudioState().then((function(i){window.ccLog("SuiLeYoo音频状态",i),t.switchEffect=t.switchMusic=Number(i)===d.ON})),this.music&&(this.music.volume=this._volume_music),this.effect&&(this.effect.volume=this._volume_effect);else{try{var i=this.cat.storage.get(M);this.local_data=JSON.parse(i),this._volume_music=this.local_data.volume_music,this._volume_effect=this.local_data.volume_effect,this._switch_music=this.local_data.switch_music,this._switch_effect=this.local_data.switch_effect}catch(t){this.local_data={},this._volume_music=.6,this._volume_effect=1,this._switch_music=!0,this._switch_effect=!0}this.music&&(this.music.volume=this._volume_music),this.effect&&(this.effect.volume=this._volume_effect)}},e(o,[{key:"progressMusic",get:function(){return this.music.progress},set:function(t){this.music.progress=t}},{key:"volumeMusic",get:function(){return this._volume_music},set:function(t){this._volume_music=t,this.music.volume=t}},{key:"switchMusic",get:function(){return this._switch_music},set:function(t){if(window.ccLog("AudioManager--\x3eswitchMusic():",t,this._switch_music),t!=this._switch_music){this._switch_music=t,t?this.resumeMusic():this.pauseMusic();var i=t?g.MUSIC_ON:g.MUSIC_OFF;this.cat.event.has(i)&&this.cat.event.dispatchEvent(i),this.save()}}},{key:"volumeEffect",get:function(){return this._volume_effect},set:function(t){this._volume_effect=t,this.effect.volume=t}},{key:"switchEffect",get:function(){return this._switch_effect},set:function(t){var i,e;if(t!=this._switch_effect){this._switch_effect=t,t?null==(i=this.effect)||i.play():null==(e=this.effect)||e.stop();var s=t?g.EFFECT_ON:g.EFFECT_OFF;this.cat.event.has(s)&&this.cat.event.dispatchEvent(s),this.save()}}}]),o}(m));o._RF.pop()}}}));

System.register("chunks:///_virtual/index6.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var t,n,s,r,i,o,u,a;return{setters:[function(e){t=e.extends,n=e.asyncToGenerator,s=e.regeneratorRuntime},function(e){r=e.cclegacy,i=e.assetManager,o=e.Asset,u=e.Prefab,a=e.resources}],execute:function(){r._RF.push({},"154154ja3VEfIu/QBoulXsL","index",void 0);e("ResLoader",function(){function e(){var e=this;this.get=function(e,t,n){return void 0===n&&(n="resources"),i.getBundle(n).get(e,t)},this.isAssetType=function(e){return"function"==typeof e&&e.prototype instanceof o},this.loadBundle=function(e,t){return new Promise((function(n,s){var r=function(e,t){if(e)return s(e);n(t)};t?i.loadBundle(e,{version:t},r):i.loadBundle(e,r)}))},this.releasePrefabtDepsRecursively=function(e){var t=i.assets.get(e);(i.releaseAsset(t),t instanceof u)&&i.dependUtil.getDepsRecursively(e).forEach((function(e){i.assets.get(e).decRef()}))},this.release=function(t,n){void 0===n&&(n="resources");var s=i.getBundle(n);if(s){var r=s.get(t);r&&e.releasePrefabtDepsRecursively(r._uuid)}},this.releaseDir=function(t,n){void 0===n&&(n="resources");var s=i.getBundle(n),r=null==s?void 0:s.getDirWithPath(t);null==r||r.map((function(t){e.releasePrefabtDepsRecursively(t.uuid)})),!t&&"resources"!=n&&s&&i.removeBundle(s)},this.dump=function(){i.assets.forEach((function(e,t){window.ccLog(i.assets.get(t))})),window.ccLog("当前资源总数:"+i.assets.count)}}var r=e.prototype;return r.load=function(){var e=n(s().mark((function e(){var t,n,r,o,u,l,c,f,d,h,p=arguments;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(t=null,n=null,r=null,o=p.length,u=new Array(o),l=0;l<o;l++)u[l]=p[l];if("string"==typeof u[0]&&"string"==typeof u[1]&&(t=u.shift()),c=u.shift(),this.isAssetType(u[0])&&(n=u.shift()),2==u.length&&(r=u.shift()),f=u.shift(),d=a,!t||"resources"==t){e.next=17;break}if(i.bundles.has(t)){e.next=15;break}return e.next=15,this.loadBundle(t);case 15:(h=i.bundles.get(t))&&(d=h);case 17:r&&f?d.load(c,n,r,f):f?d.load(c,n,f):d.load(c,n);case 18:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.loadDir=function(){var e=n(s().mark((function e(){var t,n,r,o,u,l,c,f,d,h,p=arguments;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(t=null,n=null,r=null,o=null,u=p.length,l=new Array(u),c=0;c<u;c++)l[c]=p[c];if("string"==typeof l[0]&&"string"==typeof l[1]&&(t=l.shift()),f=l.shift(),this.isAssetType(l[0])&&(n=l.shift()),2==l.length&&(r=l.shift()),o=l.shift(),d=a,!t||"resources"==t){e.next=17;break}if(i.bundles.has(t)){e.next=15;break}return e.next=15,this.loadBundle(t);case 15:(h=i.bundles.get(t))&&(d=h);case 17:r&&o?d.loadDir(f,n,r,o):o?d.loadDir(f,n,o):d.loadDir(f,n);case 18:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.loadRemote=function(e){for(var n=null,s=null,r=arguments.length,o=new Array(r>1?r-1:0),u=1;u<r;u++)o[u-1]=arguments[u];2==o.length&&(n=o.shift()),s=o.shift(),i.loadRemote(e,t({ext:".png"},n),s)},e}());r._RF.pop()}}}));

System.register("chunks:///_virtual/index7.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index3.js","./index23.ts","./index47.ts","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./from-binary.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./index21.ts","./BaseManager.ts","./msg_pb.js","./index.b2.ts"],(function(e){var n,t,s,a,r,c,i,o,u,d,l,f;return{setters:[function(e){n=e.inheritsLoose},function(e){t=e.cclegacy,s=e.error},null,function(e){a=e.default},null,null,null,null,function(e){r=e.fromBinary},null,null,null,null,function(e){c=e.cat},function(e){i=e.BaseManager},function(e){o=e.WatchBroadcastType,u=e.BattleInitialDataSchema,d=e.PlayerSchema,l=e.WatchBroadcastMessageSchema},function(e){f=e.businessEventResponsePairs}],execute:function(){t._RF.push({},"2c7ffP0sQJEZYkvDsfiyCGm","index",void 0);var p=[].concat([{event:"BattleInitialize",responseType:u},{event:"PlayerStatusChanged",responseType:d},{event:"WatchBroadcast",responseType:l}],f).reduce((function(e,n){return e[n.event]=n.responseType,e}),{}),h=["DataBroadcast"];e("WatchSystem",function(e){function t(n){var t;return(t=e.call(this,n)||this).init(),t}n(t,e);var i=t.prototype;return i.init=function(){this.cat.event.on("WatchBroadcast",this.onWatchBroadcastHandler,this)},i.onWatchBroadcastHandler=function(e){var n=e.type,t=e.offset,i=e.data,u=(e.request,e.response,a.user),d=e.route;window.ccLog("%c 观战路由 %c["+new Date+"] %c "+d+" %c","background:#ff00ff ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff","background:#3d7d3d ; padding: 1px; color: #fff","background:#ff00ff ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff","background:transparent",e);var l=null;p[d]?([o.PRIVATE,o.PLAYER_REQUEST].includes(n)?t===u.userIndex&&(l=n===o.PLAYER_REQUEST?e:r(p[d],i)):h.includes(d)&&u.userIndex!==t||(l=r(p[d],i)),l&&c.event.dispatchEvent(d,l)):h.includes(d)||s("未处理路由:"+d)},t}(i));t._RF.pop()}}}));

System.register("chunks:///_virtual/index8.ts",["cc","./index21.ts"],(function(t){var i,e,d;return{setters:[function(t){i=t.cclegacy,e=t.error},function(t){d=t.cat}],execute:function(){i._RF.push({},"38da6i49R1Hi5VLz+UgQD76","index",void 0);t("default",function(){function t(){var t=this;this.interstitialAd=void 0,this.rewardedVideoAd=void 0,this.interstitialAdCount=1,this.hideInterstitialAd=function(i){if(i){var e=t.interstitialAd.get(i);i||t.interstitialAd.forEach((function(t){null==t||t.destroy()})),null==e||e.destroy()}}}var i=t.prototype;return i.getRewardedVideoAdInstance=function(){return this.rewardedVideoAd},i.showRewardedVideoAd=function(){var t=this;return new Promise((function(i,n){var r;t.rewardedVideoAd||(t.rewardedVideoAd=wx.createRewardedVideoAd({adUnitId:"adunit-76394f0e8cf4f036",multiton:!0})),t.rewardedVideoAd.onLoad((function(){i()})),t.rewardedVideoAd.onError((function(t){e(t),n()})),t.rewardedVideoAd.onClose((function(t){t&&t.isEnded?window.ccLog("正常播放结束"):(window.ccLog("播放中途退出"),d.gui.showToast({title:"需要完整观看视频才能获得奖励哦！"}))})),null==(r=t.rewardedVideoAd)||r.load()}))},i.hideRewardedVideoAd=function(){var t;null==(t=this.rewardedVideoAd)||t.destroy()},i.showInterstitialAd=function(){var t=this;return new Promise((function(i,d){var n=wx.createInterstitialAd({adUnitId:"adunit-363fbc552a67bdb6"});t.interstitialAdCount+=1,t.interstitialAd.set(t.interstitialAdCount,n),n.onError((function(t){e(t),d()})),n.onClose((function(t){n.destroy()})),n.onLoad((function(){i(t.interstitialAdCount)})),n.load()}))},t}());i._RF.pop()}}}));

System.register("chunks:///_virtual/index9.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index45.ts","./Bridge.ts","./BaseAPI.ts"],(function(t){var e,r,n,a,u,c,o;return{setters:[function(t){e=t.inheritsLoose,r=t.asyncToGenerator,n=t.regeneratorRuntime},function(t){a=t.cclegacy},function(t){u=t.default},function(t){c=t.Bridge},function(t){o=t.BaseAPI}],execute:function(){a._RF.push({},"3ed36AhegVO25QIDtKH8MnN","index",void 0);t("SuileyooAPI",function(t){function a(){for(var e,a=arguments.length,o=new Array(a),s=0;s<a;s++)o[s]=arguments[s];return(e=t.call.apply(t,[this].concat(o))||this).serverLogin=function(){return e.cat.http({url:c.EventName.APIName.ENTER_PARAM,method:"POST"})},e.getAssets=function(){return e.cat.http({url:c.EventName.APIName.ASSETS,method:"POST",data:{}})},e.matchRoom=function(t){return e.cat.http({url:c.EventName.APIName.MATCHING,method:"POST",data:t})},e.searchRoom=function(t){return e.cat.http({url:c.EventName.APIName.SEARCH,method:"POST",data:t})},e.getUserInfo=function(t){return e.cat.http({url:c.EventName.APIName.USER_GAME_DATA,method:"POST",data:t})},e.getUserProfile=r(n().mark((function t(r){var a;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIName.CHECK_USER_PROFILES,method:"POST",data:r});case 2:return a=t.sent,u.nativeAPI.dispatchEvent(c.EventName.APIName.CHECK_USER_PROFILES,{code:0,data:a}),t.abrupt("return",a);case 5:case"end":return t.stop()}}),t)}))),e.getMikeList=function(t){return e.cat.http({url:c.EventName.APIName.MIKE_LIST,method:"POST",data:t})},e.joinChangeMike=function(t){return e.cat.http({url:c.EventName.APIName.MIKE_CHANGE_JOIN,method:"POST",data:t})},e.ready=function(t){return e.cat.http({url:c.EventName.APIName.READY,method:"POST",data:t})},e.unReady=function(t){return e.cat.http({url:c.EventName.APIName.UNREADY,method:"POST",data:t})},e.getRoomInfo=function(t){return e.cat.http({url:c.EventName.APIName.ROOM_INFO,method:"POST",data:t})},e.exitRoom=function(t){return e.cat.http({url:c.EventName.APIName.EXIT,method:"POST",data:t})},e.roomList=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",e.cat.http({url:c.EventName.APIV2Name.ROOM_LIST,method:"POST",data:r}));case 1:case"end":return t.stop()}}),t)}))),e.roomExpertList=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.ROOM_EXPERT_LIST,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.tierRank=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.TIER_RANK,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.levelRank=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.LEVEL_RANK,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.seasonSettle=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.SEASON_SETTLE,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.createRoom=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIName.CREATE,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.longLink=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.LONG_LINK,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.joinArena=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.JOIN_ARENA,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.quickJoin=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.QUICK_JOIN,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.cancelQuickJoin=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.CANCEL_QUICK_JOIN,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.cancelJoinArena=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.EXIT_ARENA,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.gameStateInfo=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.GAME_STATE_INFO,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.taskReceive=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.TASK_RECEIVE,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.task=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.TASK,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e.modifyJoinState=r(n().mark((function t(r){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.cat.http({url:c.EventName.APIV2Name.MODIFY_JOIN_STATE,method:"POST",data:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)}))),e}return e(a,t),a}(o));a._RF.pop()}}}));

System.register("chunks:///_virtual/InitialScene.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index21.ts"],(function(n){var t,e,o,r,i,c;return{setters:[function(n){t=n.inheritsLoose,e=n.asyncToGenerator,o=n.regeneratorRuntime},function(n){r=n.cclegacy},function(n){i=n.BaseComponent},function(n){c=n.cat}],execute:function(){r._RF.push({},"2deabpbgxBJ+YtvPFd0zchb","InitialScene",void 0);n("default",function(n){function r(){return n.apply(this,arguments)||this}t(r,n);var i=r.prototype;return i.onLoad=function(){var n=e(o().mark((function n(){return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,c.boot();case 2:this.onLoaded();case 3:case"end":return n.stop()}}),n,this)})));return function(){return n.apply(this,arguments)}}(),i.onLoaded=function(){},r}(i));r._RF.pop()}}}));

System.register("chunks:///_virtual/Interaction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./CardItem.ts","./LightCardItem.ts","./HandCard.ts","./CommonAction.ts","./msg_pb.ts","./BaseComponent.ts","./index21.ts","./index40.ts","./index23.ts","./AudioEffectConstant.b.ts","./GameEventConstant.b.ts"],(function(e){var t,n,r,o,a,i,s,c,d,u,l,p,f,h,m,y,_,v,w,g,C,P,b,x,T;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,r=e.initializerDefineProperty,o=e.assertThisInitialized,a=e.asyncToGenerator,i=e.regeneratorRuntime},function(e){s=e.cclegacy,c=e._decorator,d=e.Node,u=e.Prefab,l=e.v3,p=e.UITransform,f=e.instantiate,h=e.Vec3},function(e){m=e.CardItem},function(e){y=e.LightCardItem},function(e){_=e.HandCard},function(e){v=e.CommonAction},function(e){w=e.CardState,g=e.Card},function(e){C=e.BaseComponent},function(e){P=e.cat},null,function(e){b=e.default},function(e){x=e.AudioEffectConstant},function(e){T=e.GameEventConstant}],execute:function(){var E,I,B,O,S,z,A,L,R,D,k,N,Z,H,U,V,G;s._RF.push({},"7e724AFhLRC+qSufmjVPYp/","Interaction",void 0);var M=c.ccclass,W=c.property;e("Interaction",(E=M("Interaction"),I=W({type:d,tooltip:"游戏节点"}),B=W({type:u,tooltip:"明-卡牌预制体"}),O=W({type:u,tooltip:"暗-卡牌预制体"}),S=W({type:_,tooltip:"手牌组件"}),z=W({type:d,tooltip:"抽牌区"}),A=W({type:d,tooltip:"出牌区"}),L=W({type:d,tooltip:"玩家节点"}),E((k=t((D=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return t=e.call.apply(e,[this].concat(a))||this,r(t,"card_move_tween",k,o(t)),r(t,"light_card_prefab",N,o(t)),r(t,"dark_card_prefab",Z,o(t)),r(t,"hand_card",H,o(t)),r(t,"draw_zone",U,o(t)),r(t,"play_zone",V,o(t)),r(t,"players_node",G,o(t)),t.drawZonePos=l(),t.playZonePos=l(),t.handZonePos=l(),t}n(t,e);var s=t.prototype;return s.onLoad=function(){this.drawZonePos=this.card_move_tween.getComponent(p).convertToNodeSpaceAR(this.draw_zone.worldPosition),this.playZonePos=this.card_move_tween.getComponent(p).convertToNodeSpaceAR(this.play_zone.worldPosition),this.handZonePos=this.card_move_tween.getComponent(p).convertToNodeSpaceAR(this.hand_card.list.worldPosition)},s.onEventListener=function(){P.event.on("EVENT_DRAW",this.onDrawBroadcastHandler,this).on("EVENT_POST",this.onGamePostBroadcastHandler,this)},s.onGamePostBroadcastHandler=function(){var e=a(i().mark((function e(t){var n;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(window.ccLog("接受出牌广播",t),P.event.dispatchEvent(T.CLOSE_COMMON_UI),b.game.getPlayerByIndex(t.index),!b.game.isUserOrWatchTeammate(t.index)){e.next=8;break}b.game.showActionButton=!1,this.autoPlayCard(t.post),e.next=11;break;case 8:return n=b.game.getPlayerComponentByIndex(this.players_node,t.index).node,e.next=11,this.otherPlay({player:n,card:t.post});case 11:[g.BYPASS,g.DOUBLE_BYPASS,g.TURN].includes(t.post)&&this.getComponent(v).showCardEffect(t.post);case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),s.onDrawBroadcastHandler=function(e){window.ccLog("抽牌广播",e),P.audio.playEffect(x.DRAW_POST_CARD),P.event.dispatchEvent(T.CLOSE_COMMON_UI);var t=b.game.getPlayerByIndex(e.index);if(window.ccLog(e.index==b.user.userIndex?"自己抽牌":"观看队友抽牌"),e.index==b.user.userIndex||this.isWatchTeam(t.index));else{window.ccLog("别人抽牌");var n=b.game.getPlayerComponentByIndex(this.players_node,e.index).node;this.otherDrawTween({player:n})}this.scheduleOnce((function(){var t=w.USED;P.event.dispatchEvent(T.TAG_TOOTH_STATE,{index:e.number,state:t})}),.4)},s.autoPlayCard=function(){var e=a(i().mark((function e(t){var n,r,o,a;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(n=this.hand_card.getCardById(t)).length){e.next=13;break}if((r=n.find((function(e){var t=e.getComponent(y),n=t.is_card_selected,r=t.isValidCard;return n&&r})))||(r=n.find((function(e){var n=e.getComponent(y),r=n.props,o=n.isValidCard;return r.card==t&&o}))),!(o=r)){e.next=13;break}return(a=o.getComponent(y)).isValidCard=!1,a.setHandCard(!1),e.next=11,a.selectTween(!0,!0);case 11:return e.next=13,this.selfPlay({card:o});case 13:b.game.removeCard(t);case 14:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),s.otherDrawTween=function(e){var t=e.player,n=e.duration,r=void 0===n?.3:n,o=t.position,a=f(this.dark_card_prefab).getComponent(m);return a.addToParent(this.card_move_tween).setPosition(this.drawZonePos).setScale(h.ZERO),a.moveTween(.3,{scale:h.ONE}).then((function(){a.moveTween(r,{position:o,scale:h.ZERO})}))},s.selfPlay=function(){var e=a(i().mark((function e(t){var n,r,o,a,s=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.card,r=t.duration,o=void 0===r?.3:r,(a=n.getComponent(y)).setParent(this.card_move_tween,!0),e.next=5,Promise.all([a.moveTween(o,{scale:h.ONE},a.card),n.getComponent(m).moveTween(o,{position:this.playZonePos}).then((function(){n.setParent(s.play_zone,!0)}))]);case 5:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),s.otherPlay=function(){var e=a(i().mark((function e(t){var n,r,o,a,s,c,d,u=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.player,r=t.card,o=t.duration,a=void 0===o?.2:o,s=n.position,c=f(this.light_card_prefab),(d=c.getComponent(y)).addToParent(this.card_move_tween,{props:{card:r}}).setPosition(s).node.setScale(h.ZERO),e.abrupt("return",d.moveTween(a,{position:this.playZonePos,scale:h.ONE}).then((function(){c.setParent(u.play_zone,!0)})));case 6:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),s.isTeamByIndex=function(e){var t=b.game.roomData.players.find((function(t){return t.index==e}));return(null==t?void 0:t.teamId)===b.user.userTeamId},s.isWatchTeam=function(e){var t;return this.isTeamByIndex(e)&&(null==(t=b.game.getPlayerByIndex(b.user.userIndex))?void 0:t.die)},t}(C)).prototype,"card_move_tween",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=t(D.prototype,"light_card_prefab",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Z=t(D.prototype,"dark_card_prefab",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=t(D.prototype,"hand_card",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=t(D.prototype,"draw_zone",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=t(D.prototype,"play_zone",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=t(D.prototype,"players_node",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=D))||R));s._RF.pop()}}}));

System.register("chunks:///_virtual/invite_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./file.js","./message.js"],(function(e){var n,i,c;return{setters:[function(e){n=e.cclegacy},null,null,null,function(e){i=e.fileDesc},function(e){c=e.messageDesc}],execute:function(){n._RF.push({},"6a1b70W81VEe7BysdpagAHA","invite_pb",void 0);var s=e("file_invite",i("CgxpbnZpdGUucHJvdG8SBnByb3RvcyJIChxJbnZpdGVBdXRvRm9sbG93Tm90aWZpY2F0aW9uEhIKCmludml0ZXJfaWQYASABKAQSFAoMZGlzcGxheV9uYW1lGAIgASgJQjAKJGNvbS5zdG50cy5jbG91ZC5zdWlsZXlvby5lY2hvLnByb3Rvc1oILi9wcm90b3NiBnByb3RvMw"));e("InviteAutoFollowNotificationSchema",c(s,0));n._RF.pop()}}}));

System.register("chunks:///_virtual/ITool.ts",["cc"],(function(e){var n;return{setters:[function(e){n=e.cclegacy}],execute:function(){n._RF.push({},"0d9c7Uax4JJULNJg9B9PyFa","ITool",void 0);e("EraseSize",function(e){return e[e.SMALL=8]="SMALL",e[e.MEDIUM=16]="MEDIUM",e[e.DEFAULT=24]="DEFAULT",e[e.LARGE=32]="LARGE",e[e.X_LARGE=64]="X_LARGE",e}({})),e("ColorType",function(e){return e.Red="D81D35",e.Orange="FE6030",e.Pink="FF808C",e.LightPink="FEC2B0",e.Green="65B40A",e.Blue="158AFC",e.Black="010101",e.White="FFFFFF",e.Yellow="FDE566",e}({})),e("BrushSize",function(e){return e[e.SMALL=8]="SMALL",e[e.DEFAULT=12]="DEFAULT",e[e.MEDIUM=16]="MEDIUM",e[e.LARGE=20]="LARGE",e}({}));n._RF.pop()}}}));

System.register("chunks:///_virtual/JoinBattle.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Toast.ts","./index23.ts","./index21.ts","./JSBridge.ts"],(function(e){var t,n,r,i,o,c,s,a,u;return{setters:[function(e){t=e.asyncToGenerator,n=e.regeneratorRuntime},function(e){r=e.cclegacy,i=e.error},function(e){o=e.ToastType},function(e){c=e.default},function(e){s=e.cat},function(e){a=e.JSBridgeClient,u=e.GameCloseType}],execute:function(){function l(){return(l=t(n().mark((function e(r){var l,f,d,p,g;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===r&&(r=!1),l=c.user.isAudience,f=l?r?"加入观战中":"等待游戏开局...":r?"加入战局中":"等待其他玩家加入...",s.gui.showLoading({title:f}),e.prev=4,e.next=7,s.util.commontils.retryRequest(t(n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!l){e.next=6;break}return e.next=3,s.ws.Watch();case 3:window.ccLog("加入观战"),e.next=9;break;case 6:return e.next=8,s.ws.JoinBattle();case 8:window.ccLog("加入战局");case 9:case"end":return e.stop()}}),e)}))));case 7:e.next=16;break;case 9:e.prev=9,e.t0=e.catch(4),s.ws.destroy(),d=e.t0,p=d.msg,g=d.code,s.gui.showToast({title:g+"：游戏异常",type:o.FIXED,fixed_time:2}),i(l?"加入观战错误:":"加入战局错误:",g,p),a.closeGame(u.JoinOverTime,JSON.stringify({code:g,msg:p,info:"加入战局错误"}));case 16:case"end":return e.stop()}}),e,null,[[4,9]])})))).apply(this,arguments)}e("default",(function(e){return l.apply(this,arguments)})),r._RF.push({},"b6c23m6P3dBxrV2k+oUKMGJ","JoinBattle",void 0),r._RF.pop()}}}));

System.register("chunks:///_virtual/JSBridge.ts",["./rollupPluginModLoBabelHelpers.js","cc","./StringUtil.ts","./index43.ts","./index.mjs_cjs=&original=2.js","./index21.ts","./index40.ts","./WKWebViewJavascriptBridge.mjs_cjs=&original=.js","./index6.js","./AudioEffectConstant.b.ts"],(function(e){var n,t,i,a,r,o,l,s;return{setters:[function(e){n=e.inheritsLoose},function(e){t=e.cclegacy,i=e.game},function(e){a=e.getURLParameters},function(e){r=e.MessageManager},null,function(e){o=e.cat},null,null,function(e){l=e.default},function(e){s=e.AudioEffectConstant}],execute:function(){t._RF.push({},"80440mYscZF1Y26FBnmThZG","JSBridge",void 0);var c=e("PlatformType",function(e){return e.ANDROID="android",e.IOS="ios",e.WEB="web",e.FLUTTER="flutter",e}({})),u=e("GameCloseType",function(e){return e[e.JoinOverTime=0]="JoinOverTime",e[e.GameOver=1]="GameOver",e}({})),_=(e("AudioState",function(e){return e[e.ON=0]="ON",e[e.OFF=1]="OFF",e}({})),function(e){var n=window;if(null!=n&&n.WKWebViewJavascriptBridge)return e(n.WKWebViewJavascriptBridge);if(null!=n&&n.WKWVJBCallbacks)return n.WKWVJBCallbacks.push(e);n.WKWVJBCallbacks=[e];try{n.webkit.messageHandlers.iOS_Native_InjectJavascript.postMessage(null)}catch(e){}}),d=function(e){return e.CLOSE_GAME="close_game",e.GAME_LOAD_FINISHED="game_load_finished",e.OPEN_PROFILE="open_profile",e.GET_AUDIO_STATE="get_audio_state",e.DISABLE_TOUCH="disable_touch",e.DISABLE_CHAT="disable_chat",e.AI_DRAW_SWITCH="ai_draw_switch",e.AI_DRAW="ai_draw",e.SHOW_CHANGE_WORD="show_change_word",e.CHANGE_WORD="change_word",e.CALL_PLATFORM_API="call_platform_api",e.GET_MIKE_VOICE_STATUS="get_mike_voice_status",e.ENABLE_INPUT_BOX="enable_input_box",e.GET_MIKE_USERS_INFO="get_mike_users_info",e.SAVE_LOG="save_log",e.PLAY_AUDIO="play_audio",e.PLAY_MUSIC="play_music",e}(d||{}),E=e("EnumJSBridgeWebView",function(e){return e.GAME_START="game_start",e.CHANGE_AUDIO_STATE="change_audio_state",e.PLAYMIKEEMOJI="playMikeEmoji",e.CHAT_TEXT="chat_text",e.MIKE_SOUND_WAVE="mike_sound_wave",e.MIKE_VOICE_STATUS="mike_voice_status",e.MIKE_USERS_INFO="mike_users_info",e.UPDATE_BALANCE="updateBalance",e.USER_ROOM_GAME_MSG="user_room_game_msg",e}({})),A=a(decodeURIComponent(window.location.href)),f=(null==A?void 0:A.platform)||c.WEB,T=function(){function e(){}var n=e.prototype;return n.callHandler=function(e,n){return void 0===n&&(n={}),window.ccLog("callHandler",e,n),new Promise((function(t){var i;switch(f){case c.ANDROID:return void(null==(i=window)||null==(i=i.WebViewJavascriptBridge)||i.callHandler(e,n,(function(n){window.ccLog("Response from Android:",n,e),t(n)})));case c.IOS:return void _((function(i){i.callHandler(e,n,(function(n){window.ccLog("Response from IOS:",n,e),t(n)}))}));case c.FLUTTER:return l.call(e,n,(function(e){window.ccLog("Response from FLUTTER:",e),t(e)})),setTimeout((function(){o.audio.playEffect(s.SILENT)}),1),void setTimeout((function(){o.audio.playEffect(s.SILENT)}),50);default:window.parent.postMessage({type:e,data:n},"*")}}))},n.closeGame=function(e,n){return void 0===e&&(e=u.GameOver),void 0===n&&(n=""),i.pause(),this.callHandler(d.CLOSE_GAME,{type:e,payload:n})},n.disableTouch=function(e){return this.callHandler(d.DISABLE_TOUCH,{disable:e})},n.gameLoadFinished=function(){return i.pause(),this.callHandler(d.GAME_LOAD_FINISHED)},n.openProfile=function(e){return this.callHandler(d.OPEN_PROFILE,e)},n.getAudioState=function(){return this.callHandler(d.GET_AUDIO_STATE)},n.disableChat=function(e){return this.callHandler(d.DISABLE_CHAT,{disable:e})},n.aiDrawSwitch=function(e){return this.callHandler(d.AI_DRAW_SWITCH,e)},n.aiDraw=function(e){return this.callHandler(d.AI_DRAW,e)},n.showChangeWord=function(e){return this.callHandler(d.SHOW_CHANGE_WORD,e)},n.changeWord=function(e){return this.callHandler(d.CHANGE_WORD,e)},n.callPlatformApi=function(e){return this.callHandler(d.CALL_PLATFORM_API,e)},n.getMikeVoiceStatus=function(){return this.callHandler(d.GET_MIKE_VOICE_STATUS)},n.enableInputBox=function(e){return this.callHandler(d.ENABLE_INPUT_BOX,{enable:e})},n.getMikeUsersInfo=function(){return this.callHandler(d.GET_MIKE_USERS_INFO)},n.saveLog=function(e){return this.callHandler(d.SAVE_LOG,e)},n.playMusic=function(e){return this.callHandler(d.PLAY_MUSIC,{path:e})},n.playAudio=function(e){return this.callHandler(d.PLAY_AUDIO,{path:e})},e}(),S=function(e){function t(){var n;return(n=e.call(this)||this).addEventListener(E.GAME_START).addEventListener(E.CHANGE_AUDIO_STATE).addEventListener(E.PLAYMIKEEMOJI).addEventListener(E.CHAT_TEXT).addEventListener(E.MIKE_SOUND_WAVE).addEventListener(E.MIKE_VOICE_STATUS).addEventListener(E.MIKE_USERS_INFO).addEventListener(E.UPDATE_BALANCE).addEventListener(E.USER_ROOM_GAME_MSG),n}n(t,e);var a=t.prototype;return a.registerHandler=function(e,n){var t;switch(f){case c.ANDROID:return void(null==(t=window)||t.WebViewJavascriptBridge.registerHandler(e,n));case c.IOS:return void _((function(t){t.registerHandler(e,n)}));case c.FLUTTER:return void l.register(e,n)}},a.addEventListener=function(e){var n=this;return this.registerHandler(e,(function(t){var a;window.ccLog("桥接",e,t,n.has(e),null==(a=n.getEvents(e))?void 0:a.size),e===E.GAME_START&&i.resume(),n.dispatchEvent(e,t)})),this},t}(r);e("JSBridgeClient",new T),e("JSBridgeWebView",new S),e("closeGameToServer",(function(){}));t._RF.pop()}}}));

System.register("chunks:///_virtual/LightCardItem.ts",["./rollupPluginModLoBabelHelpers.js","cc","./CardItem.ts","./msg_pb.ts","./index21.ts","./index40.ts","./index23.ts","./Decorator.ts","./GameEventConstant.b.ts"],(function(e){var t,n,r,a,i,s,o,c,d,l,u,p,_,h,C,f,v,E,g,m,y,H;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,r=e.initializerDefineProperty,a=e.assertThisInitialized},function(e){i=e.cclegacy,s=e._decorator,o=e.SpriteFrame,c=e.Vec3,d=e.Sprite,l=e.math,u=e.Node,p=e.tween,_=e.Tween,h=e.v3},function(e){C=e.CardItem},function(e){f=e.Card},function(e){v=e.cat},null,function(e){E=e.default},function(e){g=e.audioEffect,m=e.buttonLock,y=e.watchUser},function(e){H=e.GameEventConstant}],execute:function(){var T,A,D,S,L,w,I,N;i._RF.push({},"3f518kApRlMzquJtf5T2Sjl","LightCardItem",void 0);var b=s.ccclass,F=s.property;e("LightCardItem",(T=b("LightCardItem"),A=F({type:[o],tooltip:"卡牌精灵图集"}),D=y(),S=g(),L=m(.2),T((N=t((I=function(e){function t(){for(var t,n=arguments.length,i=new Array(n),s=0;s<n;s++)i[s]=arguments[s];return t=e.call.apply(e,[this].concat(i))||this,r(t,"card_spriteFrames",N,a(t)),t.is_card_selected=!1,t.origin_scale=c.ONE,t.is_to_be_removed=!1,t.props={card:f.UNSPECIFIED},t.data={is_gray:!1},t}n(t,e);var i=t.prototype;return i.onLoad=function(){},i.onAutoObserver=function(){var e=this;this.addAutorun([function(){e.card.getComponent(d).spriteFrame=e.card_spriteFrames[e.props.card]},function(){e.card.getComponent(d).color=e.data.is_gray?l.Color.GRAY:l.Color.WHITE},function(){E.game.giveMeYourCardSelectedCardIndex===e.currentIndex?e.selectTween(!0):e.selectTween(!1)}])},i.start=function(){this.node.on(u.EventType.TOUCH_END,this.onCardHandler,this)},i.onEventListener=function(){v.event.on(H.SELECT_HAND_CARD,this.onSelectedCardHandler,this).on(H.CANCEL_SELECT_HAND_CARD,this.onCancelSelectCardHandler,this)},i.onCardHandler=function(){v.event.dispatchEvent(H.SELECT_HAND_CARD,{node:this.node,card:this.props.card,is_card_selected:this.is_card_selected}),v.event.dispatchEvent(H.HAND_CARD_SELECT_STATE_CHANGE)},i.onSelectedCardHandler=function(e){var t=e.node;this.isHandCard&&(this.node==t?this.selectTween(!0):this.selectTween(!1))},i.onCancelSelectCardHandler=function(){this.isHandCard&&this.selectTween(!1)},i.selectTween=function(e,t){var n=this;void 0===t&&(t=!1);var r=this.is_card_selected;return this.is_card_selected=!!e&&!this.is_card_selected,new Promise((function(a){r?p(n.card).to(n.selected_duration,{scale:n.origin_scale}).call((function(){_.stopAllByTarget(n.card),a()})).start():e&&(t||v.event.dispatchEvent(H.SHOW_CARD_INFO,{card:n.props.card}),p(n.card).to(n.selected_duration,{scale:h(n.selected_scale,n.selected_scale,1)}).call((function(){_.stopAllByTarget(n.card),a()})).start())}))},i.setHandCard=function(e){return void 0===e&&(e=!0),this.isHandCard=e,!e&&this.data.is_gray&&(this.data.is_gray=!1),this},i.setParent=function(e,t){return void 0===t&&(t=!1),this.node.setParent(e,t),this},t}(C)).prototype,"card_spriteFrames",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),t(I.prototype,"onCardHandler",[D,S,L],Object.getOwnPropertyDescriptor(I.prototype,"onCardHandler"),I.prototype),w=I))||w));i._RF.pop()}}}));

System.register("chunks:///_virtual/ListView.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var i,e,n,s,r,o,h,a,l,c,u,p,g,d,m,f,w,_,b,v,y;return{setters:[function(t){i=t.applyDecoratedDescriptor,e=t.inheritsLoose,n=t.initializerDefineProperty,s=t.assertThisInitialized,r=t.asyncToGenerator,o=t.regeneratorRuntime},function(t){h=t.cclegacy,a=t._decorator,l=t.Prefab,c=t.Vec2,u=t.Rect,p=t.ScrollView,g=t.Node,d=t.NodePool,m=t.UITransform,f=t.v2,w=t.instantiate,_=t.Component,b=t.error,v=t.rect,y=t.warn}],execute:function(){var V,C,z,I,R,x,H,M,P,T,W,k,A,L;h._RF.push({},"d9b05eVDV5Adp9iewhRb68q","ListView",void 0);var U=a.ccclass,X=a.property;t("default",(V=X({type:l,tooltip:"预制体项模板"}),C=X(c),z=X(u),I=X(p),R=X(g),U((M=i((H=function(t){function i(){for(var i,e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return i=t.call.apply(t,[this].concat(r))||this,n(i,"itemTemplate",M,s(i)),n(i,"spacing",P,s(i)),n(i,"margin",T,s(i)),n(i,"spawnCount",W,s(i)),n(i,"column",k,s(i)),n(i,"scrollView",A,s(i)),n(i,"emptyView",L,s(i)),i.content=null,i.adapter=null,i._items=new d,i._filledIds={},i.horizontal=!1,i._itemHeight=1,i._itemWidth=1,i._itemsVisible=1,i.dataChanged=!1,i._isInited=!1,i.visibleRange=[-1,-1],i.comp=null,i}e(i,t);var h=i.prototype;return h.onLoad=function(){this.init(),this.scrollView},h.setAdapter=function(){var t=r(o().mark((function t(i){return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.adapter!==i){t.next=3;break}return this.notifyUpdate(),t.abrupt("return");case 3:if(this.adapter=i,null!=this.adapter){t.next=7;break}return y("adapter 为空."),t.abrupt("return");case 7:if(null!=this.itemTemplate){t.next=10;break}return b("Listview 未设置待显示的Item模板."),t.abrupt("return");case 10:this.visibleRange[0]=this.visibleRange[1]=-1,this.recycleAll(),this.notifyUpdate();case 13:case"end":return t.stop()}}),t,this)})));return function(i){return t.apply(this,arguments)}}(),h.getAdapter=function(){return this.adapter},h.getScrollView=function(){return this.scrollView},h.getAllItems=function(){return this._items},h.scrollToPage=function(t,i,e){if(!this.adapter||!this.scrollView)return!1;this.adapter.getCount();var n=this.content.getComponent(m),s=this.content.parent.getComponent(m);if(this.horizontal){var r=0,o=n.width,h=this.getColumnWH();if(i)r=h*i;else{var a=s.width;r=Math.floor(a/h)*h}return this.scrollView.scrollToOffset(f(r*t,0),e),r*(t+1)>=o}var l=n.height,c=this.getColumnWH(),u=0;if(i)u=c*i;else{var p=s.height;u=Math.floor(p/c)*c}return this.scrollView.scrollToOffset(f(0,u*t),e),u*(t+1)>=l},h.getVisibleElements=function(){var t=0,i=this.content.parent.getComponent(m);if(this.horizontal){var e=i.width;t=Math.floor(e/this.getColumnWH())}else{var n=i.height;t=Math.floor(n/this.getColumnWH())}return t*this.column},h.getColumnWH=function(){return this.horizontal?this._itemWidth+this.spacing.x:this._itemHeight+this.spacing.y},h.notifyUpdate=function(){if(null!=this.adapter&&(this._isInited||this.init(),this.scrollView&&this.content)){this.emptyView&&(this.emptyView.active=this.adapter.getCount()<=0),this.visibleRange[0]=this.visibleRange[1]=-1;var t=this.content.getComponent(m);this.horizontal?t.width=Math.ceil(this.adapter.getCount()/this.column)*(this._itemWidth+this.spacing.x)-this.spacing.x+this.margin.x+this.margin.width:t.height=Math.ceil(this.adapter.getCount()/this.column)*(this._itemHeight+this.spacing.y)-this.spacing.y+this.margin.y+this.margin.height,this.dataChanged=!0}},h.lateUpdate=function(){var t=this.getVisibleRange();this.checkNeedUpdate(t)&&(this.recycleDirty(t),this.updateView(t))},h._layoutVertical=function(t,i){this.content.addChild(t);var e=i%(this.column||1),n=Math.floor(i/(this.column||1)),s=t.getComponent(m),r=this.content.getComponent(m);t.setPosition(this.column>1?this.margin.x+s.width*s.anchorX+(s.width+this.spacing.x)*e-r.width*r.anchorX:0,-this.margin.y-s.height*(s.anchorY+n)-this.spacing.y*n)},h._layoutHorizontal=function(t,i){this.content.addChild(t);var e=i%(this.column||1),n=Math.floor(i/(this.column||1)),s=t.getComponent(m),r=this.content.getComponent(m);t.setPosition(s.width*(s.anchorX+n)+this.spacing.x*n+this.margin.x,this.column>1?-1*(this.margin.y+s.height*s.anchorY+(s.height+this.spacing.y)*e-r.height*r.anchorY):0)},h.recycleAll=function(){for(var t in this._filledIds)this._filledIds.hasOwnProperty(t)&&this._items.put(this._filledIds[t]);this._filledIds={}},h.recycleDirty=function(t){if(t&&!(t.length<2)){for(var i=this.visibleRange[0];i<t[0];i++)i<0||!this._filledIds[i]||(this._items.put(this._filledIds[i]),this._filledIds[i]=null);for(var e=this.visibleRange[1];e>t[1];e--)e<0||!this._filledIds[e]||(this._items.put(this._filledIds[e]),this._filledIds[e]=null);this.visibleRange[0]=t[0],this.visibleRange[1]=t[1]}},h.checkNeedUpdate=function(t){return t&&this.visibleRange&&(this.visibleRange[0]!=t[0]||this.visibleRange[1]!=t[1])},h.updateView=function(t){for(var i=t[0];i<=t[1];i++)if(this.dataChanged||!this._filledIds[i]){var e=this._filledIds[i]||this._items.get()||w(this.itemTemplate);!this.comp||e.getComponent(_)instanceof this.comp||(e.getComponent(_).destroy(),e.addComponent(this.comp)),e.removeFromParent(),this.horizontal?this._layoutHorizontal(e,i):this._layoutVertical(e,i),this._filledIds[i]=this.adapter._getView(e,i)}this.dataChanged=!1},h.getVisibleRange=function(){if(null==this.adapter)return null;var t=this.scrollView.getScrollOffset(),i=0;(i=this.horizontal?Math.floor(-t.x/(this._itemWidth+this.spacing.x)):Math.floor(t.y/(this._itemHeight+this.spacing.y)))<0&&(i=0);var e=this.column*(i+this._itemsVisible+this.spawnCount);return e>=this.adapter.getCount()&&(e=this.adapter.getCount()-1),[i*this.column,e]},h.init=function(){if(!this._isInited){if(this._isInited=!0,this.scrollView){this.content=this.scrollView.content,this.horizontal=this.scrollView.horizontal;var t=this.content.getComponent(m),i=this.content.parent.getComponent(m);this.horizontal?(this.scrollView.vertical=!1,t.anchorX=0,t.anchorY=i.anchorY,this.content.setPosition(0-i.width*i.anchorX,0)):(this.scrollView.vertical=!0,t.anchorX=i.anchorX,t.anchorY=1,this.content.setPosition(0,i.height*i.anchorY))}else b("ListView need a scrollView for showing.");var e=this._items.get()||w(this.itemTemplate);this._items.put(e);var n=e.getComponent(m),s=this.content.parent.getComponent(m);this._itemHeight=n.height||10,this._itemWidth=n.width||10,this.horizontal?this._itemsVisible=Math.ceil((s.width-this.margin.x-this.margin.width)/(this._itemWidth+this.spacing.x)):this._itemsVisible=Math.ceil((s.height-this.margin.y-this.margin.height)/(this._itemHeight+this.spacing.y))}},i}(_)).prototype,"itemTemplate",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=i(H.prototype,"spacing",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return f(0,0)}}),T=i(H.prototype,"margin",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return v(0,0,0,0)}}),W=i(H.prototype,"spawnCount",[X],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 2}}),k=i(H.prototype,"column",[X],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1}}),A=i(H.prototype,"scrollView",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=i(H.prototype,"emptyView",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=H))||x));h._RF.pop()}}}));

System.register("chunks:///_virtual/Loading.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseLoading.ts","./index21.ts","./EnterGame.ts","./JSBridge.ts","./index23.ts"],(function(e){var r,n,t,a,o,i,s,c,u,l,p,f,g,d,m,h,v,y;return{setters:[function(e){r=e.applyDecoratedDescriptor,n=e.inheritsLoose,t=e.initializerDefineProperty,a=e.assertThisInitialized,o=e.asyncToGenerator,i=e.regeneratorRuntime},function(e){s=e.cclegacy,c=e._decorator,u=e.Node,l=e.error,p=e.Prefab,f=e.ProgressBar},function(e){g=e.default},function(e){d=e.cat},function(e){m=e.default},function(e){h=e.JSBridgeClient,v=e.GameCloseType},function(e){y=e.default}],execute:function(){var P,x,L,b,w;s._RF.push({},"cb242YGAq9FnLpQypepPG8u","Loading",void 0);var k=c.ccclass,G=c.property;e("Loading",(P=k("Loading"),x=G({type:u,tooltip:"进度条"}),P((w=r((b=function(e){function r(){for(var r,n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return r=e.call.apply(e,[this].concat(o))||this,t(r,"progress",w,a(r)),r.maxPro=0,r}n(r,e);var s=r.prototype;return s.init=function(){var e=o(i().mark((function e(){var r,n;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=y.global.url_params,n=[this.loadRes()],Promise.all(n).then(o(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!r.params){e.next=6;break}return e.next=3,m();case 3:y.game.isParamsFromUrl=!0,y.game.gameLoadFinished=!0,d.gui.hideLoading();case 6:d.gui.loadScene("game");case 7:case"end":return e.stop()}}),e)}))),(function(e){l(e),h.closeGame(v.JoinOverTime,JSON.stringify({err:e,info:"加载资源错误"}))}));case 3:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),s.loadRes=function(){var e=o(i().mark((function e(){var r,n=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=function(e,r){var t=e/r;n.maxPro<t&&(n.maxPro=t),n.progress.getComponent(f).progress=n.maxPro},d.tracking.loading.loadingProgress(0),e.next=4,new Promise((function(e){d.res.loadDir("prefabs/",p,r,(function(){d.tracking.loading.loadingProgress(1),e()}))}));case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),r}(g)).prototype,"progress",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=b))||L));s._RF.pop()}}}));

System.register("chunks:///_virtual/LoginValidate.ts",["cc","./index23.ts","./index21.ts"],(function(n){var t,i,e;return{setters:[function(n){t=n.cclegacy},function(n){i=n.default},function(n){e=n.cat}],execute:function(){n("LoginValidation",(function(){return function(n,t,o){var c=o.value;return o.value=function(){var n=i.login;if(n.isLogin){for(var t=arguments.length,o=new Array(t),u=0;u<t;u++)o[u]=arguments[u];return c.apply(this,o)}window.ccLog("User is not logged in. Skipping method execution."),e.gui.showToast({title:"请先登录"})},o}})),t._RF.push({},"29dbaNdcC1JCbKE2FSsJnFc","LoginValidate",void 0),t._RF.pop()}}}));

System.register("chunks:///_virtual/main",["./BaseAPI.ts","./index36.ts","./index20.ts","./index31.ts","./index9.ts","./index39.ts","./Bridge.ts","./Cocos.ts","./Native.ts","./index45.ts","./Chat.ts","./ActionEventConstant.ts","./AudioEffectConstant.b.ts","./AudioEffectConstant.ts","./AudioEventConstant.ts","./GameEventConstant.b.ts","./GameEventConstant.ts","./GlobalEventConstant.ts","./MatchEventConstant.ts","./PartyGameEventConstant.ts","./PlatformConstant.ts","./RoomEventConstant.ts","./TaskEventConstant.ts","./index40.ts","./index16.ts","./index10.ts","./index18.ts","./index30.ts","./util.ts","./index3.ts","./Decorator.ts","./EnterGame.ts","./GetTimeDifferenceFromServer.ts","./JoinBattle.ts","./LoginValidate.ts","./PhaseValidation.ts","./Share.ts","./ToBeGetProfiles.ts","./index8.ts","./index46.ts","./CommonSign.ts","./SuileyooSign.ts","./StopServiceNotice.ts","./index14.ts","./JSBridge.ts","./WKWebViewJavascriptBridge.mjs_cjs=&original=.js","./BaseLoading.ts","./index29.ts","./index48.ts","./index.ts","./index37.ts","./index2.ts","./index4.ts","./BaseStore.ts","./index.b.ts","./index26.ts","./index32.ts","./index23.ts","./index35.ts","./index38.ts","./index42.ts","./index13.ts","./BaseTracking.ts","./index11.ts","./index24.ts","./index34.ts","./index33.ts","./index12.ts","./index17.ts","./IGame.b.ts","./IGame.ts","./ITool.ts","./ArrayUtils.ts","./BlobUtils.ts","./Commontils.ts","./Debugout.ts","./Decorator2.ts","./EncryptUtil.ts","./NodeUtils.ts","./StringUtil.ts","./TimeUtils.ts","./index19.ts","./index7.ts","./BaseWebSocket.ts","./SuileyooWS.ts","./index.b2.ts","./index47.ts","./types.b.ts","./BreakHosting.b.ts","./FitWidthCamera.ts","./Scene3dFitWith2d.ts","./Flex.ts","./AbsAdapter.ts","./ListView.ts","./PlistParse.ts","./plistCache.ts","./plistImage.ts","./plistMgr.ts","./FullScreenArea.ts","./WrapSafeArea.ts","./Toast.ts","./VideoSprite.ts","./BaseManager.ts","./CardManager.b.ts","./AudioEffect.ts","./AudioMusic.ts","./CommonAudio.ts","./index5.ts","./index44.ts","./index43.ts","./Gui.ts","./AudioSourceBaseComponent.ts","./BaseComponent.ts","./BasePool.ts","./BasePool__.ts","./BlackMask.ts","./UIContainer.ts","./exportLog.ts","./index22.ts","./AudioSourceUILayer.ts","./RootUILayer.ts","./UILayer.ts","./index28.ts","./InitialScene.ts","./SceneLayer.ts","./index21.ts","./index15.ts","./SuiLeYooSocket.ts","./index27.ts","./index6.ts","./index41.ts","./index25.ts","./UIModal.ts","./Notice.ts","./Reconnection.ts","./ShowLoading.ts","./UploadLog.ts","./game_pb.ts","./handler_pb.ts","./msg_pb.ts","./report_battle_pb.ts","./router_pb.ts","./account_pb.ts","./authorization_pb.ts","./connect_pb.ts","./growth_pb.ts","./invite_pb.ts","./medal_pb.ts","./member_pb.ts","./message_pb.ts","./new-message_pb.ts","./notification_pb.ts","./recharge_pb.ts","./task_pb.ts","./user_pb.ts","./worker_pb.ts","./EffectSwitch.ts","./MusicSwitch.ts","./Setting.ts","./Switch.ts","./Game.ts","./Arrow.ts","./TypeButton.ts","./CardInfo.ts","./CardItem.ts","./DarkCardItem.ts","./LightCardItem.ts","./BlessCardActiom.ts","./ClampCardAction.ts","./CurseCardAction.ts","./DoublePassCardAction.ts","./ExchangeCardAction.ts","./PassCardAction.ts","./RemoveCardAction.ts","./RequestCardAction.ts","./ResistanceCardAction.ts","./ScoutCardAction.ts","./ThrowMineCardAction.ts","./TurnCardAction.ts","./CommonAction.ts","./SelectPlayerAction.ts","./BaseHandCard.ts","./DarkCardList.ts","./Desk.ts","./HandCard.ts","./Interaction.ts","./Overtime.ts","./Curse.ts","./PlayerItem.ts","./ThrowMine.ts","./Shark.ts","./SharkMove.ts","./Tooth.ts","./TimeCount.ts","./Clamp.ts","./AudioSourceCommonActionUI.ts","./CommonActionUI.ts","./ActiveChoosePlayer.ts","./UIDraw.ts","./ActiveRemoveMine.ts","./ActiveScout.ts","./PassiveRequest.ts","./BasePlayerOutOver.ts","./UIPlayerOut.ts","./RuleItem.ts","./UIRule.ts","./Loading.ts","./StartScene.ts"],(function(){return{setters:[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){}}}));

System.register("chunks:///_virtual/MatchEventConstant.ts",["cc"],(function(t){var n;return{setters:[function(t){n=t.cclegacy}],execute:function(){n._RF.push({},"b82a0C3/OdOx4n4p8cTCAEP","MatchEventConstant",void 0);t("MatchEventConstant",function(t){return t.MATCH_STATE="MatchEventConstant/MATCH_STATE",t}({}));n._RF.pop()}}}));

System.register("chunks:///_virtual/medal_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./file.js","./message.js"],(function(e){var c,n,s;return{setters:[function(e){c=e.cclegacy},null,null,null,function(e){n=e.fileDesc},function(e){s=e.messageDesc}],execute:function(){c._RF.push({},"a785fS5r8xLaaRFGEj//RQp","medal_pb",void 0);var t=e("file_medal",n("CgttZWRhbC5wcm90bxIGcHJvdG9zIj8KFU1lZGFsRG9uZU5vdGlmaWNhdGlvbhIKCgJpZBgBIAEoBBIMCgRuYW1lGAIgASgJEgwKBGtpbmQYAyABKAVCMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z"));e("MedalDoneNotificationSchema",s(t,0));c._RF.pop()}}}));

System.register("chunks:///_virtual/member_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./file.js","./message.js"],(function(e){var c,b,i;return{setters:[function(e){c=e.cclegacy},null,null,null,function(e){b=e.fileDesc},function(e){i=e.messageDesc}],execute:function(){c._RF.push({},"6e5028XxAFKO5L/b6W1bKWv","member_pb",void 0);var n=e("file_member",b("CgxtZW1iZXIucHJvdG8SBnByb3RvcyIyCg5NZW1iZXJJbmZvSXRlbRIMCgR0eXBlGAEgASgJEhIKCmV4cGlyZWRfYXQYAiABKAkiTgoWTWVtYmVySW5mb05vdGlmaWNhdGlvbhINCgV1c2luZxgBIAEoCRIlCgVJdGVtcxgCIAMoCzIWLnByb3Rvcy5NZW1iZXJJbmZvSXRlbUIwCiRjb20uc3RudHMuY2xvdWQuc3VpbGV5b28uZWNoby5wcm90b3NaCC4vcHJvdG9zYgZwcm90bzM"));e("MemberInfoItemSchema",i(n,0)),e("MemberInfoNotificationSchema",i(n,1));c._RF.pop()}}}));

System.register("chunks:///_virtual/message_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./enum.js","./file.js","./message.js"],(function(N){var T,E,O,I;return{setters:[function(N){T=N.cclegacy},null,null,null,function(N){E=N.enumDesc},function(N){O=N.fileDesc},function(N){I=N.messageDesc}],execute:function(){T._RF.push({},"13e10IOadpBwIh92ipJ4KfA","message_pb",void 0);var R=N("file_message",O("Cg1tZXNzYWdlLnByb3RvEgZwcm90b3MimAYKB01lc3NhZ2USIgoEdHlwZRgBIAEoDjIULnByb3Rvcy5NZXNzYWdlLlR5cGUSDwoHY29udGVudBgCIAEoDCLXBQoEVHlwZRILCgdSRVNFUlZFEAASCQoEUElORxCRThIJCgRQT05HEJJOEhEKDE5PVElGSUNBVElPThD0ThIcChdQQVJUWV9HQU1FX05PVElGSUNBVElPThD1ThIfChpBVVRIT1JJWkFUSU9OX05PVElGSUNBVElPThD4VRIZChRCQUxBTkNFX05PVElGSUNBVElPThD5VRIaChVSRUNIQVJHRV9OT1RJRklDQVRJT04Q+lUSGwoWVVNFUl9JTkZPX05PVElGSUNBVElPThD7VRIdChhNRU1CRVJfSU5GT19OT1RJRklDQVRJT04Q/FUSHQoYR1JPV1RIX0lORk9fTk9USUZJQ0FUSU9OEP1VEhwKF01FREFMX0RPTkVfTk9USUZJQ0FUSU9OEP5VEiAKG0NPTk5FQ1RfT05MSU5FX05PVElGSUNBVElPThDhXRIhChxDT05ORUNUX09GRkxJTkVfTk9USUZJQ0FUSU9OEOJdEh0KGE5FV19NRVNTQUdFX05PVElGSUNBVElPThDJZRIcChdUQVNLX0FXQVJEX05PVElGSUNBVElPThDKZRIkCh9JTlZJVEVfQVVUT19GT0xMT1dfTk9USUZJQ0FUSU9OEMtlEhcKEVVTRVJfSU5GT19SRVFVRVNUELiOAxIZChJVU0VSX0lORk9fUkVTUE9OU0UQweWoGBIcChZDT05ORUNUX1BBUkFNU19SRVFVRVNUEKCWAxIeChdDT05ORUNUX1BBUkFNU19SRVNQT05TRRCB6uUYEhwKFkNPTk5FQ1RfU1RBVFVTX1JFUVVFU1QQoZYDEh4KF0NPTk5FQ1RfU1RBVFVTX1JFU1BPTlNFEOnx5RgSJwohQ09OTkVDVF9BVVRIX0FDQ0VTU19UT0tFTl9SRVFVRVNUEKKWAxIpCiJDT05ORUNUX0FVVEhfQUNDRVNTX1RPS0VOX1JFU1BPTlNFENH55RhCMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z"));N("MessageSchema",I(R,0)),N("Message_Type",function(N){return N[N.RESERVE=0]="RESERVE",N[N.PING=10001]="PING",N[N.PONG=10002]="PONG",N[N.NOTIFICATION=10100]="NOTIFICATION",N[N.PARTY_GAME_NOTIFICATION=10101]="PARTY_GAME_NOTIFICATION",N[N.AUTHORIZATION_NOTIFICATION=11e3]="AUTHORIZATION_NOTIFICATION",N[N.BALANCE_NOTIFICATION=11001]="BALANCE_NOTIFICATION",N[N.RECHARGE_NOTIFICATION=11002]="RECHARGE_NOTIFICATION",N[N.USER_INFO_NOTIFICATION=11003]="USER_INFO_NOTIFICATION",N[N.MEMBER_INFO_NOTIFICATION=11004]="MEMBER_INFO_NOTIFICATION",N[N.GROWTH_INFO_NOTIFICATION=11005]="GROWTH_INFO_NOTIFICATION",N[N.MEDAL_DONE_NOTIFICATION=11006]="MEDAL_DONE_NOTIFICATION",N[N.CONNECT_ONLINE_NOTIFICATION=12001]="CONNECT_ONLINE_NOTIFICATION",N[N.CONNECT_OFFLINE_NOTIFICATION=12002]="CONNECT_OFFLINE_NOTIFICATION",N[N.NEW_MESSAGE_NOTIFICATION=13001]="NEW_MESSAGE_NOTIFICATION",N[N.TASK_AWARD_NOTIFICATION=13002]="TASK_AWARD_NOTIFICATION",N[N.INVITE_AUTO_FOLLOW_NOTIFICATION=13003]="INVITE_AUTO_FOLLOW_NOTIFICATION",N[N.USER_INFO_REQUEST=51e3]="USER_INFO_REQUEST",N[N.USER_INFO_RESPONSE=51000001]="USER_INFO_RESPONSE",N[N.CONNECT_PARAMS_REQUEST=52e3]="CONNECT_PARAMS_REQUEST",N[N.CONNECT_PARAMS_RESPONSE=52000001]="CONNECT_PARAMS_RESPONSE",N[N.CONNECT_STATUS_REQUEST=52001]="CONNECT_STATUS_REQUEST",N[N.CONNECT_STATUS_RESPONSE=52001001]="CONNECT_STATUS_RESPONSE",N[N.CONNECT_AUTH_ACCESS_TOKEN_REQUEST=52002]="CONNECT_AUTH_ACCESS_TOKEN_REQUEST",N[N.CONNECT_AUTH_ACCESS_TOKEN_RESPONSE=52002001]="CONNECT_AUTH_ACCESS_TOKEN_RESPONSE",N}({})),N("Message_TypeSchema",E(R,0,0));T._RF.pop()}}}));

System.register("chunks:///_virtual/msg_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./enum.js","./file.js","./message.js"],(function(E){var A,S,V,R;return{setters:[function(E){A=E.cclegacy},null,null,null,function(E){S=E.enumDesc},function(E){V=E.fileDesc},function(E){R=E.messageDesc}],execute:function(){A._RF.push({},"9f9c4Gp6s9G+YQbf80+8bNZ","msg_pb",void 0);var F=E("file_shark_suileyoo_v1_msg",V("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"));E("AbnormalExitResponseSchema",R(F,0)),E("AbnormalExitRequestSchema",R(F,1)),E("FoundOnHandRequestSchema",R(F,2)),E("FoundOnHandResponseSchema",R(F,3)),E("RefreshCardResponseSchema",R(F,4)),E("PlayerSchema",R(F,5)),E("TeamSchema",R(F,6)),E("GameBroadcastSchema",R(F,7)),E("SyncRequestSchema",R(F,8)),E("SyncResponseSchema",R(F,9)),E("ExitRequestSchema",R(F,10)),E("ExitResponseSchema",R(F,11)),E("GiveMeYourCardRequestSchema",R(F,12)),E("GiveMeYourCardResponseSchema",R(F,13)),E("SelectTargetRequestSchema",R(F,14)),E("SelectTargetResponseSchema",R(F,15)),E("ClampCardRequestSchema",R(F,16)),E("ClampCardResponseSchema",R(F,17)),E("PostCardRequestSchema",R(F,18)),E("PostCardResponseSchema",R(F,19)),E("WatchRequestSchema",R(F,20)),E("WatchResponseSchema",R(F,21)),E("ReadyRequestSchema",R(F,22)),E("ReadyResponseSchema",R(F,23)),E("DrawCardRequestSchema",R(F,24)),E("DrawCardResponseSchema",R(F,25)),E("RefreshCardRequestSchema",R(F,26)),E("PostCardBroadcastSchema",R(F,27)),E("TeamPredictionBroadcastSchema",R(F,28)),E("TeamScoutBroadcastSchema",R(F,29)),E("JoinRequestSchema",R(F,30)),E("JoinResponseSchema",R(F,31)),E("DrawToOtherSchema",R(F,32)),E("DrawToMeSchema",R(F,33)),E("DrawSchema",R(F,34)),E("TeammateRemoveBroadcastSchema",R(F,35)),E("PostBroadcastSchema",R(F,36)),E("SelectTargetBroadcastSchema",R(F,37)),E("DrawBottomBroadcastSchema",R(F,38)),E("GiveMeYourCardBroadcastSchema",R(F,39)),E("ResponseExchangeBroadcastSchema",R(F,40)),E("ThrowLandmineBroadcastSchema",R(F,41)),E("CurseBroadcastSchema",R(F,42)),E("ClampBroadcastSchema",R(F,43)),E("PlayerSettleResultSchema",R(F,44)),E("ExitBroadcastSchema",R(F,45)),E("MeOverBroadcastSchema",R(F,46)),E("DeleteGroupUidRequestSchema",R(F,47)),E("DeleteGroupUidResponseSchema",R(F,48)),E("DataBroadcastRequestSchema",R(F,49)),E("DataBroadcastResponseSchema",R(F,50)),E("selectDiagnoseBroadcastSchema",R(F,51)),E("SelectDiagnoseRequestSchema",R(F,52)),E("SelectDiagnoseResponseSchema",R(F,53)),E("UiBroadcastSchema",R(F,54)),E("UiBroadcastRequestSchema",R(F,55)),E("UiBroadcastResponseSchema",R(F,56)),E("ProtocolType",function(E){return E[E.UNSPECIFIED=0]="UNSPECIFIED",E}({})),E("ProtocolTypeSchema",S(F,0)),E("PutLandminePos",function(E){return E[E.UNSPECIFIED=0]="UNSPECIFIED",E[E.FIRST=1]="FIRST",E[E.SECOND=2]="SECOND",E[E.THIRD=3]="THIRD",E[E.FOURTH=4]="FOURTH",E[E.FIVE=5]="FIVE",E[E.BOTTOM=6]="BOTTOM",E[E.RANDOM=7]="RANDOM",E}({})),E("PutLandminePosSchema",S(F,1)),E("Card",function(E){return E[E.UNSPECIFIED=0]="UNSPECIFIED",E[E.LANDMINE=1]="LANDMINE",E[E.PREDICTION=2]="PREDICTION",E[E.SCOUT=3]="SCOUT",E[E.GIVE_ME_YOUR_CARD=4]="GIVE_ME_YOUR_CARD",E[E.EXCHANGE=5]="EXCHANGE",E[E.SHUFFLE=6]="SHUFFLE",E[E.BYPASS=7]="BYPASS",E[E.TURN=8]="TURN",E[E.THROW_LANDMINE=9]="THROW_LANDMINE",E[E.DOUBLE_BYPASS=10]="DOUBLE_BYPASS",E[E.REMOVE_LANDMINE=12]="REMOVE_LANDMINE",E[E.RESISTANCE=13]="RESISTANCE",E[E.CURSE=14]="CURSE",E[E.BLESSING=15]="BLESSING",E[E.CLAMP=16]="CLAMP",E}({})),E("CardSchema",S(F,2)),E("DeskState",function(E){return E[E.DESK_STATE_UNSPECIFIED=0]="DESK_STATE_UNSPECIFIED",E[E.DESK_STATE_GAME_START=1]="DESK_STATE_GAME_START",E[E.DESK_STATE_DRAW_OR_POST=2]="DESK_STATE_DRAW_OR_POST",E[E.DESK_STATE_SELECT_TARGET=3]="DESK_STATE_SELECT_TARGET",E[E.DESK_STATE_SELECT_DIAGNOSE_TARGET=4]="DESK_STATE_SELECT_DIAGNOSE_TARGET",E[E.DESK_DIAGNOSE_RESULT=5]="DESK_DIAGNOSE_RESULT",E[E.DESK_STATE_GIVE_ME_YOUR_CARD=6]="DESK_STATE_GIVE_ME_YOUR_CARD",E[E.DESK_STATE_REMOVE_LANDMINE=7]="DESK_STATE_REMOVE_LANDMINE",E[E.DESK_STATE_FINISHED=8]="DESK_STATE_FINISHED",E[E.DESK_STATE_DRAW_OR_POST_TIMEOUT=9]="DESK_STATE_DRAW_OR_POST_TIMEOUT",E[E.DESK_STATE_RESISTANCE_EFFECTIVE=10]="DESK_STATE_RESISTANCE_EFFECTIVE",E[E.DESK_STATE_HAS_CARD_QUEUE=11]="DESK_STATE_HAS_CARD_QUEUE",E[E.DESK_STATE_CLAMP=12]="DESK_STATE_CLAMP",E[E.DESK_STATE_BLESSING=13]="DESK_STATE_BLESSING",E}({})),E("DeskStateSchema",S(F,3)),E("CardState",function(E){return E[E.UNSPECIFIED=0]="UNSPECIFIED",E[E.UNUSED=1]="UNUSED",E[E.USED=2]="USED",E[E.BAD=3]="BAD",E[E.GOOD=4]="GOOD",E}({})),E("CardStateSchema",S(F,4)),E("RoomPattern",function(E){return E[E.UNSPECIFIED=0]="UNSPECIFIED",E[E.PERSONAGE=1]="PERSONAGE",E[E.TEAM=2]="TEAM",E}({})),E("RoomPatternSchema",S(F,5)),E("UiAction",function(E){return E[E.UNSPECIFIED=0]="UNSPECIFIED",E[E.DRAW=1]="DRAW",E[E.GIVE_ME_YOUR_CARD=2]="GIVE_ME_YOUR_CARD",E[E.SCOUT=3]="SCOUT",E}({})),E("UiActionSchema",S(F,6));A._RF.pop()}}}));

System.register("chunks:///_virtual/MusicSwitch.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts","./Switch.ts"],(function(t){var i,c,s,u,n;return{setters:[function(t){i=t.inheritsLoose},function(t){c=t.cclegacy,s=t._decorator},function(t){u=t.cat},function(t){n=t.Switch}],execute:function(){var o;c._RF.push({},"ac25b0Ns3pBXKHzu/oxTge6","MusicSwitch",void 0);var r=s.ccclass;s.property,t("MusicSwitch",r("MusicSwitch")(o=function(t){function c(){return t.apply(this,arguments)||this}i(c,t);var s=c.prototype;return s.start=function(){this.data.is_on=u.audio.switchMusic},s.onSwitchHandler=function(){u.audio.switchMusic=!u.audio.switchMusic,this.data.is_on=u.audio.switchMusic},c}(n))||o);c._RF.pop()}}}));

System.register("chunks:///_virtual/Native.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Bridge.ts","./index43.ts"],(function(e){var t,i,a,n;return{setters:[function(e){t=e.inheritsLoose},function(e){i=e.cclegacy},function(e){a=e.Bridge},function(e){n=e.MessageManager}],execute:function(){i._RF.push({},"1206fORr3ZL+JFhNfBVJMK2","Native",void 0);e("NativeAPI",function(e,i,a,n,s){function o(){return e.apply(this,arguments)||this}t(o,e);var r=o.prototype;return r[i]=function(e){window.ccLog("API接口桥接返回",e);var t=JSON.parse(e),i=t.data;"errorCode"in i&&console.error(i.detailMessage),this.has(t.name)&&this.dispatchEvent(t.name,i)},r[a]=function(e){window.ccLog("BRIDGE接口桥接返回",e);var t=JSON.parse(e),i=t.name,a=t.data;this.has(i)&&this.dispatchEvent(i,a)},r[n]=function(e){window.ccLog("COMMON接口桥接返回",e);var t=JSON.parse(e),i=t.name,a=t.data;this.has(i)&&this.dispatchEvent(i,a)},r[s]=function(e){window.ccLog("CHATROOM接口桥接返回",e);var t=JSON.parse(e),i=t.name,a=t.data;this.has(i)&&this.dispatchEvent(i,a)},o}(n,a.MethodName.NativeBridge.CALLBACK_API_RESPONSE,a.MethodName.NativeBridge.CALLBACK_BRIDGE_RESPONSE,a.MethodName.NativeBridge.CALLBACK_COMMON_RESPONSE,a.MethodName.NativeBridge.CALLBACK_CHATROOM_RESPONSE));i._RF.pop()}}}));

System.register("chunks:///_virtual/new-message_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./file.js","./message.js"],(function(e){var s,c,n;return{setters:[function(e){s=e.cclegacy},null,null,null,function(e){c=e.fileDesc},function(e){n=e.messageDesc}],execute:function(){s._RF.push({},"2ed2e8o/TJIgog8iOcn0yVO","new-message_pb",void 0);var i=e("file_new_message",c("ChFuZXctbWVzc2FnZS5wcm90bxIGcHJvdG9zIhgKFk5ld01lc3NhZ2VOb3RpZmljYXRpb25CMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z"));e("NewMessageNotificationSchema",n(i,0));s._RF.pop()}}}));

System.register("chunks:///_virtual/NodeUtils.ts",["cc","./index21.ts"],(function(e){var t,n,r,o,i,a,c;return{setters:[function(e){t=e.cclegacy,n=e.Component,r=e.Sprite,o=e.isValid,i=e.SpriteFrame,a=e.Layers},function(e){c=e.cat}],execute:function(){t._RF.push({},"64c71rTPxZEJYotDA0LTiWu","NodeUtils",void 0);e("setNodeGray",(function(e,t,o){void 0===t&&(t=!0),void 0===o&&(o=!0);var i=e instanceof n?e.node:e;o?i.getComponentsInChildren(r).forEach((function(e){s(e,t)})):i.getComponents(r).forEach((function(e){s(e,t)}))}));var s=e("setSpriteGray",(function(e,t){e.grayscale=t}));e("getSpriteFrameFromimageAtlas",(function(e,t){return new Promise((function(n,r){var a=e.getSpriteFrame(t);a?n(a):c.res.loadRemote(t,(function(a,c){if(a||!o(e))r(a);else{var s=i.createWithImage(c);e.spriteFrames[t]=s,n(s)}}))}))})),e("setNodeAndChildrenLayer",(function e(t,n){t.layer="string"==typeof n?Math.pow(2,a.nameToLayer(n)):n,null==t||t.children.forEach((function(t){e(t,n)}))}));t._RF.pop()}}}));

System.register("chunks:///_virtual/Notice.ts",["./rollupPluginModLoBabelHelpers.js","cc","./RootUILayer.ts","./index21.ts"],(function(t){var e,i,n,o,r,c,a,l,s,p;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.inheritsLoose,n=t.initializerDefineProperty,o=t.assertThisInitialized},function(t){r=t.cclegacy,c=t._decorator,a=t.Label,l=t.Button},function(t){s=t.default},function(t){p=t.cat}],execute:function(){var u,f,h,d,y,b,m;r._RF.push({},"0ca4bTrLM5LP68TdGQk9X2t","Notice",void 0);var v=c.ccclass,g=c.property;t("Notice",(u=v("Notice"),f=g({type:a,tooltip:"提示文本"}),h=g({type:l,tooltip:"确定按钮"}),u((b=e((y=function(t){function e(){for(var e,i=arguments.length,r=new Array(i),c=0;c<i;c++)r[c]=arguments[c];return e=t.call.apply(t,[this].concat(r))||this,n(e,"text",b,o(e)),n(e,"btn_confirm",m,o(e)),e}i(e,t);var r=e.prototype;return r.onLoad=function(){this.btn_confirm.node.on(l.EventType.CLICK,this.onConfrimHandler,this)},r.start=function(){this.props&&this.updateProps(this.props)},r.onConfrimHandler=function(){var t;null==(t=this.props)||null==t.confrim||t.confrim(),p.gui.hideNotice()},r.updateProps=function(t){this.text.string=""+t.text},e}(s)).prototype,"text",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),m=e(y.prototype,"btn_confirm",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),d=y))||d));r._RF.pop()}}}));

System.register("chunks:///_virtual/notification_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./file.js","./message.js"],(function(n){var c,i,e;return{setters:[function(n){c=n.cclegacy},null,null,null,function(n){i=n.fileDesc},function(n){e=n.messageDesc}],execute:function(){c._RF.push({},"25b11cWTANBBaHPp0G3Jx6h","notification_pb",void 0);var t=n("file_notification",i("ChJub3RpZmljYXRpb24ucHJvdG8SBnByb3RvcyI7CgxOb3RpZmljYXRpb24SDAoEQ29kZRgBIAEoDRIPCgdDb250ZW50GAIgASgJEgwKBERhdGEYAyABKAlCMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z"));n("NotificationSchema",e(t,0));c._RF.pop()}}}));

System.register("chunks:///_virtual/Overtime.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts"],(function(t){var e,n,o,r,i;return{setters:[function(t){e=t.inheritsLoose},function(t){n=t.cclegacy,o=t._decorator,r=t.Animation},function(t){i=t.BaseComponent}],execute:function(){var c;n._RF.push({},"391bbcqy2FNGoNRz6GA9IQY","Overtime",void 0);var s=o.ccclass;o.property,t("Overtime",s("Overtime")(c=function(t){function n(){return t.apply(this,arguments)||this}return e(n,t),n.prototype.start=function(){var t;null==(t=this.node.getComponent(r))||t.play()},n}(i))||c);n._RF.pop()}}}));

System.register("chunks:///_virtual/PartyGameEventConstant.ts",["cc"],(function(_){var A;return{setters:[function(_){A=_.cclegacy}],execute:function(){A._RF.push({},"2a6c6L3GctGgLvrqeD9PVCB","PartyGameEventConstant",void 0);_("PartyGameEventConstant",function(_){return _[_.PARTY_GAME_QUICK_JOIN_ERROR=132185]="PARTY_GAME_QUICK_JOIN_ERROR",_[_.PARTY_GAME_QUICK_JOIN_SUCCESS=132186]="PARTY_GAME_QUICK_JOIN_SUCCESS",_[_.PARTY_GAME_ARENA_MATCH_SUCCESS=132187]="PARTY_GAME_ARENA_MATCH_SUCCESS",_[_.PARTY_GAME_BEGIN_START=132188]="PARTY_GAME_BEGIN_START",_[_.PARTY_GAME_UNREADY_KICK_OUT=132189]="PARTY_GAME_UNREADY_KICK_OUT",_}({}));A._RF.pop()}}}));

System.register("chunks:///_virtual/PassCardAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index21.ts","./index40.ts","./msg_pb.ts","./GameEventConstant.b.ts","./AudioEffectConstant.b.ts"],(function(n){var t,o,s,e,i,a,c,r;return{setters:[function(n){t=n.inheritsLoose},function(n){o=n.cclegacy,s=n._decorator},function(n){e=n.BaseComponent},function(n){i=n.cat},null,function(n){a=n.Card},function(n){c=n.GameEventConstant},function(n){r=n.AudioEffectConstant}],execute:function(){var u;o._RF.push({},"fefadT1KtpLRIXOb+d0Hk4k","PassCardAction",void 0);var f=s.ccclass;s.property,n("PassCardAction",f("PassCardAction")(u=function(n){function o(){return n.apply(this,arguments)||this}t(o,n);var s=o.prototype;return s.onEventListener=function(){i.event.on("EVENT_POST",this.onGamePostHandler,this).on(c.BYPASS_CARD_RESPONSE,this.onBypassAction,this)},s.onBypassAction=function(n){},s.onGamePostHandler=function(n){a.BYPASS==n.post&&(i.audio.playEffect(r.PASS),window.ccLog("绕过"))},o}(e))||u);o._RF.pop()}}}));

System.register("chunks:///_virtual/PassiveRequest.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index40.ts","./Decorator.ts","./LightCardItem.ts","./BaseHandCard.ts","./DarkCardList.ts","./CommonActionUI.ts","./index21.ts","./reflect.js","./descriptors.js","./msg_pb.ts","./index23.ts","./create.js","./descriptor_pb.js","./binary-encoding.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./GameEventConstant.b.ts"],(function(e){var t,n,r,a,i,o,s,d,c,l,u,p,h,_,f,v,C,y,g,m,b,E,R,A,D,I,P;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,r=e.initializerDefineProperty,a=e.assertThisInitialized},function(e){i=e.cclegacy,o=e._decorator,s=e.Node,d=e.Label,c=e.Prefab,l=e.Button,u=e.Layers,p=e.instantiate},null,function(e){h=e.audioEffect,_=e.buttonLock,f=e.watchUser},function(e){v=e.LightCardItem},function(e){C=e.BaseHandCard},function(e){y=e.DarkCardList},function(e){g=e.CommonActionUI},function(e){m=e.cat},function(e){b=e.reflect},null,function(e){E=e.GiveMeYourCardRequestSchema,R=e.GiveMeYourCardResponseSchema,A=e.UiAction},function(e){D=e.default},function(e){I=e.create},null,null,null,null,null,null,function(e){P=e.GameEventConstant}],execute:function(){var T,q,B,L,S,x,G,H,k,j,w,M,U,O;i._RF.push({},"6e482dzk5BGSKFwxmh4r8+f","PassiveRequest",void 0);var Y=o.ccclass,z=o.property;e("PassiveRequest",(T=Y("PassiveRequest"),q=z({type:s,tooltip:"给牌按钮"}),B=z({type:d,tooltip:"索要提示"}),L=z({type:C,tooltip:"手牌节点"}),S=z({type:c,tooltip:"暗牌容器节点"}),x=f(),G=h(),H=_(),T((w=t((j=function(e){function t(){for(var t,n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return t=e.call.apply(e,[this].concat(i))||this,r(t,"btn_give_card",w,a(t)),r(t,"request_tips",M,a(t)),r(t,"hand_card",U,a(t)),r(t,"dark_card_list",O,a(t)),t.dark_card_list_node=null,t}n(t,e);var i=t.prototype;return i.onLoad=function(){this.btn_give_card.on(l.EventType.CLICK,this.onGiveCardHandler,this)},i.start=function(){var e=this;if(this.request_tips.string=this.props.isRequestPlayer?"你向"+this.props.responsePlayer.nickname+"索要,请选择一张牌":this.props.requestPlayer.nickname+"向你索要",m.event.dispatchEvent(P.HAND_CARD_STATE,!1),this.props.isRequestPlayer){var t=p(this.dark_card_list),n=t.getComponent(y);this.dark_card_list_node=t,n.addToParent(this.node,{props:{length:this.props.responsePlayer.onHandCount}})}else this.btn_give_card.removeFromParent(),this.props.handCard.list.children.forEach((function(t){var n=t.getComponent(v).props.card;e.hand_card.onceAddCard([n])})),this.hand_card.setNodeAndChildrenLayer(u.Enum.DEFAULT).setHandCardGray(!0)},i.onEventListener=function(){m.event.on("EVENT_GIVE_ME_YOUR_CARD_BROADCAST",this.onResponseRequestCardBroadcast,this).on("EVENT_DATA_BROADCAST",this.onDataBroadcastHandler,this).on("EVENT_UI_BROADCAST",this.onEventUIBroadcast,this)},i.onGiveCardHandler=function(){var e=D.game.giveMeYourCardSelectedCardIndex;if(void 0===e)return m.gui.showToast({title:"请选择一张手牌"});m.ws.Request("GiveMeYourCard",b(E,I(E,{cardIndex:e,fromPlayerIndex:this.props.responsePlayer.index})),R)},i.onResponseRequestCardBroadcast=function(){this.close()},i.onDataBroadcastHandler=function(e){var t=JSON.parse(e.data).selectedCardIndex;D.game.giveMeYourCardSelectedCardIndex=t},i.onEventUIBroadcast=function(e){switch(e.action){case A.GIVE_ME_YOUR_CARD:return this.scheduleOnce((function(){D.game.giveMeYourCardSelectedCardIndex=e.cardIndices[0]}),1.5)}},i.onDestroy=function(){m.event.dispatchEvent(P.HAND_CARD_STATE,!0),D.game.giveMeYourCardSelectedCardIndex=void 0},t}(g)).prototype,"btn_give_card",[q],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=t(j.prototype,"request_tips",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=t(j.prototype,"hand_card",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=t(j.prototype,"dark_card_list",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),t(j.prototype,"onGiveCardHandler",[x,G,H],Object.getOwnPropertyDescriptor(j.prototype,"onGiveCardHandler"),j.prototype),k=j))||k));i._RF.pop()}}}));

System.register("chunks:///_virtual/PhaseValidation.ts",["cc","./index23.ts"],(function(n){var t;return{setters:[function(n){t=n.cclegacy},null],execute:function(){n("PhaseValidation",(function(n){return function(n,t,e){e.value;return e.value=function(){},e}})),t._RF.push({},"b4381eRY1VMILLTUqsUyHKx","PhaseValidation",void 0),t._RF.pop()}}}));

System.register("chunks:///_virtual/PlatformConstant.ts",["cc"],(function(F){var E,A;return{setters:[function(F){E=F.cclegacy,A=F.v3}],execute:function(){E._RF.push({},"2594bfQ7VpPI48VkbOzYuxK","PlatformConstant",void 0);var t=F("PlatformConstant",(function(){}));t.wave_scale=A(1,1,1),t.iconFrame_scale=A(1,1,1),t.face_scale=A(.8,.8,.8),t.vipLevel=[["EDA989","EFC8B0","EDA989","EFC8B0"],["EDA989","EFC8B0","EDA989","EFC8B0"],["EDA989","EFC8B0","EDA989","EFC8B0"],["6EA0FF","C0DBFF","6EA0FF","C0DBFF"],["6EA0FF","C0DBFF","6EA0FF","C0DBFF"],["6EA0FF","C0DBFF","6EA0FF","C0DBFF"],["FFAE6A","FFE6C4","FFAE6A","FFE6C4"],["FFAE6A","FFE6C4","FFAE6A","FFE6C4"],["FFAE6A","FFE6C4","FFAE6A","FFE6C4"],["B769F3","FFE6D2","B769F3","FFE6D2"]],E._RF.pop()}}}));

System.register("chunks:///_virtual/PlayerItem.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index21.ts","./index40.ts","./create.js","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./index23.ts","./msg_pb.ts","./ThrowMine.ts","./Curse.ts","./index3.js","./GetTimeDifferenceFromServer.ts","./JSBridge.ts","./PlatformConstant.ts","./plistImage.ts","./GameEventConstant.b.ts","./msg_pb.js"],(function(e){var t,n,i,o,r,a,l,s,u,c,d,p,f,h,_,m,y,b,v,g,w,E,T,P,A,S,O,I,L,C,U,N,D,M,R;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){r=e.cclegacy,a=e._decorator,l=e.Sprite,s=e.Label,u=e.Node,c=e.SpriteFrame,d=e.Prefab,p=e.Input,f=e.sp,h=e.error,_=e.isValid,m=e.Tween,y=e.Vec3,b=e.tween,v=e.v3,g=e.instantiate},function(e){w=e.BaseComponent},function(e){E=e.cat},null,function(e){T=e.create},null,null,null,null,null,null,null,function(e){P=e.default},function(e){A=e.DeskState,S=e.PlayerSchema},function(e){O=e.ThrowMine},function(e){I=e.Curse},null,function(e){L=e.default},function(e){C=e.JSBridgeWebView,U=e.EnumJSBridgeWebView},function(e){N=e.PlatformConstant},function(e){D=e.plistImage},function(e){M=e.GameEventConstant},function(e){R=e.PlayerSchema}],execute:function(){var z,F,k,V,W,B,H,j,x,K,J,Y,G,q,Q,X,Z,$,ee,te,ne,ie,oe,re,ae,le,se,ue,ce,de,pe,fe,he,_e,me,ye,be,ve,ge,we,Ee,Te,Pe,Ae,Se,Oe,Ie;r._RF.push({},"2c08afmBpNGJJIm4yIWKtUs","PlayerItem",void 0);var Le=a.ccclass,Ce=a.property,Ue=e("PlayerState",function(e){return e[e.NORMAL=0]="NORMAL",e[e.AUTO=1]="AUTO",e[e.OUT=2]="OUT",e[e.LEAVE=3]="LEAVE",e[e.WATCH=4]="WATCH",e}({})),Ne=e("RoundState",function(e){return e[e.NORMAL=0]="NORMAL",e[e.COUNTDOWN=1]="COUNTDOWN",e}({}));e("PlayerItem",(z=Le("Player"),F=Ce({type:l,tooltip:"玩家节点"}),k=Ce({type:s,tooltip:"昵称节点"}),V=Ce({type:l,tooltip:"头像节点"}),W=Ce({type:s,tooltip:"卡牌数量节点"}),B=Ce({type:u,tooltip:"选择箭头节点"}),H=Ce({type:l,tooltip:"操作倒计时节点"}),j=Ce({type:u,tooltip:"标识节点"}),x=Ce({type:u,tooltip:"托管标识节点"}),K=Ce({type:u,tooltip:"出局标识节点"}),J=Ce({type:u,tooltip:"离开标识节点"}),Y=Ce({type:u,tooltip:"观战标识节点"}),G=Ce({type:u,tooltip:"摸雷标识节点"}),q=Ce({type:O,tooltip:"抛雷(甩锅)动画组件"}),Q=Ce({type:I,tooltip:"诅咒状态和动画组件"}),X=Ce({type:[c],tooltip:"边框精灵图集"}),Z=Ce({type:l,tooltip:"边框"}),$=Ce({type:u,tooltip:"声波特效"}),ee=Ce({type:u,tooltip:"声波特效"}),te=Ce({type:u,tooltip:"表情包节点"}),ne=Ce({type:u,tooltip:"头像框节点"}),ie=Ce(u),oe=Ce(d),z((le=t((ae=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];return t=e.call.apply(e,[this].concat(r))||this,i(t,"player_node",le,o(t)),i(t,"nick_name_node",se,o(t)),i(t,"avatar_node",ue,o(t)),i(t,"card_count_node",ce,o(t)),i(t,"select_arrow_node",de,o(t)),i(t,"countdown_node",pe,o(t)),i(t,"tag_node",fe,o(t)),i(t,"auto_node",he,o(t)),i(t,"out_node",_e,o(t)),i(t,"leave_node",me,o(t)),i(t,"watch_node",ye,o(t)),i(t,"draw_mine_node",be,o(t)),i(t,"throw_mine",ve,o(t)),i(t,"curse",ge,o(t)),i(t,"border_spriteFrame",we,o(t)),i(t,"border",Ee,o(t)),i(t,"sound_effect",Te,o(t)),i(t,"wave_node",Pe,o(t)),i(t,"node_face",Ae,o(t)),i(t,"node_frame",Se,o(t)),i(t,"node_iFrame",Oe,o(t)),i(t,"plistImg",Ie,o(t)),t.stateNodeMap=void 0,t.time_switch=!1,t.end_time=0,t.run_time=0,t.duration=0,t.props={player:T(S)},t.data={state:Ue.NORMAL,basePlayer:T(R),show_select:!1},t}n(t,e);var r=t.prototype;return r.onLoad=function(){var e,t=this;this.node.on(p.EventType.TOUCH_END,this.onClickPlayerHandler,this),null==(e=this.sound_effect.getComponent(f.Skeleton))||e.setCompleteListener((function(e){t.sound_effect.active=!1}))},r.start=function(){},r.initUI=function(){var e,t=this;this.throw_mine.node.active=this.select_arrow_node.active=!1;var n=this.props.player,i=P.game.getBasePlayerByUid(n.id);i&&(this.data.basePlayer=i),this.nick_name_node.string=""+E.util.stringUtil.sub(null!=(e=null==i?void 0:i.nickname)?e:"",10),(null==i?void 0:i.avatar)&&E.res.loadRemote(null==i?void 0:i.avatar,(function(e,n){if(e)return h(e);if(_(t.avatar_node)){var i=c.createWithImage(n);i.packable=!1,t.avatar_node.spriteFrame=i}}))},r.updatePlatform=function(){this.showIconFrame()},r.onEventListener=function(){E.event.on(M.STOP_PLAYER_Timer,this.onStopPlayerTimerHandler,this).on("EVENT_PLAYER_OVER",this.onPlayerOverHandler,this).on(M.UPDATE_PLATFORM,this.updatePlatform,this),C.on(U.MIKE_SOUND_WAVE,this.onSoundWave,this),C.on(U.PLAYMIKEEMOJI,this.showFaceImg,this)},r.removeListener=function(){C.off(U.MIKE_SOUND_WAVE,this.onSoundWave,this),C.off(U.PLAYMIKEEMOJI,this.showFaceImg,this)},r.onAutoObserver=function(){var e=this;this.addAutorun([function(){switch(e.data.state===Ue.AUTO?(window.ccLog("用户进入托管"),E.tracking.game.gameEntrust("on")):(window.ccLog("用户解除托管"),E.tracking.game.gameEntrust("off")),window.ccLog("更新状态",e.data.state),e.auto_node.active=e.leave_node.active=e.watch_node.active=e.out_node.active=!1,E.event.dispatchEvent(M.UPDATE_PLAYER_STATE),e.data.state){case Ue.LEAVE:e.leave_node.active=!0;break;case Ue.WATCH:e.watch_node.active=!0;break;case Ue.OUT:E.util.nodeUtils.setNodeGray(e.node),e.out_node.active=!0,e.throw_mine.hide(),e.curse.hide();break;case Ue.AUTO:e.auto_node.active=!0;break;case Ue.NORMAL:}e.tag_node.active=e.leave_node.active||e.watch_node.active||e.out_node.active||e.auto_node.active},function(){e.updatePalyerInfoByPlayer()},function(){e.card_count_node.string=""+e.props.player.onHandCount},function(){var t=P.game.roomData,n=t.state,i=t.timeoutSec,o=t.cursor;[A.DESK_STATE_GAME_START,A.DESK_STATE_FINISHED,A.DESK_STATE_UNSPECIFIED].includes(n)?e.stopTimer():e.isCurrentPlayer(o)?(window.ccLog("更新指定玩家"+o+"计时",i),e.updateActionTime(L(Number(i)))):e.stopTimer()},function(){e.select_arrow_node.active=e.data.show_select},function(){var t=e.props.player;e.throw_mine.setUpdateProps({player:t}),e.curse.setUpdateProps(e.props)}]),this.addReaction((function(){return P.game.roomData.state===A.DESK_STATE_REMOVE_LANDMINE&&e.isCurrentPlayer(P.game.roomData.cursor)}),(function(t){t?e.playEffectDrawMine():e.stopEffectDrawMine()}),{fireImmediately:!0})},r.showFaceImg=function(e){if(this.data.basePlayer){var t=e;"string"==typeof e&&(t=JSON.parse(e)),console.log("表情显示内容============",t.playUserId,this.data.basePlayer.id,t.playUrl),t.playUserId===this.data.basePlayer.id&&this.createVideoSprite(t.playUrl,N.face_scale,!1,this.node_face)}},r.onClickPlayerHandler=function(){E.event.has(M.SELECT_PLAYER)?E.event.dispatchEvent(M.SELECT_PLAYER,this.props.player):E.platform.openProfile({userId:Number(this.props.player.id)})},r.update=function(e){var t;this.time_switch&&(this.run_time+=1e3*e,this.run_time>=this.end_time&&(window.ccLog("计时结束"),this.run_time=this.end_time,this.stopTimer()),this.countdown_node.fillRange=(this.end_time-this.run_time)/this.duration,this.props.player.id===(null==(t=P.game.getUserPlayer)?void 0:t.id)&&E.event.dispatchEvent(M.DRAW_SCOUT_TIME_OUT,{time:Math.round((this.end_time-this.run_time)/1e3),progress:this.countdown_node.fillRange}))},r.startTimer=function(e){var t=Date.now();this.run_time=t,this.end_time=e,this.duration=e-t,this.time_switch=!0,this.countdown_node.node.active=!0},r.stopTimer=function(){this.time_switch=!1,this.changePlayerRonudSatate(Ne.NORMAL),this.countdown_node.node.active=!1},r.updateActionTime=function(e){this.changePlayerRonudSatate(Ne.COUNTDOWN),this.startTimer(e)},r.onStopPlayerTimerHandler=function(e){e==this.props.player.index&&this.stopTimer()},r.updatePalyerInfoByPlayer=function(){window.ccLog("更新玩家信息",this.props.player);var e=this.props.player;e.exit||!this.data.basePlayer.online?this.data.state=Ue.LEAVE:e.die?this.data.state=Ue.OUT:e.auto?this.data.state=Ue.AUTO:this.data.state=Ue.NORMAL},r.resetPlayerTimer=function(e){this.stopTimer(),this.updateActionTime(L(e))},r.playEffectBoom=function(){},r.playEffectDrawMine=function(){this.draw_mine_node.active=!0},r.stopEffectDrawMine=function(){this.draw_mine_node.active=!1},r.onDestroy=function(){m.stopAllByTarget(this.player_node.node),this.unscheduleAllCallbacks()},r.onPlayerOverHandler=function(e){this.isCurrentPlayer(e.index)&&(this.stopEffectDrawMine(),this.playEffectBoom(),this.stopTimer())},r.isCurrentPlayer=function(e){return this.props.player.index===e},r.isCurrentCursor=function(e){return P.user.userIndex==e},r.changePlayerRonudSatate=function(e){m.stopAllByTarget(this.player_node.node),e==Ne.NORMAL?(this.player_node.node.setScale(y.ONE),this.border.spriteFrame=this.border_spriteFrame[0],this.countdown_node.node.active=!1):(this.player_node.node.setScale(1.2,1.2),this.border.spriteFrame=this.border_spriteFrame[1],this.countdown_node.node.active=!0,b(this.node).to(.2,{scale:v(1.2,1.2,1)}).to(.2,{scale:v(1,1,1)}).start())},r.onSoundWave=function(e){var t,n,i;if(console.log("声波信息============",typeof e,e),null!=(t=this.props)&&null!=(t=t.player)&&t.id&&!(e.length<=0)&&!(e.indexOf(Number(null==(n=this.props)||null==(n=n.player)?void 0:n.id))<=-1)){var o=P.game.getPlatUserWave(null==(i=this.props)||null==(i=i.player)?void 0:i.id);if(o){var r=this.wave_node;r.children.length<=0&&this.createVideoSprite(o,N.wave_scale,!1,r)}else{var a;if(this.sound_effect.active)return;this.sound_effect.active=!0,null==(a=this.sound_effect.getComponent(f.Skeleton))||a.setAnimation(0,"animation",!1)}}},r.showIconFrame=function(){if(this.props.player.id){var e=P.game.getPlatUserAvatarFrame(this.props.player.id);if(console.log("showIconFrame===========",e),e){var t=this.node_frame,n=t.children;if(n.length>0&&_(n[0])){var i=n[0].getComponent(D);if((null==i?void 0:i.remoteUrl)==e)return;this.createVideoSprite(e,N.iconFrame_scale,!0,t)}else this.createVideoSprite(e,N.iconFrame_scale,!0,t)}}},r.createVideoSprite=function(e,t,n,i){var o;i.destroyAllChildren();var r=g(this.plistImg);r.scale=t,null==(o=r.getComponent(D))||o.loadUrl(e,n),i.addChild(r)},t}(w)).prototype,"player_node",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),se=t(ae.prototype,"nick_name_node",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ue=t(ae.prototype,"avatar_node",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ce=t(ae.prototype,"card_count_node",[W],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),de=t(ae.prototype,"select_arrow_node",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),pe=t(ae.prototype,"countdown_node",[H],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),fe=t(ae.prototype,"tag_node",[j],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),he=t(ae.prototype,"auto_node",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_e=t(ae.prototype,"out_node",[K],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),me=t(ae.prototype,"leave_node",[J],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ye=t(ae.prototype,"watch_node",[Y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),be=t(ae.prototype,"draw_mine_node",[G],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ve=t(ae.prototype,"throw_mine",[q],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),ge=t(ae.prototype,"curse",[Q],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),we=t(ae.prototype,"border_spriteFrame",[X],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),Ee=t(ae.prototype,"border",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Te=t(ae.prototype,"sound_effect",[$],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Pe=t(ae.prototype,"wave_node",[ee],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ae=t(ae.prototype,"node_face",[te],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Se=t(ae.prototype,"node_frame",[ne],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Oe=t(ae.prototype,"node_iFrame",[ie],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Ie=t(ae.prototype,"plistImg",[oe],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),re=ae))||re));r._RF.pop()}}}));

System.register("chunks:///_virtual/plistCache.ts",["cc"],(function(t){var n,e;return{setters:[function(t){n=t.cclegacy,e=t._decorator}],execute:function(){n._RF.push({},"74e76hl+aNLYYgH/HMGGQOw","plistCache",void 0);e.ccclass,e.property;t("plistCache",function(){function t(){this.plistCacheMap={}}t.getInstance=function(){return t.instance||(t.instance=new t),t.instance};var n=t.prototype;return n.add=function(t,n){this.has(t)||(this.plistCacheMap[t]=n)},n.getItemFrame=function(t){return this.plistCacheMap[t]},n.has=function(t){return null!=this.plistCacheMap[t]},t}()).instance=null,n._RF.pop()}}}));

System.register("chunks:///_virtual/plistImage.ts",["./rollupPluginModLoBabelHelpers.js","cc","./plistMgr.ts"],(function(t){var e,r,s,i,n,o,a,c,l,p,u;return{setters:[function(t){e=t.applyDecoratedDescriptor,r=t.inheritsLoose,s=t.initializerDefineProperty,i=t.assertThisInitialized,n=t.asyncToGenerator,o=t.regeneratorRuntime},function(t){a=t.cclegacy,c=t._decorator,l=t.Sprite,p=t.Component},function(t){u=t.plistMgr}],execute:function(){var h,f,m;a._RF.push({},"b8cc44XHuNPg7/1PiaB4Dbq","plistImage",void 0);var d=c.ccclass,g=c.property;t("plistImage",d("plistImage")((m=e((f=function(t){function e(){for(var e,r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return e=t.call.apply(t,[this].concat(n))||this,s(e,"loop",m,i(e)),e.frameIdx=0,e.frames=[],e.remoteUrl="",e}r(e,t);var a=e.prototype;return a.loadUrl=function(){var t=n(o().mark((function t(e,r){var s;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return void 0===r&&(r=!1),t.prev=1,this.remoteUrl=e,this.loop=r,t.next=6,u.getInstance().loadRemoteAssets(e);case 6:s=t.sent,this.frames=s.spAtlas.getSpriteFrames(),this.stop(),this.play(),t.next=16;break;case 12:t.prev=12,t.t0=t.catch(1),this.node.destroy(),console.error("动画加载失败:",t.t0);case 16:case"end":return t.stop()}}),t,this,[[1,12]])})));return function(e,r){return t.apply(this,arguments)}}(),a.play=function(){var t=this;if(this.frames.length){if(this.frameIdx>=this.frames.length&&(this.frameIdx=0,!this.loop))return void this.node.destroy();this.getComponent(l).spriteFrame=this.frames[this.frameIdx],this.scheduleOnce((function(){t.play()}),.03),this.frameIdx++}},a.stop=function(){this.frameIdx=0,this.unscheduleAllCallbacks()},e}(p)).prototype,"loop",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),h=f))||h);a._RF.pop()}}}));

System.register("chunks:///_virtual/plistMgr.ts",["./rollupPluginModLoBabelHelpers.js","cc","./PlistParse.ts","./plistCache.ts"],(function(t){var e,n,r,s,a,o,c;return{setters:[function(t){e=t.asyncToGenerator,n=t.regeneratorRuntime},function(t){r=t.cclegacy,s=t._decorator,a=t.assetManager},function(t){o=t.PlistParse},function(t){c=t.plistCache}],execute:function(){r._RF.push({},"e437buN+7hM0pnaK3omwiG4","plistMgr",void 0);s.ccclass,s.property;t("plistMgr",function(){function t(){}t.getInstance=function(){return t.instance||(t.instance=new t),t.instance};var r=t.prototype;return r.loadRemoteAssets=function(){var t=e(n().mark((function t(e){var r,s,a,u,i,p;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e+".plist",s=e+".png",!c.getInstance().getItemFrame(e)){t.next=6;break}return t.abrupt("return",c.getInstance().getItemFrame(e));case 6:return t.next=8,Promise.all([this.loadRemoteImage(s),this.loadRemoteText(r)]);case 8:return a=t.sent,u=a[0],i=a[1],p=o.parse(u,i),c.getInstance().add(e,{spAtlas:p}),t.abrupt("return",{spAtlas:p});case 14:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),r.loadRemoteImage=function(){var t=e(n().mark((function t(e){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,n){a.loadRemote(e,{ext:".png"},(function(e,r){e?n(e):t(r)}))})));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),r.loadRemoteText=function(){var t=e(n().mark((function t(e){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,n){a.loadRemote(e,{ext:".plist"},(function(e,r){e?n(e):(console.log("textAsset========",r,r.text),t(r))}))})));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),t}()).instance=null,r._RF.pop()}}}));

System.register("chunks:///_virtual/PlistParse.ts",["cc"],(function(e){var t,r,n,a,s,i,l;return{setters:[function(e){t=e.cclegacy,r=e.SpriteAtlas,n=e.Texture2D,a=e.SpriteFrame,s=e.Rect,i=e.Size,l=e.Vec2}],execute:function(){t._RF.push({},"cf45dLc6/BPJ7mbV3rBEj31","PlistParse",void 0);e("PlistParse",function(){function e(){}return e.parse=function(t,a){var s=new r,i=new n;i.image=t;var l=a._file;console.log(" plist._file",l);var c=s.spriteFrames;for(var u in l.frames)c[u.slice(0,-4)]=e.getSpriteFrame(i,l,u);return s},e.parse_frame=function(t,a){var s=new r,i=new n;i.image=t;var l=a;console.log(" parse_frame=======",l);var c=s.spriteFrames;for(var u in l.frames)c[u.slice(0,-4)]=e.getSpriteFrame(i,l,u);return s},e.getSpriteFrame=function(t,r,n){var s=r.frames[n],i=e.GetFrameData(s.frame),l=e.GetSizeData(s.sourceSize),c=e.GetOffsetData(s.offset),u=new a;return u.reset({texture:t,rect:i,offset:c,originalSize:l,isRotate:s.rotated}),u},e.GetFrameData=function(e){if(e.length<13)return console.log("---解析plist的frame rect，数据错误-----"),null;var t=e,r=(t=(t=t.slice(2)).slice(0,t.length-2)).split("},{"),n=r[0].split(","),a=r[1].split(",");return n.length<2||a.length<2?null:new s(parseInt(n[0]),parseInt(n[1]),parseInt(a[0]),parseInt(a[1]))},e.GetSizeData=function(e){if(e.length<5)return null;var t=e,r=(t=(t=t.slice(1)).slice(0,t.length-1)).split(",");return r.length<2?null:new i(parseInt(r[0]),parseInt(r[1]))},e.GetOffsetData=function(e){if(e.length<5)return null;var t=e,r=(t=(t=t.slice(1)).slice(0,t.length-1)).split(",");return r.length<2?null:new l(parseInt(r[0]),parseInt(r[1]))},e}());t._RF.pop()}}}));

System.register("chunks:///_virtual/recharge_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./enum.js","./file.js","./message.js"],(function(e){var c,n,i,t;return{setters:[function(e){c=e.cclegacy},null,null,null,function(e){n=e.enumDesc},function(e){i=e.fileDesc},function(e){t=e.messageDesc}],execute:function(){c._RF.push({},"dceb0SFQPFDNIKkF1qX7XWc","recharge_pb",void 0);var o=e("file_recharge",i("Cg5yZWNoYXJnZS5wcm90bxIGcHJvdG9zIoECChRSZWNoYXJnZU5vdGlmaWNhdGlvbhIzCgZzdGF0dXMYASABKA4yIy5wcm90b3MuUmVjaGFyZ2VOb3RpZmljYXRpb24uU3RhdHVzEhAKCG9yZGVyX2lkGAIgASgEEjoKCm9yZGVyX3R5cGUYAyABKA4yJi5wcm90b3MuUmVjaGFyZ2VOb3RpZmljYXRpb24uT3JkZXJUeXBlEg4KBmFtb3VudBgEIAEoCRIMCgRob3VyGAUgASgEIiIKBlN0YXR1cxILCgdSRVNFUlZFEAASCwoHU1VDQ0VTUxABIiQKCU9yZGVyVHlwZRIICgRDT0lOEAASDQoJUExBWV9USU1FEAFCMAokY29tLnN0bnRzLmNsb3VkLnN1aWxleW9vLmVjaG8ucHJvdG9zWgguL3Byb3Rvc2IGcHJvdG8z"));e("RechargeNotificationSchema",t(o,0)),e("RechargeNotification_Status",function(e){return e[e.RESERVE=0]="RESERVE",e[e.SUCCESS=1]="SUCCESS",e}({})),e("RechargeNotification_StatusSchema",n(o,0,0)),e("RechargeNotification_OrderType",function(e){return e[e.COIN=0]="COIN",e[e.PLAY_TIME=1]="PLAY_TIME",e}({})),e("RechargeNotification_OrderTypeSchema",n(o,0,1));c._RF.pop()}}}));

System.register("chunks:///_virtual/Reconnection.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Decorator.ts","./index21.ts","./RootUILayer.ts"],(function(t){var n,o,e,i,r,c,a,l,p,s,u;return{setters:[function(t){n=t.applyDecoratedDescriptor,o=t.inheritsLoose,e=t.initializerDefineProperty,i=t.assertThisInitialized},function(t){r=t.cclegacy,c=t._decorator,a=t.Label,l=t.Button},function(t){p=t.audioEffect},function(t){s=t.cat},function(t){u=t.default}],execute:function(){var f,h,m,d,R,_,C,E;r._RF.push({},"fc9e86BCa1D/Y+uhIaSh5bW","Reconnection",void 0);var y=c.ccclass,N=c.property,b=t("ReconnectPrompt",function(t){return t.RECONNECTED="重连成功",t.RECONNECTING="正在重连",t.MAX_RECONNECT="重连次数超出限制,请检查网络",t.RECONNECTED_ERROR="重连失败,请检查网络",t.CONNECT_PARAM_ERROR="游戏参数错误,请重新加载",t.KICK="账号已下线",t.OFFLINE="网络已断开",t.ONLINE="网络已连接",t.GAME_ERROR="连接游戏服错误",t}({}));t("Reconnection",(f=y("Reconnection"),h=N({type:a,tooltip:"通用提示文本"}),m=N({type:l,tooltip:"确定按钮"}),d=p(),f((C=n((_=function(t){function n(){for(var n,o=arguments.length,r=new Array(o),c=0;c<o;c++)r[c]=arguments[c];return n=t.call.apply(t,[this].concat(r))||this,e(n,"common_prompt_text",C,i(n)),e(n,"btn_confirm",E,i(n)),n.is_close=!1,n.props={content:null},n}o(n,t);var r=n.prototype;return r.onLoad=function(){var t=this;this.btn_confirm.node.on(l.EventType.CLICK,this.onConfirmHandler,this),this.addAutorun([function(){var n;t.common_prompt_text.string=null!=(n=t.props.content)?n:""}])},r.onDestroy=function(){this.unscheduleAllCallbacks(),this.is_close&&s.platform.back()},r.updateProps=function(t){this.updatePromptText(t,t==b.RECONNECTING)},r.updatePromptText=function(t,n){var o=this;if(void 0===n&&(n=!1),this.unscheduleAllCallbacks(),this.props.content=t,window.ccLog("更新提示文案:",t),n){var e=0;this.schedule((function(){e=(e+1)%4,o.common_prompt_text.string=t+["","·","··","···"][e]}),.5)}},r.onConfirmHandler=function(){this.is_close=!0,s.gui.hideReconnect()},n}(u)).prototype,"common_prompt_text",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),E=n(_.prototype,"btn_confirm",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),n(_.prototype,"onConfirmHandler",[d],Object.getOwnPropertyDescriptor(_.prototype,"onConfirmHandler"),_.prototype),R=_))||R));r._RF.pop()}}}));

System.register("chunks:///_virtual/RemoveCardAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./AudioSourceBaseComponent.ts","./index21.ts","./index40.ts","./msg_pb.ts","./index23.ts","./CommonAction.ts","./AudioEffectConstant.b.ts"],(function(e){var t,n,o,i,r,a,u,c,s,l,m,p,f,d,_,E;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,o=e.initializerDefineProperty,i=e.assertThisInitialized},function(e){r=e.cclegacy,a=e._decorator,u=e.Prefab,c=e.Node,s=e.instantiate},function(e){l=e.AudioSourceBaseComponent},function(e){m=e.cat},null,function(e){p=e.DeskState,f=e.Card},function(e){d=e.default},function(e){_=e.CommonAction},function(e){E=e.AudioEffectConstant}],execute:function(){var v,A,y,b,h,M,T,D,R;r._RF.push({},"c9546pt8wlOZ7crXARuUkoM","RemoveCardAction",void 0);var g=a.ccclass,N=a.property;e("RemoveCardAction",(v=g("RemoveCardAction"),A=N({type:u,tooltip:"交互-主动-拆除UI-预制体"}),y=N({type:c,tooltip:"玩家节点"}),b=N({type:c,tooltip:"摸雷(按坏牙)节点"}),v((T=t((M=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];return t=e.call.apply(e,[this].concat(r))||this,o(t,"active_remove_ui_prefab",T,i(t)),o(t,"players",D,i(t)),o(t,"draw_mine",R,i(t)),t}n(t,e);var r=t.prototype;return r.onEventListener=function(){m.event.on("EVENT_POST",this.onGamePostHandler,this).on("EVENT_TEAMMATE_REMOVE",this.onTeamDropMineHandler,this)},r.onAutoObserver=function(){var e=this;this.addReaction((function(){return d.game.roomData.state}),(function(t){t===p.DESK_STATE_REMOVE_LANDMINE?(window.ccLog("拆除地雷阶段",t===p.DESK_STATE_REMOVE_LANDMINE),e.playAudio(),e.drawMine(d.game.roomData)):e.stopAudio(),e.draw_mine.active=t===p.DESK_STATE_REMOVE_LANDMINE}),{fireImmediately:!0})},r.drawMine=function(e){var t=e.isMeRemove,n=e.isTeammateRemove;if(e.cursor===d.user.userIndex){var o=s(this.active_remove_ui_prefab);m.gui.openUI(o,{props:{is_has_remove:t,timeout_sec:Number(e.timeoutSec)}})}else if(this.isTeammateByIndex(e.cursor)&&!t&&n){var i=s(this.active_remove_ui_prefab);m.gui.openUI(i,{props:{is_has_remove:n,timeout_sec:Number(e.timeoutSec)}})}},r.onGamePostHandler=function(e){e.post==f.REMOVE_LANDMINE&&(m.audio.playEffect(E.RUN),this.getComponent(_).showCardEffect(f.REMOVE_LANDMINE))},r.isTeammateByIndex=function(e){var t=d.game.roomData.players.find((function(t){return t.index===e}));return(null==t?void 0:t.teamId)===d.user.userTeamId},r.onTeamDropMineHandler=function(e){window.ccLog("队友埋雷",e)},t}(l)).prototype,"active_remove_ui_prefab",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=t(M.prototype,"players",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=t(M.prototype,"draw_mine",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),h=M))||h));r._RF.pop()}}}));

System.register("chunks:///_virtual/report_battle_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./enum.js","./file.js","./message.js","./msg_pb.ts"],(function(A){var e,l,V,R,S;return{setters:[function(A){e=A.cclegacy},null,null,null,function(A){l=A.enumDesc},function(A){V=A.fileDesc},function(A){R=A.messageDesc},function(A){S=A.file_shark_suileyoo_v1_msg}],execute:function(){e._RF.push({},"136f5t1dwNNfp+OeoqNY5Nn","report_battle_pb",void 0);var G=A("file_shark_suileyoo_v1_report_battle",V("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",[S]));A("ReportGameStateRequestSchema",R(G,0)),A("ReportGameStateRequest_GameSettlePayloadSchema",R(G,0,0)),A("ReportGameStateRequest_TeamSettleResultSchema",R(G,0,1)),A("ReportGameStateRequest_PlayerSchema",R(G,0,2)),A("ReportGameStateRequest_ResultSchema",R(G,0,3)),A("ReportGameStateRequest_GameState",function(A){return A[A.UNSPECIFIED=0]="UNSPECIFIED",A[A.START=1]="START",A[A.SETTLE=2]="SETTLE",A[A.ABNORMAL_EXIT=3]="ABNORMAL_EXIT",A}({})),A("ReportGameStateRequest_GameStateSchema",l(G,0,0)),A("ReportPlayerGameStateRequestSchema",R(G,1)),A("ReportPlayerGameStateRequest_PlayerSchema",R(G,1,0)),A("ReportPlayerGameStateRequest_PlayerGameState",function(A){return A[A.UNSPECIFIED=0]="UNSPECIFIED",A[A.BREAK_LINE=1]="BREAK_LINE",A}({})),A("ReportPlayerGameStateRequest_PlayerGameStateSchema",l(G,1,0)),A("TeamIdErrorBroadcastSchema",R(G,2)),A("RankingSettlementRequestSchema",R(G,3));e._RF.pop()}}}));

System.register("chunks:///_virtual/RequestCardAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./SelectPlayerAction.ts","./DarkCardItem.ts","./LightCardItem.ts","./HandCard.ts","./index21.ts","./index40.ts","./Decorator.ts","./index23.ts","./msg_pb.ts","./AudioEffectConstant.b.ts"],(function(e){var t,o,r,n,a,i,s,d,c,l,u,p,_,m,g,y,f,h,v,C,T,E,b,R,I,w;return{setters:[function(e){t=e.applyDecoratedDescriptor,o=e.inheritsLoose,r=e.initializerDefineProperty,n=e.assertThisInitialized,a=e.asyncToGenerator,i=e.regeneratorRuntime},function(e){s=e.cclegacy,d=e._decorator,c=e.Prefab,l=e.Node,u=e.sp,p=e.find,_=e.instantiate,m=e.UITransform,g=e.tween,y=e.isValid},function(e){f=e.SelectPlayerAction},function(e){h=e.DarkCardItem},function(e){v=e.LightCardItem},function(e){C=e.HandCard},function(e){T=e.cat},null,function(e){E=e.audioEffect},function(e){b=e.default},function(e){R=e.DeskState,I=e.Card},function(e){w=e.AudioEffectConstant}],execute:function(){var x,A,D,P,S,k,L,B,q,O,U,z,G,N,M,V,Y,H;s._RF.push({},"65251Y77GRAT75tiUu/xZJT","RequestCardAction",void 0);var Z=d.ccclass,F=d.property;e("RequestCardAction",(x=Z("RequestCardAction"),A=F({type:c,tooltip:"交互-被动-索取UI预制体"}),D=F({type:c,tooltip:"暗牌预制体"}),P=F({type:c,tooltip:"明牌预制体"}),S=F({type:C,tooltip:"手牌组件"}),k=F({type:l,tooltip:"玩家节点"}),L=F({type:l,tooltip:"索取小动画-容器-节点"}),B=F({type:u.Skeleton,tooltip:"索取小动画-动画-节点"}),q=E(w.REQUEST_FINISHED),x((z=t((U=function(e){function t(){for(var t,o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=e.call.apply(e,[this].concat(a))||this,r(t,"passive_request_prefab",z,n(t)),r(t,"dark_card_prefab",G,n(t)),r(t,"light_card_prefab",N,n(t)),r(t,"hand_card",M,n(t)),r(t,"players",V,n(t)),r(t,"give_me_your_card_node",Y,n(t)),r(t,"give_me_your_card_skeleton",H,n(t)),t.play_zone=void 0,t}o(t,e);var s=t.prototype;return s.start=function(){var e=p("Game UI"),t=null==e?void 0:e.getComponentInChildren("Desk").node;this.play_zone=null==t?void 0:t.getChildByName("play_zone")},s.onEventListener=function(){e.prototype.onEventListener.call(this),T.event.on("EVENT_SELECT_TARGET_BROADCAST",this.onSelectTargetBroadcast,this).on("EVENT_GIVE_ME_YOUR_CARD_BROADCAST",this.onResponseRequestCardBroadcast,this)},s.onAutoObserver=function(){var t=this;e.prototype.onAutoObserver,this.addReaction((function(){return b.game.roomData.state}),(function(e){e===R.DESK_STATE_SELECT_TARGET&&I.GIVE_ME_YOUR_CARD===b.game.getLastCardOnPostZone&&(window.ccLog("索取-指定阶段",e===R.DESK_STATE_SELECT_TARGET),T.audio.playEffect(w.REQUEST),t.onStateGameSelectTarget(b.game.roomData))})),this.addAutorun([function(){if(b.game.roomData.state===R.DESK_STATE_GIVE_ME_YOUR_CARD&&I.GIVE_ME_YOUR_CARD===b.game.getLastCardOnPostZone)if(T.audio.playEffect(w.REQUESTED),window.ccLog(b.game.roomData.cursor+"向"+b.game.roomData.lastTargetPlayerIndex+"发起索取"),[b.game.roomData.cursor,b.game.roomData.lastTargetPlayerIndex].includes(b.user.userIndex)){var e=_(t.passive_request_prefab),o=b.game.getPlayerByIndex(b.game.roomData.cursor),r=b.game.getPlayerByIndex(b.game.roomData.lastTargetPlayerIndex),n=b.game.roomData.cursor===b.user.userIndex;T.gui.openUI(e,{props:{handCard:t.hand_card,requestPlayer:o,responsePlayer:r,isRequestPlayer:n},isMotion:!1})}else{var a=b.game.getPlayerComponentByIndex(t.players,b.game.roomData.cursor).node,i=b.game.getPlayerComponentByIndex(t.players,b.game.roomData.lastTargetPlayerIndex).node;t.selectTargetTween(a,i),window.ccLog("其他人 显示指向动画",a,i)}}])},s.checkValidPlayer=function(){return b.game.roomData.players.filter((function(e){var t=e.die,o=e.onHandCount,r=e.index;return window.ccLog("------",r,b.user.userIndex,!t&&o&&r!==b.user.userIndex),!t&&o&&r!==b.user.userIndex}))},s.onSelectTargetBroadcast=function(e){if(b.game.getLastCardOnPostZone===I.GIVE_ME_YOUR_CARD){var t=b.game.getPlayerComponentByIndex(this.players,e.responseIndex);this.playGiveMeYourCardAnimation({to:t})}},s.playGiveMeYourCardAnimation=function(e){var t=this,o=e.to;this.give_me_your_card_skeleton.setCompleteListener((function(){y(t.give_me_your_card_skeleton.node)&&(t.give_me_your_card_skeleton.node.active=!1)})),this.give_me_your_card_skeleton.node.active=!0,this.give_me_your_card_skeleton.setAnimation(0,"idle",!1);var r=this.play_zone.parent.getComponent(m).convertToNodeSpaceAR(this.play_zone.worldPosition),n=o.node.parent.getComponent(m).convertToNodeSpaceAR(o.node.worldPosition);this.give_me_your_card_node.setPosition(r),this.give_me_your_card_node.setScale(.4,.4),g(this.give_me_your_card_node).to(.3,{position:n}).delay(.8).call((function(){})).start()},s.onResponseRequestCardBroadcast=function(){var e=a(i().mark((function e(t){var o,r,n,a,s,d,c,l,u,p,g,y;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(window.ccLog(t.responseIndex+"向"+t.requestIndex+"响应索取-card:"+t.card),o=b.game.getPlayerComponentByIndex(this.players,t.responseIndex).node,r=b.game.getPlayerComponentByIndex(this.players,t.requestIndex).node,n=t.card,a=.25,s=this.hand_card,!b.game.isUserOrWatchTeammate(t.requestIndex)){e.next=18;break}return d=_(this.light_card_prefab),(c=d.getComponent(v)).addToParent(this.card_move_tween,{props:{card:n}}),l=this.card_move_tween.getComponent(m).convertToNodeSpaceAR(this.hand_card.list.worldPosition),e.next=13,c.moveTween(a,{position:l});case 13:c.setHandCard(!0),this.hand_card.addCard(d),window.ccLog(" 索取者 显示从被索取玩家位置-飞到手牌的动画"),e.next=32;break;case 18:if(!b.game.isUserOrWatchTeammate(t.responseIndex)){e.next=22;break}s.deleteCard(n),e.next=32;break;case 22:return(u=_(this.dark_card_prefab)).setScale(.5,.5),p=u.getComponent(h),g=this.card_move_tween.getComponent(m).convertToNodeSpaceAR(o.worldPosition),y=this.card_move_tween.getComponent(m).convertToNodeSpaceAR(r.worldPosition),p.addToParent(this.card_move_tween).setPosition(g),e.next=30,p.moveTween(a,{position:y});case 30:window.ccLog("其他人 显示 索取者->被索取者 飞牌动画"),u.destroy();case 32:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),t}(f)).prototype,"passive_request_prefab",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=t(U.prototype,"dark_card_prefab",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=t(U.prototype,"light_card_prefab",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=t(U.prototype,"hand_card",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=t(U.prototype,"players",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Y=t(U.prototype,"give_me_your_card_node",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=t(U.prototype,"give_me_your_card_skeleton",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),t(U.prototype,"onResponseRequestCardBroadcast",[q],Object.getOwnPropertyDescriptor(U.prototype,"onResponseRequestCardBroadcast"),U.prototype),O=U))||O));s._RF.pop()}}}));

System.register("chunks:///_virtual/ResistanceCardAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts","./index40.ts","./msg_pb.ts","./index23.ts","./BaseComponent.ts","./AudioEffectConstant.b.ts"],(function(e){var t,n,i,o,s,r,a,c,l,u,p,d,f,_,E;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){s=e.cclegacy,r=e._decorator,a=e.Node,c=e.sp,l=e.isValid},function(e){u=e.cat},null,function(e){p=e.DeskState,d=e.Card},function(e){f=e.default},function(e){_=e.BaseComponent},function(e){E=e.AudioEffectConstant}],execute:function(){var y,h,m,S,A,C,T,w,b;s._RF.push({},"69882ep9XxOmIs/h9j/EU7n","ResistanceCardAction",void 0);var v=r.ccclass,g=r.property;e("ResistanceAction",(y=v("ResistanceCardAction"),h=g({type:a,tooltip:"玩家节点"}),m=g({type:a,tooltip:"休想动画容器节点"}),S=g({type:c.Skeleton,tooltip:"休想骨骼动画节点"}),y((T=t((C=function(e){function t(){for(var t,n=arguments.length,s=new Array(n),r=0;r<n;r++)s[r]=arguments[r];return t=e.call.apply(e,[this].concat(s))||this,i(t,"players",T,o(t)),i(t,"resistance_tween_node",w,o(t)),i(t,"resistance_skeleton_node",b,o(t)),t}n(t,e);var s=t.prototype;return s.onEventListener=function(){u.event.on("EVENT_POST",this.onGamePostHandler,this)},s.onAutoObserver=function(){this.addReaction((function(){return f.game.roomData.state}),(function(e){e===p.DESK_STATE_RESISTANCE_EFFECTIVE&&window.ccLog("指定完成阶段",e===p.DESK_STATE_RESISTANCE_EFFECTIVE)}),{fireImmediately:!0})},s.onGamePostHandler=function(e){d.RESISTANCE==e.post&&(u.audio.playEffect(E.RESISTANCE),window.ccLog("休想"),this.playTwween())},s.playTwween=function(){var e=this;this.resistance_skeleton_node.setCompleteListener((function(){l(e.resistance_skeleton_node.node)&&(e.resistance_skeleton_node.node.active=!1)})),this.resistance_skeleton_node.node.active=!0,this.resistance_skeleton_node.setAnimation(0,"idle",!1)},t}(_)).prototype,"players",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=t(C.prototype,"resistance_tween_node",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=t(C.prototype,"resistance_skeleton_node",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=C))||A));s._RF.pop()}}}));

System.register("chunks:///_virtual/RoomEventConstant.ts",["cc"],(function(t){var n;return{setters:[function(t){n=t.cclegacy}],execute:function(){n._RF.push({},"94b1aWh6ExOe7dmL5LSyFr/","RoomEventConstant",void 0);t("RoomEventConstant",function(t){return t.MIKE_CHANGE="RoomEventConstant/MIKE_CHANGE",t.UPDATE_MIC_STATE="RoomEventConstant/UPDATE_MIC_STATE",t.UPDATE_MIKE_CONTROLLER="RoomEventConstant/UPDATE_MIKE_CONTROLLER",t.JUMP_SEASON="RoomEventConstant/JUMP_SEASON",t.UDPATE_ROOM_LIST="RoomEventConstant/UDPATE_ROOM_LIST",t}({}));n._RF.pop()}}}));

System.register("chunks:///_virtual/RootUILayer.ts",["./rollupPluginModLoBabelHelpers.js","cc","./UILayer.ts","./index21.ts","./index44.ts"],(function(t){var n,e,o,i,a;return{setters:[function(t){n=t.inheritsLoose},function(t){e=t.cclegacy},function(t){o=t.default},function(t){i=t.cat},function(t){a=t.GlobalEventConstant}],execute:function(){e._RF.push({},"dba21wHh0NJ+Y8l6zO1Lo5j","RootUILayer",void 0);t("default",function(t){function e(){return t.apply(this,arguments)||this}n(e,t);var o=e.prototype;return o.onEnable=function(){t.prototype.onEnable.call(this),window.ccLog("---------onEnable",this.node.name),this.updateMask()},o.onDisable=function(){t.prototype.onDisable.call(this),window.ccLog("---------onDisable",this.node.name),this.updateMask()},o.updateMask=function(){i.event.dispatchEvent(a.ROOT_MASK_UPDATE)},e}(o));e._RF.pop()}}}));

System.register("chunks:///_virtual/router_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./enum.js","./file.js"],(function(E){var V,T,R;return{setters:[function(E){V=E.cclegacy},null,null,null,function(E){T=E.enumDesc},function(E){R=E.fileDesc}],execute:function(){V._RF.push({},"1b7e4xbiJJAIYk+szXWKPez","router_pb",void 0);var _=E("file_shark_suileyoo_v1_router",R("Ch5zaGFyay9zdWlsZXlvby92MS9yb3V0ZXIucHJvdG8SEXNoYXJrLnN1aWxleW9vLnYxKqIFCgVFdmVudBIWChJFVkVOVDBfVU5TUEVDSUZJRUQQABIOCgpFVkVOVF9QT1NUEAESIQodRVZFTlRfU0VMRUNUX1RBUkdFVF9CUk9BRENBU1QQAhIjCh9FVkVOVF9URUFNX1BSRURJQ1RJT05fQlJPQURDQVNUEAMSHgoaRVZFTlRfVEVBTV9TQ09VVF9CUk9BRENBU1QQBBIfChtFVkVOVF9EUkFXX0JPVFRPTV9CUk9BRENBU1QQBRIlCiFFVkVOVF9HSVZFX01FX1lPVVJfQ0FSRF9CUk9BRENBU1QQBxIlCiFFVkVOVF9SRVNQT05TRV9FWENIQU5HRV9CUk9BRENBU1QQChIiCh5FVkVOVF9USFJPV19MQU5ETUlORV9CUk9BRENBU1QQDRIZChVFVkVOVF9DVVJTRV9CUk9BRENBU1QQHBIZChVFVkVOVF9DTEFNUF9CUk9BRENBU1QQHhIYChRFVkVOVF9FWElUX0JST0FEQ0FTVBAOEhMKD0VWRU5UX0JST0FEQ0FTVBAPEhYKEkVWRU5UX1VJX0JST0FEQ0FTVBAdEhAKDEVWRU5UX01FT1ZFUhAREhIKDkVWRU5UX1VTRVJJTkZPEBISDgoKRVZFTlRfRFJBVxATEhkKFUVWRU5UX1RFQU1NQVRFX1JFTU9WRRAUEhEKDUVWRU5UX1JBTktJTkcQFRIWChJFVkVOVF9GSU5JU0hFRF9PTkUQFhIWChJFVkVOVF9GSU5JU0hFRF9UV08QFxIZChVFVkVOVF9NRV9PVkVSX01FU1NBR0UQGBIYChRFVkVOVF9EQVRBX0JST0FEQ0FTVBAZEhkKFUVWRU5UX1NFTEVDVF9ESUFHTk9TRRAaEhUKEUVWRU5UX1BMQVlFUl9PVkVSEBsqtQEKBVJvdXRlEhYKElJPVVRFMF9VTlNQRUNJRklFRBAAEg8KC1JlZnJlc2hDYXJkEAESDAoIRHJhd0NhcmQQAhIPCgtGb3VuZE9uSGFuZBADEgwKCFBvc3RDYXJkEAQSEAoMU2VsZWN0VGFyZ2V0EAUSEgoOR2l2ZU1lWW91ckNhcmQQBhIJCgVXYXRjaBAHEhEKDURhdGFCcm9hZGNhc3QQCBISCg5TZWxlY3REaWFnbm9zZRAJYgZwcm90bzM"));E("Event",function(E){return E[E.EVENT0_UNSPECIFIED=0]="EVENT0_UNSPECIFIED",E[E.EVENT_POST=1]="EVENT_POST",E[E.EVENT_SELECT_TARGET_BROADCAST=2]="EVENT_SELECT_TARGET_BROADCAST",E[E.EVENT_TEAM_PREDICTION_BROADCAST=3]="EVENT_TEAM_PREDICTION_BROADCAST",E[E.EVENT_TEAM_SCOUT_BROADCAST=4]="EVENT_TEAM_SCOUT_BROADCAST",E[E.EVENT_DRAW_BOTTOM_BROADCAST=5]="EVENT_DRAW_BOTTOM_BROADCAST",E[E.EVENT_GIVE_ME_YOUR_CARD_BROADCAST=7]="EVENT_GIVE_ME_YOUR_CARD_BROADCAST",E[E.EVENT_RESPONSE_EXCHANGE_BROADCAST=10]="EVENT_RESPONSE_EXCHANGE_BROADCAST",E[E.EVENT_THROW_LANDMINE_BROADCAST=13]="EVENT_THROW_LANDMINE_BROADCAST",E[E.EVENT_CURSE_BROADCAST=28]="EVENT_CURSE_BROADCAST",E[E.EVENT_CLAMP_BROADCAST=30]="EVENT_CLAMP_BROADCAST",E[E.EVENT_EXIT_BROADCAST=14]="EVENT_EXIT_BROADCAST",E[E.EVENT_BROADCAST=15]="EVENT_BROADCAST",E[E.EVENT_UI_BROADCAST=29]="EVENT_UI_BROADCAST",E[E.EVENT_MEOVER=17]="EVENT_MEOVER",E[E.EVENT_USERINFO=18]="EVENT_USERINFO",E[E.EVENT_DRAW=19]="EVENT_DRAW",E[E.EVENT_TEAMMATE_REMOVE=20]="EVENT_TEAMMATE_REMOVE",E[E.EVENT_RANKING=21]="EVENT_RANKING",E[E.EVENT_FINISHED_ONE=22]="EVENT_FINISHED_ONE",E[E.EVENT_FINISHED_TWO=23]="EVENT_FINISHED_TWO",E[E.EVENT_ME_OVER_MESSAGE=24]="EVENT_ME_OVER_MESSAGE",E[E.EVENT_DATA_BROADCAST=25]="EVENT_DATA_BROADCAST",E[E.EVENT_SELECT_DIAGNOSE=26]="EVENT_SELECT_DIAGNOSE",E[E.EVENT_PLAYER_OVER=27]="EVENT_PLAYER_OVER",E}({})),E("EventSchema",T(_,0)),E("Route",function(E){return E[E.ROUTE0_UNSPECIFIED=0]="ROUTE0_UNSPECIFIED",E[E.RefreshCard=1]="RefreshCard",E[E.DrawCard=2]="DrawCard",E[E.FoundOnHand=3]="FoundOnHand",E[E.PostCard=4]="PostCard",E[E.SelectTarget=5]="SelectTarget",E[E.GiveMeYourCard=6]="GiveMeYourCard",E[E.Watch=7]="Watch",E[E.DataBroadcast=8]="DataBroadcast",E[E.SelectDiagnose=9]="SelectDiagnose",E}({})),E("RouteSchema",T(_,1));V._RF.pop()}}}));

System.register("chunks:///_virtual/RuleItem.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index21.ts","./index40.ts","./GameEventConstant.b.ts"],(function(t){var e,n,o,i,r,l,s,c,a,u;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,o=t.initializerDefineProperty,i=t.assertThisInitialized},function(t){r=t.cclegacy,l=t._decorator,s=t.Button},function(t){c=t.BaseComponent},function(t){a=t.cat},null,function(t){u=t.GameEventConstant}],execute:function(){var p,f,d,v,h;r._RF.push({},"0c065WicL5Jzb4wBbtlY/io","RuleItem",void 0);var y=l.ccclass,b=l.property;t("RuleItem",(p=y("RuleItem"),f=b({type:s,tooltip:"关闭节点"}),p((h=e((v=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),l=0;l<n;l++)r[l]=arguments[l];return e=t.call.apply(t,[this].concat(r))||this,o(e,"btn_close",h,i(e)),e}n(e,t);var r=e.prototype;return r.onLoad=function(){this.btn_close.node.on(s.EventType.CLICK,this.onCloseHandler,this)},r.onCloseHandler=function(){a.event.dispatchEvent(u.CLOSE_RULE_UI)},e}(c)).prototype,"btn_close",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),d=v))||d));r._RF.pop()}}}));

System.register("chunks:///_virtual/Scene3dFitWith2d.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index21.ts","./FitWidthCamera.ts"],(function(e){var t,a,i,r,n,o,c,l,s,u,p,d;return{setters:[function(e){t=e.applyDecoratedDescriptor,a=e.inheritsLoose,i=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){n=e.cclegacy,o=e._decorator,c=e.Node,l=e.Camera,s=e.v3},function(e){u=e.BaseComponent},function(e){p=e.cat},function(e){d=e.UPDATE_FOV}],execute:function(){var D,m,b,f,y,h,v,w,F,g,z;n._RF.push({},"d525bQIK4pCWrYMIQKRZhLU","Scene3dFitWith2d",void 0);var P=o.ccclass,W=o.property;e("Scene3dFitWith2d",(D=P("Scene3dFitWith2d"),m=W({type:c,tooltip:"2D参考点"}),b=W({type:c,tooltip:"3D参考点"}),f=W({type:l,tooltip:"2D相机"}),y=W({type:l,tooltip:"3D相机"}),D((w=t((v=function(e){function t(){for(var t,a=arguments.length,n=new Array(a),o=0;o<a;o++)n[o]=arguments[o];return t=e.call.apply(e,[this].concat(n))||this,i(t,"base2D",w,r(t)),i(t,"base3D",F,r(t)),i(t,"camera2D",g,r(t)),i(t,"camera3D",z,r(t)),t}a(t,e);var n=t.prototype;return n.onEventListener=function(){p.event.on(d,this.updateFit,this)},n.start=function(){this.updateFit()},n.updateFit=function(){var e=this;window.ccLog("updateFit"),this.scheduleOnce((function(){e.camera2D.camera.update(!0);var t=e.base2D.worldPosition.clone();t.y=t.y-90;var a=s();e.camera2D.camera.worldToScreen(a,t);var i=s();e.camera3D.camera.update(!0),e.camera3D.camera.screenToWorld(i,s(a.x,a.y,(e.base3D.worldPosition.z-e.camera2D.near)/e.camera2D.far));var r=i.clone().y;e.camera3D.node.setWorldPosition(s(e.camera3D.node.worldPosition.x,r,e.camera3D.node.worldPosition.z))}),.016)},t}(u)).prototype,"base2D",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),F=t(v.prototype,"base3D",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),g=t(v.prototype,"camera2D",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),z=t(v.prototype,"camera3D",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),h=v))||h));n._RF.pop()}}}));

System.register("chunks:///_virtual/SceneLayer.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts"],(function(e){var n,t,o;return{setters:[function(e){n=e.inheritsLoose},function(e){t=e.cclegacy},function(e){o=e.BaseComponent}],execute:function(){t._RF.push({},"4374fO5f+NHSqx5W61PbHC0","SceneLayer",void 0);e("default",function(e){function t(){for(var n,t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];return(n=e.call.apply(e,[this].concat(o))||this).isReload=!1,n}return n(t,e),t}(o));t._RF.pop()}}}));

System.register("chunks:///_virtual/ScoutCardAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index21.ts","./index40.ts","./index23.ts","./msg_pb.ts","./CommonAction.ts","./AudioEffectConstant.b.ts"],(function(t){var n,e,o,i,a,r,c,s,u,f,l,p,d,m,_;return{setters:[function(t){n=t.applyDecoratedDescriptor,e=t.inheritsLoose,o=t.initializerDefineProperty,i=t.assertThisInitialized},function(t){a=t.cclegacy,r=t._decorator,c=t.Prefab,s=t.instantiate},function(t){u=t.BaseComponent},function(t){f=t.cat},null,function(t){l=t.default},function(t){p=t.DeskState,d=t.Card},function(t){m=t.CommonAction},function(t){_=t.AudioEffectConstant}],execute:function(){var C,g,v,S,h;a._RF.push({},"43813VIbdVNFrJU4z00gHmO","ScoutCardAction",void 0);var A=r.ccclass,E=r.property;t("ScontCardAction",(C=A("ScontCardAction"),g=E({type:c,tooltip:"UI主动侦查-预制体"}),C((h=n((S=function(t){function n(){for(var n,e=arguments.length,a=new Array(e),r=0;r<e;r++)a[r]=arguments[r];return n=t.call.apply(t,[this].concat(a))||this,o(n,"action_scout_ui_prefab",h,i(n)),n}e(n,t);var a=n.prototype;return a.onEventListener=function(){f.event.on("EVENT_POST",this.onGamePostHandler,this)},a.onAutoObserver=function(){var t=this;this.addReaction((function(){var t;return l.game.roomData.state===p.DESK_STATE_SELECT_DIAGNOSE_TARGET&&l.game.roomData.cursor===(null==(t=l.game.getUserPlayer)?void 0:t.relBattleOffset)}),(function(n){if(n){var e=s(t.action_scout_ui_prefab);f.gui.openUI(e)}}))},a.onGamePostHandler=function(t){t.post==d.SCOUT&&(l.game.isUserOrTeammate(t.index)||(f.audio.playEffect(_.SCOUT),this.getComponent(m).showCardEffect(d.SCOUT)))},n}(u)).prototype,"action_scout_ui_prefab",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),v=S))||v));a._RF.pop()}}}));

System.register("chunks:///_virtual/SelectPlayerAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index21.ts","./index23.ts","./index40.ts","./Arrow.ts","./GameEventConstant.b.ts"],(function(e){var t,r,n,o,i,a,l,c,s,p,u,f,d,y,_;return{setters:[function(e){t=e.applyDecoratedDescriptor,r=e.inheritsLoose,n=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){i=e.cclegacy,a=e._decorator,l=e.Prefab,c=e.Node,s=e.instantiate,p=e.error},function(e){u=e.BaseComponent},function(e){f=e.cat},function(e){d=e.default},null,function(e){y=e.Arrow},function(e){_=e.GameEventConstant}],execute:function(){var h,b,g,w,v,m,S,T,E,A,P;i._RF.push({},"87cadJOHklPnZG/oioO/c5S","SelectPlayerAction",void 0);var C=a.ccclass,x=a.property;e("SelectPlayerAction",(h=C("SelectPlayerAction"),b=x({type:l,tooltip:"选择玩家UI预制体"}),g=x({type:l,tooltip:"箭头预制体"}),w=x({type:c,tooltip:"游戏区域"}),v=x({type:c,tooltip:"玩家节点"}),h((T=t((S=function(e){function t(){for(var t,r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return t=e.call.apply(e,[this].concat(i))||this,n(t,"choose_player_prefab",T,o(t)),n(t,"arrow_prefab",E,o(t)),n(t,"card_move_tween",A,o(t)),n(t,"players_node",P,o(t)),t}r(t,e);var i=t.prototype;return i.onEventListener=function(){f.event.on("EVENT_SELECT_TARGET_BROADCAST",this.onSelectTargetBroadcast,this)},i.onStateGameSelectTarget=function(e){if(window.ccLog("轮到"+e.cursor+"指定玩家",this.players_node.children.length,this.players_node),e.cursor===d.user.userIndex){var t=s(this.choose_player_prefab),r=this.checkValidPlayer();window.ccLog("有效玩家",r,r.length),f.gui.openUI(t,{props:{players:r}})}},i.onSelectTargetBroadcast=function(e){e.responseIndex==d.user.userIndex&&f.event.dispatchEvent(_.CLOSE_TEAM_DROP_MINE)},i.selectTargetTween=function(e,t){if(!e||!t)return p((!!e||"from未找到")+","+(!!t||"to未找到"));s(this.arrow_prefab).getComponent(y).addToParent(this.card_move_tween,{props:{from:e,to:t}})},t}(u)).prototype,"choose_player_prefab",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=t(S.prototype,"arrow_prefab",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),A=t(S.prototype,"card_move_tween",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),P=t(S.prototype,"players_node",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),m=S))||m));i._RF.pop()}}}));

System.register("chunks:///_virtual/Setting.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./Decorator.ts"],(function(t){var n,e,i,o,r,s,a,p,u,c;return{setters:[function(t){n=t.applyDecoratedDescriptor,e=t.inheritsLoose,i=t.initializerDefineProperty,o=t.assertThisInitialized},function(t){r=t.cclegacy,s=t._decorator,a=t.Button,p=t.Node},function(t){u=t.BaseComponent},function(t){c=t.audioEffect}],execute:function(){var l,g,d,_,f,y,h,b;r._RF.push({},"d38d3cUXs5BoLXxTu1ZKPX0","Setting",void 0);var v=s.ccclass,m=s.property;t("Setting",(l=v("Setting"),g=m({type:a,tooltip:"设置按钮节点"}),d=m({type:p,tooltip:"设置ui mask节点"}),_=c(),l((h=n((y=function(t){function n(){for(var n,e=arguments.length,r=new Array(e),s=0;s<e;s++)r[s]=arguments[s];return n=t.call.apply(t,[this].concat(r))||this,i(n,"btn_setting",h,o(n)),i(n,"setting_ui_mask_node",b,o(n)),n.data={is_open_setting:!1},n}e(n,t);var r=n.prototype;return r.initUI=function(){this.btn_setting.node.on(a.EventType.CLICK,this.onOpenSettingHandler,this)},r.onLoad=function(){},r.onDestroy=function(){},r.onAutoObserver=function(){var t=this;this.addAutorun([function(){t.setting_ui_mask_node.active=t.data.is_open_setting}])},r.onOpenSettingHandler=function(){this.data.is_open_setting=!this.data.is_open_setting},n}(u)).prototype,"btn_setting",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),b=n(y.prototype,"setting_ui_mask_node",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),n(y.prototype,"onOpenSettingHandler",[_],Object.getOwnPropertyDescriptor(y.prototype,"onOpenSettingHandler"),y.prototype),f=y))||f));r._RF.pop()}}}));

System.register("chunks:///_virtual/Share.ts",["cc","./index21.ts","./index23.ts"],(function(e){var t,i,n;return{setters:[function(e){t=e.cclegacy},function(e){i=e.cat},function(e){n=e.default}],execute:function(){e("default",(function(){var e=n.global.channel;return{setting:function(t){var n=void 0===t?{}:t,a=n.title,r=void 0===a?"":a,c=n.imageUrl,o=void 0===c?"":c;i.platform.onShareAppMessage({title:r,imageUrl:o,query:"wxgamecid="+e}),i.platform.onShareTimeline({title:r,imageUrl:o,query:"wxgamecid="+e})}}})),t._RF.push({},"903c6QofRZN3rl39tFPJyea","Share",void 0),t._RF.pop()}}}));

System.register("chunks:///_virtual/Shark.ts",["./rollupPluginModLoBabelHelpers.js","cc","./msg_pb.ts","./index21.ts","./index40.ts","./index23.ts","./IGame.ts","./Tooth.ts","./BaseComponent.ts","./create.js","./descriptors.js","./reflect.js","./descriptor_pb.js","./binary-encoding.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./Decorator.ts","./index26.ts","./IGame.b.ts","./GameEventConstant.b.ts","./AudioEffectConstant.b.ts","./index.b.ts"],(function(t){var e,o,a,n,i,r,s,l,c,h,u,p,d,f,m,_,g,b,v,y,T,k,w,D,S,E,R,A,C,O,z,P,x,B,U,M,N,L,I,F,H;return{setters:[function(t){e=t.applyDecoratedDescriptor,o=t.inheritsLoose,a=t.initializerDefineProperty,n=t.assertThisInitialized,i=t.asyncToGenerator,r=t.regeneratorRuntime,s=t.createForOfIteratorHelperLoose},function(t){l=t.cclegacy,c=t._decorator,h=t.Camera,u=t.Node,p=t.sp,d=t.Material,f=t.geometry,m=t.SkeletalAnimation,_=t.Animation,g=t.v3,b=t.Vec3,v=t.Quat,y=t.Tween,T=t.tween,k=t.Vec2,w=t.PhysicsSystem,D=t.isValid},function(t){S=t.CardState,E=t.UiAction,R=t.DeskState,A=t.DataBroadcastRequestSchema,C=t.DataBroadcastResponseSchema},function(t){O=t.cat},null,function(t){z=t.default},null,function(t){P=t.Tooth},function(t){x=t.BaseComponent},function(t){B=t.create},null,function(t){U=t.reflect},null,null,null,null,null,null,function(t){M=t.buttonLock,N=t.watchUser},null,function(t){L=t.CardDistribution},function(t){I=t.GameEventConstant},function(t){F=t.AudioEffectConstant},function(t){H=t.ClickToothType}],execute:function(){var j,V,K,Z,G,W,J,q,Q,X,Y,$,tt,et,ot,at,nt,it,rt,st,lt,ct,ht,ut,pt,dt,ft,mt,_t;l._RF.push({},"df21abiO6xMA79NvC6JdKvu","Shark",void 0);var gt=c.ccclass,bt=c.property,vt=t("ZoomState",function(t){return t[t.FAR=0]="FAR",t[t.NEAR=1]="NEAR",t}({})),yt={0:160,1:152,2:146,3:138,4:130,5:120,6:110,7:100,8:95,9:85,10:80,11:72,12:62,13:52,14:42,15:36,16:28,17:20,18:160,19:152,20:146,21:138,22:130,23:120,24:110,25:100,26:90,27:80,28:70,29:60,30:50,31:40,32:30,33:20};t("Shark",(j=gt("Shark"),V=bt({type:h,tooltip:"3D摄像机"}),K=bt({type:h,tooltip:"2D摄像机"}),Z=bt({type:u,tooltip:"基准2D节点"}),G=bt({type:u,tooltip:"鲨鱼3D信息节点"}),W=bt({type:u,tooltip:"鲨鱼模型(旋转)节点"}),J=bt({type:u,tooltip:"(咬合动效)鲨鱼模型节点"}),q=bt({type:u,tooltip:"(牙齿交互)鲨鱼模型节点"}),Q=bt({type:u,tooltip:"鲨鱼咬人动画容器节点"}),X=bt({type:p.Skeleton,tooltip:"鲨鱼咬人骨骼动画节点"}),Y=bt({type:[u]}),$=bt({type:[u]}),tt=bt({type:[d]}),et=M(.2),ot=N(),j((it=e((nt=function(t){function e(){for(var e,o=arguments.length,i=new Array(o),r=0;r<o;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i))||this,a(e,"camera3D",it,n(e)),a(e,"camera2D",rt,n(e)),a(e,"base2D",st,n(e)),a(e,"shark3D",lt,n(e)),a(e,"shark_ratate",ct,n(e)),a(e,"shark_bite",ht,n(e)),a(e,"shark_teeth",ut,n(e)),a(e,"bite_tween_node",pt,n(e)),a(e,"bite_skeleton_node",dt,n(e)),a(e,"top_teeth",ft,n(e)),a(e,"bot_teeth",mt,n(e)),a(e,"colorMaterials",_t,n(e)),e.camera3DState=vt.NEAR,e.ray=new f.Ray,e.props={card_state:[]},e.validTeeth=[],e.invalidTeeth=[],e.safeTop=0,e.safeBottom=0,e}o(e,t);var l=e.prototype;return l.onLoad=function(){var t=O.util.stringUtil.getURLParameters(decodeURIComponent(window.location.href));this.safeTop=Number(t.safe_top||0),this.safeBottom=Number(t.safe_bottom||0)},l.initTooth=function(t){var e=this;if(t){this.validTeeth=[],this.invalidTeeth=[],z.game.player_teeth=t;var o=L[z.game.battle.teams]||t.length/2,a=t.slice(0,o),n=t.slice(o);this.topValidTooth(a),this.botValidTooth(n),this.validTeeth.forEach((function(o,a){var n;null==(n=o.getComponent(P))||n.setOptions({props:{state:t[a],index:a,colorMaterials:e.colorMaterials}})}))}},l.topValidTooth=function(t){var e=this,o=this.top_teeth.length-t.length,a=this.top_teeth;o%2!=0&&(a=a.slice(1),this.invalidTeeth.push(this.top_teeth[0]),o-=1);var n,i=o/2;o>0&&((n=this.invalidTeeth).push.apply(n,[].concat(a.slice(0,i),a.slice(-i))),this.invalidTeeth.forEach((function(t){var o;null==(o=t.getComponent(P))||o.setUpdateProps({state:S.USED,colorMaterials:e.colorMaterials})})));this.top_teeth.forEach((function(t){e.invalidTeeth.includes(t)||(window.ccLog("有效"),e.validTeeth.push(t))}))},l.botValidTooth=function(t){var e=this,o=this.bot_teeth.length-t.length,a=this.bot_teeth;o%2!=0&&(a=a.slice(1),this.invalidTeeth.push(this.bot_teeth[0]),o-=1);var n,i=o/2;o>0&&((n=this.invalidTeeth).push.apply(n,[].concat(a.slice(0,i),a.slice(-i))),this.invalidTeeth.forEach((function(t){var o;null==(o=t.getComponent(P))||o.setUpdateProps({state:S.USED,colorMaterials:e.colorMaterials})})));this.bot_teeth.forEach((function(t){e.invalidTeeth.includes(t)||e.validTeeth.push(t)}))},l.onEventListener=function(){O.event.on(I.RATATE_SHARK,this.rotationShark,this).on(I.CLICK_TOOTH,this.clickTooth,this).on(I.SHARK_ZOOM,this.onSharkZoomHandler,this).on(I.SET_SHARK_ROTATE_CENTER,this.setSharkRotateCenter,this).on("EVENT_DATA_BROADCAST",this.onDataBroadcastHandler,this).on("EVENT_UI_BROADCAST",this.onEventUIBroadcast,this)},l.onEventUIBroadcast=function(t){var e=this;switch(t.action){case E.DRAW:case E.SCOUT:return this.scheduleOnce((function(){e.onAiClickToothPre(t.cardIndices)}),1)}},l.onAutoObserver=function(){var t=this;this.addAutorun([function(){var e=z.game.roomData,o=e.state,a=e.cursor,n=z.game.lastRoomData,i=n.state,r=n.cursor;i===R.DESK_STATE_REMOVE_LANDMINE&&o===R.DESK_STATE_DRAW_OR_POST&&a===z.user.userIndex&&r===z.user.userIndex&&(z.game.lastRoomData=z.game.roomData,t.zoomFarCamera())}]),this.addReaction((function(){return t.props.card_state}),(function(e){t.validTeeth.forEach((function(o,a){var n;null==(n=o.getComponent(P))||n.setUpdateProps({state:e[a],colorMaterials:t.colorMaterials})}))}),{fireImmediately:!0}).addReaction((function(){return z.game.is_bite}),(function(e){if(t.shark_teeth.active=!(t.shark_bite.active=e),window.ccLog("-------------is_bite",e),e){t.playBiteTwween();var o=t.shark_bite.getComponent(m),a=o.clips[0];a&&(O.audio.playEffect(F.BITE),o.getState(a.name).wrapMode=1,o.on(_.EventType.FINISHED,t.onAnimationFinished,t),o.getState(a.name).play())}}),{fireImmediately:!0}),this.addAutorun([function(){var e,o,a=z.game.roomData,n=a.state,i=a.cursor;n===R.DESK_STATE_DRAW_OR_POST&&i!==(null==(e=z.game.getUserPlayer)?void 0:e.relBattleOffset)&&(window.ccLog("------收回鲨鱼"),t.zoomFarCamera()),n===R.DESK_STATE_REMOVE_LANDMINE&&i===(null==(o=z.game.getUserPlayer)?void 0:o.relBattleOffset)&&t.zoomNearCamera()}])},l.playBiteTwween=function(){var t=this;this.bite_tween_node.setPosition(this.camera3D.convertToUINode(this.bot_teeth[8].getWorldPosition(),this.bite_tween_node.parent));this.bite_skeleton_node.setCompleteListener((function(){D(t.bite_skeleton_node.node)&&(t.bite_skeleton_node.node.active=!1)})),this.bite_skeleton_node.node.active=!0,this.bite_skeleton_node.setAnimation(0,"idle",!1)},l.zoomNearCamera=function(){var t=g(this.base2D.worldPosition.x,this.base2D.worldPosition.y,0),e=g();this.camera2D.camera.update(!0),this.camera2D.camera.worldToScreen(e,t),this.camera3D.camera.update(!0);var o=this.camera3D.camera.screenPointToRay(new f.Ray,e.x,e.y),a=new b;o.computeHit(a,7.6),this.camera3D.priority=100;var n=g(0,a.y,a.z),i=v.fromEuler(new v,0,1.3,0);y.stopAllByTarget(this.shark3D),T(this.shark3D).to(.2,{position:n,rotation:i}).call((function(){})).start()},l.zoomFarCamera=function(){var t=this,e=g(-.7,.6,-14.2),o=v.fromEuler(new v,22,0,0);y.stopAllByTarget(this.shark3D),T(this.shark3D).to(.2,{position:e,rotation:o}).call((function(){z.game.is_bite=!1,t.camera3D.priority=1})).start()},l.onDataBroadcastHandler=function(t){var e,o,a=JSON.parse(t.data),n=a.rotate,i=a.selected,r=a.index;if(n&&r!==(null==(e=z.game.getUserPlayer)?void 0:e.relBattleOffset)){var s=n.x,l=n.y,c=n.z,h=n.w;this.shark_ratate.setRotation(new v(s,l,c,h))}i&&r!==(null==(o=z.game.getUserPlayer)?void 0:o.relBattleOffset)&&[].concat(this.top_teeth,this.bot_teeth).forEach((function(t,e){var o,a;i.includes(e)?null==(o=t.getComponent(P))||o.selected():null==(a=t.getComponent(P))||a.unselected()}))},l.rotationShark=function(t){void 0===t&&(t=k.ZERO);var e=Math.abs(t.x)>Math.abs(t.y)?g(0,t.x>=0?1:-1,0):g(0,0,t.y<0?1:-1),o=.01*t.length(),a=this.shark_ratate.getRotation(),n=new v;v.rotateAroundLocal(n,a,e,o);var i=g();v.toEuler(i,n),i.z<-10?i.z=-10:i.z>15&&(i.z=15),i.y>0&&i.y<175&&this.setSharkRotate(i)},l.onSharkZoomHandler=function(t){window.ccLog("onSharkZoomHandler：",t),t===vt.FAR?this.zoomFarCamera():this.zoomNearCamera()},l.clickTooth=function(t){if(this.camera3D.screenPointToRay(t.getLocationX(),t.getLocationY(),this.ray),w.instance.sweepSphereClosest(this.ray,.001)){var e=w.instance.sweepCastClosestResult;if(window.ccLog("--------------射线检测",e.collider.node.name),!z.game.selected_tooth.includes(e.collider.node)){var o=e.collider.node.getComponent(P);o&&![S.USED].includes(o.props.state)&&(O.audio.playEffect(F.PRESS_TOOTH),z.game.selected_tooth.push(e.collider.node))}switch(z.game.click_tooth_type){case H.DRAW:z.game.selected_tooth=z.game.selected_tooth.slice(-z.game.getPlayerDrawCount);break;case H.SCOUT:z.game.selected_tooth=z.game.selected_tooth.slice(-2);break;case H.CLAMP:z.game.selected_tooth=z.game.selected_tooth.slice(-1)}var a,n=[];if([].concat(this.top_teeth,this.bot_teeth).forEach((function(t,e){var o,a;z.game.selected_tooth.includes(t)?(null==(o=t.getComponent(P))||o.selected(),window.ccLog("selected",t.name),n.push(e)):null==(a=t.getComponent(P))||a.unselected()})),n&&z.game.is_allow_click_tooth)O.ws.Request("DataBroadcast",U(A,B(A,{data:JSON.stringify({selected:n,index:null==(a=z.game.getUserPlayer)?void 0:a.relBattleOffset})})),C)}else console.log("raycast does not hit the target node !")},l.playBite=function(){z.game.is_bite=!0},l.onAnimationFinished=function(){var t=this;this.scheduleOnce((function(){1==t.camera3D.priority?z.game.is_bite=!1:t.zoomFarCamera()}),1)},l.setSharkRotateCenter=function(){this.setSharkRotate(g(0,90,-1.5))},l.setSharkRotate=function(t){var e;void 0===t&&(t=b.ZERO);var o=v.fromEuler(new v,0,t.y,t.z);this.shark_ratate.setRotation(o),O.ws.Request("DataBroadcast",U(A,B(A,{data:JSON.stringify({rotate:o,index:null==(e=z.game.getUserPlayer)?void 0:e.relBattleOffset})})),C)},l.onAiClickToothPre=function(){var t=i(r().mark((function t(e){var o,a,n;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:o=s(e);case 1:if((a=o()).done){t.next=9;break}return n=a.value,t.next=5,this.mockSharkRotate(n);case 5:return t.next=7,this.mockToothSelected(n);case 7:t.next=1;break;case 9:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),l.mockSharkRotate=function(t,e){var o=this;void 0===e&&(e=.3);var a=this.shark_ratate.getRotation(),n=g();v.toEuler(n,a);var i=n.y,r=[].concat(this.top_teeth,this.bot_teeth).indexOf(this.validTeeth[t]),s=yt[r];v.fromEuler(new v,n.x,s,n.z);return new Promise((function(t){T(o.shark_ratate).set({eulerAngles:g(n.x,i,n.z)}).to(e,{eulerAngles:g(n.x,s,n.z)},{progress:function(t,e,o,a){return t+(e-t)*a}}).call((function(){t()})).start()}))},l.mockToothSelected=function(t,e){var o=this;return void 0===e&&(e=.2),new Promise((function(a){o.scheduleOnce((function(){o.validTeeth.forEach((function(e,o){var a;t===o&&(null==(a=e.getComponent(P))||a.selected())})),a()}),e)}))},e}(x)).prototype,"camera3D",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),rt=e(nt.prototype,"camera2D",[K],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),st=e(nt.prototype,"base2D",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),lt=e(nt.prototype,"shark3D",[G],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ct=e(nt.prototype,"shark_ratate",[W],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ht=e(nt.prototype,"shark_bite",[J],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ut=e(nt.prototype,"shark_teeth",[q],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),pt=e(nt.prototype,"bite_tween_node",[Q],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),dt=e(nt.prototype,"bite_skeleton_node",[X],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ft=e(nt.prototype,"top_teeth",[Y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),mt=e(nt.prototype,"bot_teeth",[$],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),_t=e(nt.prototype,"colorMaterials",[tt],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),e(nt.prototype,"clickTooth",[et],Object.getOwnPropertyDescriptor(nt.prototype,"clickTooth"),nt.prototype),e(nt.prototype,"setSharkRotate",[ot],Object.getOwnPropertyDescriptor(nt.prototype,"setSharkRotate"),nt.prototype),at=nt))||at));l._RF.pop()}}}));

System.register("chunks:///_virtual/SharkMove.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index21.ts","./index40.ts","./GameEventConstant.b.ts"],(function(n){var t,e,o,s,c,a,i,r;return{setters:[function(n){t=n.inheritsLoose},function(n){e=n.cclegacy,o=n._decorator,s=n.Node,c=n.Vec2},function(n){a=n.BaseComponent},function(n){i=n.cat},null,function(n){r=n.GameEventConstant}],execute:function(){var u;e._RF.push({},"d067brVOgVCCbVBs2XCJStv","SharkMove",void 0);var h=o.ccclass;o.property,n("SharkMove",h("SharkMove")(u=function(n){function e(){return n.apply(this,arguments)||this}t(e,n);var o=e.prototype;return o.onLoad=function(){this.node.on(s.EventType.TOUCH_MOVE,this.onTouchMoveHandler,this),this.node.on(s.EventType.TOUCH_END,this.onTouchEndHandler,this),this.node.on(s.EventType.TOUCH_CANCEL,this.onTouchEndHandler,this)},o.onTouchMoveHandler=function(n){var t=n.getDelta();i.event.dispatchEvent(r.RATATE_SHARK,t)},o.onTouchEndHandler=function(n){var t=n.getDelta(),e=n.touch;c.equals(c.ZERO,t)&&(i.event.dispatchEvent(r.CLICK_TOOTH,e),window.ccLog("-----点击了"))},e}(a))||u);e._RF.pop()}}}));

System.register("chunks:///_virtual/ShowLoading.ts",["./rollupPluginModLoBabelHelpers.js","cc","./RootUILayer.ts"],(function(t){var e,o,i,n,r,a,l,u,s,c;return{setters:[function(t){e=t.applyDecoratedDescriptor,o=t.inheritsLoose,i=t.initializerDefineProperty,n=t.assertThisInitialized},function(t){r=t.cclegacy,a=t._decorator,l=t.Label,u=t.Node,s=t.BlockInputEvents},function(t){c=t.default}],execute:function(){var p,d,g,h,f,v,y;r._RF.push({},"27960MiunNM/YnOymX0hNhP","ShowLoading",void 0);var b=a.ccclass,w=a.property;t("ShowLoading",(p=b("ShowLoading"),d=w({type:l,tooltip:"标题"}),g=w({type:u,tooltip:"动画"}),p((v=e((f=function(t){function e(){for(var e,o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return e=t.call.apply(t,[this].concat(r))||this,i(e,"title",v,n(e)),i(e,"loadingTween",y,n(e)),e.loading_rotate=0,e.props={title:"",mask:!0,black:!0},e}o(e,t);var r=e.prototype;return r.onLoad=function(){},r.onAutoObserver=function(){var t=this;this.addAutorun([function(){var e,o;null!=(e=t.props)&&e.title?(t.title.string=""+(null==(o=t.props)?void 0:o.title),t.title.node.active=!0):t.title.node.active=!1},function(){var e;t.getComponent(s).enabled=!(null==(e=t.props)||!e.mask)}])},r.update=function(t){this.loading_rotate+=220*t,this.loadingTween.setRotationFromEuler(0,0,-this.loading_rotate%360),this.loading_rotate>360&&(this.loading_rotate-=360)},e}(c)).prototype,"title",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),y=e(f.prototype,"loadingTween",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),h=f))||h));r._RF.pop()}}}));

System.register("chunks:///_virtual/StartScene.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts","./InitialScene.ts"],(function(t){var e,n,o,c,r;return{setters:[function(t){e=t.inheritsLoose},function(t){n=t.cclegacy,o=t._decorator},function(t){c=t.cat},function(t){r=t.default}],execute:function(){var i;n._RF.push({},"06da3fdwBtNEblTLu0iQXd2","StartScene",void 0);var a=o.ccclass;o.property,t("StartScene",a("StartScene")(i=function(t){function n(){return t.apply(this,arguments)||this}return e(n,t),n.prototype.onLoaded=function(){console.log("loading"),c.gui.loadScene("loading")},n}(r))||i);n._RF.pop()}}}));

System.register("chunks:///_virtual/StopServiceNotice.ts",["cc","./index23.ts","./index21.ts"],(function(e){var t,o,c,i;return{setters:[function(e){t=e.error,o=e.cclegacy},function(e){c=e.default},function(e){i=e.cat}],execute:function(){e("default",(function(){var e=c.global,o=e.social_game_feature,n=e.social_game_id;i.res.loadRemote(i.env.stop_service_url+"/social_games/stop_service/admin/"+o+"-"+n+".json?"+Date.now(),{ext:".json"},(function(e,o){if(e)return t(e);if(o.json){var c=o.json;i.gui.showNotice({text:c.notice,confrim:function(){window.ccLog("获取停服公告退出"),i.platform.back()}})}}))})),o._RF.push({},"15fcdJXVhJLy7AgHQw6G6h9","StopServiceNotice",void 0),o._RF.pop()}}}));

System.register("chunks:///_virtual/StringUtil.ts",["cc"],(function(t){var n;return{setters:[function(t){n=t.cclegacy}],execute:function(){n._RF.push({},"9e3ec0m/nlLh4ZPjtxMUJAw","StringUtil",void 0);t("guid",(function(){for(var t="",n=1;n<=32;n++){t+=Math.floor(16*Math.random()).toString(16),8!=n&&12!=n&&16!=n&&20!=n||(t+="-")}return t})),t("sub",(function(t,n,r){void 0===r&&(r=!0),t=decodeURIComponent(t);for(var e=Array.from(t),o=0,i="",c=0;c<e.length;c++){var u=e[c],a=u.codePointAt(0);if((o+=a?a>=19968&&a<=40959||a>=13312&&a<=19903?2:a>=65&&a<=90||a>=97&&a<=122||a>=48&&a<=57?1:a>65535?2:Array.from(u).length:Array.from(u).length)>n){e[c]&&r&&(i+="...");break}i+=u}return i})),t("stringLen",(function(t){for(var n=0,r=t.length,e=-1,o=0;o<r;o++)n+=(e=t.charCodeAt(o))>=0&&e<=128?1:2;return n})),t("getURLParameters",(function(t){var n={};return new URL(t).searchParams.forEach((function(t,r){n[r]=t})),n})),t("processHtmlString",(function(t,n,r){void 0===r&&(r="s");var e,o=(e=/(<\/?[^>]+>)/g,t.split(e).map((function(t){return t.match(e)?{type:"html",content:t}:{type:"text",content:t}}))),i="";return o.forEach((function(t){"text"===t.type?i+=function(t,n,r){var e=1638.4/r;if(t.length>e){var o=new RegExp(".{1,"+Math.floor(e)+"}","g"),i=t.match(o)||[];return i.map((function(t,r){return 0===r||r===i.length-1?t:"<"+n+">"+t+"</"+n+">"})).join("")}return t}(t.content,r,n):i+=t.content})),i})),t("safeStringify",(function(t,n){try{return JSON.stringify(t,(function(t,n){return"bigint"==typeof n?n.toString():n}),n)}catch(t){return"[Circular Reference]"}}));n._RF.pop()}}}));

System.register("chunks:///_virtual/SuileyooSign.ts",["cc","./index2.js","./index21.ts"],(function(e){var n,t,c;return{setters:[function(e){n=e.cclegacy},function(e){t=e.default},function(e){c=e.cat}],execute:function(){n._RF.push({},"9422bNbzXJEUpMx1zq2gB9p","SuileyooSign",void 0);e("attachSign",(function(e,n,t){return void 0===e&&(e={}),void 0===n&&(n=[]),void 0===t&&(t=[]),e.app_id="996865",e.access_token="6f3f6cf78c0bc7e8f6d84c2909247bc1",e.nonce=function(e){void 0===e&&(e=8);for(var n="",t=e,c=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],i=0;i<t;i++)n+=c[Math.round(Math.random()*(c.length-1))];return n}(),e.timestamp=(new Date).getTime().toString(),e.supportPk="2",e.convertMode="0",e.client_type="android",t.length>0&&t.push("app_id","nonce","timestamp"),e.sign=i(e,{key:c.env.secret,excludes:n.concat(["r"]),includes:t}),e}));var i=function(e,n){void 0===n&&(n={}),n=Object.assign({key:"",excludes:[],includes:[]},n);var c={};for(var i in e)n.excludes.includes(i)||(0==n.includes.length||n.includes.includes(i))&&(c[i]=e[i]);var r=o(c);return t.MD5(r+n.key).toString()},o=function(e){for(var n=Object.keys(e).sort(),t={},c=0;c<n.length;c++)t[n[c]]=e[n[c]];return Object.keys(t).map((function(e){return e+"="+t[e]})).join("&")};n._RF.pop()}}}));

System.register("chunks:///_virtual/SuiLeYooSocket.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index4.js","./create.js","./descriptors.js","./reflect.js","./descriptor_pb.js","./from-binary.js","./to-binary.js","./type_pb.js","./empty_pb.js","./struct_pb.js","./plugin_pb.js","./binary-encoding.js","./size-delimited.js","./index21.ts","./message_pb.ts","./authorization_pb.ts","./notification_pb.ts","./index7.js","./index.mjs_cjs=&original=3.js"],(function(e){var t,n,c,o,i,s,r,a,u,l,h,d,f,p,g,b,k;return{setters:[function(e){t=e.inheritsLoose,n=e.createClass},function(e){c=e.cclegacy,o=e.warn,i=e.error},null,function(e){s=e.create},null,function(e){r=e.reflect},null,function(e){a=e.fromBinary},function(e){u=e.toBinary},null,function(e){l=e.EmptySchema},null,null,null,null,function(e){h=e.cat},function(e){d=e.Message_Type,f=e.MessageSchema},function(e){p=e.AuthorizationNotification_Status,g=e.AuthorizationNotificationSchema},function(e){b=e.NotificationSchema},function(e){k=e.default},null],execute:function(){c._RF.push({},"2a668BOoE5HHbpMcWkASSpZ","SuiLeYooSocket",void 0);e("SuiLeYooSocket",function(e){function c(t,n){var c;return void 0===n&&(n=""),(c=e.call(this)||this).socket=void 0,c.url="",c.port="",c.reconnectMaxTimes=3,c.reconnectTimeGap=3,c.reconnectCurTime=0,c.reconnectCurTimes=0,c.heartBeatTime=0,c.heartBeatReconnectTime=30,c.heartBeatIntervalID=0,c.reconnectIntervalID=null,c.isActiveClose=!1,c.connectSuccessCB=void 0,c.url=t,c.port=n,c.resetReconnectConfig(),c}t(c,e);var k=c.prototype;return k.resetReconnectConfig=function(){this.reconnectMaxTimes=3,this.reconnectTimeGap=3,this.reconnectCurTime=0,this.reconnectCurTimes=0,this.heartBeatTime=0},k.connect=function(){var e=this;return console.log("连接随乐游 ws",this.url),this.socket=new WebSocket(this.url),this.socket.binaryType="arraybuffer",new Promise((function(t,n){e.connectSuccessCB=function(){e.isReConnecting&&(h.gui.showToast({title:"重连成功"}),h.gui.reloadScene()),console.log("随乐游ws 连接成功"),t(e)},e.socket.onmessage=e.onReceiveMessage.bind(e),e.socket.onclose=e.onSocketClose.bind(e),e.socket.onerror=e.onSocketError.bind(e),e.socket.onopen=e.onSocketOpen.bind(e)}))},k.onSocketOpen=function(e){var t;window.ccLog("socket onopen",e),null==(t=this.connectSuccessCB)||t.call(this),this.setHeartbeat(),this.resetReconnectConfig()},k.onSocketClose=function(e){var t;o("socket close",e),null==(t=this.socket)||t.close(),this.startReconnect()},k.onSocketError=function(e){i("socket close",e)},k.onReceiveMessage=function(e){var t=e.data,n=t instanceof ArrayBuffer?new Uint8Array(t):t,c=a(f,n),o=c.type,i=c.content;switch(window.ccLog("%c 随乐游ws响应消息:"+ +Date.now()+" %c type:"+o+" %c","background:#E748CE ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff","background:#E71172 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff","background:transparent",c),o){case d.PONG:this.resetHeartbeat();break;case d.AUTHORIZATION_NOTIFICATION:var s=a(g,i),r=s.status,u=s.data;window.ccLog("AUTHORIZATION_NOTIFICATION",u,r),p.SUCCESS;break;case d.PARTY_GAME_NOTIFICATION:var l=a(b,i),k=l.Code;l.Data,l.Content;window.ccLog("PARTY_GAME_NOTIFICATION",l),h.event.dispatchEvent(k)}},k.sendMsg=function(e,t){var n;if((null==(n=this.socket)?void 0:n.readyState)===WebSocket.OPEN){var c=u(f,s(f,{type:e,content:t}));this.socket.send(c)}else{var o,i;console.warn("socket is not open: readyState "+(null!=(o=null==(i=this.socket)?void 0:i.readyState)?o:-1))}},k.request=function(e,t){this.sendMsg(e,u(t.desc,t.message)),window.ccLog("%c 随乐游ws请求消息:"+ +Date.now()+" %c type:"+e+" %c","background:#AA48CE ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff","background:#AA1172 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff","background:transparent",t)},k.setHeartbeat=function(){return this.clearHeartbeat().clearReconnect(),this.heartBeatIntervalID=setInterval(this.updateHeartbeat.bind(this),1e3),this},k.clearHeartbeat=function(){return this.resetHeartbeat(),clearInterval(this.heartBeatIntervalID),this},k.resetHeartbeat=function(){this.heartBeatTime=0},k.updateHeartbeat=function(){this.heartBeatTime>this.heartBeatReconnectTime?this.startReconnect():(this.heartBeatTime==this.heartBeatReconnectTime&&this.request(d.PING,r(l)),this.heartBeatTime++)},k.onReconnectUpdate=function(){this.reconnectCurTimes>=this.reconnectMaxTimes?this.clearReconnect():this.reconnectCurTime>this.reconnectTimeGap?(this.reconnectCurTimes++,this.reconnectCurTime=0,this.connect()):this.reconnectCurTime++},k.startReconnect=function(){var e=this;this.clearReconnect().clearHeartbeat().resetReconnectConfig(),this.isActiveClose||this.isReConnecting||(window.ccLog("正在重连随乐游"),h.gui.showToast({title:"网络已断开,正在重连"}),this.reconnectIntervalID=setInterval((function(){e.onReconnectUpdate()}),1e3))},k.clearReconnect=function(){return clearInterval(this.reconnectIntervalID),this.reconnectIntervalID=null,this},k.destroy=function(){var e;(this.isActiveClose=!0,window.ccLog("销毁suileyoo ws"),this.socket)&&(null==(e=this.socket)||e.close(),this.socket=null);this.clearReconnect().clearHeartbeat().resetReconnectConfig()},n(c,[{key:"isSocketConnected",get:function(){var e;return(null==(e=this.socket)?void 0:e.readyState)===WebSocket.OPEN}},{key:"isReConnecting",get:function(){return null!==this.reconnectIntervalID}}]),c}(k.Emitter));c._RF.pop()}}}));

System.register("chunks:///_virtual/SuileyooWS.ts",["./rollupPluginModLoBabelHelpers.js","cc","./SuiLeYooSocket.ts","./index21.ts","./BaseWebSocket.ts"],(function(e){var t,r,n,o,i,c,s;return{setters:[function(e){t=e.inheritsLoose,r=e.asyncToGenerator,n=e.regeneratorRuntime},function(e){o=e.cclegacy,i=e.error},function(e){c=e.SuiLeYooSocket},null,function(e){s=e.BaseWebSocket}],execute:function(){o._RF.push({},"dc787ISrcBOt7zT6XRr3rKx","SuileyooWS",void 0);var u=e("SocialGameSuiLeYoosSocket",function(e){function o(t){var o;return(o=e.call(this,t)||this).create=r(n().mark((function e(t){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o.ins){e.next=2;break}return e.abrupt("return",console.error("[SuiLeYooSocket] is alreay exist! please invoke destroy first"));case 2:return o.ins=new c(t),e.next=5,o.ins.connect();case 5:case"end":return e.stop()}}),e)}))),o}return t(o,e),o}(s)),a=(e("createProxySuileyooSocket",(function(e){var t=new u(e);return new Proxy(t,a)})),{get:function(e,t,r){if(e.hasOwnProperty(t))return Reflect.get(e,t,r);if(e.ins)return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return Reflect.get(e.ins,t,r).apply(e.ins,o)};var n=new Error("[suileyoo ws] is not available, please invoke [create]");throw i(n),n},set:function(e,t,r){return"string"==typeof t&&(e[t]=r,window.ccLog("Setting property "+t+" to "+r),!0)}});o._RF.pop()}}}));

System.register("chunks:///_virtual/Switch.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts"],(function(t){var i,n,e,o,r,a,c,s,l,u;return{setters:[function(t){i=t.applyDecoratedDescriptor,n=t.inheritsLoose,e=t.initializerDefineProperty,o=t.assertThisInitialized},function(t){r=t.cclegacy,a=t._decorator,c=t.SpriteFrame,s=t.Sprite,l=t.Button},function(t){u=t.BaseComponent}],execute:function(){var p,f,h,d,y,m,v;r._RF.push({},"5067amNm3FNiYIBMExCEUuq","Switch",void 0);var w=a.ccclass,S=a.property;t("Switch",(p=w("Switch"),f=S({type:c,tooltip:"开"}),h=S({type:c,tooltip:"关"}),p((m=i((y=function(t){function i(){for(var i,n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];return i=t.call.apply(t,[this].concat(r))||this,e(i,"on",m,o(i)),e(i,"off",v,o(i)),i.data={is_on:!1},i}n(i,t);var r=i.prototype;return r.onAutoObserver=function(){var t=this;this.addAutorun([function(){t.getComponent(s).spriteFrame=t.data.is_on?t.on:t.off}])},r.initUI=function(){this.node.on(l.EventType.CLICK,this.onSwitchHandler,this)},r.onSwitchHandler=function(){this.data.is_on=!this.data.is_on},i}(u)).prototype,"on",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),v=i(y.prototype,"off",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),d=y))||d));r._RF.pop()}}}));

System.register("chunks:///_virtual/task_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./file.js","./message.js"],(function(c){var s,e,n;return{setters:[function(c){s=c.cclegacy},null,null,null,function(c){e=c.fileDesc},function(c){n=c.messageDesc}],execute:function(){s._RF.push({},"ed61bPujRRM2aGVfLBSdVPI","task_pb",void 0);var t=c("file_task",e("Cgp0YXNrLnByb3RvEgZwcm90b3MiSAoVVGFza0F3YXJkTm90aWZpY2F0aW9uEhIKCnRhc2tfY2xhc3MYASABKAkSDAoEdGlwcxgCIAEoCRINCgV2YWx1ZRgDIAEoBEIwCiRjb20uc3RudHMuY2xvdWQuc3VpbGV5b28uZWNoby5wcm90b3NaCC4vcHJvdG9zYgZwcm90bzM"));c("TaskAwardNotificationSchema",n(t,0));s._RF.pop()}}}));

System.register("chunks:///_virtual/TaskEventConstant.ts",["cc"],(function(t){var n;return{setters:[function(t){n=t.cclegacy}],execute:function(){n._RF.push({},"61201gCtNNKUKGXzeJwg30h","TaskEventConstant",void 0);t("TaskEventConstant",function(t){return t.CLOSE_TASK="TaskEventConstant/CLOSE_TASK",t.UPDATE_TASKLIST="TaskEventConstant/UPDATE_TASKLIST",t.SHOW_REWARD="TaskEventConstant/SHOW_REWARD",t}({}));n._RF.pop()}}}));

System.register("chunks:///_virtual/ThrowMine.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index40.ts","./index21.ts","./create.js","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./index23.ts","./msg_pb.ts","./AudioEffectConstant.b.ts"],(function(e){var n,t,i,o,r,s,a,c,u,l,p,_,f,d,h,m,w;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,i=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){r=e.cclegacy,s=e._decorator,a=e.Node,c=e.Label,u=e.sp,l=e.isValid},function(e){p=e.BaseComponent},null,function(e){_=e.cat},function(e){f=e.create},null,null,null,null,null,null,null,function(e){d=e.default},function(e){h=e.DeskState,m=e.PlayerSchema},function(e){w=e.AudioEffectConstant}],execute:function(){var v,y,b,g,T,M,S,j,A,C,D;r._RF.push({},"3c32ccUAPJMGZXtGzvGvxYD","ThrowMine",void 0);var z=s.ccclass,E=s.property;e("ThrowMine",(v=z("ThrowMine"),y=E({type:a,tooltip:"抛雷(甩锅)特效动画节点"}),b=E({type:a,tooltip:"抛雷(甩锅)次数节点"}),g=E({type:c,tooltip:"抛雷(甩锅)次数"}),T=E({type:u.Skeleton}),v((j=n((S=function(e){function n(){for(var n,t=arguments.length,r=new Array(t),s=0;s<t;s++)r[s]=arguments[s];return n=e.call.apply(e,[this].concat(r))||this,i(n,"spine_slot",j,o(n)),i(n,"count_throw_mine_node",A,o(n)),i(n,"count",C,o(n)),i(n,"spine_throw_mine",D,o(n)),n.props={player:f(m)},n}t(n,e);var r=n.prototype;return r.initUI=function(){this.spine_throw_mine.node.active=!1},r.onLoad=function(){},r.onAutoObserver=function(){var e=this;this.addAutorun([function(){var n=d.game.roomData,t=n.throwLandmineTotal,i=n.cursor;n.state===h.DESK_STATE_DRAW_OR_POST&&(e.props.player.index===i&&t?e.updateThrowMineCount(t):e.node.active=!1)}])},r.onDestroy=function(){},r.playThrowMineTween=function(e){var n=this;void 0===e&&(e=1),this.node.active=!0;var t=[];this.spine_slot.active=!0;for(var i=function(e){t.push(new Promise((function(t,i){n.spine_throw_mine.setCompleteListener((function(){l(n.spine_throw_mine)&&(n.spine_throw_mine.node.active=!1,window.ccLog(e+"播放结束"),t())})),n.scheduleOnce((function(){n.spine_throw_mine.node.active=!0,_.audio.playEffect(w.THROW_SUCCESS),n.spine_throw_mine.setAnimation(0,"shuaiguo2",!1)}),0+.5*e)})))},o=0;o<e;o++)i(o);Promise.all(t).then((function(){n.spine_slot.active=!1})),_.audio.playEffect(w.THROWN_BOMB)},r.updateThrowMineCount=function(e){void 0===e&&(e=0);var n=this.count_throw_mine_node.parent;this.count.string=""+e,n.active=!0},r.hide=function(){this.node.active=!1},n}(p)).prototype,"spine_slot",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),A=n(S.prototype,"count_throw_mine_node",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),C=n(S.prototype,"count",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),D=n(S.prototype,"spine_throw_mine",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=S))||M));r._RF.pop()}}}));

System.register("chunks:///_virtual/ThrowMineCardAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index40.ts","./index23.ts","./msg_pb.ts","./SelectPlayerAction.ts","./index21.ts","./AudioEffectConstant.b.ts"],(function(e){var t,n,o,r,i,a,s,c,u,l,p,d,f,y,T;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,o=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){i=e.cclegacy,a=e._decorator,s=e.sp,c=e.Node,u=e.isValid},null,function(e){l=e.default},function(e){p=e.DeskState,d=e.Card},function(e){f=e.SelectPlayerAction},function(e){y=e.cat},function(e){T=e.AudioEffectConstant}],execute:function(){var h,_,v,w,g,m,A;i._RF.push({},"b1236oAyMRLDa2gW+ViwrBy","ThrowMineCardAction",void 0);var E=a.ccclass,C=a.property;e("ThrowMineCardAction",(h=E("ThrowMineCardAction"),_=C({type:s.Skeleton,tooltip:"被抛雷-动画-节点"}),v=C({type:c,tooltip:"玩家节点"}),h((m=t((g=function(e){function t(){for(var t,n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return t=e.call.apply(e,[this].concat(i))||this,o(t,"passive_request_node",m,r(t)),o(t,"players",A,r(t)),t}n(t,e);var i=t.prototype;return i.onEventListener=function(){e.prototype.onEventListener.call(this),y.event.on("EVENT_THROW_LANDMINE_BROADCAST",this.onThrowMineBroadcastHandler,this)},i.onAutoObserver=function(){var t=this;e.prototype.onAutoObserver,this.addReaction((function(){return l.game.roomData.state}),(function(e){e===p.DESK_STATE_SELECT_TARGET&&d.THROW_LANDMINE===l.game.getLastCardOnPostZone&&(window.ccLog("抛雷指定阶段",e===p.DESK_STATE_SELECT_TARGET),y.audio.playEffect(T.THROW_BOMB),t.onStateGameSelectTarget(l.game.roomData))}))},i.checkValidPlayer=function(){return l.game.roomData.players.filter((function(e){var t=e.die,n=e.nickname;return window.ccLog("抛雷:",n,t),!t}))},i.onThrowMineBroadcastHandler=function(e){window.ccLog("抛雷");var t=l.game.getPlayerComponentByIndex(this.players,e.responseIndex);if(e.responseIndex==l.user.userIndex)e.requestIndex!=l.user.userIndex&&this.playTurnTween(),t.throw_mine.playThrowMineTween(e.repeat+1);else{var n=l.game.getPlayerComponentByIndex(this.players,e.requestIndex);this.selectTargetTween(n.node,t.node),t.throw_mine.playThrowMineTween(e.repeat+1)}},i.playTurnTween=function(){var e=this;this.passive_request_node.setCompleteListener((function(){u(e.passive_request_node.node)&&(e.passive_request_node.node.active=!1)})),this.passive_request_node.node.active=!0,this.passive_request_node.setAnimation(0,"shuaiguo",!1)},t}(f)).prototype,"passive_request_node",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=t(g.prototype,"players",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=g))||w));i._RF.pop()}}}));

System.register("chunks:///_virtual/TimeCount.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts","./index40.ts","./BaseComponent.ts","./GameEventConstant.b.ts"],(function(t){var e,n,i,o,r,s,a,u,c,p,l;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,i=t.initializerDefineProperty,o=t.assertThisInitialized},function(t){r=t.cclegacy,s=t._decorator,a=t.ProgressBar,u=t.Label},function(t){c=t.cat},null,function(t){p=t.BaseComponent},function(t){l=t.GameEventConstant}],execute:function(){var m,f,y,g,_,h,v;r._RF.push({},"587e48tp3ZHiaFtheajyiEe","TimeCount",void 0);var d=s.ccclass,T=s.property;t("TimeCount",(m=d("TimeCount"),f=T({type:a,tooltip:"剩余时间进度"}),y=T({type:u,tooltip:"剩余时间"}),m((h=e((_=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),s=0;s<n;s++)r[s]=arguments[s];return e=t.call.apply(t,[this].concat(r))||this,i(e,"time_progres",h,o(e)),i(e,"time_count",v,o(e)),e}n(e,t);var r=e.prototype;return r.onLoad=function(){},r.onDestroy=function(){},r.onEventListener=function(){var t=this;c.event.on(l.DRAW_SCOUT_TIME_OUT,(function(e){t.updateTime(e)}),this)},r.updateTime=function(t){this.time_progres.progress=t.progress,this.time_count.string=t.time+"s"},e}(p)).prototype,"time_progres",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),v=e(_.prototype,"time_count",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),g=_))||g));r._RF.pop()}}}));

System.register("chunks:///_virtual/TimeUtils.ts",["cc"],(function(e){var t;return{setters:[function(e){t=e.cclegacy}],execute:function(){t._RF.push({},"59b78U3FQZFvqMVUkTUYNtC","TimeUtils",void 0);e("timeFormat",(function(e,t){void 0===e&&(e=0),void 0===t&&(t="hh:mm:ss");var r={hh:{regex:/hh|H+/,value:String(Math.floor(e/3600)).padStart(2,"0")},mm:{regex:/mm|M+/,value:String(Math.floor(e%3600/60)).padStart(2,"0")},ss:{regex:/ss|S+/,value:String(Math.floor(e%60)).padStart(2,"0")}},o=t;for(var n in r){var a=r[n],s=a.regex,i=a.value;o=o.replace(s,i)}return o})),e("format",(function(e,t){var r={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};for(var o in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length))),r)new RegExp("("+o+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?r[o]:("00"+r[o]).substr((""+r[o]).length)));return t})),e("getNowFormatDate",(function(){var e=new Date,t=e.getFullYear(),r=e.getMonth()+1,o=e.getDate();return r<10&&(r="0"+r),o<10&&(o="0"+o),t+"."+r+"."+o})),e("convertTimeToObject",(function(e,t){void 0===t&&(t=!0);var r=(e=t?1e3*Math.round(e/1e3):e)%36e5,o=r%6e4;return{hours:Math.floor(e/36e5),minutes:Math.floor(r/6e4),seconds:Math.floor(o/1e3),milliseconds:t?Math.round(o%1e3):o%1e3}})),e("sleep",(function(e){return void 0===e&&(e=0),new Promise((function(t){return setTimeout(t,e)}))}));t._RF.pop()}}}));

System.register("chunks:///_virtual/Toast.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts"],(function(e){var t,n,o,i,a,l,r,s,d,c,p,u,f,y,b,h;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.inheritsLoose,o=e.initializerDefineProperty,i=e.assertThisInitialized,a=e.asyncToGenerator,l=e.regeneratorRuntime},function(e){r=e.cclegacy,s=e._decorator,d=e.Node,c=e.Label,p=e.UITransform,u=e.UIOpacity,f=e.tween,y=e.Tween,b=e.v3},function(e){h=e.BaseComponent}],execute:function(){var _,g,m,v,w,x,T,D,C,F,I;r._RF.push({},"54022HS/G1NFbF0cq5UR5SF","Toast",void 0);var z=s.ccclass,A=s.property,L=e("ToastType",function(e){return e[e.FIXED=0]="FIXED",e[e.SLIDE=1]="SLIDE",e}({}));e("Toast",(_=z("Toast"),g=A({type:d,tooltip:"固定节点"}),m=A({type:d,tooltip:"滑动节点"}),v=A({type:c,tooltip:"固定标签节点"}),w=A({type:c,tooltip:"滑动标签节点"}),_((D=t((T=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),l=0;l<n;l++)a[l]=arguments[l];return t=e.call.apply(e,[this].concat(a))||this,o(t,"fixed_node",D,i(t)),o(t,"slide_node",C,i(t)),o(t,"fixed_label",F,i(t)),o(t,"slide_label",I,i(t)),t}n(t,e);var r=t.prototype;return r.onLoad=function(){this.fixed_node.active=this.slide_node.active=!1},r.start=function(){this.toast()},r.toast=function(){var e=this;return new Promise(a(l().mark((function t(n,o){var i,a,r,s,d,c,u;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:i=e.props,a=i.title,r=i.type,s=i.fixed_time,r==L.FIXED?(e.fixed_node.active=!0,e.fixed_label.string=""+a,e.fixed_label.updateRenderData(!0),d=e.fixed_label.node.getComponent(p).height,c=e.fixed_node.getComponent(p).height,d-c>0&&(e.fixed_node.getComponent(p).height=d+100),e.scheduleOnce((function(){e.node.destroy(),n()}),s)):(e.slide_node.active=!0,e.slide_label.string=""+a,e.slide_label.updateRenderData(!0),u=e.slide_label.node.getComponent(p).width,e.slide_node.getComponent(p).width-u<100&&(e.slide_node.getComponent(p).width=u+100),e.playAnim(e.node).then((function(){n()})));case 2:case"end":return t.stop()}}),t)}))))},r.playAnim=function(e){var t=this;return new Promise((function(n,o){var i=t.node.getComponent(u),a=f(i).delay(1.2).to(.5,{opacity:0}).call((function(){y.stopAllByTarget(e),t.node.destroy(),n()}));f(e).by(.5,{position:b(0,400,0)}).call((function(){a.start()})).start()}))},r.onDestroy=function(){y.stopAllByTarget(this.node),this.unscheduleAllCallbacks()},t}(h)).prototype,"fixed_node",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),C=t(T.prototype,"slide_node",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),F=t(T.prototype,"fixed_label",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),I=t(T.prototype,"slide_label",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),x=T))||x));r._RF.pop()}}}));

System.register("chunks:///_virtual/ToBeGetProfiles.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts","./index23.ts"],(function(e){var t,n,r,o,i;return{setters:[function(e){t=e.asyncToGenerator,n=e.regeneratorRuntime},function(e){r=e.cclegacy},function(e){o=e.cat},function(e){i=e.default}],execute:function(){function s(){return(s=t(n().mark((function e(t,r){var s,u;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o.gui.showLoading({title:"获取数据中"}),s=[],u=i.lobby.profiles,t.forEach((function(e){var t=Number(e[r]);u.has(t)||s.push(""+t)})),!s.length){e.next=9;break}return e.next=7,o.platform.getUserProfile({followerIds:s.join()});case 7:e.sent.forEach((function(e){e.id&&!u.has(e.id)&&u.set(e.id,e)}));case 9:o.gui.hideLoading();case 10:case"end":return e.stop()}}),e)})))).apply(this,arguments)}e("default",(function(e,t){return s.apply(this,arguments)})),r._RF.push({},"8d64dSMIspEuIx5OUAyhyhu","ToBeGetProfiles",void 0),r._RF.pop()}}}));

System.register("chunks:///_virtual/Tooth.ts",["./rollupPluginModLoBabelHelpers.js","cc","./msg_pb.ts","./index21.ts","./index40.ts","./BaseComponent.ts","./GameEventConstant.b.ts"],(function(t){var e,n,o,i,s,r,a,c,u,p,l,h,d,f,E;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,o=t.initializerDefineProperty,i=t.assertThisInitialized},function(t){s=t.cclegacy,r=t._decorator,a=t.MeshRenderer,c=t.v3,u=t.Vec3,p=t.tween,l=t.MeshCollider},function(t){h=t.CardState},function(t){d=t.cat},null,function(t){f=t.BaseComponent},function(t){E=t.GameEventConstant}],execute:function(){var v,T,_,y,g;s._RF.push({},"75ef13gqs5HSZ0wY1RexbU/","Tooth",void 0);var D=r.ccclass,b=r.property,R=function(t){return t[t.WHITE=0]="WHITE",t[t.RED=1]="RED",t[t.GREEN=2]="GREEN",t[t.BLUE=3]="BLUE",t}(R||{});t("Tooth",(v=D("Tooth"),T=b({tooltip:"是否为上排牙齿"}),v((g=e((y=function(t){function e(){for(var e,n=arguments.length,s=new Array(n),r=0;r<n;r++)s[r]=arguments[r];return e=t.call.apply(t,[this].concat(s))||this,o(e,"is_top",g,i(e)),e.is_hidden=!1,e.props={state:h.UNSPECIFIED,index:-1,colorMaterials:[]},e}n(e,t);var s=e.prototype;return s.onLoad=function(){},s.onDestroy=function(){},s.start=function(){},s.onEventListener=function(){var t=this;d.event.on(E.TAG_TOOTH_STATE,(function(e){var n=e.index,o=e.state;t.props.index===n&&(window.ccLog("TAG_TOOTH_STATE--------",{index:n,state:o}),t.props.state=o)}),this)},s.onAutoObserver=function(){var t=this;this.addAutorun([function(){t.updateUI()}])},s.selected=function(){this.setMaterial(R.BLUE)},s.unselected=function(){this.updateUI()},s.updateUI=function(){var t=R.WHITE;switch(this.props.state){case h.USED:this.hide();break;case h.UNUSED:break;case h.BAD:t=R.RED;break;case h.GOOD:t=R.GREEN}this.setMaterial(t)},s.setMaterial=function(t){var e=this.getComponent(a);this.props.colorMaterials[t]&&(null==e||e.setMaterialInstance(this.props.colorMaterials[t],0))},s.hide=function(){var t;if(!this.is_hidden){this.is_hidden=!0;var e=c(),n=c(0,.1*(this.is_top?1:-1),0),o=this.node.getRotation(),i=this.node.getPosition();u.transformQuat(e,n,o),i.x+=e.x,i.y+=e.y,i.z+=e.z,p(this.node).to(.5,{position:i}).start(),null==(t=this.getComponent(l))||t.destroy()}},e}(f)).prototype,"is_top",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),_=y))||_));s._RF.pop()}}}));

System.register("chunks:///_virtual/TurnCardAction.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts","./index21.ts","./index40.ts","./msg_pb.ts","./CommonAction.ts","./GameEventConstant.b.ts","./AudioEffectConstant.b.ts"],(function(n){var t,o,e,i,c,s,r,u,a;return{setters:[function(n){t=n.inheritsLoose},function(n){o=n.cclegacy,e=n._decorator},function(n){i=n.BaseComponent},function(n){c=n.cat},null,function(n){s=n.Card},function(n){r=n.CommonAction},function(n){u=n.GameEventConstant},function(n){a=n.AudioEffectConstant}],execute:function(){var f;o._RF.push({},"7be08Q0Yn1MgYLFKTbNZMF3","TurnCardAction",void 0);var d=e.ccclass;e.property,n("TurnCardAction",d("TurnCardAction")(f=function(n){function o(){return n.apply(this,arguments)||this}t(o,n);var e=o.prototype;return e.onEventListener=function(){c.event.on("EVENT_POST",this.onGamePostHandler,this).on(u.TURN_CARD_RESPONSE,this.onTurnAction,this)},e.onTurnAction=function(n){},e.onGamePostHandler=function(n){n.post==s.TURN&&(c.audio.playEffect(a.TURN_ROUND),window.ccLog("显示<调头>卡牌特效"),this.getComponent(r).showCardEffect(s.TURN))},o}(i))||f);o._RF.pop()}}}));

System.register("chunks:///_virtual/TypeButton.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index23.ts","./BaseComponent.ts"],(function(t){var e,n,o,r,i,u,c,a,s;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,o=t.initializerDefineProperty,r=t.assertThisInitialized},function(t){i=t.cclegacy,u=t._decorator,c=t.Button},function(t){a=t.default},function(t){s=t.BaseComponent}],execute:function(){var p,l,f,y,d;i._RF.push({},"df541kk9TVH+ZNbKURSvDhs","TypeButton",void 0);var b=u.ccclass,h=u.property;t("TypeButton",(p=b("TypeButton"),l=h({type:c}),p((d=e((y=function(t){function e(){for(var e,n=arguments.length,i=new Array(n),u=0;u<n;u++)i[u]=arguments[u];return e=t.call.apply(t,[this].concat(i))||this,o(e,"btn",d,r(e)),e}return n(e,t),e.prototype.onAutoObserver=function(){var t=this;this.addAutorun((function(){var e=a.user.isAudience;window.ccLog("-------",e),t.btn.interactable=!e}))},e}(s)).prototype,"btn",[l],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),f=y))||f));i._RF.pop()}}}));

System.register("chunks:///_virtual/types.b.ts",["cc","./index27.ts"],(function(){var t;return{setters:[function(e){t=e.cclegacy},null],execute:function(){t._RF.push({},"67d33aPzKxOaqHlm01x90ln","types.b",void 0),t._RF.pop()}}}));

System.register("chunks:///_virtual/UIContainer.ts",["./rollupPluginModLoBabelHelpers.js","cc","./UILayer.ts","./index21.ts","./index44.ts","./BlackMask.ts"],(function(e){var n,i,t,o,s,a,r,c,l,h,d,u,g,k,b;return{setters:[function(e){n=e.applyDecoratedDescriptor,i=e.inheritsLoose,t=e.initializerDefineProperty,o=e.assertThisInitialized,s=e.createClass},function(e){a=e.cclegacy,r=e._decorator,c=e.Node,l=e.Sprite,h=e.Color,d=e.isValid},function(e){u=e.default},function(e){g=e.cat},function(e){k=e.GlobalEventConstant},function(e){b=e.BlackMask}],execute:function(){var f,p,M,w,v,C,_;a._RF.push({},"eebbbAYGlFFTJDO+Hbv40c4","UIContainer",void 0);var I=r.ccclass,m=r.property;e("UIContainer",(f=I("UIContainer"),p=m({type:b}),M=m({type:c}),f((C=n((v=function(e){function n(){for(var n,i=arguments.length,s=new Array(i),a=0;a<i;a++)s[a]=arguments[a];return n=e.call.apply(e,[this].concat(s))||this,t(n,"scene_mask",C,o(n)),t(n,"ui_container",_,o(n)),n.gui=null,n.originalMaskAlpha=180,n}i(n,e);var a=n.prototype;return a.onLoad=function(){this.setSceneMaskActive(!1)},a.onEventListener=function(){g.event.on(k.ROOT_MASK_CHANGE,this.uiMaskChanged,this)},a.addMask=function(e){window.ccLog("addMask",e);var n=this.brother[this.brother.length-1];n&&this.scene_mask.node.setSiblingIndex(n.getSiblingIndex()),this.blockMaskSiblingIndexChanged();var i=this.scene_mask.node.getComponentInChildren(l);if(i){180===this.originalMaskAlpha&&(this.originalMaskAlpha=i.color.a);var t=e?0:this.originalMaskAlpha;i.color=new h(i.color.r,i.color.g,i.color.b,t)}},a.subMask=function(){window.ccLog("subMask");var e=this.brother[this.brother.length-2];if(e&&this.scene_mask.node.setSiblingIndex(e.getSiblingIndex()),this.blockMaskSiblingIndexChanged(),0===this.brother.length&&0===this.scene_mask.tween.children.length){var n=this.scene_mask.node.getComponentInChildren(l);n&&(n.color=new h(n.color.r,n.color.g,n.color.b,this.originalMaskAlpha))}},a.blockMaskSiblingIndexChanged=function(){window.ccLog("blockMaskSiblingIndexChanged",this.brother.length,this.scene_mask.tween.children.length),this.brother.length>1||this.scene_mask.tween.children.length>1?this.show():this.hide()},a.addNodeToTween=function(e){return d(this)&&this.scene_mask&&this.scene_mask.tween.addChild(e),this},a.show=function(){window.ccLog("UIContainer show ----------------------"),this.setSceneMaskActive(!0)},a.hide=function(){window.ccLog("UIContainer hide ----------------------"),this.setSceneMaskActive(!1)},a.setGui=function(e){return this.gui=e,this},a.uiMaskChanged=function(){this.blockMaskSiblingIndexChanged()},a.setSceneMaskActive=function(e){this.scene_mask.node.active=!this.gui.root_mask.active&&e},s(n,[{key:"brother",get:function(){return this.ui_container.children||[]}}]),n}(u)).prototype,"scene_mask",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),_=n(v.prototype,"ui_container",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),w=v))||w));a._RF.pop()}}}));

System.register("chunks:///_virtual/UIDraw.ts",["./rollupPluginModLoBabelHelpers.js","cc","./create.js","./descriptors.js","./reflect.js","./descriptor_pb.js","./binary-encoding.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js","./msg_pb.ts","./TimeCount.ts","./LightCardItem.ts","./UILayer.ts","./index21.ts","./index23.ts","./Decorator.ts","./Shark.ts","./Tooth.ts","./index40.ts","./JSBridge.ts","./GameEventConstant.b.ts","./AudioEffectConstant.b.ts"],(function(t){var e,n,a,i,o,r,c,l,s,u,d,p,_,h,m,f,g,b,D,E,v,w,T,y,A,S,R,C,I,O,L,N,k,H,B;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,a=t.initializerDefineProperty,i=t.assertThisInitialized},function(t){o=t.cclegacy,r=t._decorator,c=t.Button,l=t.Node,s=t.Label,u=t.Prefab,d=t.isValid,p=t.Tween,_=t.instantiate,h=t.UITransform,m=t.tween},function(t){f=t.create},null,function(t){g=t.reflect},null,null,null,null,null,null,function(t){b=t.DeskState,D=t.Card,E=t.CardState,v=t.DrawCardRequestSchema,w=t.DrawCardResponseSchema,T=t.DataBroadcastRequestSchema,y=t.DataBroadcastResponseSchema},function(t){A=t.TimeCount},function(t){S=t.LightCardItem},function(t){R=t.default},function(t){C=t.cat},function(t){I=t.default},function(t){O=t.audioEffect},function(t){L=t.ZoomState},function(t){N=t.Tooth},null,function(t){k=t.JSBridgeClient},function(t){H=t.GameEventConstant},function(t){B=t.AudioEffectConstant}],execute:function(){var U,W,z,P,G,K,M,j,x,V,q,F,Z,J,Q,X,Y,$,tt,et,nt,at,it,ot,rt,ct,lt,st,ut,dt,pt,_t,ht;o._RF.push({},"4d7834iahBI6LnPqe/bG7jW","UIDraw",void 0);var mt=r.ccclass,ft=r.property,gt=function(t){return t[t.DRAWING=0]="DRAWING",t[t.OVERTIME=1]="OVERTIME",t[t.DRAWED=2]="DRAWED",t[t.BLESSING=3]="BLESSING",t}(gt||{});t("UIDraw",(U=mt("UIDraw"),W=ft({type:c,tooltip:"取消按钮"}),z=ft({type:l,tooltip:"超时节点"}),P=ft({type:l,tooltip:"祈祷提示节点"}),G=ft({type:l,tooltip:"鲨鱼模型位置"}),K=ft({type:c,tooltip:"确定按钮"}),M=ft({type:s,tooltip:"剩余牌(牙)"}),j=ft({type:s,tooltip:"危险牌(牙)"}),x=ft({type:l,tooltip:"桌子牌堆信息"}),V=ft({type:A,tooltip:"倒计时时间"}),q=ft({type:u,tooltip:"明牌预制体"}),F=ft({type:l,tooltip:"卡牌节点"}),Z=ft({type:l,tooltip:"提示节点"}),J=ft({type:l,tooltip:"特效节点"}),Q=ft({type:l,tooltip:"主要容器节点"}),X=O(),Y=O(),U((et=e((tt=function(t){function e(){for(var e,n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return e=t.call.apply(t,[this].concat(o))||this,a(e,"btn_cancel",et,i(e)),a(e,"overtime",nt,i(e)),a(e,"blessingTip",at,i(e)),a(e,"shark",it,i(e)),a(e,"btn_confirm",ot,i(e)),a(e,"remaining_count",rt,i(e)),a(e,"boom_count",ct,i(e)),a(e,"desk_card_info",lt,i(e)),a(e,"time_count",st,i(e)),a(e,"light_card_prefab",ut,i(e)),a(e,"card",dt,i(e)),a(e,"tips",pt,i(e)),a(e,"shine",_t,i(e)),a(e,"main",ht,i(e)),e.light_card_node=null,e.draw=null,e.props={shark:null,camera3D:null,hand_card:null},e.data={state:gt.DRAWING},e}n(e,t);var o=e.prototype;return o.onLoad=function(){window.ccLog("UIDraw onLoad ----------------"),k.disableTouch(!0),C.event.dispatchEvent(H.SHARK_ZOOM,L.NEAR),this.btn_confirm.node.on(c.EventType.CLICK,this.onConfirmHandler,this),this.btn_cancel.node.on(c.EventType.CLICK,this.onCancelHandler,this)},o.initUI=function(){I.game.showActionButton=!1,this.shine.active=!1},o.onEventListener=function(){C.event.on("EVENT_DRAW",this.onDrawBroadcastHandler,this)},o.start=function(){window.ccLog("UIDraw start ----------------"),I.game.selected_tooth=[],I.game.click_tooth_type=1,I.game.is_allow_click_tooth=!0,this.tips.getComponent(s).string="选择"+I.game.getPlayerDrawCount+"颗牙齿"},o.onAutoObserver=function(){var t=this;this.addAutorun([function(){t.remaining_count.string=""+I.game.roomData.deskRemainingCards},function(){t.boom_count.string=""+I.game.roomData.badNumber},function(){I.game.roomData.state===b.DESK_STATE_BLESSING&&(t.data.state=gt.BLESSING)},function(){switch(t.overtime.active=t.card.active=t.desk_card_info.active=t.time_count.node.active=t.btn_cancel.node.active=t.btn_confirm.node.active=t.tips.active=t.blessingTip.active=!1,t.data.state){case gt.DRAWING:t.desk_card_info.active=t.time_count.node.active=t.btn_cancel.node.active=t.btn_confirm.node.active=t.tips.active=!0;break;case gt.OVERTIME:t.desk_card_info.active=t.overtime.active=!0;break;case gt.DRAWED:t.card.active=!0;break;case gt.BLESSING:t.desk_card_info.active=t.blessingTip.active=!0}},function(){var e=I.game.roomData,n=e.state,a=e.cursor;n===b.DESK_STATE_DRAW_OR_POST&&a!==I.user.userIndex&&t.close()},function(){var e=I.game.roomData,n=e.state,a=e.cursor,i=I.game.lastRoomData,o=i.state,r=i.cursor;o===b.DESK_STATE_HAS_CARD_QUEUE&&n===b.DESK_STATE_DRAW_OR_POST&&a===I.user.userIndex&&r===I.user.userIndex&&(I.game.lastRoomData=I.game.roomData,t.close())}]),this.addReaction((function(){return I.game.roomData.state}),(function(e){e===b.DESK_STATE_DRAW_OR_POST_TIMEOUT&&(t.overtime.active=!0)}))},o.onDestroy=function(){k.disableTouch(!1),this.unscheduleAllCallbacks(),this.draw=null,this.light_card_node&&d(this.light_card_node)&&p.stopAllByTarget(this.light_card_node)},o.onDrawBroadcastHandler=function(t){var e=this;I.game.is_allow_click_tooth=!1,this.draw=t,window.ccLog("DRAW--------------",t);var n=t.got===D.LANDMINE?E.BAD:E.GOOD;C.event.dispatchEvent(H.TAG_TOOTH_STATE,{index:t.number,state:n}),I.game.roomData.state===b.DESK_STATE_DRAW_OR_POST_TIMEOUT&&C.event.dispatchEvent(H.SET_SHARK_ROTATE_CENTER),t.got!==D.LANDMINE?I.game.roomData.state===b.DESK_STATE_DRAW_OR_POST_TIMEOUT?(this.data.state=gt.OVERTIME,this.scheduleOnce((function(){e.drawTween(t)}),1.5)):this.drawTween(t):this.close()},o.drawTween=function(t){var e,n=this;this.data.state=gt.DRAWED;var a=_(this.light_card_prefab);this.light_card_node=a;var i=this.shark.position;null==(e=a.getComponent(S))||e.setNodeAndChildrenLayer("DRAW").setPosition(i).addToParent(this.main,{props:{card:t.got}});var o=this.main.getComponent(h).convertToNodeSpaceAR(this.props.hand_card.node.worldPosition).clone().subtract3f(-50,0,0),r=m(a).to(.5,{position:this.card.position.clone()}).call((function(){n.shine.active=!0,C.audio.playEffect(B.SHOW)})),c=m(a).delay(.5).call((function(){n.shine.active=!1})),l=m(a).to(.5,{position:o}).call((function(){n.close()}));m(a).then(r).then(c).then(l).start()},o.onConfirmHandler=function(){I.game.selected_tooth.length===I.game.getPlayerDrawCount?(I.game.click_tooth_type=0,C.ws.Request("DrawCard",g(v,f(v,{indices:I.game.selected_tooth.map((function(t){var e;return null==(e=t.getComponent(N))?void 0:e.props.index}))})),w).then((function(){}))):C.gui.showToast({title:"请选择"+I.game.getPlayerDrawCount+"颗牙齿"})},o.onCancelHandler=function(){C.ws.Request("DataBroadcast",g(T,f(T,{data:JSON.stringify({selected:[]})})),y),C.event.dispatchEvent(H.SHARK_ZOOM,L.FAR),this.close(),I.game.showActionButton=!0},o.close=function(){if(this.draw&&this.draw.got!==D.LANDMINE){var t=this.draw.got;if(this.draw=null,this.props.hand_card.addCard(t),I.game.roomData.state===b.DESK_STATE_HAS_CARD_QUEUE)return}window.ccLog("UIDraw close ----------------"),C.event.dispatchEvent(H.CANCEL_SELECT_HAND_CARD),C.event.dispatchEvent(H.SHARK_ZOOM,L.FAR),C.gui.closeUI(this)},e}(R)).prototype,"btn_cancel",[W],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),nt=e(tt.prototype,"overtime",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),at=e(tt.prototype,"blessingTip",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),it=e(tt.prototype,"shark",[G],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),ot=e(tt.prototype,"btn_confirm",[K],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),rt=e(tt.prototype,"remaining_count",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),ct=e(tt.prototype,"boom_count",[j],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),lt=e(tt.prototype,"desk_card_info",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),st=e(tt.prototype,"time_count",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),ut=e(tt.prototype,"light_card_prefab",[q],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),dt=e(tt.prototype,"card",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),pt=e(tt.prototype,"tips",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),_t=e(tt.prototype,"shine",[J],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),ht=e(tt.prototype,"main",[Q],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),e(tt.prototype,"onConfirmHandler",[X],Object.getOwnPropertyDescriptor(tt.prototype,"onConfirmHandler"),tt.prototype),e(tt.prototype,"onCancelHandler",[Y],Object.getOwnPropertyDescriptor(tt.prototype,"onCancelHandler"),tt.prototype),$=tt))||$));o._RF.pop()}}}));

System.register("chunks:///_virtual/UILayer.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BaseComponent.ts"],(function(e){var t,n,i,r,s,o,c,a,h;return{setters:[function(e){t=e.inheritsLoose,n=e.asyncToGenerator,i=e.regeneratorRuntime},function(e){r=e.cclegacy,s=e.view,o=e.tween,c=e.Widget,a=e.v3},function(e){h=e.BaseComponent}],execute:function(){r._RF.push({},"c11d1TK6BVEBqYRT981ODv4","UILayer",void 0);e("default",function(e){function r(){var t;return(t=e.call(this)||this).lastEnterDirection="center",t.screen=void 0,t.isClosing=!1,t.root=void 0,t._init(),t}t(r,e);var h=r.prototype;return h._init=function(){this.root=this.node,this.screen=s.getVisibleSize()},h.showTween=function(){var e=n(i().mark((function e(t){var n,r,s,o,h,u,d,f,l,p,v;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.isMotion,r=void 0===n||n,s=t.direction,o=void 0===s?"center":s,h=t.duration,u=void 0===h?.1:h,d=t.isBounce,f=void 0===d||d,t.bounceDuration,this.lastEnterDirection=o,!r){e.next=10;break}return(l=this.node.getComponent(c))&&(l.updateAlignment(),l.enabled=!1),a(1.1,1.1,1),"center"==o?(p=a(.01,.01,.01),v=a(1,1,1)):(p="left"==o?a(-this.screen.width,0,0):"right"==o?a(this.screen.width,0,0):a(0,"top"==o?this.screen.height:-this.screen.height,0),v=a(0,0,0)),e.next=9,this.handle(o,u,p,v,f);case 9:l&&(l.enabled=!0);case 10:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),h.hideTween=function(){var e=n(i().mark((function e(t){var n,r,s,h,u,d,f,l;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.isMotion,r=void 0===n||n,s=t.direction,h=t.duration,u=void 0===h?.1:h,s=s||this.lastEnterDirection,!this.isClosing){e.next=4;break}return e.abrupt("return");case 4:if(this.isClosing=!0,o(this.node).removeSelf(),!r){e.next=12;break}return(d=this.node.getComponent(c))&&(d.enabled=!1),"center"==s?(f=this.node.scale,l=a(0,0,0)):(l="left"==s?a(-this.screen.width,0,0):"right"==s?a(this.screen.width,0,0):a(0,"top"==s?this.screen.height:-this.screen.height,0),f=this.node.getPosition()),e.next=12,this.handle(s,u,f,l,!1);case 12:this.removeAndDestroy();case 13:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),h.handle=function(){var e=n(i().mark((function e(t,n,r,s,o){var c;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return"center"==t?this.node.setScale(r):this.node.setPosition(r),e.next=3,this.deftween(n,((c={})["center"==t?"scale":"position"]=s,c),o);case 3:case"end":return e.stop()}}),e,this)})));return function(t,n,i,r,s){return e.apply(this,arguments)}}(),h.deftween=function(e,t,n,i){var r=this;return new Promise((function(n,i){o(r.node).to(e,t).call((function(){n()})).start()}))},r}(h));r._RF.pop()}}}));

System.register("chunks:///_virtual/UIModal.ts",["./rollupPluginModLoBabelHelpers.js","cc","./UILayer.ts","./Decorator.ts","./index21.ts"],(function(t){var e,n,o,r,i,l,p,a,c,s,u,f,m;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,o=t.initializerDefineProperty,r=t.assertThisInitialized},function(t){i=t.cclegacy,l=t._decorator,p=t.SpriteFrame,a=t.Sprite,c=t.Label,s=t.Button},function(t){u=t.default},function(t){f=t.audioEffect},function(t){m=t.cat}],execute:function(){var y,b,d,_,h,C,v,g,w,F,H,z,I,D,L,P,U,B,M,O,T,j,E,K;i._RF.push({},"a3aa5qX19hPdoJGe+PKPHTw","UIModal",void 0);var S=l.ccclass,x=l.property;t("UIModal",(y=S("UIModal"),b=x({type:p,tooltip:"默认标题"}),d=x({type:a,tooltip:"标题节点"}),_=x({type:c,tooltip:"字符串内容按钮"}),h=x({type:a,tooltip:"图片精灵内容按钮"}),C=x({type:s,tooltip:"确认按钮"}),v=x({type:p,tooltip:"确认按钮精灵图"}),g=x({type:s,tooltip:"取消按钮"}),w=x({type:p,tooltip:"取消按钮精灵图"}),F=x({type:s,tooltip:"关闭按钮"}),H=f(),z=f(),I=f(),y((P=e((L=function(t){function e(){for(var e,n=arguments.length,i=new Array(n),l=0;l<n;l++)i[l]=arguments[l];return e=t.call.apply(t,[this].concat(i))||this,o(e,"default_title",P,r(e)),o(e,"title",U,r(e)),o(e,"prompt_content_str",B,r(e)),o(e,"prompt_content_spriteFrame",M,r(e)),o(e,"btn_confirm",O,r(e)),o(e,"confirm_spriteFrame",T,r(e)),o(e,"btn_cancel",j,r(e)),o(e,"cancel_spriteFrame",E,r(e)),o(e,"btn_close",K,r(e)),e.isConfirm=!1,e.props={title:e.default_title,content:null,confirmCB:function(){},cancelCB:function(){},style:null},e}n(e,t);var i=e.prototype;return i.onLoad=function(){var t=this;this.btn_cancel.node.on(s.EventType.CLICK,this.onCancelHandler,this),this.btn_confirm.node.on(s.EventType.CLICK,this.onConfirmHandler,this),this.btn_close.node.on(s.EventType.CLICK,this.onCloseHandler,this),this.addAutorun([function(){var e;t.title.spriteFrame=(null==(e=t.props)?void 0:e.title)||t.default_title},function(){t.props.content instanceof p?t.prompt_content_spriteFrame.spriteFrame=t.props.content:"string"==typeof t.props.content?t.prompt_content_str.string=t.props.content:console.error("未知类型的【UIModal】内容")},function(){var e,n,o,r;null===(null==(e=t.props)||null==(e=e.style)?void 0:e.confirm)?t.btn_confirm.node.active=!1:(t.btn_confirm.node.active=!0,t.btn_confirm.getComponent(a).spriteFrame=(null==(o=t.props.style)?void 0:o.confirm)||t.confirm_spriteFrame);null===(null==(n=t.props)||null==(n=n.style)?void 0:n.cancel)?t.btn_cancel.node.active=!1:(t.btn_cancel.node.active=!0,t.btn_cancel.getComponent(a).spriteFrame=(null==(r=t.props.style)?void 0:r.cancel)||t.cancel_spriteFrame)}])},i.close=function(){var t,e;null==(t=(e=this.props).cancelCB)||t.call(e),m.gui.closeUI(this)},i.onCancelHandler=function(){this.close()},i.onConfirmHandler=function(){var t,e;null==(t=(e=this.props).confirmCB)||t.call(e)},i.onDestroy=function(){var t,e;this.isConfirm&&(null==(t=(e=this.props).onDestroy)||t.call(e)),window.ccLog("-----onDestroy")},i.onCloseHandler=function(){this.close()},e}(u)).prototype,"default_title",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=e(L.prototype,"title",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),B=e(L.prototype,"prompt_content_str",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),M=e(L.prototype,"prompt_content_spriteFrame",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),O=e(L.prototype,"btn_confirm",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),T=e(L.prototype,"confirm_spriteFrame",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),j=e(L.prototype,"btn_cancel",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),E=e(L.prototype,"cancel_spriteFrame",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),K=e(L.prototype,"btn_close",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),e(L.prototype,"onCancelHandler",[H],Object.getOwnPropertyDescriptor(L.prototype,"onCancelHandler"),L.prototype),e(L.prototype,"onConfirmHandler",[z],Object.getOwnPropertyDescriptor(L.prototype,"onConfirmHandler"),L.prototype),e(L.prototype,"onCloseHandler",[I],Object.getOwnPropertyDescriptor(L.prototype,"onCloseHandler"),L.prototype),D=L))||D));i._RF.pop()}}}));

System.register("chunks:///_virtual/UIPlayerOut.ts",["./rollupPluginModLoBabelHelpers.js","cc","./BasePlayerOutOver.ts","./report_battle_pb.ts","./create.js","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./type_pb.js","./struct_pb.js","./plugin_pb.js","./size-delimited.js"],(function(t){var e,r,i,n,l,o,a,p,s,u,c,b,y;return{setters:[function(t){e=t.applyDecoratedDescriptor,r=t.inheritsLoose,i=t.initializerDefineProperty,n=t.assertThisInitialized},function(t){l=t.cclegacy,o=t._decorator,a=t.Label,p=t.Sprite,s=t.Button,u=t.SpriteFrame},function(t){c=t.BasePlayerOutOver},function(t){b=t.ReportGameStateRequest_GameSettlePayloadSchema},function(t){y=t.create},null,null,null,null,null,null,null],execute:function(){var m,_,g,f,h,F,d,v,z,j,w,P,O,S,k,R,B;l._RF.push({},"36c45bVK6xGKpwT+dZegFcx","UIPlayerOut",void 0);var I=o.ccclass,x=o.property,D=["一","二","三","四","五","六"];t("UIPlayerOut",(m=I("PlayerOut"),_=x({type:a,tooltip:"名次节点"}),g=x({type:p,tooltip:"标题节点"}),f=x({type:p,tooltip:"logo节点"}),h=x({type:s,tooltip:"观战按钮节点"}),F=x({type:[u],tooltip:"标题精灵图集"}),d=x({type:[u],tooltip:"logo精灵图集"}),v=x({type:[u],tooltip:"观战按钮精灵图集"}),m((w=e((j=function(t){function e(){for(var e,r=arguments.length,l=new Array(r),o=0;o<r;o++)l[o]=arguments[o];return e=t.call.apply(t,[this].concat(l))||this,i(e,"rank",w,n(e)),i(e,"title",P,n(e)),i(e,"logo",O,n(e)),i(e,"btn_continue",S,n(e)),i(e,"title_spriteFrame",k,n(e)),i(e,"logo_spriteFrame",R,n(e)),i(e,"btn_continue_spriteFrame",B,n(e)),e.props=y(b),e}return r(e,t),e.prototype.start=function(){if(this.props.teamSettleResults){var t=this.props.teamSettleResults[0].rank;this.rank.string="第"+D[t-1]+"名",this.logo.spriteFrame=this.logo_spriteFrame[1===t?0:1],this.title.spriteFrame=this.title_spriteFrame[1===t?0:1],this.btn_continue.getComponent(p).spriteFrame=this.btn_continue_spriteFrame[[1,2].includes(t)?1:0],this.rank.node.active=!0}else this.rank.node.active=!1},e}(c)).prototype,"rank",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),P=e(j.prototype,"title",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),O=e(j.prototype,"logo",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),S=e(j.prototype,"btn_continue",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),k=e(j.prototype,"title_spriteFrame",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),R=e(j.prototype,"logo_spriteFrame",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),B=e(j.prototype,"btn_continue_spriteFrame",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),z=j))||z));l._RF.pop()}}}));

System.register("chunks:///_virtual/UIRule.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts","./index40.ts","./UILayer.ts","./GameEventConstant.b.ts"],(function(t){var n,e,o,u,i,s;return{setters:[function(t){n=t.inheritsLoose},function(t){e=t.cclegacy,o=t._decorator},function(t){u=t.cat},null,function(t){i=t.default},function(t){s=t.GameEventConstant}],execute:function(){var c;e._RF.push({},"d8681hRnjJNZ4UWXyT/AM3G","UIRule",void 0);var r=o.ccclass;o.property,t("UIRule",r("UIRule")(c=function(t){function e(){return t.apply(this,arguments)||this}return n(e,t),e.prototype.onEventListener=function(){var t=this;u.event.on(s.CLOSE_RULE_UI,(function(){u.gui.closeUI(t)}),this)},e}(i))||c);e._RF.pop()}}}));

System.register("chunks:///_virtual/UploadLog.ts",["./rollupPluginModLoBabelHelpers.js","cc","./RootUILayer.ts","./index21.ts"],(function(t){var n,e,o,i,r,l,a,c,p,s;return{setters:[function(t){n=t.applyDecoratedDescriptor,e=t.inheritsLoose,o=t.initializerDefineProperty,i=t.assertThisInitialized},function(t){r=t.cclegacy,l=t._decorator,a=t.EditBox,c=t.Button},function(t){p=t.default},function(t){s=t.cat}],execute:function(){var u,f,d,h,y,b,g,m,v;r._RF.push({},"2200c4FeQ9IFKPYae/pDmyM","UploadLog",void 0);var L=l.ccclass,_=l.property;t("UploadLog",(u=L("UploadLog"),f=_({type:a,tooltip:"日志名称输入框"}),d=_({type:c,tooltip:"确定按钮"}),h=_({type:c,tooltip:"关闭按钮"}),u((g=n((b=function(t){function n(){for(var n,e=arguments.length,r=new Array(e),l=0;l<e;l++)r[l]=arguments[l];return n=t.call.apply(t,[this].concat(r))||this,o(n,"editBox",g,i(n)),o(n,"btn_confirm",m,i(n)),o(n,"btn_cancel",v,i(n)),n}e(n,t);var r=n.prototype;return r.onLoad=function(){this.btn_confirm.node.on(c.EventType.CLICK,this.onConfrimHandler,this),this.btn_cancel.node.on(c.EventType.CLICK,this.onCancelHanlder,this)},r.start=function(){this.props&&this.updateProps(this.props)},r.onConfrimHandler=function(){var t;null==(t=this.props)||null==t.confrim||t.confrim(this.editBox.string),s.gui.hideNotice()},r.onCancelHanlder=function(){var t;null==(t=this.props)||null==t.cancel||t.cancel()},r.updateProps=function(t){},n}(p)).prototype,"editBox",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),m=n(b.prototype,"btn_confirm",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),v=n(b.prototype,"btn_cancel",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),y=b))||y));r._RF.pop()}}}));

System.register("chunks:///_virtual/user_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./enum.js","./file.js","./message.js"],(function(g){var A,e,l,o;return{setters:[function(g){A=g.cclegacy},null,null,null,function(g){e=g.enumDesc},function(g){l=g.fileDesc},function(g){o=g.messageDesc}],execute:function(){A._RF.push({},"00719GkH2xK45S9Cyb4FvaB","user_pb",void 0);var c=g("file_user",l("Cgp1c2VyLnByb3RvEgZwcm90b3MigAEKBFVzZXIaSwoMQmluZGluZ1N0YXRlEgoKAlFRGAEgASgIEg4KBndlY2hhdBgCIAEoCBINCgVwaG9uZRgDIAEoCBIQCghwYXNzd29yZBgEIAEoCCIrCgZHZW5kZXISCwoHTk9UX1NFVBAAEggKBE1BTEUQARIKCgZGRU1BTEUQAiKAAwoQVXNlckluZm9SZXNwb25zZRILCgN1aWQYASABKAQSDgoGYXZhdGFyGAIgASgJEhEKCWNlbGxwaG9uZRgDIAEoCRIQCghiaXJ0aGRheRgEIAEoCRIUCgxkaXNwbGF5X25hbWUYBSABKAkSFwoPaW52aXRhdGlvbl9jb2RlGAYgASgJEhAKCG5pY2tuYW1lGAcgASgJEiMKBmdlbmRlchgIIAEoDjITLnByb3Rvcy5Vc2VyLkdlbmRlchIMCgRzaWduGAkgASgIEhUKDXJlZ2lzdGVyZWRfYXQYCiABKAMSDQoFbGV2ZWwYCyABKAQSDwoHYmFsYW5jZRgMIAEoCRINCgVwb2ludBgNIAEoBBIWCg5wbGF5X3RpbWVfcGFpZBgOIAEoBBIWCg5wbGF5X3RpbWVfZnJlZRgPIAEoBBIwCg1iaW5kaW5nX3N0YXRlGBAgASgLMhkucHJvdG9zLlVzZXIuQmluZGluZ1N0YXRlEg4KBnN0YXR1cxgRIAEoDSKYAQoUVXNlckluZm9Ob3RpZmljYXRpb24SDgoGYXZhdGFyGAEgASgJEhEKCWNlbGxwaG9uZRgCIAEoCRIQCghiaXJ0aGRheRgDIAEoCRIUCgxkaXNwbGF5X25hbWUYBCABKAkSEAoIbmlja25hbWUYBSABKAkSIwoGZ2VuZGVyGAYgASgOMhMucHJvdG9zLlVzZXIuR2VuZGVyQjAKJGNvbS5zdG50cy5jbG91ZC5zdWlsZXlvby5lY2hvLnByb3Rvc1oILi9wcm90b3NiBnByb3RvMw"));g("UserSchema",o(c,0)),g("User_BindingStateSchema",o(c,0,0)),g("User_Gender",function(g){return g[g.NOT_SET=0]="NOT_SET",g[g.MALE=1]="MALE",g[g.FEMALE=2]="FEMALE",g}({})),g("User_GenderSchema",e(c,0,0)),g("UserInfoResponseSchema",o(c,1)),g("UserInfoNotificationSchema",o(c,2));A._RF.pop()}}}));

System.register("chunks:///_virtual/util.ts",["cc"],(function(t){var e;return{setters:[function(t){e=t.cclegacy}],execute:function(){e._RF.push({},"1f284p9WsFDcal4eugey4Fc","util",void 0);t("queryStringToObject",(function(t){return t.split("&").reduce((function(t,e){var c=e.trim().split("="),n=c[0],u=c[1];return t[n]=decodeURIComponent(u),t}),{})}));e._RF.pop()}}}));

System.register("chunks:///_virtual/VideoSprite.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var t,i,r,o,n,a,s,d,u,l,_,p,c,h,v,E,A,T,m;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,r=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){n=e.cclegacy,a=e._decorator,s=e.VideoPlayer,d=e.Material,u=e.Vec2,l=e.Texture2D,_=e.ImageAsset,p=e.assetManager,c=e.loader,h=e.gfx,v=e.Sprite,E=e.SpriteFrame,A=e.Rect,T=e.v2,m=e.Component}],execute:function(){var f,y,R,P,x,D,S,g,L,M,I,b,N;n._RF.push({},"6f4a0dTJT1LcbCTfJapeUxH","VideoSprite",void 0);var H=a.ccclass,C=a.property,G=function(e){return e[e.ERROR=-1]="ERROR",e[e.IDLE=0]="IDLE",e[e.PREPARING=1]="PREPARING",e[e.PREPARED=2]="PREPARED",e[e.PLAYING=3]="PLAYING",e[e.PAUSED=4]="PAUSED",e[e.STOP=5]="STOP",e[e.COMPLETED=5]="COMPLETED",e}(G||{}),V=function(e){return e[e.HAVE_NOTHING=0]="HAVE_NOTHING",e[e.HAVE_METADATA=1]="HAVE_METADATA",e[e.HAVE_CURRENT_DATA=2]="HAVE_CURRENT_DATA",e[e.HAVE_FUTURE_DATA=3]="HAVE_FUTURE_DATA",e[e.HAVE_ENOUGH_DATA=4]="HAVE_ENOUGH_DATA",e}(V||{});e("VideoSprite",(f=H("VideoSprite"),y=C({type:s}),R=C({type:d,tooltip:"Sprite材质覆盖"}),P=C({type:u,tooltip:"尺寸比[原片:alpha通道块]"}),x=C({type:Boolean,tooltip:"原片与alpha通道布局是否横向放置?"}),D=C({type:Boolean,tooltip:"是否循环播放?"}),f((L=t((g=function(e){function t(){for(var t,i=arguments.length,n=new Array(i),a=0;a<i;a++)n[a]=arguments[a];return t=e.call.apply(e,[this].concat(n))||this,r(t,"videoComp",L,o(t)),r(t,"spriteMaterial",M,o(t)),r(t,"rate",I,o(t)),r(t,"isHorizontal",b,o(t)),r(t,"loop",N,o(t)),t._clip=null,t._video=void 0,t._texture=new l,t._image=new _,t._time=0,t._gl=null,t._currentState=void 0,t.currentTime=void 0,t._seekTime=void 0,t._loaded=!1,t}i(t,e);var n=t.prototype;return n.start=function(){},n.loadNetMp4=function(e){var t=this;p.loadRemote(e,(function(e,i){e?t.node.destroy():(t.videoComp||(t.videoComp=t.getComponent(s)),t.videoComp.clip=i,t._clip=t.videoComp.clip,t._video=t.videoComp._impl._video,t._texture.image=t._image,t._initializeBrowser(),t._video&&t._updateVideoSource())}))},n.onEnable=function(){this._loaded&&this._currentState==G.PLAYING&&this.play()},n._updateVideoSource=function(){var e="";this._clip&&(e=this._clip.nativeUrl),e&&c.md5Pipe&&(e=c.md5Pipe.transformURL(e)),this._loaded=!1,this._video.pause(),this._video.src=e,this.node.emit("preparing",this)},n.update=function(e){this._time+=e,this._time<.032||!this._loaded||(this._time=0,this.updateTexture(),this.loop&&this._video.currentTime>this._video.duration-.15&&this._onCompleted())},n._resetTexture=function(e,t,i,r){e.setFilters(l.Filter.LINEAR,l.Filter.LINEAR),e.setMipFilter(l.Filter.LINEAR),e.setWrapMode(l.WrapMode.CLAMP_TO_EDGE,l.WrapMode.CLAMP_TO_EDGE),e.reset({width:t,height:i,format:r||h.Format.RGB8})},n._initializeBrowser=function(){var e=this;this._video.style.zIndex="-1",this._video.setAttribute("x5-video-player-type","true"),this._video.setAttribute("playsinline","true"),this._video.crossOrigin="anonymous",this._video.autoplay=!0,this._video.loop=!1,this._video.muted=!0,this._video.addEventListener("loadedmetadata",(function(){return e._onMetaLoaded()})),this._video.addEventListener("ended",(function(){return e._onCompleted()})),this._loaded=!1;var t=function(){e._loaded||e._currentState==G.PLAYING||e._video.readyState!==V.HAVE_ENOUGH_DATA&&e._video.readyState!==V.HAVE_METADATA||(e._video.currentTime=0,e._loaded=!0,e._onReadyToPlay())};this._video.addEventListener("canplay",t),this._video.addEventListener("canplaythrough",t),this._video.addEventListener("suspend",t)},n._onMetaLoaded=function(){this.node.emit("loaded",this)},n._onReadyToPlay=function(){this._currentState=G.PREPARED,this._seekTime>.1&&(this.currentTime=this._seekTime),this._resetTexture(this._texture,this._video.videoWidth,this._video.videoHeight),this.updateTexture(),this.node.emit("ready",this),this._video.play(),this._currentState=G.PLAYING,this.useSprite()},n.useSprite=function(){var e=this,t=this.getComponent(v);t&&this.scheduleOnce((function(){var i=new E;i.texture=e._texture;var r=e._texture.width,o=e._texture.height,n=new A;i.packable=!1,e.isHorizontal?n.set(0,0,Math.floor(r*e.rate.x/(e.rate.x+e.rate.y)-1),o):n.set(0,0,r,Math.floor(o*e.rate.x/(e.rate.x+e.rate.y)-1)),i.rect=n,t.spriteFrame=i,t.customMaterial=e.spriteMaterial,t.getMaterialInstance(0).setProperty("TexSize",T(r,o))}))},n._onCompleted=function(){this.loop?this._currentState==G.PLAYING&&(this.currentTime=0,this._video.currentTime=0,this._video.play()):(this._currentState=G.COMPLETED,this.node.emit("completed",this),this.node.destroy())},n.updateTexture=function(){this._isInPlaybackState()&&(this._texture.uploadData(this._video),this.spriteMaterial.setProperty("mainTexture",this._texture))},n.play=function(){this._video&&this._video.play()},n._updateGLTexture=function(e,t){var i=this._gl||e._gfxDevice._context,r=e._gfxTexture.gpuTexture.glTexture;i.bindTexture(i.TEXTURE_2D,r),i.texImage2D(i.TEXTURE_2D,0,i.RGBA,i.RGBA,i.UNSIGNED_BYTE,t)},n._isInPlaybackState=function(){return!!this._video&&this._currentState!=G.IDLE&&this._currentState!=G.PREPARING&&this._currentState!=G.ERROR},t}(m)).prototype,"videoComp",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=t(g.prototype,"spriteMaterial",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=t(g.prototype,"rate",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return T(1,1)}}),b=t(g.prototype,"isHorizontal",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),N=t(g.prototype,"loop",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),S=g))||S));n._RF.pop()}}}));

System.register("chunks:///_virtual/WKWebViewJavascriptBridge.js",["./cjs-loader.mjs"],(function(e,a){var n;return{setters:[function(e){n=e.default}],execute:function(){e("default",void 0);var r=e("__cjsMetaURL",a.meta.url);n.define(r,(function(a,n,r,i,o){!function(){if(!window.WKWebViewJavascriptBridge){window.onerror||(window.onerror=function(e,a,n){console.log("WKWebViewJavascriptBridge: ERROR:"+e+"@"+a+":"+n)}),window.WKWebViewJavascriptBridge={registerHandler:function(e,n){a[e]=n},callHandler:function(e,a,n){2==arguments.length&&"function"==typeof a&&(n=a,a=null);i({handlerName:e,data:a},n)},_fetchQueue:function(){var a=JSON.stringify(e);return e=[],a},_handleMessageFromiOS:function(e){!function(e){console.log("_dispatchMessageFromiOS： ",e);var r,o=JSON.parse(e);if(console.log(o,n),o.responseID){if(!(r=n[o.responseID]))return;r(o.responseData),delete n[o.responseID]}else{if(o.callbackID){var t=o.callbackID;r=function(e){i({handlerName:o.handlerName,responseID:t,responseData:e})}}var s=a[o.handlerName];s?s(o.data,r):console.log("WKWebViewJavascriptBridge: WARNING: no handler for message from iOS:",o)}}(e)}};var e=[],a={},n={},r=1;setTimeout((function(){var e=window.WVJBCallbacks||[];delete window.WVJBCallbacks;for(var a=0;a<e.length;a++)e[a](WKWebViewJavascriptBridge)}),0)}function i(i,o){if(o){var t="cb_"+r+++"_"+(new Date).getTime();n[t]=o,i.callbackID=t}e.push(i),a&&window.webkit&&window.webkit.messageHandlers.iOS_Native_FlushMessageQueue.postMessage(null)}}(),e("default",r.exports)}),{})}}}));

System.register("chunks:///_virtual/WKWebViewJavascriptBridge.mjs_cjs=&original=.js",["./WKWebViewJavascriptBridge.js","./cjs-loader.mjs"],(function(e,r){var t,a;return{setters:[function(r){t=r.__cjsMetaURL;var a={};a.__cjsMetaURL=r.__cjsMetaURL,a.default=r.default,e(a)},function(e){a=e.default}],execute:function(){t||a.throwInvalidWrapper("./WKWebViewJavascriptBridge.js",r.meta.url),a.require(t)}}}));

System.register("chunks:///_virtual/worker_pb.ts",["cc","./descriptors.js","./binary-encoding.js","./descriptor_pb.js","./file.js","./message.js","./service.js","./any_pb.js","./type_pb.js","./empty_pb.js","./struct_pb.js","./wrappers_pb.js","./plugin_pb.js","./account_pb.ts","./recharge_pb.ts","./connect_pb.ts","./user_pb.ts","./new-message_pb.ts","./notification_pb.ts","./member_pb.ts","./growth_pb.ts","./medal_pb.ts","./task_pb.ts","./invite_pb.ts"],(function(b){var l,m,c,Z,d,u,e,a,i,R,n,W,o,t,p,v,Y,X;return{setters:[function(b){l=b.cclegacy},null,null,null,function(b){m=b.fileDesc},function(b){c=b.messageDesc},function(b){Z=b.serviceDesc},function(b){d=b.file_google_protobuf_any},null,function(b){u=b.file_google_protobuf_empty},null,function(b){e=b.file_google_protobuf_wrappers},null,function(b){a=b.file_account},function(b){i=b.file_recharge},function(b){R=b.file_connect},function(b){n=b.file_user},function(b){W=b.file_new_message},function(b){o=b.file_notification},function(b){t=b.file_member},function(b){p=b.file_growth},function(b){v=b.file_medal},function(b){Y=b.file_task},function(b){X=b.file_invite}],execute:function(){l._RF.push({},"940d5aBoq9D9J4hLsMj4B+i","worker_pb",void 0);var G=b("file_worker",m("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",[d,u,e,a,i,R,n,W,o,t,p,v,Y,X]));b("KickOutRequestSchema",c(G,0)),b("BanRequestSchema",c(G,1)),b("BalanceNotificationRequestSchema",c(G,2)),b("RechargeNotificationRequestSchema",c(G,3)),b("ConnectOfflineNotificationRequestSchema",c(G,4)),b("ConnectOnlineNotificationRequestSchema",c(G,5)),b("UserInfoNotificationRequestSchema",c(G,6)),b("MemberInfoNotificationRequestSchema",c(G,7)),b("GrowthInfoNotificationRequestSchema",c(G,8)),b("MedalDoneNotificationRequestSchema",c(G,9)),b("NewMessageNotificationRequestSchema",c(G,10)),b("TaskAwardNotificationRequestSchema",c(G,11)),b("NotificationRequestSchema",c(G,12)),b("InviteAutoFollowNotificationRequestSchema",c(G,13)),b("Worker",Z(G,0));l._RF.pop()}}}));

System.register("chunks:///_virtual/WrapSafeArea.ts",["./rollupPluginModLoBabelHelpers.js","cc","./index21.ts"],(function(t){var e,o,r,a,n,i;return{setters:[function(t){e=t.inheritsLoose},function(t){o=t.cclegacy,r=t._decorator,a=t.Widget,n=t.Component},function(t){i=t.cat}],execute:function(){var s;o._RF.push({},"f2b7dlhPtBCi4X1Mb5KhCpy","WrapSafeArea",void 0);var c=r.ccclass,p=(r.property,r.requireComponent);t("WrapSafeArea",c("WrapSafeArea")(s=p(a)(s=function(t){function o(){for(var e,o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return(e=t.call.apply(t,[this].concat(r))||this).safeTop=0,e.safeBottom=0,e}e(o,t);var r=o.prototype;return r.onLoad=function(){var t=i.util.stringUtil.getURLParameters(decodeURIComponent(window.location.href));this.safeTop=Number(t.safe_top||0),this.safeBottom=Number(t.safe_bottom||0);var e=this.node.getComponent(a);e.top=this.safeTop,e.bottom=this.safeBottom,window.ccLog("WrapSafeArea--------",e.top,e.bottom)},r.start=function(){},r.update=function(t){},o}(n))||s)||s);o._RF.pop()}}}));

(function(r) {
  r('virtual:///prerequisite-imports/main', 'chunks:///_virtual/main'); 
})(function(mid, cid) {
    System.register(mid, [cid], function (_export, _context) {
    return {
        setters: [function(_m) {
            var _exportObj = {};

            for (var _key in _m) {
              if (_key !== "default" && _key !== "__esModule") _exportObj[_key] = _m[_key];
            }
      
            _export(_exportObj);
        }],
        execute: function () { }
    };
    });
});