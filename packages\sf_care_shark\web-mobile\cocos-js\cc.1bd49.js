System.register(["./_virtual_cc-ZcbLcty1.js"],(function(e){"use strict";return{setters:[function(t){e({Acceleration:t.ew,AffineTransform:t.a_,AlphaKey:t.cy,AmbientInfo:t.dz,AnimCurve:t.aw,Animation:t.au,AnimationClip:t.as,AnimationComponent:t.au,AnimationManager:t.aA,AnimationState:t.at,Asset:t.dZ,AssetLibrary:t.eh,AssetManager:t.ed,AsyncDelegate:t.c6,Atlas:t.A,AudioClip:t.aE,AudioPCMDataView:t.aF,AudioSource:t.aD,AudioSourceComponent:t.aD,BASELINE_RATIO:t.D,BITMASK_TAG:t.c$,BaseNode:t.dm,BaseRenderData:t.t,BatchedSkinningModelComponent:t.ak,BatchingUtility:t.a9,BitMask:t.bB,BitmapFont:t.B,BlockInputEvents:t.fJ,BlockInputEventsComponent:t.fJ,BoxCharacterController:t.f9,BoxCollider:t.eW,BoxColliderComponent:t.eW,BufferAsset:t.d_,BuiltinResMgr:t.ej,Button:t.fu,ButtonComponent:t.fu,CCBoolean:t.bY,CCClass:t.bT,CCFloat:t.bX,CCInteger:t.bW,CCLoader:t.ef,CCObject:t.bV,CCString:t.bZ,CacheMode:t.q,CachedArray:t.bA,CallbacksInvoker:t.d3,Camera:t.dM,CameraComponent:t.dM,Canvas:t.C,CanvasComponent:t.C,CapsuleCharacterController:t.f8,CapsuleCollider:t.eY,CapsuleColliderComponent:t.eY,CharacterController:t.eQ,Collider:t.eV,ColliderComponent:t.eV,Color:t.b3,ColorKey:t.cx,CompactValueTypeArray:t.b_,Component:t.ds,ConeCollider:t.e$,ConfigurableConstraint:t.f5,ConstantForce:t.eR,Constraint:t.f3,CylinderCollider:t.e_,CylinderColliderComponent:t.e_,DEFAULT_OCTREE_DEPTH:t.dF,DEFAULT_WORLD_MAX_POS:t.dE,DEFAULT_WORLD_MIN_POS:t.dD,DebugMode:t.ck,DebugView:t.dj,Details:t.dX,DirectionalLight:t.ad,DirectionalLightComponent:t.ad,Director:t.dS,DirectorEvent:t.dR,DynamicAtlasManager:t.a4,EAxisDirection:t.fa,EColliderType:t.fc,ENUM_TAG:t.c_,EPSILON:t.b8,EPhysicsDrawFlags:t.fd,ERigidBodyType:t.fb,EasingMethod:t.d2,EditBox:t.fv,EditBoxComponent:t.fv,EditorExtendable:t.c3,EffectAsset:t.e6,Enum:t.bC,Event:t.en,EventAcceleration:t.eo,EventGamepad:t.es,EventHMD:t.eu,EventHandheld:t.ev,EventHandle:t.et,EventHandler:t.dr,EventInfo:t.ax,EventKeyboard:t.ep,EventMouse:t.eq,EventTarget:t.c4,EventTouch:t.er,Eventify:t.c5,ExtrapolationMode:t.cs,FixedConstraint:t.f6,FogInfo:t.dB,Font:t.F,GCObject:t.cX,Game:t.dU,Gradient:t.cz,Graphics:t.G,GraphicsComponent:t.G,HALF_PI:t.b6,HingeConstraint:t.f4,HorizontalTextAlignment:t.H,HtmlTextParser:t.z,ImageAsset:t.e3,Input:t.eA,InstanceMaterialType:t.I,InstancedBuffer:t.df,Intersection2D:t.eG,JavaScript:t.ea,JsonAsset:t.e2,KeyCode:t.ex,LOD:t.am,LODGroup:t.an,LRUCache:t.K,Label:t.r,LabelAtlas:t.L,LabelComponent:t.r,LabelOutline:t.m,LabelOutlineComponent:t.m,LabelShadow:t.o,Layers:t.dp,Layout:t.fw,LayoutComponent:t.fw,Light:t.ae,LightComponent:t.ae,LightProbeInfo:t.dJ,MATH_FLOAT_ARRAY:t.bw,MIDDLE_RATIO:t.E,Mask:t.h,MaskComponent:t.h,Mat3:t.aX,Mat4:t.aY,Material:t.e7,MathBase:t.bx,Mesh:t.aa,MeshBuffer:t.M,MeshCollider:t.eZ,MeshColliderComponent:t.eZ,MeshRenderData:t.v,MeshRenderer:t.ac,MissingScript:t.dP,MobilityMode:t.dx,ModelComponent:t.ac,ModelRenderer:t.dN,MotionStreak:t.eI,MotionStreakAssemblerManager:t.eJ,Node:t.dm,NodeActivator:t.dt,NodeEventType:t.dy,NodePool:t.el,NodeSpace:t.dv,ObjectCurve:t.cw,OctreeInfo:t.dG,Overflow:t.O,PageView:t.fF,PageViewComponent:t.fF,PageViewIndicator:t.fG,PageViewIndicatorComponent:t.fG,ParticleAsset:t.eL,ParticleSystem2D:t.eH,ParticleSystem2DAssembler:t.eK,PhysicMaterial:t.eS,PhysicsLineStripCastResult:t.eU,PhysicsMaterial:t.eS,PhysicsRayResult:t.eT,PhysicsSystem:t.eO,PipelineEventProcessor:t.dh,PipelineEventType:t.di,PipelineInputAssemblerData:t.dl,PipelineSceneData:t.de,PipelineStateManager:t.dg,PlaneCollider:t.f2,PointLight:t.ah,PointToPointConstraint:t.f7,Pool:t.by,PostSettingsInfo:t.dI,Prefab:t.du,PrefabLink:t.dQ,Primitive:t.ff,PrivateNode:t.dL,Profiler:t.fg,ProgressBar:t.fx,ProgressBarComponent:t.fx,QuadRenderData:t.Q,Quat:t.aV,QuatCurve:t.cu,QuatInterpolationMode:t.cv,RangedDirectionalLight:t.ai,RatioSampler:t.av,RealCurve:t.cq,RealInterpolationMode:t.cr,Rect:t.b1,RecyclePool:t.bz,ReflectionProbe:t.ao,ReflectionProbeManager:t.ap,ReflectionProbeType:t.aq,RenderComponent:t.d,RenderData:t.u,RenderRoot2D:t.R,RenderTexture:t.e8,Renderable2D:t.d,RenderableComponent:t.dN,Renderer:t.dO,RenderingSubMesh:t.d$,ResolutionPolicy:t.fN,RichText:t.i,RichTextComponent:t.i,RigidBody:t.eP,RigidBodyComponent:t.eP,Root:t.aI,SafeArea:t.fH,SafeAreaComponent:t.fH,Scene:t.dn,SceneAsset:t.e0,SceneGlobals:t.dK,Scheduler:t.cp,Script:t.e9,ScrollBar:t.fy,ScrollBarComponent:t.fy,ScrollView:t.fz,ScrollViewComponent:t.fz,Settings:t.cB,SettingsCategory:t.cA,ShadowsInfo:t.dC,SimplexCollider:t.f1,Size:t.a$,SkelAnimDataHub:t.fi,SkeletalAnimation:t.fl,SkeletalAnimationComponent:t.fl,SkeletalAnimationState:t.fj,Skeleton:t.ab,SkinInfo:t.dH,SkinnedMeshBatchRenderer:t.ak,SkinnedMeshRenderer:t.aj,SkinnedMeshUnit:t.al,SkinningModelComponent:t.aj,SkinningModelUnit:t.al,SkyboxInfo:t.dA,Slider:t.fA,SliderComponent:t.fA,Socket:t.fk,Sorting:t.a7,SortingLayers:t.a6,SphereCollider:t.eX,SphereColliderComponent:t.eX,SphereLight:t.af,SphereLightComponent:t.af,SpotLight:t.ag,SpotLightComponent:t.ag,Sprite:t.j,SpriteAtlas:t.a,SpriteComponent:t.j,SpriteFrame:t.c,SpriteFrameEvent:t.b,SpriteRenderer:t.f,StencilManager:t.S,SubContextView:t.fK,System:t.cD,SystemEvent:t.eC,SystemEventType:t.em,TTFFont:t.T,TWO_PI:t.b7,TangentWeightMode:t.ct,TerrainCollider:t.f0,TextAsset:t.e1,Texture2D:t.e4,TextureCube:t.e5,Toggle:t.fB,ToggleComponent:t.fB,ToggleContainer:t.fC,ToggleContainerComponent:t.fC,Touch:t.ey,TransformBit:t.dw,Tween:t.fr,TweenAction:t.fq,TweenSystem:t.fp,TypeScript:t.eb,UI:t.x,UIComponent:t.U,UICoordinateTracker:t.fI,UICoordinateTrackerComponent:t.fI,UIDrawBatch:t.y,UIMeshRenderer:t.k,UIModelComponent:t.k,UIOpacity:t.p,UIOpacityComponent:t.p,UIRenderable:t.d,UIRenderer:t.d,UIReorderComponent:t.fP,UIStaticBatch:t.n,UIStaticBatchComponent:t.n,UITransform:t.e,UITransformComponent:t.e,UIVertexFormat:t.w,VERSION:t.aM,ValueType:t.bE,Vec2:t.aP,Vec3:t.aR,Vec4:t.aT,VerticalTextAlignment:t.V,VideoClip:t.fQ,VideoPlayer:t.fR,View:t.fM,ViewGroup:t.fD,WebGLDevice:t.eF,Widget:t.fE,WidgetComponent:t.fE,WorldNode3DToLocalNodeUI:t.bP,WorldNode3DToWorldNodeUI:t.bQ,__checkObsoleteInNamespace__:t.bO,__checkObsolete__:t.bN,_decorator:t.bS,_resetDebugSetting:t.d5,absMax:t.bs,absMaxComponent:t.br,animation:t.ar,applyMixins:t.d4,approx:t.ba,assert:t.cb,assertID:t.cg,assertIsNonNullable:t.d9,assertIsTrue:t.da,assertsArrayIndex:t.db,assetManager:t.ec,bezier:t.cG,bezierByTime:t.cH,binarySearch:t.d6,binarySearchBy:t.d8,binarySearchEpsilon:t.d7,bits:t.aO,builtinResMgr:t.ek,ccenum:t.bD,cclegacy:t.aN,clamp:t.bb,clamp01:t.bc,color:t.b4,computeRatioByType:t.az,convertUtils:t.bR,debug:t.c7,debugID:t.cc,deprecateModuleExportedName:t.bM,deserialize:t.dW,deserializeTag:t.c0,director:t.dT,disallowAnimation:t.cR,displayName:t.cM,displayOrder:t.cN,dynamicAtlasManager:t.a5,easing:t.cE,editable:t.cJ,editorExtrasTag:t.b$,enumerableProps:t.bt,equals:t.b9,error:t.c9,errorID:t.ce,find:t.dq,flattenCodeArray:t.dc,floatToHalf:t.bu,formerlySerializedAs:t.cT,fragmentText:t.a3,game:t.dV,garbageCollectionManager:t.cW,geometry:t.aL,getBaselineOffset:t.J,getEnglishWordPartAtFirst:t.a1,getEnglishWordPartAtLast:t.a2,getError:t.cj,getPathFromRoot:t.aB,getPhaseID:t.dk,getSerializationMetadata:t.c2,getSymbolAt:t.Y,getSymbolCodeAt:t.Z,getSymbolLength:t.X,getWorldTransformUntilRoot:t.aC,gfx:t.aG,graphicsAssembler:t.g,halfToFloat:t.bv,input:t.ez,instantiate:t.dY,inverseLerp:t.bq,isCCClassOrFastDefined:t.bU,isCCObject:t.d0,isDisplayStats:t.ch,isEnglishWordPartAtFirst:t.$,isEnglishWordPartAtLast:t.a0,isUnicodeCJK:t.N,isUnicodeSpace:t.P,isValid:t.d1,js:t.bF,jsbUtils:t.cI,labelAssembler:t.l,lerp:t.bd,loadWasmModuleBullet:t.eM,loadWasmModuleSpine:t.fn,loader:t.eg,log:t.c8,logID:t.cd,macro:t.cn,markAsWarning:t.bL,mat4:t.aZ,math:t.aJ,memop:t.aK,misc:t.bG,murmurhash2_32_gc:t.cF,native:t.eD,nextPow2:t.bn,override:t.cS,path:t.bH,physics:t.eN,pingPong:t.bp,pipeline:t.dd,preTransforms:t.b5,primitives:t.fe,profiler:t.fh,pseudoRandom:t.bk,pseudoRandomRange:t.bl,pseudoRandomRangeInt:t.bm,quat:t.aW,random:t.bg,randomRange:t.bi,randomRangeInt:t.bj,range:t.cO,rangeStep:t.cP,rect:t.b2,removeProperty:t.bK,renderer:t.aH,rendering:t.eE,repeat:t.bo,replaceProperty:t.bJ,resources:t.ee,safeMeasureText:t.W,sampleAnimationCurve:t.ay,screen:t.cl,serializable:t.cU,serializeTag:t.c1,setDefaultLogTimes:t.bI,setDisplayStats:t.ci,setPropertyEnumType:t.cY,setPropertyEnumTypeOnAttrs:t.cZ,setRandGenerator:t.bh,settings:t.cC,shift:t.cV,size:t.b0,slide:t.cQ,sp:t.fm,spriteAssembler:t.s,sys:t.cm,systemEvent:t.eB,toDegree:t.bf,toRadian:t.be,tooltip:t.cK,tween:t.fs,tweenProgress:t.fo,tweenUtil:t.ft,url:t.ei,utils:t.a8,v2:t.aQ,v3:t.aS,v4:t.aU,view:t.fO,visible:t.cL,visibleRect:t.co,warn:t.ca,warnID:t.cf,widgetManager:t.fL})}],execute:function(){}}}));
