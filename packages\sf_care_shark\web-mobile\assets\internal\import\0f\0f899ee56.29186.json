[1, ["609xlc7CpF67qUiVX2DoHQ", "a3zQCfCrBCDZJ4uf2rk5u8", "81Dpbk5FZEaJtZ9OjzlzLA", "c2chXYaDVLaL+7verGEAwE", "1cAq5vRJJJFbj4dJKjseTN"], ["_effectAsset"], [["cc.EffectAsset", ["_name", "shaders", "techniques", "combinations"], -1], ["cc.Material", ["_name", "_props", "_states", "_defines"], -1], ["cc.Material", ["_name", "_states", "_defines", "_props"], 0, 12], ["cc.PhysicsMaterial", ["_name", "_friction", "_rollingFriction", "_spinningFriction", "_restitution"], -2]], [[0, 0, 1, 2, 4], [1, 0, 1, 2, 3, 5], [0, 0, 3, 1, 2, 5], [2, 0, 1, 2, 3, 4], [3, 0, 1, 2, 3, 4, 6]], [[[[0, "internal/builtin-graphics", [{"hash": 586721616, "name": "internal/builtin-graphics|vs:vert|fs:frag", "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_color", "format": 44, "location": 1, "defines": []}, {"name": "a_dist", "format": 11, "location": 2, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matViewProj;\nuniform highp mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\nattribute float a_dist;\nvarying float v_dist;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matViewProj * cc_matWorld * pos;\n  v_color = a_color;\n  v_dist = a_dist;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\n#ifdef GL_OES_standard_derivatives\n#extension GL_OES_standard_derivatives: enable\n#endif\nprecision highp float;\nvarying vec4 v_color;\nvarying float v_dist;\nvec4 frag () {\n  vec4 o = v_color;\n    #ifdef GL_OES_standard_derivatives\n      float aa = fwidth(v_dist);\n    #else\n      float aa = 0.05;\n    #endif\n  float alpha = 1. - smoothstep(-aa, 0., abs(v_dist) - 1.0);\n  o.rgb *= o.a;\n  o *= alpha;\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 0}}, "defines": []}], [{"passes": [{"program": "internal/builtin-graphics|vs:vert|fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 4, "blendSrcAlpha": 1, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}}]}]]], 0, 0, [], [], []], [[[1, "ui-alpha-test-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_ALPHA_TEST": true, "USE_TEXTURE": true, "IS_GRAY": false, "CC_USE_EMBEDDED_ALPHA": false}]]], 0, 0, [0], [0], [0]], [[[0, "pipeline/skybox", [{"hash": 4049110380, "name": "pipeline/skybox|sky-vs:vert|sky-fs:frag", "blocks": [], "samplerTextures": [{"name": "environmentMap", "type": 31, "count": 1, "stageFlags": 16, "binding": 0, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [{"name": "environmentMap", "type": 31, "count": 1, "stageFlags": 16, "binding": 0, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [{"name": "cc_environment", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "global"}, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n#endif\nvarying mediump vec4 viewDir;\nvec4 vert () {\n    viewDir = vec4(a_position, 1.0);\n  mat4 matViewRotOnly = mat4(mat3(cc_matView));\n  vec4 pos = matViewRotOnly * viewDir;\n  if (cc_matProj[3].w > 0.0) {\n    mat4 matProj = cc_matProj;\n    matProj[0].x = 5.2;\n    matProj[1].y = 2.6;\n    matProj[2].zw = vec2(-1.0);\n    matProj[3].zw = vec2(0.0);\n    pos = matProj * pos;\n  } else {\n    pos = cc_matProj * pos;\n  }\n  pos.z = 0.99999 * pos.w;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\n#ifdef GL_EXT_shader_texture_lod\n#extension GL_EXT_shader_texture_lod: enable\n#endif\nprecision mediump float;\nuniform mediump vec4 cc_surfaceTransform;\n  uniform mediump vec4 cc_ambientSky;\nuniform samplerCube cc_environment;\nvec4 fragTextureLod (sampler2D tex, vec2 coord, float lod) {\n    #ifdef GL_EXT_shader_texture_lod\n      return texture2DLodEXT(tex, coord, lod);\n    #else\n      return texture2D(tex, coord, lod);\n    #endif\n}\nvec4 fragTextureLod (samplerCube tex, vec3 coord, float lod) {\n    #ifdef GL_EXT_shader_texture_lod\n      return textureCubeLodEXT(tex, coord, lod);\n    #else\n      return textureCube(tex, coord, lod);\n    #endif\n}\nvec4 packRGBE (vec3 rgb) {\n  highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n  highp float e = 128.0;\n  if (maxComp > 0.0001) {\n    e = log(maxComp) / log(1.1);\n    e = ceil(e);\n    e = clamp(e + 128.0, 0.0, 255.0);\n  }\n  highp float sc = 1.0 / pow(1.1, e - 128.0);\n  vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n  vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n  return vec4(encode_rounded, e) / 255.0;\n}\nvec3 unpackRGBE (vec4 rgbe) {\n  return rgbe.rgb * pow(1.1, rgbe.a * 255.0 - 128.0);\n}\nvec3 SRGBToLinear (vec3 gamma) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return gamma;\n    }\n  #endif\n#endif\n  return gamma * gamma;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec3 HDRToLDR(vec3 color)\n{\n  #if CC_USE_HDR\n    #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n      if (IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING)\n    #endif\n    {\n    #if CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    }\n  #endif\n  return color;\n}\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nvec3 RotationVecFromAxisY(vec3 v, float cosTheta, float sinTheta)\n{\n    vec3 result;\n    result.x = dot(v, vec3(cosTheta, 0.0, -sinTheta));\n    result.y = v.y;\n    result.z = dot(v, vec3(sinTheta, 0.0,  cosTheta));\n    return result;\n}\nvec3 RotationVecFromAxisY(vec3 v, float rotateAngleArc)\n{\n  return RotationVecFromAxisY(v, cos(rotateAngleArc), sin(rotateAngleArc));\n}\nvarying mediump vec4 viewDir;\nuniform samplerCube environmentMap;\nvec4 frag () {\n  vec3 rotationDir = RotationVecFromAxisY(viewDir.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n  #if USE_RGBE_CUBEMAP\n    vec3 c = unpackRGBE(fragTextureLod(environmentMap, rotationDir.xyz, 0.0));\n  #else\n    vec3 c = SRGBToLinear(fragTextureLod(environmentMap, rotationDir.xyz, 0.0).rgb);\n  #endif\n  vec4 color = vec4(c * cc_ambientSky.w, 1.0);\n  #if CC_USE_RGBE_OUTPUT\n    color = packRGBE(color.rgb);\n  #else\n    color.rgb = HDRToLDR(color.rgb);\n    color.rgb = LinearToSRGB(color.rgb);\n  #endif\n  return color;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [{"name": "cc_environment", "defines": []}], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 42, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 42}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean"}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_USE_IBL", "type": "number", "range": [0, 2]}, {"name": "CC_USE_DEBUG_VIEW", "type": "number", "range": [0, 3]}, {"name": "CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC", "type": "boolean"}, {"name": "CC_SURFACES_ENABLE_DEBUG_VIEW", "type": "boolean"}, {"name": "CC_USE_HDR", "type": "boolean"}, {"name": "CC_TONE_MAPPING_TYPE", "type": "number", "range": [0, 3]}, {"name": "HDR_TONE_MAPPING_ACES", "type": "boolean"}, {"name": "USE_RGBE_CUBEMAP", "type": "boolean"}, {"name": "CC_USE_RGBE_OUTPUT", "type": "boolean"}]}], [{"passes": [{"program": "pipeline/skybox|sky-vs:vert|sky-fs:frag", "priority": 245, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"environmentMap": {"value": "grey", "type": 31}}}, {"propertyIndex": 0, "phase": "deferred-forward", "program": "pipeline/skybox|sky-vs:vert|sky-fs:frag", "priority": 245, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": true, "depthWrite": false}}]}]]], 0, 0, [], [], []], [[[0, "pipeline/deferred-lighting", [{"hash": 1995297623, "name": "pipeline/deferred-lighting|lighting-vs|lighting-fs", "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}], "fragColors": [{"name": "fragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 16, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "CCSH", "stageFlags": 16, "tags": {"builtin": "local"}, "members": [{"name": "cc_sh_linear_const_r", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_g", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_b", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_r", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_g", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_b", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_a", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_LIGHT_PROBE", "!USE_INSTANCING"]}, {"name": "CCForwardLight", "stageFlags": 16, "tags": {"builtin": "local"}, "members": [{"name": "cc_lightPos", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}, {"name": "cc_lightColor", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightSizeRangeAngle", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightDir", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightBoundingSizeVS", "typename": "vec4", "type": 16, "count": 0, "isArray": true}], "defines": ["CC_ENABLE_CLUSTERED_LIGHT_CULLING"]}], "samplerTextures": [{"name": "cc_reflectionProbeCubemap", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbePlanarMap", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeDataMap", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeBlendCubemap", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCShadow", "stageFlags": 16, "tags": {"builtin": "global"}, "members": [{"name": "cc_matLightView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matLightViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_shadowInvProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowNFLSInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowWHPBInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowLPNNInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowColor", "typename": "vec4", "type": 16, "count": 1, "precision": "lowp "}, {"name": "cc_planarNDInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCSM", "stageFlags": 16, "tags": {"builtin": "global"}, "members": [{"name": "cc_csmViewDir0", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmViewDir1", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmViewDir2", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmAtlas", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_matCSMViewProj", "typename": "mat4", "type": 25, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmProjDepthInfo", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmProjInfo", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmSplitsInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_SUPPORT_CASCADED_SHADOW_MAP"]}], "samplerTextures": [{"name": "cc_shadowMap", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 16, "tags": {"builtin": "global"}, "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_spotShadowMap", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 16, "tags": {"builtin": "global"}, "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_environment", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "global"}, "defines": []}, {"name": "cc_diffuseMap", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "global"}, "defines": ["CC_USE_IBL", "CC_USE_DIFFUSEMAP"]}, {"name": "albedoMap", "type": 28, "count": 1, "stageFlags": 16, "binding": 0, "defines": []}, {"name": "normalMap", "type": 28, "count": 1, "stageFlags": 16, "binding": 1, "defines": []}, {"name": "emissiveMap", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}, {"name": "depthStencil", "type": 28, "count": 1, "stageFlags": 16, "binding": 3, "defines": []}], "samplers": [], "textures": [], "buffers": [{"name": "b_ccLightsBuffer", "memoryAccess": 1, "stageFlags": 16, "binding": 4, "defines": ["CC_ENABLE_CLUSTERED_LIGHT_CULLING"]}, {"name": "b_clusterLightIndicesBuffer", "memoryAccess": 1, "stageFlags": 16, "binding": 5, "defines": ["CC_ENABLE_CLUSTERED_LIGHT_CULLING"]}, {"name": "b_clusterLightGridBuffer", "memoryAccess": 1, "stageFlags": 16, "binding": 6, "defines": ["CC_ENABLE_CLUSTERED_LIGHT_CULLING"]}], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n#endif\nuniform highp vec4 cc_cameraPos;\nvarying vec2 v_uv;\nvoid main () {\n  vec4 position;\n    position = vec4(a_position, 1.0);\n  position.xy = cc_cameraPos.w == 0.0 ? vec2(position.xy.x, -position.xy.y) : position.xy;\n  gl_Position = vec4(position.x, position.y, 1.0, 1.0);\n  v_uv = a_texCoord;\n}", "frag": "\n#ifdef GL_OES_standard_derivatives\n#extension GL_OES_standard_derivatives: enable\n#endif\n#ifdef GL_EXT_shader_texture_lod\n#extension GL_EXT_shader_texture_lod: enable\n#endif\n  precision highp float;\n  uniform mediump vec4 cc_probeInfo;\n  uniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n  uniform highp mat4 cc_matViewProj;\n  uniform highp mat4 cc_matViewProjInv;\n  uniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_surfaceTransform;\n  uniform mediump vec4 cc_exposure;\n  uniform mediump vec4 cc_mainLitDir;\n  uniform mediump vec4 cc_mainLitColor;\n  uniform mediump vec4 cc_ambientSky;\n  uniform mediump vec4 cc_ambientGround;\n  uniform mediump vec4 cc_fogColor;\n  uniform mediump vec4 cc_fogBase;\n  uniform mediump vec4 cc_fogAdd;\n  uniform mediump vec4 cc_nearFar;\n  uniform mediump vec4 cc_viewPort;\n  #define QUATER_PI         0.78539816340\n  #define HALF_PI           1.57079632679\n  #define PI                3.14159265359\n  #define PI2               6.28318530718\n  #define PI4               12.5663706144\n  #define INV_QUATER_PI     1.27323954474\n  #define INV_HALF_PI       0.63661977237\n  #define INV_PI            0.31830988618\n  #define INV_PI2           0.15915494309\n  #define INV_PI4           0.07957747155\n  #define EPSILON           1e-6\n  #define EPSILON_LOWP      1e-4\n  #define LOG2              1.442695\n  #define EXP_VALUE         2.71828183\n  #define FP_MAX            65504.0\n  #define FP_SCALE          0.0009765625\n  #define FP_SCALE_INV      1024.0\n  #define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n      #define LIGHT_MAP_TYPE_DISABLED 0\n  #define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n  #define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n  #define REFLECTION_PROBE_TYPE_NONE 0\n  #define REFLECTION_PROBE_TYPE_CUBE 1\n  #define REFLECTION_PROBE_TYPE_PLANAR 2\n  #define REFLECTION_PROBE_TYPE_BLEND 3\n  #define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n      #define LIGHT_TYPE_DIRECTIONAL 0.0\n  #define LIGHT_TYPE_SPHERE 1.0\n  #define LIGHT_TYPE_SPOT 2.0\n  #define LIGHT_TYPE_POINT 3.0\n  #define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n  #define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n  #define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n  #define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n  #define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n  #define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n  #define TONE_MAPPING_ACES 0\n  #define TONE_MAPPING_LINEAR 1\n  #define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n  #ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n    #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n  #endif\n  #ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n    #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n  #endif\n  vec3 SRGBToLinear (vec3 gamma) {\n  #ifdef CC_USE_SURFACE_SHADER\n    #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n      if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n        return gamma;\n      }\n    #endif\n  #endif\n    return gamma * gamma;\n  }\n  vec3 LinearToSRGB(vec3 linear) {\n  #ifdef CC_USE_SURFACE_SHADER\n    #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n      if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n        return linear;\n      }\n    #endif\n  #endif\n    return sqrt(linear);\n  }\n  uniform highp mat4 cc_matLightView;\n  uniform highp mat4 cc_matLightViewProj;\n  uniform highp vec4 cc_shadowInvProjDepthInfo;\n  uniform highp vec4 cc_shadowProjDepthInfo;\n  uniform highp vec4 cc_shadowProjInfo;\n  uniform mediump vec4 cc_shadowNFLSInfo;\n  uniform mediump vec4 cc_shadowWHPBInfo;\n  #if CC_SUPPORT_CASCADED_SHADOW_MAP\n    uniform highp vec4 cc_csmViewDir0[4];\n  uniform highp vec4 cc_csmViewDir1[4];\n  uniform highp vec4 cc_csmViewDir2[4];\n  uniform highp vec4 cc_csmAtlas[4];\n  uniform highp mat4 cc_matCSMViewProj[4];\n  uniform highp vec4 cc_csmProjDepthInfo[4];\n  uniform highp vec4 cc_csmProjInfo[4];\n  uniform highp vec4 cc_csmSplitsInfo;\n  #endif\n  #if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n  #define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n  #else\n  #define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n  #endif\n  vec2 GetPlanarReflectScreenUV(vec3 worldPos, mat4 matVirtualCameraViewProj, float flipNDCSign, vec3 viewDir, vec3 reflectDir)\n  {\n    vec4 clipPos = matVirtualCameraViewProj * vec4(worldPos, 1.0);\n    vec2 screenUV = clipPos.xy / clipPos.w * 0.5 + 0.5;\n    screenUV = vec2(1.0 - screenUV.x, screenUV.y);\n    screenUV = flipNDCSign == 1.0 ? vec2(screenUV.x, 1.0 - screenUV.y) : screenUV;\n    return screenUV;\n  }\n  float GetCameraDepthRH(float depthHS, mat4 matProj)\n  {\n      return -matProj[3][2] / (depthHS + matProj[2][2]);\n  }\n  float GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n  {\n      return -matProj32 / (depthHS + matProj22);\n  }\n  vec4 GetWorldPosFromNDCPosRH(vec3 posHS, mat4 matProj, mat4 matViewProjInv)\n  {\n      float w = -GetCameraDepthRH(posHS.z, matProj);\n      return matViewProjInv * vec4(posHS * w, w);\n  }\n  float GetLinearDepthFromViewSpace(vec3 viewPos, float near, float far) {\n    float dist = length(viewPos);\n    return (dist - near) / (far - near);\n  }\n  vec3 RotationVecFromAxisY(vec3 v, float cosTheta, float sinTheta)\n  {\n      vec3 result;\n      result.x = dot(v, vec3(cosTheta, 0.0, -sinTheta));\n      result.y = v.y;\n      result.z = dot(v, vec3(sinTheta, 0.0,  cosTheta));\n      return result;\n  }\n  vec3 RotationVecFromAxisY(vec3 v, float rotateAngleArc)\n  {\n    return RotationVecFromAxisY(v, cos(rotateAngleArc), sin(rotateAngleArc));\n  }\n  float CCGetLinearDepth(vec3 worldPos, float viewSpaceBias) {\n  \tvec4 viewPos = cc_matLightView * vec4(worldPos.xyz, 1.0);\n    viewPos.z += viewSpaceBias;\n  \treturn GetLinearDepthFromViewSpace(viewPos.xyz, cc_shadowNFLSInfo.x, cc_shadowNFLSInfo.y);\n  }\n  float CCGetLinearDepth(vec3 worldPos) {\n  \treturn CCGetLinearDepth(worldPos, 0.0);\n  }\n  #if CC_RECEIVE_SHADOW\n    uniform highp sampler2D cc_shadowMap;\n    uniform highp sampler2D cc_spotShadowMap;\n        #define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n        highp float unpackHighpData (float mainPart, float modPart) {\n          highp float data = mainPart;\n          return data + modPart;\n        }\n        highp float unpackHighpData (float mainPart, float modPart, const float modValue) {\n          highp float data = mainPart * modValue;\n          return data + modPart * modValue;\n        }\n        highp vec2 unpackHighpData (vec2 mainPart, vec2 modPart) {\n          highp vec2 data = mainPart;\n          return data + modPart;\n        }\n        highp vec2 unpackHighpData (vec2 mainPart, vec2 modPart, const float modValue) {\n          highp vec2 data = mainPart * modValue;\n          return data + modPart * modValue;\n        }\n        highp vec3 unpackHighpData (vec3 mainPart, vec3 modPart) {\n          highp vec3 data = mainPart;\n          return data + modPart;\n        }\n        highp vec3 unpackHighpData (vec3 mainPart, vec3 modPart, const float modValue) {\n          highp vec3 data = mainPart * modValue;\n          return data + modPart * modValue;\n        }\n        highp vec4 unpackHighpData (vec4 mainPart, vec4 modPart) {\n          highp vec4 data = mainPart;\n          return data + modPart;\n        }\n        highp vec4 unpackHighpData (vec4 mainPart, vec4 modPart, const float modValue) {\n          highp vec4 data = mainPart * modValue;\n          return data + modPart * modValue;\n        }\n    vec4 shadowTexure(highp sampler2D shadowMap, vec2 coord) {\n        #if defined(CC_USE_WGPU)\n            return texture2DLod(shadowMap, coord, 0.0);\n        #else\n          return texture2D(shadowMap, coord);\n        #endif\n    }\n    float NativePCFShadowFactorHard (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n    {\n      #if CC_SHADOWMAP_FORMAT == 1\n        return step(shadowNDCPos.z, dot(shadowTexure(shadowMap, shadowNDCPos.xy), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      #else\n        return step(shadowNDCPos.z, shadowTexure(shadowMap, shadowNDCPos.xy).x);\n      #endif\n    }\n    float NativePCFShadowFactorSoft (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n    {\n      vec2 oneTap = 1.0 / shadowMapResolution;\n      vec2 shadowNDCPos_offset = shadowNDCPos.xy + oneTap;\n      float block0, block1, block2, block3;\n      #if CC_SHADOWMAP_FORMAT == 1\n        block0 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos_offset.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      #else\n        block0 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)).x);\n        block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos.y)).x);\n        block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset.y)).x);\n        block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos_offset.y)).x);\n      #endif\n      float coefX   = mod(shadowNDCPos.x, oneTap.x) * shadowMapResolution.x;\n      float resultX = mix(block0, block1, coefX);\n      float resultY = mix(block2, block3, coefX);\n      float coefY   = mod(shadowNDCPos.y, oneTap.y) * shadowMapResolution.y;\n      return mix(resultX, resultY, coefY);\n    }\n    float NativePCFShadowFactorSoft3X (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n    {\n      vec2 oneTap = 1.0 / shadowMapResolution;\n      float shadowNDCPos_offset_L = shadowNDCPos.x - oneTap.x;\n      float shadowNDCPos_offset_R = shadowNDCPos.x + oneTap.x;\n      float shadowNDCPos_offset_U = shadowNDCPos.y - oneTap.y;\n      float shadowNDCPos_offset_D = shadowNDCPos.y + oneTap.y;\n      float block0, block1, block2, block3, block4, block5, block6, block7, block8;\n      #if CC_SHADOWMAP_FORMAT == 1\n        block0 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block4 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block5 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block6 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block7 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block8 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      #else\n        block0 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_U)).x);\n        block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_U)).x);\n        block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_U)).x);\n        block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos.y)).x);\n        block4 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)).x);\n        block5 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos.y)).x);\n        block6 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_D)).x);\n        block7 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_D)).x);\n        block8 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_D)).x);\n      #endif\n      float coefX = mod(shadowNDCPos.x, oneTap.x) * shadowMapResolution.x;\n      float coefY = mod(shadowNDCPos.y, oneTap.y) * shadowMapResolution.y;\n      float shadow = 0.0;\n      float resultX = mix(block0, block1, coefX);\n      float resultY = mix(block3, block4, coefX);\n      shadow += mix(resultX , resultY, coefY);\n      resultX = mix(block1, block2, coefX);\n      resultY = mix(block4, block5, coefX);\n      shadow += mix(resultX , resultY, coefY);\n      resultX = mix(block3, block4, coefX);\n      resultY = mix(block6, block7, coefX);\n      shadow += mix(resultX, resultY, coefY);\n      resultX = mix(block4, block5, coefX);\n      resultY = mix(block7, block8, coefX);\n      shadow += mix(resultX, resultY, coefY);\n      return shadow * 0.25;\n    }\n    float NativePCFShadowFactorSoft5X (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n    {\n      vec2 oneTap = 1.0 / shadowMapResolution;\n      vec2 twoTap = oneTap * 2.0;\n      vec2 offset1 = shadowNDCPos.xy + vec2(-twoTap.x, -twoTap.y);\n      vec2 offset2 = shadowNDCPos.xy + vec2(-oneTap.x, -twoTap.y);\n      vec2 offset3 = shadowNDCPos.xy + vec2(0.0, -twoTap.y);\n      vec2 offset4 = shadowNDCPos.xy + vec2(oneTap.x, -twoTap.y);\n      vec2 offset5 = shadowNDCPos.xy + vec2(twoTap.x, -twoTap.y);\n      vec2 offset6 = shadowNDCPos.xy + vec2(-twoTap.x, -oneTap.y);\n      vec2 offset7 = shadowNDCPos.xy + vec2(-oneTap.x, -oneTap.y);\n      vec2 offset8 = shadowNDCPos.xy + vec2(0.0, -oneTap.y);\n      vec2 offset9 = shadowNDCPos.xy + vec2(oneTap.x, -oneTap.y);\n      vec2 offset10 = shadowNDCPos.xy + vec2(twoTap.x, -oneTap.y);\n      vec2 offset11 = shadowNDCPos.xy + vec2(-twoTap.x, 0.0);\n      vec2 offset12 = shadowNDCPos.xy + vec2(-oneTap.x, 0.0);\n      vec2 offset13 = shadowNDCPos.xy + vec2(0.0, 0.0);\n      vec2 offset14 = shadowNDCPos.xy + vec2(oneTap.x, 0.0);\n      vec2 offset15 = shadowNDCPos.xy + vec2(twoTap.x, 0.0);\n      vec2 offset16 = shadowNDCPos.xy + vec2(-twoTap.x, oneTap.y);\n      vec2 offset17 = shadowNDCPos.xy + vec2(-oneTap.x, oneTap.y);\n      vec2 offset18 = shadowNDCPos.xy + vec2(0.0, oneTap.y);\n      vec2 offset19 = shadowNDCPos.xy + vec2(oneTap.x, oneTap.y);\n      vec2 offset20 = shadowNDCPos.xy + vec2(twoTap.x, oneTap.y);\n      vec2 offset21 = shadowNDCPos.xy + vec2(-twoTap.x, twoTap.y);\n      vec2 offset22 = shadowNDCPos.xy + vec2(-oneTap.x, twoTap.y);\n      vec2 offset23 = shadowNDCPos.xy + vec2(0.0, twoTap.y);\n      vec2 offset24 = shadowNDCPos.xy + vec2(oneTap.x, twoTap.y);\n      vec2 offset25 = shadowNDCPos.xy + vec2(twoTap.x, twoTap.y);\n      float block1, block2, block3, block4, block5, block6, block7, block8, block9, block10, block11, block12, block13, block14, block15, block16, block17, block18, block19, block20, block21, block22, block23, block24, block25;\n      #if CC_SHADOWMAP_FORMAT == 1\n        block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset1), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset2), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset3), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block4 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset4), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block5 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset5), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block6 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset6), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block7 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset7), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block8 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset8), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block9 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset9), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block10 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset10), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block11 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset11), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block12 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset12), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block13 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset13), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block14 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset14), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block15 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset15), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block16 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset16), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block17 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset17), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block18 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset18), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block19 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset19), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block20 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset20), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block21 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset21), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block22 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset22), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block23 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset23), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block24 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset24), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n        block25 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset25), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      #else\n        block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset1).x);\n        block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset2).x);\n        block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset3).x);\n        block4 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset4).x);\n        block5 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset5).x);\n        block6 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset6).x);\n        block7 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset7).x);\n        block8 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset8).x);\n        block9 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset9).x);\n        block10 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset10).x);\n        block11 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset11).x);\n        block12 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset12).x);\n        block13 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset13).x);\n        block14 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset14).x);\n        block15 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset15).x);\n        block16 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset16).x);\n        block17 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset17).x);\n        block18 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset18).x);\n        block19 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset19).x);\n        block20 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset20).x);\n        block21 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset21).x);\n        block22 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset22).x);\n        block23 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset23).x);\n        block24 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset24).x);\n        block25 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset25).x);\n      #endif\n      vec2 coef = fract(shadowNDCPos.xy * shadowMapResolution);\n      vec2 v1X1 = mix(vec2(block1, block6), vec2(block2, block7), coef.xx);\n      vec2 v1X2 = mix(vec2(block2, block7), vec2(block3, block8), coef.xx);\n      vec2 v1X3 = mix(vec2(block3, block8), vec2(block4, block9), coef.xx);\n      vec2 v1X4 = mix(vec2(block4, block9), vec2(block5, block10), coef.xx);\n      float v1 = mix(v1X1.x, v1X1.y, coef.y) + mix(v1X2.x, v1X2.y, coef.y) + mix(v1X3.x, v1X3.y, coef.y) + mix(v1X4.x, v1X4.y, coef.y);\n      vec2 v2X1 = mix(vec2(block6, block11), vec2(block7, block12), coef.xx);\n      vec2 v2X2 = mix(vec2(block7, block12), vec2(block8, block13), coef.xx);\n      vec2 v2X3 = mix(vec2(block8, block13), vec2(block9, block14), coef.xx);\n      vec2 v2X4 = mix(vec2(block9, block14), vec2(block10, block15), coef.xx);\n      float v2 = mix(v2X1.x, v2X1.y, coef.y) + mix(v2X2.x, v2X2.y, coef.y) + mix(v2X3.x, v2X3.y, coef.y) + mix(v2X4.x, v2X4.y, coef.y);\n      vec2 v3X1 = mix(vec2(block11, block16), vec2(block12, block17), coef.xx);\n      vec2 v3X2 = mix(vec2(block12, block17), vec2(block13, block18), coef.xx);\n      vec2 v3X3 = mix(vec2(block13, block18), vec2(block14, block19), coef.xx);\n      vec2 v3X4 = mix(vec2(block14, block19), vec2(block15, block20), coef.xx);\n      float v3 = mix(v3X1.x, v3X1.y, coef.y) + mix(v3X2.x, v3X2.y, coef.y) + mix(v3X3.x, v3X3.y, coef.y) + mix(v3X4.x, v3X4.y, coef.y);\n      vec2 v4X1 = mix(vec2(block16, block21), vec2(block17, block22), coef.xx);\n      vec2 v4X2 = mix(vec2(block17, block22), vec2(block18, block23), coef.xx);\n      vec2 v4X3 = mix(vec2(block18, block23), vec2(block19, block24), coef.xx);\n      vec2 v4X4 = mix(vec2(block19, block24), vec2(block20, block25), coef.xx);\n      float v4 = mix(v4X1.x, v4X1.y, coef.y) + mix(v4X2.x, v4X2.y, coef.y) + mix(v4X3.x, v4X3.y, coef.y) + mix(v4X4.x, v4X4.y, coef.y);\n      float fAvg = (v1 + v2 + v3 + v4) * 0.0625;\n      return fAvg;\n    }\n    bool GetShadowNDCPos(out vec3 shadowNDCPos, vec4 shadowPosWithDepthBias)\n    {\n    \tshadowNDCPos = shadowPosWithDepthBias.xyz / shadowPosWithDepthBias.w * 0.5 + 0.5;\n    \tif (shadowNDCPos.x < 0.0 || shadowNDCPos.x > 1.0 ||\n    \t\tshadowNDCPos.y < 0.0 || shadowNDCPos.y > 1.0 ||\n    \t\tshadowNDCPos.z < 0.0 || shadowNDCPos.z > 1.0) {\n    \t\treturn false;\n    \t}\n    \tshadowNDCPos.xy = cc_cameraPos.w == 1.0 ? vec2(shadowNDCPos.xy.x, 1.0 - shadowNDCPos.xy.y) : shadowNDCPos.xy;\n    \treturn true;\n    }\n    vec4 ApplyShadowDepthBias_FaceNormal(vec4 shadowPos, vec3 worldNormal, float normalBias, vec3 matViewDir0, vec3 matViewDir1, vec3 matViewDir2, vec2 projScaleXY)\n    {\n      vec4 newShadowPos = shadowPos;\n      if (normalBias > EPSILON_LOWP)\n      {\n        vec3 viewNormal = vec3(dot(matViewDir0, worldNormal), dot(matViewDir1, worldNormal), dot(matViewDir2, worldNormal));\n        if (viewNormal.z < 0.1)\n          newShadowPos.xy += viewNormal.xy * projScaleXY * normalBias * clamp(viewNormal.z, 0.001, 0.1);\n      }\n      return newShadowPos;\n    }\n    vec4 ApplyShadowDepthBias_FaceNormal(vec4 shadowPos, vec3 worldNormal, float normalBias, mat4 matLightView, vec2 projScaleXY)\n    {\n    \tvec4 newShadowPos = shadowPos;\n    \tif (normalBias > EPSILON_LOWP)\n    \t{\n    \t\tvec4 viewNormal = matLightView * vec4(worldNormal, 0.0);\n    \t\tif (viewNormal.z < 0.1)\n    \t\t\tnewShadowPos.xy += viewNormal.xy * projScaleXY * normalBias * clamp(viewNormal.z, 0.001, 0.1);\n    \t}\n    \treturn newShadowPos;\n    }\n    float GetViewSpaceDepthFromNDCDepth_Orthgraphic(float NDCDepth, float projScaleZ, float projBiasZ)\n    {\n    \treturn (NDCDepth - projBiasZ) / projScaleZ;\n    }\n    float GetViewSpaceDepthFromNDCDepth_Perspective(float NDCDepth, float homogenousDividW, float invProjScaleZ, float invProjBiasZ)\n    {\n    \treturn NDCDepth * invProjScaleZ + homogenousDividW * invProjBiasZ;\n    }\n    vec4 ApplyShadowDepthBias_Perspective(vec4 shadowPos, float viewspaceDepthBias)\n    {\n    \tvec3 viewSpacePos;\n    \tviewSpacePos.xy = shadowPos.xy * cc_shadowProjInfo.zw;\n    \tviewSpacePos.z = GetViewSpaceDepthFromNDCDepth_Perspective(shadowPos.z, shadowPos.w, cc_shadowInvProjDepthInfo.x, cc_shadowInvProjDepthInfo.y);\n    \tviewSpacePos.xyz += cc_shadowProjDepthInfo.z * normalize(viewSpacePos.xyz) * viewspaceDepthBias;\n    \tvec4 clipSpacePos;\n    \tclipSpacePos.xy = viewSpacePos.xy * cc_shadowProjInfo.xy;\n    \tclipSpacePos.zw = viewSpacePos.z * cc_shadowProjDepthInfo.xz + vec2(cc_shadowProjDepthInfo.y, 0.0);\n    \t#if CC_SHADOWMAP_USE_LINEAR_DEPTH\n    \t\tclipSpacePos.z = GetLinearDepthFromViewSpace(viewSpacePos.xyz, cc_shadowNFLSInfo.x, cc_shadowNFLSInfo.y);\n    \t\tclipSpacePos.z = (clipSpacePos.z * 2.0 - 1.0) * clipSpacePos.w;\n    \t#endif\n    \treturn clipSpacePos;\n    }\n    vec4 ApplyShadowDepthBias_Orthographic(vec4 shadowPos, float viewspaceDepthBias, float projScaleZ, float projBiasZ)\n    {\n    \tfloat coeffA = projScaleZ;\n    \tfloat coeffB = projBiasZ;\n    \tfloat viewSpacePos_z = GetViewSpaceDepthFromNDCDepth_Orthgraphic(shadowPos.z, projScaleZ, projBiasZ);\n    \tviewSpacePos_z += viewspaceDepthBias;\n    \tvec4 result = shadowPos;\n    \tresult.z = viewSpacePos_z * coeffA + coeffB;\n    \treturn result;\n    }\n    vec4 ApplyShadowDepthBias_PerspectiveLinearDepth(vec4 shadowPos, float viewspaceDepthBias, vec3 worldPos)\n    {\n      shadowPos.z = CCGetLinearDepth(worldPos, viewspaceDepthBias) * 2.0 - 1.0;\n      shadowPos.z *= shadowPos.w;\n      return shadowPos;\n    }\n    float CCGetDirLightShadowFactorHard (vec4 shadowPosWithDepthBias) {\n  \t  vec3 shadowNDCPos;\n  \t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n  \t\t  return 1.0;\n  \t  }\n      return NativePCFShadowFactorHard(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n    }\n    float CCGetDirLightShadowFactorSoft (vec4 shadowPosWithDepthBias) {\n  \t  vec3 shadowNDCPos;\n  \t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n  \t\t  return 1.0;\n  \t  }\n      return NativePCFShadowFactorSoft(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n    }\n    float CCGetDirLightShadowFactorSoft3X (vec4 shadowPosWithDepthBias) {\n  \t  vec3 shadowNDCPos;\n  \t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n  \t\t  return 1.0;\n  \t  }\n      return NativePCFShadowFactorSoft3X(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n    }\n    float CCGetDirLightShadowFactorSoft5X (vec4 shadowPosWithDepthBias) {\n  \t  vec3 shadowNDCPos;\n  \t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n  \t\t  return 1.0;\n  \t  }\n      return NativePCFShadowFactorSoft5X(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n    }\n    float CCGetSpotLightShadowFactorHard (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n  \t  vec3 shadowNDCPos;\n  \t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n  \t\t  return 1.0;\n  \t  }\n      return NativePCFShadowFactorHard(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n    }\n    float CCGetSpotLightShadowFactorSoft (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n  \t  vec3 shadowNDCPos;\n  \t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n  \t\t  return 1.0;\n  \t  }\n      return NativePCFShadowFactorSoft(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n    }\n    float CCGetSpotLightShadowFactorSoft3X (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n  \t  vec3 shadowNDCPos;\n  \t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n  \t\t  return 1.0;\n  \t  }\n      return NativePCFShadowFactorSoft3X(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n    }\n    float CCGetSpotLightShadowFactorSoft5X (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n  \t  vec3 shadowNDCPos;\n  \t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n  \t\t  return 1.0;\n  \t  }\n      return NativePCFShadowFactorSoft5X(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n    }\n    float CCSpotShadowFactorBase(out vec4 shadowPosWithDepthBias, vec4 shadowPos, vec3 worldPos, vec2 shadowBias)\n    {\n      float pcf = cc_shadowWHPBInfo.z;\n      vec4 pos = vec4(1.0);\n      #if CC_SHADOWMAP_USE_LINEAR_DEPTH\n        pos = ApplyShadowDepthBias_PerspectiveLinearDepth(shadowPos, shadowBias.x, worldPos);\n      #else\n        pos = ApplyShadowDepthBias_Perspective(shadowPos, shadowBias.x);\n      #endif\n      float realtimeShadow = 1.0;\n      if (pcf > 2.9) {\n        realtimeShadow = CCGetSpotLightShadowFactorSoft5X(pos, worldPos);\n      }else if (pcf > 1.9) {\n        realtimeShadow = CCGetSpotLightShadowFactorSoft3X(pos, worldPos);\n      }else if (pcf > 0.9) {\n        realtimeShadow = CCGetSpotLightShadowFactorSoft(pos, worldPos);\n      }else {\n        realtimeShadow = CCGetSpotLightShadowFactorHard(pos, worldPos);\n      }\n      shadowPosWithDepthBias = pos;\n      return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n    }\n    float CCShadowFactorBase(out vec4 shadowPosWithDepthBias, vec4 shadowPos, vec3 N, vec2 shadowBias)\n    {\n      vec4 pos = ApplyShadowDepthBias_FaceNormal(shadowPos, N, shadowBias.y, cc_matLightView, cc_shadowProjInfo.xy);\n      pos = ApplyShadowDepthBias_Orthographic(pos, shadowBias.x, cc_shadowProjDepthInfo.x, cc_shadowProjDepthInfo.y);\n      float realtimeShadow = 1.0;\n      #if CC_DIR_SHADOW_PCF_TYPE == 3\n        realtimeShadow = CCGetDirLightShadowFactorSoft5X(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 2\n        realtimeShadow =  CCGetDirLightShadowFactorSoft3X(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 1\n        realtimeShadow = CCGetDirLightShadowFactorSoft(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 0\n        realtimeShadow = CCGetDirLightShadowFactorHard(pos);\n      #endif\n      shadowPosWithDepthBias = pos;\n      return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n    }\n    #if CC_SUPPORT_CASCADED_SHADOW_MAP\n      bool CCGetCSMLevelWithTransition(out highp float ratio, vec3 clipPos) {\n        highp float maxRange = 1.0 - cc_csmSplitsInfo.x;\n        highp float minRange = cc_csmSplitsInfo.x;\n        highp float thresholdInvert = 1.0 / cc_csmSplitsInfo.x;\n        ratio = 0.0;\n        if (clipPos.x <= minRange) {\n          ratio = clipPos.x * thresholdInvert;\n          return true;\n        }\n        if (clipPos.x >= maxRange) {\n          ratio = 1.0 - (clipPos.x - maxRange) * thresholdInvert;\n          return true;\n        }\n        if (clipPos.y <= minRange) {\n          ratio = clipPos.y  * thresholdInvert;\n          return true;\n        }\n        if (clipPos.y >= maxRange) {\n          ratio = 1.0 - (clipPos.y - maxRange) * thresholdInvert;\n          return true;\n        }\n        return false;\n      }\n      bool CCHasCSMLevel(int level, vec3 worldPos) {\n        highp float layerThreshold = cc_csmViewDir0[0].w;\n        bool hasLevel = false;\n        for (int i = 0; i < 4; i++) {\n          if (i == level) {\n            vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n            vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n            if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n                clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n                clipPos.z >= 0.0 && clipPos.z <= 1.0) {\n              hasLevel = true;\n            }\n          }\n        }\n        return hasLevel;\n      }\n      void CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos, int level) {\n        highp float layerThreshold = cc_csmViewDir0[0].w;\n        for (int i = 0; i < 4; i++) {\n          vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n          if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n              clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n              clipPos.z >= 0.0 && clipPos.z <= 1.0 && i == level) {\n            csmPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n            csmPos.xy = csmPos.xy * cc_csmAtlas[i].xy + cc_csmAtlas[i].zw;\n            shadowProjDepthInfo = cc_csmProjDepthInfo[i];\n            shadowProjInfo = cc_csmProjInfo[i];\n            shadowViewDir0 = cc_csmViewDir0[i].xyz;\n            shadowViewDir1 = cc_csmViewDir1[i].xyz;\n            shadowViewDir2 = cc_csmViewDir2[i].xyz;\n          }\n        }\n      }\n      int CCGetCSMLevel(out bool isTransitionArea, out highp float transitionRatio, out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos)\n      {\n        int level = -1;\n        highp float layerThreshold = cc_csmViewDir0[0].w;\n        for (int i = 0; i < 4; i++) {\n          vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n          if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n              clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n              clipPos.z >= 0.0 && clipPos.z <= 1.0 && level < 0) {\n            #if CC_CASCADED_LAYERS_TRANSITION\n              isTransitionArea = CCGetCSMLevelWithTransition(transitionRatio, clipPos);\n            #endif\n            csmPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n            csmPos.xy = csmPos.xy * cc_csmAtlas[i].xy + cc_csmAtlas[i].zw;\n            shadowProjDepthInfo = cc_csmProjDepthInfo[i];\n            shadowProjInfo = cc_csmProjInfo[i];\n            shadowViewDir0 = cc_csmViewDir0[i].xyz;\n            shadowViewDir1 = cc_csmViewDir1[i].xyz;\n            shadowViewDir2 = cc_csmViewDir2[i].xyz;\n            level = i;\n          }\n        }\n        return level;\n      }\n      int CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos)\n      {\n        bool isTransitionArea = false;\n        highp float transitionRatio = 0.0;\n        return CCGetCSMLevel(isTransitionArea, transitionRatio, csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n      }\n      float CCCSMFactorBase(out vec4 csmPos, out vec4 csmPosWithBias, vec3 worldPos, vec3 N, vec2 shadowBias)\n      {\n        bool isTransitionArea = false;\n        highp float ratio = 0.0;\n        csmPos = vec4(1.0);\n        vec4 shadowProjDepthInfo, shadowProjInfo;\n        vec3 shadowViewDir0, shadowViewDir1, shadowViewDir2;\n        int level = -1;\n        #if CC_CASCADED_LAYERS_TRANSITION\n          level = CCGetCSMLevel(isTransitionArea, ratio, csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n        #else\n          level = CCGetCSMLevel(csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n        #endif\n        if (level < 0) { return 1.0; }\n        vec4 pos = ApplyShadowDepthBias_FaceNormal(csmPos, N, shadowBias.y, shadowViewDir0, shadowViewDir1, shadowViewDir2, shadowProjInfo.xy);\n        pos = ApplyShadowDepthBias_Orthographic(pos, shadowBias.x, shadowProjDepthInfo.x, shadowProjDepthInfo.y);\n        csmPosWithBias = pos;\n        float realtimeShadow = 1.0;\n        #if CC_DIR_SHADOW_PCF_TYPE == 3\n          realtimeShadow = CCGetDirLightShadowFactorSoft5X(pos);\n        #endif\n        #if CC_DIR_SHADOW_PCF_TYPE == 2\n          realtimeShadow = CCGetDirLightShadowFactorSoft3X(pos);\n        #endif\n        #if CC_DIR_SHADOW_PCF_TYPE == 1\n          realtimeShadow = CCGetDirLightShadowFactorSoft(pos);\n        #endif\n        #if CC_DIR_SHADOW_PCF_TYPE == 0\n          realtimeShadow = CCGetDirLightShadowFactorHard(pos);\n        #endif\n        #if CC_CASCADED_LAYERS_TRANSITION\n          vec4 nextCSMPos = vec4(1.0);\n          vec4 nextShadowProjDepthInfo, nextShadowProjInfo;\n          vec3 nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2;\n          float nextRealtimeShadow = 1.0;\n          CCGetCSMLevel(nextCSMPos, nextShadowProjDepthInfo, nextShadowProjInfo, nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2, worldPos, level + 1);\n          bool hasNextLevel = CCHasCSMLevel(level + 1, worldPos);\n          if (hasNextLevel && isTransitionArea) {\n            vec4 nexPos = ApplyShadowDepthBias_FaceNormal(nextCSMPos, N, shadowBias.y, nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2, nextShadowProjInfo.xy);\n            nexPos = ApplyShadowDepthBias_Orthographic(nexPos, shadowBias.x, nextShadowProjDepthInfo.x, nextShadowProjDepthInfo.y);\n            #if CC_DIR_SHADOW_PCF_TYPE == 3\n              nextRealtimeShadow = CCGetDirLightShadowFactorSoft5X(nexPos);\n            #endif\n            #if CC_DIR_SHADOW_PCF_TYPE == 2\n              nextRealtimeShadow = CCGetDirLightShadowFactorSoft3X(nexPos);\n            #endif\n            #if CC_DIR_SHADOW_PCF_TYPE == 1\n              nextRealtimeShadow = CCGetDirLightShadowFactorSoft(nexPos);\n            #endif\n            #if CC_DIR_SHADOW_PCF_TYPE == 0\n              nextRealtimeShadow = CCGetDirLightShadowFactorHard(nexPos);\n            #endif\n            return mix(mix(nextRealtimeShadow, realtimeShadow, ratio), 1.0, cc_shadowNFLSInfo.w);\n          }\n          return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n        #else\n          return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n        #endif\n      }\n    #else\n      int CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos) {\n        return -1;\n      }\n      float CCCSMFactorBase(out vec4 csmPos, out vec4 csmPosWithBias, vec3 worldPos, vec3 N, vec2 shadowBias) {\n        csmPos = cc_matLightViewProj * vec4(worldPos, 1.0);\n        return CCShadowFactorBase(csmPosWithBias, csmPos, N, shadowBias);\n      }\n    #endif\n    float CCShadowFactorBase(vec4 shadowPos, vec3 N, vec2 shadowBias) {\n      vec4 shadowPosWithDepthBias;\n      return CCShadowFactorBase(shadowPosWithDepthBias, shadowPos, N, shadowBias);\n    }\n    float CCCSMFactorBase(vec3 worldPos, vec3 N, vec2 shadowBias) {\n      vec4 csmPos, csmPosWithBias;\n      return CCCSMFactorBase(csmPos, csmPosWithBias, worldPos, N, shadowBias);\n    }\n    float CCSpotShadowFactorBase(vec4 shadowPos, vec3 worldPos, vec2 shadowBias)\n    {\n      vec4 shadowPosWithDepthBias;\n      return CCSpotShadowFactorBase(shadowPosWithDepthBias, shadowPos, worldPos, shadowBias);\n    }\n  #endif\n  highp float decode32 (highp vec4 rgba) {\n    rgba = rgba * 255.0;\n    highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n    highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n    highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n    return Sign * exp2(Exponent - 23.0) * Mantissa;\n  }\n  vec4 packRGBE (vec3 rgb) {\n    highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n    highp float e = 128.0;\n    if (maxComp > 0.0001) {\n      e = log(maxComp) / log(1.1);\n      e = ceil(e);\n      e = clamp(e + 128.0, 0.0, 255.0);\n    }\n    highp float sc = 1.0 / pow(1.1, e - 128.0);\n    vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n    vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n    return vec4(encode_rounded, e) / 255.0;\n  }\n  vec3 unpackRGBE (vec4 rgbe) {\n    return rgbe.rgb * pow(1.1, rgbe.a * 255.0 - 128.0);\n  }\n  vec4 fragTextureLod (sampler2D tex, vec2 coord, float lod) {\n      #ifdef GL_EXT_shader_texture_lod\n        return texture2DLodEXT(tex, coord, lod);\n      #else\n        return texture2D(tex, coord, lod);\n      #endif\n  }\n  vec4 fragTextureLod (samplerCube tex, vec3 coord, float lod) {\n      #ifdef GL_EXT_shader_texture_lod\n        return textureCubeLodEXT(tex, coord, lod);\n      #else\n        return textureCube(tex, coord, lod);\n      #endif\n  }\n  uniform samplerCube cc_environment;\n  vec3 CalculateReflectDirection(vec3 N, vec3 V, float NoV)\n  {\n    float sideSign = NoV < 0.0 ? -1.0 : 1.0;\n    N *= sideSign;\n    return reflect(-V, N);\n  }\n  vec3 CalculatePlanarReflectPositionOnPlane(vec3 N, vec3 V, vec3 worldPos, vec4 plane, vec3 cameraPos, float probeReflectedDepth)\n  {\n    float distPixelToPlane = -dot(plane, vec4(worldPos, 1.0));\n    plane.w += distPixelToPlane;\n    float distCameraToPlane = abs(-dot(plane, vec4(cameraPos, 1.0)));\n    vec3 planeN = plane.xyz;\n    vec3 virtualCameraPos = cameraPos - 2.0 * distCameraToPlane * planeN;\n    vec3 bumpedR = normalize(reflect(-V, N));\n    vec3 reflectedPointPos = worldPos + probeReflectedDepth * bumpedR;\n    vec3 virtualCameraToReflectedPoint = normalize(reflectedPointPos - virtualCameraPos);\n    float y = distCameraToPlane / max(EPSILON_LOWP, dot(planeN, virtualCameraToReflectedPoint));\n    return virtualCameraPos + y * virtualCameraToReflectedPoint;\n  }\n  vec4 CalculateBoxProjectedDirection(vec3 R, vec3 worldPos, vec3 cubeCenterPos, vec3 cubeBoxHalfSize)\n  {\n    vec3 W = worldPos - cubeCenterPos;\n    vec3 projectedLength = (sign(R) * cubeBoxHalfSize - W) / (R + vec3(EPSILON));\n    float len = min(min(projectedLength.x, projectedLength.y), projectedLength.z);\n    vec3 P = W + len * R;\n    float weight = len < 0.0 ? 0.0 : 1.0;\n    return vec4(P, weight);\n  }\n  #if CC_USE_IBL\n    #if CC_USE_DIFFUSEMAP\n      uniform samplerCube cc_diffuseMap;\n    #endif\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    uniform samplerCube cc_reflectionProbeCubemap;\n    uniform sampler2D cc_reflectionProbePlanarMap;\n    uniform sampler2D cc_reflectionProbeDataMap;\n    uniform samplerCube cc_reflectionProbeBlendCubemap;\n    uniform highp vec4 cc_reflectionProbeData1;\n  uniform highp vec4 cc_reflectionProbeData2;\n  uniform highp vec4 cc_reflectionProbeBlendData1;\n  uniform highp vec4 cc_reflectionProbeBlendData2;\n    vec4 GetTexData(sampler2D dataMap, float dataMapWidth, float x, float uv_y)\n    {\n      return vec4(\n          decode32(texture2D(dataMap, vec2(((x + 0.5)/dataMapWidth), uv_y))),\n          decode32(texture2D(dataMap, vec2(((x + 1.5)/dataMapWidth), uv_y))),\n          decode32(texture2D(dataMap, vec2(((x + 2.5)/dataMapWidth), uv_y))),\n          decode32(texture2D(dataMap, vec2(((x + 3.5)/dataMapWidth), uv_y)))\n        );\n    }\n    void GetPlanarReflectionProbeData(out vec4 plane, out float planarReflectionDepthScale, out float mipCount, float probeId)\n    {\n        #if USE_INSTANCING\n          float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n          float dataMapWidth = 12.0;\n          vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n          vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n          plane.xyz = texData1.xyz;\n          plane.w = texData2.x;\n          planarReflectionDepthScale = texData2.y;\n          mipCount = texData2.z;\n        #else\n          plane = cc_reflectionProbeData1;\n          planarReflectionDepthScale = cc_reflectionProbeData2.x;\n          mipCount = cc_reflectionProbeData2.w;\n        #endif\n    }\n    void GetCubeReflectionProbeData(out vec3 centerPos, out vec3 boxHalfSize, out float mipCount, float probeId)\n    {\n        #if USE_INSTANCING\n          float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n          float dataMapWidth = 12.0;\n          vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n          vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n          vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n          centerPos = texData1.xyz;\n          boxHalfSize = texData2.xyz;\n          mipCount = texData3.x;\n        #else\n          centerPos = cc_reflectionProbeData1.xyz;\n          boxHalfSize = cc_reflectionProbeData2.xyz;\n          mipCount = cc_reflectionProbeData2.w;\n        #endif\n        if (mipCount > 1000.0) mipCount -= 1000.0;\n    }\n    bool isReflectProbeUsingRGBE(float probeId)\n    {\n      #if USE_INSTANCING\n          float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n          float dataMapWidth = 12.0;\n          vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n          return texData3.x > 1000.0;\n      #else\n        return cc_reflectionProbeData2.w > 1000.0;\n      #endif\n    }\n    bool isBlendReflectProbeUsingRGBE(float probeId)\n    {\n      #if USE_INSTANCING\n          float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n          float dataMapWidth = 12.0;\n          vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n          return texData3.x > 1000.0;\n      #else\n        return cc_reflectionProbeBlendData2.w > 1000.0;\n      #endif\n    }\n    void GetBlendCubeReflectionProbeData(out vec3 centerPos, out vec3 boxHalfSize, out float mipCount, float probeId)\n    {\n        #if USE_INSTANCING\n          float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n          float dataMapWidth = 12.0;\n          vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n          vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n          vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n          centerPos = texData1.xyz;\n          boxHalfSize = texData2.xyz;\n          mipCount = texData3.x;\n        #else\n          centerPos = cc_reflectionProbeBlendData1.xyz;\n          boxHalfSize = cc_reflectionProbeBlendData2.xyz;\n          mipCount = cc_reflectionProbeBlendData2.w;\n        #endif\n        if (mipCount > 1000.0) mipCount -= 1000.0;\n    }\n  #endif\n  #if CC_USE_LIGHT_PROBE\n  #if CC_USE_LIGHT_PROBE\n    #if USE_INSTANCING\n      varying mediump vec4 v_sh_linear_const_r;\n      varying mediump vec4 v_sh_linear_const_g;\n      varying mediump vec4 v_sh_linear_const_b;\n    #else\n      uniform vec4 cc_sh_linear_const_r;\n  uniform vec4 cc_sh_linear_const_g;\n  uniform vec4 cc_sh_linear_const_b;\n  uniform vec4 cc_sh_quadratic_r;\n  uniform vec4 cc_sh_quadratic_g;\n  uniform vec4 cc_sh_quadratic_b;\n  uniform vec4 cc_sh_quadratic_a;\n    #endif\n    #if CC_USE_LIGHT_PROBE\n    vec3 SHEvaluate(vec3 normal)\n    {\n        vec3 result;\n    #if USE_INSTANCING\n        vec4 normal4 = vec4(normal, 1.0);\n        result.r = dot(v_sh_linear_const_r, normal4);\n        result.g = dot(v_sh_linear_const_g, normal4);\n        result.b = dot(v_sh_linear_const_b, normal4);\n    #else\n        vec4 normal4 = vec4(normal, 1.0);\n        result.r = dot(cc_sh_linear_const_r, normal4);\n        result.g = dot(cc_sh_linear_const_g, normal4);\n        result.b = dot(cc_sh_linear_const_b, normal4);\n        vec4 n14 = normal.xyzz * normal.yzzx;\n        float n5 = normal.x * normal.x - normal.y * normal.y;\n        result.r += dot(cc_sh_quadratic_r, n14);\n        result.g += dot(cc_sh_quadratic_g, n14);\n        result.b += dot(cc_sh_quadratic_b, n14);\n        result += (cc_sh_quadratic_a.rgb * n5);\n    #endif\n      #if CC_USE_HDR\n        result *= cc_exposure.w * cc_exposure.x;\n      #endif\n      return result;\n    }\n    #endif\n  #endif\n  #endif\n  float GGXMobile (float roughness, float NoH, vec3 H, vec3 N) {\n    vec3 NxH = cross(N, H);\n    float OneMinusNoHSqr = dot(NxH, NxH);\n    float a = roughness * roughness;\n    float n = NoH * a;\n    float p = a / max(EPSILON, OneMinusNoHSqr + n * n);\n    return p * p;\n  }\n  float CalcSpecular (float roughness, float NoH, vec3 H, vec3 N) {\n    return (roughness * 0.25 + 0.25) * GGXMobile(roughness, NoH, H, N);\n  }\n  vec3 BRDFApprox (vec3 specular, float roughness, float NoV) {\n    const vec4 c0 = vec4(-1.0, -0.0275, -0.572, 0.022);\n    const vec4 c1 = vec4(1.0, 0.0425, 1.04, -0.04);\n    vec4 r = roughness * c0 + c1;\n    float a004 = min(r.x * r.x, exp2(-9.28 * NoV)) * r.x + r.y;\n    vec2 AB = vec2(-1.04, 1.04) * a004 + r.zw;\n    AB.y *= clamp(50.0 * specular.g, 0.0, 1.0);\n    return max(vec3(0.0), specular * AB.x + AB.y);\n  }\n  #if USE_REFLECTION_DENOISE\n    vec3 GetEnvReflectionWithMipFiltering(vec3 R, float roughness, float mipCount, float denoiseIntensity, vec2 screenUV) {\n      #if CC_USE_IBL\n      \tfloat mip = roughness * (mipCount - 1.0);\n      \tfloat delta = (dot(dFdx(R), dFdy(R))) * 1000.0;\n      \tfloat mipBias = mix(0.0, 5.0, clamp(delta, 0.0, 1.0));\n        #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n          vec4 biased = fragTextureLod(cc_reflectionProbeCubemap, R, mip + mipBias);\n       \t  vec4 filtered = textureCube(cc_reflectionProbeCubemap, R);\n        #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_PLANAR\n          vec4 biased = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, mip + mipBias);\n          vec4 filtered = texture2D(cc_reflectionProbePlanarMap, screenUV);\n        #else\n          vec4 biased = fragTextureLod(cc_environment, R, mip + mipBias);\n       \t  vec4 filtered = textureCube(cc_environment, R);\n        #endif\n        #if CC_USE_IBL == 2 || CC_USE_REFLECTION_PROBE != REFLECTION_PROBE_TYPE_NONE\n          biased.rgb = unpackRGBE(biased);\n        \tfiltered.rgb = unpackRGBE(filtered);\n        #else\n        \tbiased.rgb = SRGBToLinear(biased.rgb);\n        \tfiltered.rgb = SRGBToLinear(filtered.rgb);\n        #endif\n        return mix(biased.rgb, filtered.rgb, denoiseIntensity);\n      #else\n        return vec3(0.0, 0.0, 0.0);\n      #endif\n    }\n  #endif\n  struct StandardSurface {\n    vec4 albedo;\n        #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n        vec3 position, position_fract_part;\n        #else\n        vec3 position;\n        #endif\n    vec3 normal;\n    vec3 emissive;\n    vec4 lightmap;\n    float lightmap_test;\n    float roughness;\n    float metallic;\n    float occlusion;\n    float specularIntensity;\n    #if CC_RECEIVE_SHADOW\n      vec2 shadowBias;\n    #endif\n    #if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n      float reflectionProbeId;\n    #endif\n    #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n      float reflectionProbeBlendId;\n      float reflectionProbeBlendFactor;\n    #endif\n  };\n   vec3 SampleReflectionProbe(samplerCube tex, vec3 R, float roughness, float mipCount, bool isRGBE) {\n      vec4 envmap = fragTextureLod(tex, R, roughness * (mipCount - 1.0));\n      if (isRGBE)\n        return unpackRGBE(envmap);\n      else\n        return SRGBToLinear(envmap.rgb);\n    }\n  vec4 CCStandardShadingBase (StandardSurface s, vec4 shadowPos) {\n    vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n    vec3 specular = mix(vec3(0.08 * s.specularIntensity), s.albedo.rgb, s.metallic);\n    vec3 position;\n        #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n        position = unpackHighpData(s.position, s.position_fract_part);\n        #else\n        position = s.position;\n        #endif\n    vec3 N = normalize(s.normal);\n    vec3 V = normalize(cc_cameraPos.xyz - position);\n    vec3 L = normalize(-cc_mainLitDir.xyz);\n    float NL = max(dot(N, L), 0.0);\n    float shadow = 1.0;\n    #if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n      if (NL > 0.0 && cc_mainLitDir.w > 0.0) {\n        #if CC_DIR_LIGHT_SHADOW_TYPE == 2\n          shadow = CCCSMFactorBase(position, N, s.shadowBias);\n        #endif\n        #if CC_DIR_LIGHT_SHADOW_TYPE == 1\n          shadow = CCShadowFactorBase(shadowPos, N, s.shadowBias);\n        #endif\n      }\n    #endif\n    vec3 finalColor = vec3(0.0);\n    #if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n      vec3 lightmap = s.lightmap.rgb;\n      #if CC_USE_HDR\n          lightmap.rgb *= cc_exposure.w * cc_exposure.x;\n      #endif\n      #if CC_USE_LIGHTMAP == LIGHT_MAP_TYPE_INDIRECT_OCCLUSION\n        shadow *= s.lightmap.a;\n        finalColor += diffuse * lightmap.rgb;\n      #else\n        finalColor += diffuse * lightmap.rgb * shadow;\n      #endif\n      s.occlusion *= s.lightmap_test;\n    #endif\n    #if !CC_DISABLE_DIRECTIONAL_LIGHT\n      float NV = max(abs(dot(N, V)), 0.0);\n      specular = BRDFApprox(specular, s.roughness, NV);\n      vec3 H = normalize(L + V);\n      float NH = max(dot(N, H), 0.0);\n      vec3 lightingColor = NL * cc_mainLitColor.rgb * cc_mainLitColor.w;\n      vec3 diffuseContrib = diffuse / PI;\n      vec3 specularContrib = specular * CalcSpecular(s.roughness, NH, H, N);\n      vec3 dirlightContrib = (diffuseContrib + specularContrib);\n      dirlightContrib *= shadow;\n      finalColor += lightingColor * dirlightContrib;\n    #endif\n    float fAmb = max(EPSILON, 0.5 - N.y * 0.5);\n    vec3 ambDiff = mix(cc_ambientSky.rgb, cc_ambientGround.rgb, fAmb);\n    vec3 env = vec3(0.0), rotationDir;\n    #if CC_USE_IBL\n      #if CC_USE_DIFFUSEMAP && !CC_USE_LIGHT_PROBE\n        rotationDir = RotationVecFromAxisY(N.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n        vec4 diffuseMap = textureCube(cc_diffuseMap, rotationDir);\n        #if CC_USE_DIFFUSEMAP == 2\n          ambDiff = unpackRGBE(diffuseMap);\n        #else\n          ambDiff = SRGBToLinear(diffuseMap.rgb);\n        #endif\n      #endif\n      #if !CC_USE_REFLECTION_PROBE\n        vec3 R = normalize(reflect(-V, N));\n        rotationDir = RotationVecFromAxisY(R.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n        #if USE_REFLECTION_DENOISE && !CC_IBL_CONVOLUTED\n          env = GetEnvReflectionWithMipFiltering(rotationDir, s.roughness, cc_ambientGround.w, 0.6, vec2(0.0));\n        #else\n          vec4 envmap = fragTextureLod(cc_environment, rotationDir, s.roughness * (cc_ambientGround.w - 1.0));\n          #if CC_USE_IBL == 2\n            env = unpackRGBE(envmap);\n          #else\n            env = SRGBToLinear(envmap.rgb);\n          #endif\n        #endif\n      #endif\n    #endif\n    float lightIntensity = cc_ambientSky.w;\n    #if CC_USE_REFLECTION_PROBE\n      vec4 probe = vec4(0.0);\n      vec3 R = normalize(reflect(-V, N));\n      #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n        if(s.reflectionProbeId < 0.0){\n          env = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2);\n        }else{\n          vec3 centerPos, boxHalfSize;\n          float mipCount;\n          GetCubeReflectionProbeData(centerPos, boxHalfSize, mipCount, s.reflectionProbeId);\n          vec4 fixedR = CalculateBoxProjectedDirection(R, position, centerPos, boxHalfSize);\n          env = mix(SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2) * lightIntensity,\n            SampleReflectionProbe(cc_reflectionProbeCubemap, fixedR.xyz, s.roughness, mipCount, isReflectProbeUsingRGBE(s.reflectionProbeId)), fixedR.w);\n        }\n      #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_PLANAR\n        if(s.reflectionProbeId < 0.0){\n          vec2 screenUV = GetPlanarReflectScreenUV(s.position, cc_matViewProj, cc_cameraPos.w, V, R);\n          probe = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, 1.0);\n        }else{\n          vec4 plane;\n          float planarReflectionDepthScale, mipCount;\n          GetPlanarReflectionProbeData(plane, planarReflectionDepthScale, mipCount, s.reflectionProbeId);\n          R = normalize(CalculateReflectDirection(N, V, max(abs(dot(N, V)), 0.0)));\n          vec3 worldPosOffset = CalculatePlanarReflectPositionOnPlane(N, V, s.position, plane, cc_cameraPos.xyz, planarReflectionDepthScale);\n          vec2 screenUV = GetPlanarReflectScreenUV(worldPosOffset, cc_matViewProj, cc_cameraPos.w, V, R);\n          probe = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, mipCount);\n        }\n        env = unpackRGBE(probe);\n      #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n        if (s.reflectionProbeId < 0.0) {\n          env = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2);\n        } else {\n          vec3 centerPos, boxHalfSize;\n          float mipCount;\n          GetCubeReflectionProbeData(centerPos, boxHalfSize, mipCount, s.reflectionProbeId);\n          vec4 fixedR = CalculateBoxProjectedDirection(R, s.position, centerPos, boxHalfSize);\n          env = SampleReflectionProbe(cc_reflectionProbeCubemap, fixedR.xyz, s.roughness, mipCount, isReflectProbeUsingRGBE(s.reflectionProbeId));\n          if (s.reflectionProbeBlendId < 0.0) {\n            vec3 skyBoxEnv = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2) * lightIntensity;\n            #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n              env = mix(env, skyBoxEnv, s.reflectionProbeBlendFactor);\n            #else\n              env = mix(skyBoxEnv, env, fixedR.w);\n            #endif\n          } else {\n            vec3 centerPosBlend, boxHalfSizeBlend;\n            float mipCountBlend;\n            GetBlendCubeReflectionProbeData(centerPosBlend, boxHalfSizeBlend, mipCountBlend, s.reflectionProbeBlendId);\n            vec4 fixedRBlend = CalculateBoxProjectedDirection(R, s.position, centerPosBlend, boxHalfSizeBlend);\n            vec3 probe1 = SampleReflectionProbe(cc_reflectionProbeBlendCubemap, fixedRBlend.xyz, s.roughness, mipCountBlend, isBlendReflectProbeUsingRGBE(s.reflectionProbeBlendId));\n            env = mix(env, probe1, s.reflectionProbeBlendFactor);\n          }\n        }\n      #endif\n    #endif\n    #if CC_USE_REFLECTION_PROBE\n      lightIntensity = s.reflectionProbeId < 0.0 ? lightIntensity : 1.0;\n    #endif\n    finalColor += env * lightIntensity * specular * s.occlusion;\n  #if CC_USE_LIGHT_PROBE\n    finalColor += SHEvaluate(N) * diffuse * s.occlusion;\n  #endif\n    finalColor += ambDiff.rgb * cc_ambientSky.w * diffuse * s.occlusion;\n    finalColor += s.emissive;\n    return vec4(finalColor, s.albedo.a);\n  }\n  #if CC_PIPELINE_TYPE == 0\n    #define LIGHTS_PER_PASS 1\n  #else\n    #define LIGHTS_PER_PASS 10\n  #endif\n  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 0\n  uniform highp vec4 cc_lightPos[LIGHTS_PER_PASS];\n  uniform vec4 cc_lightColor[LIGHTS_PER_PASS];\n  uniform vec4 cc_lightSizeRangeAngle[LIGHTS_PER_PASS];\n  uniform vec4 cc_lightDir[LIGHTS_PER_PASS];\n  uniform vec4 cc_lightBoundingSizeVS[LIGHTS_PER_PASS];\n  #endif\n  float SmoothDistAtt (float distSqr, float invSqrAttRadius) {\n    float factor = distSqr * invSqrAttRadius;\n    float smoothFactor = clamp(1.0 - factor * factor, 0.0, 1.0);\n    return smoothFactor * smoothFactor;\n  }\n  float GetDistAtt (float distSqr, float invSqrAttRadius) {\n    float attenuation = 1.0 / max(distSqr, 0.01*0.01);\n    attenuation *= SmoothDistAtt(distSqr , invSqrAttRadius);\n    return attenuation;\n  }\n  float GetAngleAtt (vec3 L, vec3 litDir, float litAngleScale, float litAngleOffset) {\n    float cd = dot(litDir, L);\n    float attenuation = clamp(cd * litAngleScale + litAngleOffset, 0.0, 1.0);\n    return (attenuation * attenuation);\n  }\n  float GetOutOfRange (vec3 worldPos, vec3 lightPos, vec3 lookAt, vec3 right, vec3 BoundingHalfSizeVS) {\n    vec3 v = vec3(0.0);\n    vec3 up = cross(right, lookAt);\n    worldPos -= lightPos;\n    v.x = dot(worldPos, right);\n    v.y = dot(worldPos, up);\n    v.z = dot(worldPos, lookAt);\n    vec3 result = step(abs(v), BoundingHalfSizeVS);\n    return result.x * result.y * result.z;\n  }\n  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 0\n  vec4 CCStandardShadingAdditive (StandardSurface s, vec4 shadowPos) {\n    vec3 position;\n        #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n        position = unpackHighpData(s.position, s.position_fract_part);\n        #else\n        position = s.position;\n        #endif\n    vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n    vec3 specular = mix(vec3(0.04), s.albedo.rgb, s.metallic);\n    vec3 diffuseContrib = diffuse / PI;\n    vec3 N = normalize(s.normal);\n    vec3 V = normalize(cc_cameraPos.xyz - position);\n    float NV = max(abs(dot(N, V)), 0.0);\n    specular = BRDFApprox(specular, s.roughness, NV);\n    vec3 finalColor = vec3(0.0);\n    int numLights = CC_PIPELINE_TYPE == 0 ? LIGHTS_PER_PASS : int(cc_lightDir[0].w);\n    for (int i = 0; i < LIGHTS_PER_PASS; i++) {\n      if (i >= numLights) break;\n      vec3 SLU = IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w) ? -cc_lightDir[i].xyz : cc_lightPos[i].xyz - position;\n      vec3 SL = normalize(SLU);\n      vec3 SH = normalize(SL + V);\n      float SNL = max(dot(N, SL), 0.0);\n      float SNH = max(dot(N, SH), 0.0);\n      vec3 lspec = specular * CalcSpecular(s.roughness, SNH, SH, N);\n      float illum = 1.0;\n      float att = 1.0;\n      if (IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w)) {\n        att = GetOutOfRange(position, cc_lightPos[i].xyz, cc_lightDir[i].xyz, cc_lightSizeRangeAngle[i].xyz, cc_lightBoundingSizeVS[i].xyz);\n      } else {\n        float distSqr = dot(SLU, SLU);\n        float litRadius = cc_lightSizeRangeAngle[i].x;\n        float litRadiusSqr = litRadius * litRadius;\n        illum = (IS_POINT_LIGHT(cc_lightPos[i].w) || IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w)) ? 1.0 : litRadiusSqr / max(litRadiusSqr, distSqr);\n        float attRadiusSqrInv = 1.0 / max(cc_lightSizeRangeAngle[i].y, 0.01);\n        attRadiusSqrInv *= attRadiusSqrInv;\n        att = GetDistAtt(distSqr, attRadiusSqrInv);\n        if (IS_SPOT_LIGHT(cc_lightPos[i].w)) {\n          float cosInner = max(dot(-cc_lightDir[i].xyz, SL), 0.01);\n          float cosOuter = cc_lightSizeRangeAngle[i].z;\n          float strength = clamp(cc_lightBoundingSizeVS[i].w, 0.0, 1.0);\n          float litAngleScale = 1.0 / max(0.001, mix(cosInner, 1.0, strength) - cosOuter);\n          float litAngleOffset = -cosOuter * litAngleScale;\n          att *= GetAngleAtt(SL, -cc_lightDir[i].xyz, litAngleScale, litAngleOffset);\n        }\n      }\n      float shadow = 1.0;\n      #if CC_RECEIVE_SHADOW  && CC_SHADOW_TYPE == 2\n        if (IS_SPOT_LIGHT(cc_lightPos[i].w) && cc_lightSizeRangeAngle[i].w > 0.0) {\n          shadow = CCSpotShadowFactorBase(shadowPos, position, s.shadowBias);\n        }\n      #endif\n      finalColor += SNL * cc_lightColor[i].rgb * shadow * cc_lightColor[i].w * illum * att * (diffuseContrib + lspec);\n    }\n    return vec4(finalColor, 0.0);\n  }\n  #endif\n#if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 1\n  readonly buffer b_ccLightsBuffer { vec4 b_ccLights[]; };\n  readonly buffer b_clusterLightIndicesBuffer { uint b_clusterLightIndices[]; };\n  readonly buffer b_clusterLightGridBuffer { uvec4 b_clusterLightGrid[]; };\n  struct CCLight\n  {\n    vec4 cc_lightPos;\n    vec4 cc_lightColor;\n    vec4 cc_lightSizeRangeAngle;\n    vec4 cc_lightDir;\n    vec4 cc_lightBoundingSizeVS;\n  };\n  struct Cluster\n  {\n    vec3 minBounds;\n    vec3 maxBounds;\n  };\n  struct LightGrid\n  {\n    uint offset;\n    uint ccLights;\n  };\n  CCLight getCCLight(uint i)\n  {\n    CCLight light;\n    light.cc_lightPos = b_ccLights[5u * i + 0u];\n    light.cc_lightColor = b_ccLights[5u * i + 1u];\n    light.cc_lightSizeRangeAngle = b_ccLights[5u * i + 2u];\n    light.cc_lightDir = b_ccLights[5u * i + 3u];\n    light.cc_lightBoundingSizeVS = b_ccLights[5u * i + 4u];\n    return light;\n  }\n  LightGrid getLightGrid(uint cluster)\n  {\n    uvec4 gridvec = b_clusterLightGrid[cluster];\n    LightGrid grid;\n    grid.offset = gridvec.x;\n    grid.ccLights = gridvec.y;\n    return grid;\n  }\n  uint getGridLightIndex(uint start, uint offset)\n  {\n    return b_clusterLightIndices[start + offset];\n  }\n  uint getClusterZIndex(vec4 worldPos)\n  {\n    float scale = float(24u) / log(cc_nearFar.y / cc_nearFar.x);\n    float bias = -(float(24u) * log(cc_nearFar.x) / log(cc_nearFar.y / cc_nearFar.x));\n    float eyeDepth = -(cc_matView * worldPos).z;\n    uint zIndex = uint(max(log(eyeDepth) * scale + bias, 0.0));\n    return zIndex;\n  }\n  uint getClusterIndex(vec4 fragCoord, vec4 worldPos)\n  {\n    uint zIndex = getClusterZIndex(worldPos);\n    float clusterSizeX = ceil(cc_viewPort.z / float(16u));\n    float clusterSizeY = ceil(cc_viewPort.w / float(8u));\n    uvec3 indices = uvec3(uvec2(fragCoord.xy / vec2(clusterSizeX, clusterSizeY)), zIndex);\n    uint cluster = (16u * 8u) * indices.z + 16u * indices.y + indices.x;\n    return cluster;\n  }\n  vec4 CCClusterShadingAdditive (StandardSurface s, vec4 shadowPos) {\n    vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n    vec3 specular = mix(vec3(0.04), s.albedo.rgb, s.metallic);\n    vec3 diffuseContrib = diffuse / PI;\n    vec3 position;\n        #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n        position = unpackHighpData(s.position, s.position_fract_part);\n        #else\n        position = s.position;\n        #endif\n    vec3 N = normalize(s.normal);\n    vec3 V = normalize(cc_cameraPos.xyz - position);\n    float NV = max(abs(dot(N, V)), 0.001);\n    specular = BRDFApprox(specular, s.roughness, NV);\n    vec3 finalColor = vec3(0.0);\n    uint cluster = getClusterIndex(gl_FragCoord, vec4(position, 1.0));\n    LightGrid grid = getLightGrid(cluster);\n    uint numLights = grid.ccLights;\n    for (uint i = 0u; i < 200u; i++) {\n      if (i >= numLights) break;\n      uint lightIndex = getGridLightIndex(grid.offset, i);\n      CCLight light = getCCLight(lightIndex);\n      vec3 SLU = light.cc_lightPos.xyz - position;\n      vec3 SL = normalize(SLU);\n      vec3 SH = normalize(SL + V);\n      float SNL = max(dot(N, SL), 0.001);\n      float SNH = max(dot(N, SH), 0.0);\n      float distSqr = dot(SLU, SLU);\n      float litRadius = light.cc_lightSizeRangeAngle.x;\n      float litRadiusSqr = litRadius * litRadius;\n      float illum = PI * (litRadiusSqr / max(litRadiusSqr , distSqr));\n      float attRadiusSqrInv = 1.0 / max(light.cc_lightSizeRangeAngle.y, 0.01);\n      attRadiusSqrInv *= attRadiusSqrInv;\n      float att = GetDistAtt(distSqr, attRadiusSqrInv);\n      vec3 lspec = specular * CalcSpecular(s.roughness, SNH, SH, N);\n      if (IS_SPOT_LIGHT(light.cc_lightPos.w)) {\n        float cosInner = max(dot(-light.cc_lightDir.xyz, SL), 0.01);\n        float cosOuter = light.cc_lightSizeRangeAngle.z;\n        float litAngleScale = 1.0 / max(0.001, cosInner - cosOuter);\n        float litAngleOffset = -cosOuter * litAngleScale;\n        att *= GetAngleAtt(SL, -light.cc_lightDir.xyz, litAngleScale, litAngleOffset);\n      }\n      vec3 lightColor = light.cc_lightColor.rgb;\n      float shadow = 1.0;\n      #if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n        if (IS_SPOT_LIGHT(light.cc_lightPos.w)  && light.cc_lightSizeRangeAngle.w > 0.0) {\n          shadow = CCSpotShadowFactorBase(shadowPos, position, s.shadowBias);\n        }\n      #endif\n      lightColor *= shadow;\n      finalColor += SNL * lightColor * light.cc_lightColor.w * illum * att * (diffuseContrib + lspec);\n    }\n    return vec4(finalColor, 0.0);\n  }\n#endif\n  vec3 ACESToneMap (vec3 color) {\n    color = min(color, vec3(8.0));\n    const float A = 2.51;\n    const float B = 0.03;\n    const float C = 2.43;\n    const float D = 0.59;\n    const float E = 0.14;\n    return (color * (A * color + B)) / (color * (C * color + D) + E);\n  }\n  vec4 CCFragOutput (vec4 color) {\n    #if CC_USE_RGBE_OUTPUT\n      color = packRGBE(color.rgb);\n    #elif !CC_USE_FLOAT_OUTPUT\n      #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n        color.rgb = ACESToneMap(color.rgb);\n      #endif\n      color.rgb = LinearToSRGB(color.rgb);\n    #endif\n    return color;\n  }\n  #if CC_USE_FOG != 4\n    float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n        vec4 wPos = pos;\n        float cam_dis = distance(cameraPos, wPos.xyz);\n        return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n    }\n    float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n        vec4 wPos = pos;\n        float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n        float f = exp(-cam_dis * fogDensity);\n        return f;\n    }\n    float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n        vec4 wPos = pos;\n        float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n        float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n        return f;\n    }\n    float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n        vec4 wPos = pos;\n        vec3 camWorldProj = cameraPos.xyz;\n        camWorldProj.y = 0.;\n        vec3 worldPosProj = wPos.xyz;\n        worldPosProj.y = 0.;\n        float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n        float fDeltaY, fDensityIntegral;\n        if (cameraPos.y > fogTop) {\n            if (wPos.y < fogTop) {\n                fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n                fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n            }\n            else {\n                fDeltaY = 0.;\n                fDensityIntegral = 0.;\n            }\n        }\n        else {\n            if (wPos.y < fogTop) {\n                float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n                float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n                fDeltaY = abs(fDeltaA - fDeltaB);\n                fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n            }\n            else {\n                fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n                fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n            }\n        }\n        float fDensity;\n        if (fDeltaY != 0.) {\n            fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n        }\n        else {\n            fDensity = 0.;\n        }\n        float f = exp(-fDensity);\n        return f;\n    }\n  #endif\n  void CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n  {\n  #if CC_USE_FOG == 0\n  \tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n  #elif CC_USE_FOG == 1\n  \tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n  #elif CC_USE_FOG == 2\n  \tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n  #elif CC_USE_FOG == 3\n  \tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n  #else\n  \tfactor = 1.0;\n  #endif\n  }\n  void CC_APPLY_FOG_BASE(inout vec4 color, float factor) {\n  \tcolor = vec4(mix(cc_fogColor.rgb, color.rgb, factor), color.a);\n  }\n  vec2 signNotZero(vec2 v) {\n    return vec2((v.x >= 0.0) ? +1.0 : -1.0, (v.y >= 0.0) ? +1.0 : -1.0);\n  }\n  vec3 oct_to_float32x3(vec2 e) {\n    vec3 v = vec3(e.xy, 1.0 - abs(e.x) - abs(e.y));\n    if (v.z < 0.0) v.xy = (1.0 - abs(v.yx)) * signNotZero(v.xy);\n    return normalize(v);\n  }\n  varying vec2 v_uv;\n  uniform sampler2D albedoMap;\n  uniform sampler2D normalMap;\n  uniform sampler2D emissiveMap;\n  uniform sampler2D depthStencil;\n  vec4 screen2WS(vec3 coord) {\n    vec3 ndc = vec3(\n      2.0 * (coord.x - cc_viewPort.x) / cc_viewPort.z - 1.0,\n      2.0 * (coord.y - cc_viewPort.y) / cc_viewPort.w - 1.0,\n      2.0 * coord.z - 1.0);\n    CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(ndc.y);\n    return GetWorldPosFromNDCPosRH(ndc, cc_matProj, cc_matViewProjInv);\n  }\n  void main () {\n    StandardSurface s;\n    vec4 albedo = texture2D(albedoMap, v_uv);\n    vec4 normal = texture2D(normalMap, v_uv);\n    vec4 emissive = texture2D(emissiveMap, v_uv);\n    float depth = texture2D(depthStencil, v_uv).x;\n    s.albedo = albedo;\n    vec3 position = screen2WS(vec3(gl_FragCoord.xy, depth)).xyz;\n    s.position = position;\n    s.roughness = normal.z;\n    s.normal = oct_to_float32x3(normal.xy);\n    s.specularIntensity = 0.5;\n    s.metallic = normal.w;\n    s.emissive = emissive.xyz;\n    s.occlusion = emissive.w;\n#if CC_RECEIVE_SHADOW\n    s.shadowBias = vec2(0, 0);\n#endif\n    float fogFactor;\n    CC_TRANSFER_FOG_BASE(vec4(position, 1), fogFactor);\n    vec4 shadowPos;\n    shadowPos = cc_matLightViewProj * vec4(position, 1);\n    vec4 color = CCStandardShadingBase(s, shadowPos) +\n#if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 1\n                 CCClusterShadingAdditive(s, shadowPos);\n#else\n                 CCStandardShadingAdditive(s, shadowPos);\n#endif\n    CC_APPLY_FOG_BASE(color, fogFactor);\n    color = CCFragOutput(color);\n#if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_SINGLE\n    color = vec4(albedoMap.rgb, 1.0);\n#endif\n    gl_FragColor = color;\n  }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}, {"name": "CCShadow", "defines": []}, {"name": "CCCSM", "defines": ["CC_SUPPORT_CASCADED_SHADOW_MAP"]}], "samplerTextures": [{"name": "cc_shadowMap", "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_spotShadowMap", "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_environment", "defines": []}, {"name": "cc_diffuseMap", "defines": ["CC_USE_IBL", "CC_USE_DIFFUSEMAP"]}], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "CCSH", "defines": ["CC_USE_LIGHT_PROBE", "!USE_INSTANCING"]}, {"name": "CCForwardLight", "defines": ["CC_ENABLE_CLUSTERED_LIGHT_CULLING"]}], "samplerTextures": [{"name": "cc_reflectionProbeCubemap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbePlanarMap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeDataMap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeBlendCubemap", "defines": ["CC_USE_REFLECTION_PROBE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 42, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 120}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "number", "range": [0, 3]}, {"name": "CC_USE_REFLECTION_PROBE", "type": "number", "range": [0, 3]}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "default": 0}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_USE_DEBUG_VIEW", "type": "number", "range": [0, 3]}, {"name": "CC_SURFACES_ENABLE_DEBUG_VIEW", "type": "boolean"}, {"name": "CC_SUPPORT_CASCADED_SHADOW_MAP", "type": "boolean"}, {"name": "CC_SHADOWMAP_FORMAT", "type": "number", "range": [0, 3]}, {"name": "CC_SHADOWMAP_USE_LINEAR_DEPTH", "type": "boolean"}, {"name": "CC_DIR_SHADOW_PCF_TYPE", "type": "number", "range": [0, 3]}, {"name": "CC_CASCADED_LAYERS_TRANSITION", "type": "boolean"}, {"name": "CC_USE_IBL", "type": "number", "range": [0, 2]}, {"name": "CC_USE_DIFFUSEMAP", "type": "number", "range": [0, 2]}, {"name": "CC_USE_HDR", "type": "boolean"}, {"name": "USE_REFLECTION_DENOISE", "type": "boolean"}, {"name": "CC_SHADOW_TYPE", "type": "number", "range": [0, 3]}, {"name": "CC_DIR_LIGHT_SHADOW_TYPE", "type": "number", "range": [0, 3]}, {"name": "CC_FORWARD_ADD", "type": "boolean"}, {"name": "CC_DISABLE_DIRECTIONAL_LIGHT", "type": "boolean"}, {"name": "CC_IBL_CONVOLUTED", "type": "boolean"}, {"name": "CC_PIPELINE_TYPE", "type": "number", "range": [0, 1]}, {"name": "CC_FORCE_FORWARD_SHADING", "type": "boolean"}, {"name": "CC_ENABLE_CLUSTERED_LIGHT_CULLING", "type": "number", "range": [0, 3]}, {"name": "CC_USE_RGBE_OUTPUT", "type": "boolean"}, {"name": "CC_USE_FLOAT_OUTPUT", "type": "boolean"}, {"name": "CC_TONE_MAPPING_TYPE", "type": "number", "range": [0, 3]}, {"name": "HDR_TONE_MAPPING_ACES", "type": "boolean"}, {"name": "CC_USE_FOG", "type": "number", "range": [0, 4]}]}], [{"passes": [{"pass": "deferred-lighting", "program": "pipeline/deferred-lighting|lighting-vs|lighting-fs", "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}}]}]]], 0, 0, [], [], []], [[[2, "for2d/builtin-sprite", [{}], [{"hash": 2249878161, "name": "for2d/builtin-sprite|sprite-vs:vert|sprite-fs:frag", "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}, {"name": "a_color", "format": 44, "location": 2, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["USE_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n  uniform highp mat4 cc_matViewProj;\n  uniform highp vec4 cc_cameraPos;\n#if USE_LOCAL\n  uniform highp mat4 cc_matWorld;\n#endif\n#if SAMPLE_FROM_RT\n  #define QUATER_PI         0.78539816340\n  #define HALF_PI           1.57079632679\n  #define PI                3.14159265359\n  #define PI2               6.28318530718\n  #define PI4               12.5663706144\n  #define INV_QUATER_PI     1.27323954474\n  #define INV_HALF_PI       0.63661977237\n  #define INV_PI            0.31830988618\n  #define INV_PI2           0.15915494309\n  #define INV_PI4           0.07957747155\n  #define EPSILON           1e-6\n  #define EPSILON_LOWP      1e-4\n  #define LOG2              1.442695\n  #define EXP_VALUE         2.71828183\n  #define FP_MAX            65504.0\n  #define FP_SCALE          0.0009765625\n  #define FP_SCALE_INV      1024.0\n  #define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n      #define LIGHT_MAP_TYPE_DISABLED 0\n  #define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n  #define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n  #define REFLECTION_PROBE_TYPE_NONE 0\n  #define REFLECTION_PROBE_TYPE_CUBE 1\n  #define REFLECTION_PROBE_TYPE_PLANAR 2\n  #define REFLECTION_PROBE_TYPE_BLEND 3\n  #define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n      #define LIGHT_TYPE_DIRECTIONAL 0.0\n  #define LIGHT_TYPE_SPHERE 1.0\n  #define LIGHT_TYPE_SPOT 2.0\n  #define LIGHT_TYPE_POINT 3.0\n  #define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n  #define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n  #define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n  #define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n  #define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n  #define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n  #define TONE_MAPPING_ACES 0\n  #define TONE_MAPPING_LINEAR 1\n  #define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n  #ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n    #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n  #endif\n  #ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n    #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n  #endif\n#endif\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\nvarying vec4 color;\nvarying vec2 uv0;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  #if USE_PIXEL_ALIGNMENT\n    pos = cc_matView * pos;\n    pos.xyz = floor(pos.xyz);\n    pos = cc_matProj * pos;\n  #else\n    pos = cc_matViewProj * pos;\n  #endif\n  uv0 = a_texCoord;\n  #if SAMPLE_FROM_RT\n    uv0 = cc_cameraPos.w > 1.0 ? vec2(uv0.x, 1.0 - uv0.y) : uv0;\n  #endif\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 CCSampleWithAlphaSeparated(sampler2D tex, vec2 uv) {\n#if CC_USE_EMBEDDED_ALPHA\n  return vec4(texture2D(tex, uv).rgb, texture2D(tex, uv + vec2(0.0, 0.5)).r);\n#else\n  return texture2D(tex, uv);\n#endif\n}\n#if USE_ALPHA_TEST\n      uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 color;\n#if USE_TEXTURE\n  varying vec2 uv0;\n  uniform sampler2D cc_spriteTexture;\n#endif\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n    o *= CCSampleWithAlphaSeparated(cc_spriteTexture, uv0);\n    #if IS_GRAY\n      float gray  = 0.2126 * o.r + 0.7152 * o.g + 0.0722 * o.b;\n      o.r = o.g = o.b = gray;\n    #endif\n  #endif\n  o *= color;\n  ALPHA_TEST(o);\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "defines": ["USE_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 1}}, "defines": [{"name": "USE_LOCAL", "type": "boolean"}, {"name": "SAMPLE_FROM_RT", "type": "boolean"}, {"name": "USE_PIXEL_ALIGNMENT", "type": "boolean"}, {"name": "CC_USE_EMBEDDED_ALPHA", "type": "boolean"}, {"name": "USE_ALPHA_TEST", "type": "boolean"}, {"name": "USE_TEXTURE", "type": "boolean"}, {"name": "IS_GRAY", "type": "boolean"}]}], [{"passes": [{"program": "for2d/builtin-sprite|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"alphaThreshold": {"type": 13, "value": [0.5]}}}]}]]], 0, 0, [], [], []], [[[0, "pipeline/post-process/tone-mapping", [{"hash": 879047950, "name": "pipeline/post-process/tone-mapping|vs|fs-tonemap", "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}], "fragColors": [{"name": "fragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "Pipeline", "stageFlags": 1, "binding": 0, "members": [{"name": "g_platform", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [{"name": "inputTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 1, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n#endif\n  uniform vec4 g_platform;\nvarying vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  (In.position).y = g_platform.w == 0.0 ? -(In.position).y : (In.position).y;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec3 HDRToLDR(vec3 color)\n{\n  #if CC_USE_HDR\n    #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n      if (IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING)\n    #endif\n    {\n    #if CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    }\n  #endif\n  return color;\n}\nvarying vec2 v_uv;\nuniform sampler2D inputTexture;\nvoid main () {\n  gl_FragColor = texture2D(inputTexture, v_uv);\n  #if CC_USE_FLOAT_OUTPUT\n    gl_FragColor.rgb = HDRToLDR(gl_FragColor.rgb);\n    gl_FragColor.rgb = LinearToSRGB(gl_FragColor.rgb);\n  #endif\n}"}, "builtins": {"globals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 1, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 0}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean"}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_USE_DEBUG_VIEW", "type": "number", "range": [0, 3]}, {"name": "CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC", "type": "boolean"}, {"name": "CC_SURFACES_ENABLE_DEBUG_VIEW", "type": "boolean"}, {"name": "CC_USE_HDR", "type": "boolean"}, {"name": "CC_TONE_MAPPING_TYPE", "type": "number", "range": [0, 3]}, {"name": "HDR_TONE_MAPPING_ACES", "type": "boolean"}, {"name": "CC_USE_FLOAT_OUTPUT", "type": "boolean"}]}, {"hash": 4126709291, "name": "pipeline/post-process/tone-mapping|vs|fs-copy", "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}], "fragColors": [{"name": "fragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "Pipeline", "stageFlags": 1, "binding": 0, "members": [{"name": "g_platform", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [{"name": "inputTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 1, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n#endif\n  uniform vec4 g_platform;\nvarying vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  (In.position).y = g_platform.w == 0.0 ? -(In.position).y : (In.position).y;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nvarying vec2 v_uv;\nuniform sampler2D inputTexture;\nvoid main () {\n  gl_FragColor = texture2D(inputTexture, v_uv);\n}"}, "builtins": {"globals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 1, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 0}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean"}, {"name": "CC_USE_MORPH", "type": "boolean"}]}], [{"passes": [{"pass": "cc-tone-mapping", "program": "pipeline/post-process/tone-mapping|vs|fs-tonemap", "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}}, {"pass": "cc-tone-mapping", "program": "pipeline/post-process/tone-mapping|vs|fs-copy", "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}}]}]]], 0, 0, [], [], []], [[[2, "internal/builtin-clear-stencil", [{}], [{"hash": 3507038093, "name": "internal/builtin-clear-stencil|sprite-vs:vert|sprite-fs:frag", "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\nattribute vec3 a_position;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 frag () {\n  vec4 o = vec4(1.0);\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 0, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 0}}, "defines": []}], [{"passes": [{"program": "internal/builtin-clear-stencil|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}}]}]]], 0, 0, [], [], []], [[[0, "util/profiler", [{"hash": 394204838, "name": "util/profiler|profiler-vs:vert|profiler-fs:frag", "blocks": [{"name": "Constants", "stageFlags": 1, "binding": 0, "members": [{"name": "offset", "type": 16, "count": 1}], "defines": []}, {"name": "PerFrameInfo", "stageFlags": 1, "binding": 1, "members": [{"name": "digits", "type": 16, "count": 22}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_color", "format": 44, "location": 1, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constants", "stageFlags": 1, "binding": 0, "members": [{"name": "offset", "type": 16, "count": 1}], "defines": []}, {"name": "PerFrameInfo", "stageFlags": 1, "binding": 1, "members": [{"name": "digits", "type": 16, "count": 22}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision mediump float;\nuniform highp mat4 cc_matProj;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec2 v_uv;\n   uniform vec4 offset;\n    uniform vec4 digits[22];\nfloat getComponent(vec4 v, float i) {\n  if (i < 1.0) { return v.x; }\n  else if (i < 2.0) { return v.y; }\n  else if (i < 3.0) { return v.z; }\n  else { return v.w; }\n}\nvec4 vert () {\n  mat2 proj = mat2(cc_matProj[0].xy, cc_matProj[1].xy);\n  proj /= abs(proj[1].x + proj[1].y);\n  vec2 position = proj * a_position.xy + offset.xy;\n  v_uv = a_color.xy;\n  if (a_color.z >= 0.0) {\n    float n = getComponent(digits[int(a_color.z)], a_color.w);\n    v_uv += vec2(offset.z * n, 0.0);\n  }\n  return vec4(position, 0.0, 1.0);\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying vec2 v_uv;\nuniform sampler2D mainTexture;\nvec4 frag () {\n  return CCFragOutput(texture2D(mainTexture, v_uv));\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 65, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 42}}, "defines": []}], [{"passes": [{"program": "util/profiler|profiler-vs:vert|profiler-fs:frag", "priority": 255, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}}]}]]], 0, 0, [], [], []], [[[1, "default-clear-stencil", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": false}]]], 0, 0, [0], [0], [2]], [[[0, "pipeline/planar-shadow", [{"hash": 3680218420, "name": "pipeline/planar-shadow|planar-shadow-vs:vert|planar-shadow-fs:frag", "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCMorph", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_displacementWeights", "typename": "vec4", "type": 16, "count": 15, "isArray": true}, {"name": "cc_displacementTextureInfo", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointTextureInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointAnimInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_joints", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}], "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCShadow", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matLightView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matLightViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_shadowInvProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowNFLSInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowWHPBInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowLPNNInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowColor", "typename": "vec4", "type": 16, "count": 1, "precision": "lowp "}, {"name": "cc_planarNDInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  uniform vec4 cc_displacementWeights[15];\n  uniform vec4 cc_displacementTextureInfo;\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n        int pixelIndex = elementIndex;\n        vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n        vec2 uv = getPixelCoordFromLocation(location, cc_displacementTextureInfo.xy);\n        return texture2D(tex, uv);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture2D(tex, x)),\n        decode32(texture2D(tex, y)),\n        decode32(texture2D(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    uniform highp vec4 cc_jointTextureInfo;\n    uniform highp vec4 cc_jointAnimInfo;\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      uniform highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout vec4 In)\n{\n    In = vec4(a_position, 1.0);\n  #if CC_USE_MORPH\n    applyMorph(In);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In);\n  #endif\n}\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n  uniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_mainLitDir;\n  uniform mediump vec4 cc_nearFar;\n#if !USE_INSTANCING\n  uniform highp mat4 cc_matWorld;\n  uniform highp mat4 cc_matWorldIT;\n#endif\nvoid CCGetWorldMatrixFull(out mat4 matWorld, out mat4 matWorldIT)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n    vec3 scale = 1.0 / vec3(length(a_matWorld0.xyz), length(a_matWorld1.xyz), length(a_matWorld2.xyz));\n    vec3 scale2 = scale * scale;\n    matWorldIT = mat4(\n      vec4(a_matWorld0.xyz * scale2.x, 0.0),\n      vec4(a_matWorld1.xyz * scale2.y, 0.0),\n      vec4(a_matWorld2.xyz * scale2.z, 0.0),\n      vec4(0.0, 0.0, 0.0, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n    matWorldIT = cc_matWorldIT;\n  #endif\n}\nuniform mediump vec4 cc_shadowWHPBInfo;\n  uniform mediump vec4 cc_planarNDInfo;\nvec4 CalculatePlanarShadowPos(vec3 meshWorldPos, vec3 cameraPos, vec3 lightDir, vec4 plane) {\n  vec3 P = meshWorldPos;\n  vec3 L = lightDir;\n  vec3 N = plane.xyz;\n  float d = plane.w + EPSILON_LOWP;\n  float dist = (-d - dot(P, N)) / (dot(L, N) + EPSILON_LOWP);\n  vec3 shadowPos = P + L * dist;\n  return vec4(shadowPos, dist);\n}\nvec4 CalculatePlanarShadowClipPos(vec4 shadowPos, vec3 cameraPos, mat4 matView, mat4 matProj, vec4 nearFar, float bias) {\n  vec4 camPos = matView * vec4(shadowPos.xyz, 1.0);\n  float lerpCoef = saturate((nearFar.z < 0.0 ? -camPos.z : camPos.z) / (nearFar.y - nearFar.x));\n  camPos.z += mix(nearFar.x * 0.01, nearFar.y * EPSILON_LOWP * bias, lerpCoef);\n  return matProj * camPos;\n}\nvarying float v_dist;\nvec4 vert () {\n  vec4 position;\n  CCVertInput(position);\n  mat4 matWorld, matWorldIT;\n  CCGetWorldMatrixFull(matWorld, matWorldIT);\n  vec3 worldPos = (matWorld * position).xyz;\n  vec4 shadowPos = CalculatePlanarShadowPos(worldPos, cc_cameraPos.xyz, cc_mainLitDir.xyz, cc_planarNDInfo);\n  position = CalculatePlanarShadowClipPos(shadowPos, cc_cameraPos.xyz, cc_matView, cc_matProj, cc_nearFar, cc_shadowWHPBInfo.w);\n  v_dist = shadowPos.w;\n  return position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nuniform lowp vec4 cc_shadowColor;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying float v_dist;\nvec4 frag () {\n  if(v_dist < 0.0)\n    discard;\n  return CCFragOutput(cc_shadowColor);\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}, {"name": "CCShadow", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCMorph", "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 90, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 58}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean"}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_MORPH_TARGET_COUNT", "type": "number", "range": [2, 8]}, {"name": "CC_MORPH_TARGET_HAS_POSITION", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_NORMAL", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_TANGENT", "type": "boolean"}, {"name": "CC_MORPH_PRECOMPUTED", "type": "boolean"}, {"name": "CC_USE_REAL_TIME_JOINT_TEXTURE", "type": "boolean"}]}], [{"passes": [{"phase": "planarShadow", "program": "pipeline/planar-shadow|planar-shadow-vs:vert|planar-shadow-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false, "stencilTestFront": true, "stencilFuncFront": 5, "stencilPassOpFront": 2, "stencilRefBack": 128, "stencilRefFront": 128, "stencilReadMaskBack": 128, "stencilReadMaskFront": 128, "stencilWriteMaskBack": 128, "stencilWriteMaskFront": 128}}]}]]], 0, 0, [], [], []], [[[0, "util/splash-screen", [{"hash": 3189094080, "name": "util/splash-screen|splash-screen-vs:vert|splash-screen-fs:frag", "blocks": [{"name": "Constant", "stageFlags": 1, "binding": 0, "members": [{"name": "u_buffer0", "type": 16, "count": 1}, {"name": "u_buffer1", "type": 16, "count": 1}, {"name": "u_projection", "type": 25, "count": 1}], "defines": []}, {"name": "Factor", "stageFlags": 16, "binding": 1, "members": [{"name": "u_percent", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 21, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constant", "stageFlags": 1, "binding": 0, "members": [{"name": "u_buffer0", "type": 16, "count": 1}, {"name": "u_buffer1", "type": 16, "count": 1}, {"name": "u_projection", "type": 25, "count": 1}], "defines": []}, {"name": "Factor", "stageFlags": 16, "binding": 1, "members": [{"name": "u_percent", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision mediump float;\nattribute vec2 a_position;\nattribute vec2 a_texCoord;\nvarying vec2 v_uv;\n  uniform vec4 u_buffer0;\n  uniform vec4 u_buffer1;\n  uniform mat4 u_projection;\nvec4 vert () {\n  vec2 worldPos = a_position * u_buffer1.xy + u_buffer1.zw;\n  vec2 clipSpace = worldPos / u_buffer0.xy * 2.0 - 1.0;\n  vec4 screenPos = u_projection * vec4(clipSpace, 0.0, 1.0);\n  v_uv = a_texCoord;\n  return screenPos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nvarying vec2 v_uv;\n  uniform float u_percent;\nuniform sampler2D mainTexture;\nvec4 frag () {\n  vec4 color = texture2D(mainTexture, v_uv);\n  float percent = clamp(u_percent, 0.0, 1.0);\n  color.xyz *= percent;\n  return color;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 6, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 1}}, "defines": []}], [{"name": "default", "passes": [{"program": "util/splash-screen|splash-screen-vs:vert|splash-screen-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "resolution": {"type": 14, "value": [640, 960], "handleInfo": ["u_buffer0", 0, 14]}, "percent": {"type": 13, "value": [0.5], "handleInfo": ["u_percent", 0, 13]}, "scale": {"type": 14, "value": [200, 500], "handleInfo": ["u_buffer1", 0, 14]}, "translate": {"type": 14, "value": [320, 480], "handleInfo": ["u_buffer1", 2, 14]}, "u_buffer0": {"type": 16, "value": [640, 960, 0, 0]}, "u_percent": {"type": 13, "value": [0.5]}, "u_buffer1": {"type": 16, "value": [200, 500, 320, 480]}}}]}]]], 0, 0, [], [], []], [[[0, "builtin-unlit", [{"hash": 340555192, "name": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "blocks": [{"name": "TexCoords", "stageFlags": 1, "binding": 0, "members": [{"name": "tilingOffset", "type": 16, "count": 1}], "defines": ["USE_TEXTURE"]}, {"name": "Constant", "stageFlags": 16, "binding": 1, "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "colorScaleAndCutoff", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": ["USE_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}, {"name": "a_color", "format": 44, "location": 17, "defines": ["USE_VERTEX_COLOR"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCMorph", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_displacementWeights", "typename": "vec4", "type": 16, "count": 15, "isArray": true}, {"name": "cc_displacementTextureInfo", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointTextureInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointAnimInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_joints", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}], "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "TexCoords", "stageFlags": 1, "binding": 0, "members": [{"name": "tilingOffset", "type": 16, "count": 1}], "defines": ["USE_TEXTURE"]}, {"name": "Constant", "stageFlags": 16, "binding": 1, "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "colorScaleAndCutoff", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": ["USE_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  uniform vec4 cc_displacementWeights[15];\n  uniform vec4 cc_displacementTextureInfo;\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n        int pixelIndex = elementIndex;\n        vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n        vec2 uv = getPixelCoordFromLocation(location, cc_displacementTextureInfo.xy);\n        return texture2D(tex, uv);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture2D(tex, x)),\n        decode32(texture2D(tex, y)),\n        decode32(texture2D(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    uniform highp vec4 cc_jointTextureInfo;\n    uniform highp vec4 cc_jointAnimInfo;\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      uniform highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout vec4 In)\n{\n    In = vec4(a_position, 1.0);\n  #if CC_USE_MORPH\n    applyMorph(In);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In);\n  #endif\n}\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n  uniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_fogBase;\n  uniform mediump vec4 cc_fogAdd;\n#if !USE_INSTANCING\n  uniform highp mat4 cc_matWorld;\n#endif\nvoid CCGetWorldMatrix(out mat4 matWorld)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n  #endif\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\n#if !CC_USE_ACCURATE_FOG\nvarying mediump float v_fog_factor;\n#endif\nvoid CC_TRANSFER_FOG(vec4 pos) {\n#if !CC_USE_ACCURATE_FOG\n    CC_TRANSFER_FOG_BASE(pos, v_fog_factor);\n#endif\n}\n#if USE_VERTEX_COLOR\n  attribute lowp vec4 a_color;\n  varying lowp vec4 v_color;\n#endif\n#if USE_TEXTURE\n  varying vec2 v_uv;\n      uniform vec4 tilingOffset;\n#endif\nvec4 vert () {\n  vec4 position;\n  CCVertInput(position);\n  mat4 matWorld;\n  CCGetWorldMatrix(matWorld);\n  #if USE_TEXTURE\n    v_uv = a_texCoord * tilingOffset.xy + tilingOffset.zw;\n    #if SAMPLE_FROM_RT\n      v_uv = cc_cameraPos.w > 1.0 ? vec2(v_uv.x, 1.0 - v_uv.y) : v_uv;\n    #endif\n  #endif\n  #if USE_VERTEX_COLOR\n    v_color = a_color;\n  #endif\n  CC_TRANSFER_FOG(matWorld * position);\n  return cc_matProj * (cc_matView * matWorld) * position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec3 SRGBToLinear (vec3 gamma) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return gamma;\n    }\n  #endif\n#endif\n  return gamma * gamma;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nvec4 packRGBE (vec3 rgb) {\n  highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n  highp float e = 128.0;\n  if (maxComp > 0.0001) {\n    e = log(maxComp) / log(1.1);\n    e = ceil(e);\n    e = clamp(e + 128.0, 0.0, 255.0);\n  }\n  highp float sc = 1.0 / pow(1.1, e - 128.0);\n  vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n  vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n  return vec4(encode_rounded, e) / 255.0;\n}\nvec4 CCFragOutput (vec4 color) {\n  #if CC_USE_RGBE_OUTPUT\n    color = packRGBE(color.rgb);\n  #elif !CC_USE_FLOAT_OUTPUT\n    #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    color.rgb = LinearToSRGB(color.rgb);\n  #endif\n  return color;\n}\nuniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_fogColor;\n  uniform mediump vec4 cc_fogBase;\n  uniform mediump vec4 cc_fogAdd;\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nvoid CC_APPLY_FOG_BASE(inout vec4 color, float factor) {\n\tcolor = vec4(mix(cc_fogColor.rgb, color.rgb, factor), color.a);\n}\n#if !CC_USE_ACCURATE_FOG\nvarying mediump float v_fog_factor;\n#endif\nvoid CC_APPLY_FOG(inout vec4 color) {\n#if !CC_USE_ACCURATE_FOG\n    CC_APPLY_FOG_BASE(color, v_fog_factor);\n#endif\n}\nvoid CC_APPLY_FOG(inout vec4 color, vec3 worldPos) {\n#if CC_USE_ACCURATE_FOG\n    float factor;\n    CC_TRANSFER_FOG_BASE(vec4(worldPos, 1.0), factor);\n#else\n    float factor = v_fog_factor;\n#endif\n    CC_APPLY_FOG_BASE(color, factor);\n}\n#if USE_ALPHA_TEST\n#endif\n#if USE_TEXTURE\n  varying vec2 v_uv;\n  uniform sampler2D mainTexture;\n#endif\n   uniform vec4 mainColor;\n   uniform vec4 colorScaleAndCutoff;\n#if USE_VERTEX_COLOR\n  varying lowp vec4 v_color;\n#endif\nvec4 frag () {\n  vec4 o = mainColor;\n  o.rgb *= colorScaleAndCutoff.xyz;\n  #if USE_VERTEX_COLOR\n    o.rgb *= SRGBToLinear(v_color.rgb);\n    o.a *= v_color.a;\n  #endif\n  #if USE_TEXTURE\n    vec4 texColor = texture2D(mainTexture, v_uv);\n    texColor.rgb = SRGBToLinear(texColor.rgb);\n    o *= texColor;\n  #endif\n  #if USE_ALPHA_TEST\n    if (o.ALPHA_TEST_CHANNEL < colorScaleAndCutoff.w) discard;\n  #endif\n  CC_APPLY_FOG(o);\n  return CCFragOutput(o);\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCMorph", "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 75, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 44}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean"}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_MORPH_TARGET_COUNT", "type": "number", "range": [2, 8]}, {"name": "CC_MORPH_TARGET_HAS_POSITION", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_NORMAL", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_TANGENT", "type": "boolean"}, {"name": "CC_MORPH_PRECOMPUTED", "type": "boolean"}, {"name": "CC_USE_REAL_TIME_JOINT_TEXTURE", "type": "boolean"}, {"name": "CC_USE_FOG", "type": "number", "range": [0, 4]}, {"name": "CC_USE_ACCURATE_FOG", "type": "boolean"}, {"name": "USE_VERTEX_COLOR", "type": "boolean"}, {"name": "USE_TEXTURE", "type": "boolean"}, {"name": "SAMPLE_FROM_RT", "type": "boolean"}, {"name": "CC_USE_DEBUG_VIEW", "type": "number", "range": [0, 3]}, {"name": "CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC", "type": "boolean"}, {"name": "CC_SURFACES_ENABLE_DEBUG_VIEW", "type": "boolean"}, {"name": "CC_USE_RGBE_OUTPUT", "type": "boolean"}, {"name": "CC_USE_FLOAT_OUTPUT", "type": "boolean"}, {"name": "CC_USE_HDR", "type": "boolean"}, {"name": "CC_TONE_MAPPING_TYPE", "type": "number", "range": [0, 3]}, {"name": "HDR_TONE_MAPPING_ACES", "type": "boolean"}, {"name": "USE_ALPHA_TEST", "type": "boolean"}, {"name": "ALPHA_TEST_CHANNEL", "type": "string", "options": ["a", "r", "g", "b"]}]}, {"hash": 3680218420, "name": "builtin-unlit|planar-shadow-vs:vert|planar-shadow-fs:frag", "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCMorph", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_displacementWeights", "typename": "vec4", "type": 16, "count": 15, "isArray": true}, {"name": "cc_displacementTextureInfo", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointTextureInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointAnimInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_joints", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}], "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCShadow", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matLightView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matLightViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_shadowInvProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowNFLSInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowWHPBInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowLPNNInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowColor", "typename": "vec4", "type": 16, "count": 1, "precision": "lowp "}, {"name": "cc_planarNDInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  uniform vec4 cc_displacementWeights[15];\n  uniform vec4 cc_displacementTextureInfo;\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n        int pixelIndex = elementIndex;\n        vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n        vec2 uv = getPixelCoordFromLocation(location, cc_displacementTextureInfo.xy);\n        return texture2D(tex, uv);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture2D(tex, x)),\n        decode32(texture2D(tex, y)),\n        decode32(texture2D(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    uniform highp vec4 cc_jointTextureInfo;\n    uniform highp vec4 cc_jointAnimInfo;\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      uniform highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout vec4 In)\n{\n    In = vec4(a_position, 1.0);\n  #if CC_USE_MORPH\n    applyMorph(In);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In);\n  #endif\n}\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n  uniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_mainLitDir;\n  uniform mediump vec4 cc_nearFar;\n#if !USE_INSTANCING\n  uniform highp mat4 cc_matWorld;\n  uniform highp mat4 cc_matWorldIT;\n#endif\nvoid CCGetWorldMatrixFull(out mat4 matWorld, out mat4 matWorldIT)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n    vec3 scale = 1.0 / vec3(length(a_matWorld0.xyz), length(a_matWorld1.xyz), length(a_matWorld2.xyz));\n    vec3 scale2 = scale * scale;\n    matWorldIT = mat4(\n      vec4(a_matWorld0.xyz * scale2.x, 0.0),\n      vec4(a_matWorld1.xyz * scale2.y, 0.0),\n      vec4(a_matWorld2.xyz * scale2.z, 0.0),\n      vec4(0.0, 0.0, 0.0, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n    matWorldIT = cc_matWorldIT;\n  #endif\n}\nuniform mediump vec4 cc_shadowWHPBInfo;\n  uniform mediump vec4 cc_planarNDInfo;\nvec4 CalculatePlanarShadowPos(vec3 meshWorldPos, vec3 cameraPos, vec3 lightDir, vec4 plane) {\n  vec3 P = meshWorldPos;\n  vec3 L = lightDir;\n  vec3 N = plane.xyz;\n  float d = plane.w + EPSILON_LOWP;\n  float dist = (-d - dot(P, N)) / (dot(L, N) + EPSILON_LOWP);\n  vec3 shadowPos = P + L * dist;\n  return vec4(shadowPos, dist);\n}\nvec4 CalculatePlanarShadowClipPos(vec4 shadowPos, vec3 cameraPos, mat4 matView, mat4 matProj, vec4 nearFar, float bias) {\n  vec4 camPos = matView * vec4(shadowPos.xyz, 1.0);\n  float lerpCoef = saturate((nearFar.z < 0.0 ? -camPos.z : camPos.z) / (nearFar.y - nearFar.x));\n  camPos.z += mix(nearFar.x * 0.01, nearFar.y * EPSILON_LOWP * bias, lerpCoef);\n  return matProj * camPos;\n}\nvarying float v_dist;\nvec4 vert () {\n  vec4 position;\n  CCVertInput(position);\n  mat4 matWorld, matWorldIT;\n  CCGetWorldMatrixFull(matWorld, matWorldIT);\n  vec3 worldPos = (matWorld * position).xyz;\n  vec4 shadowPos = CalculatePlanarShadowPos(worldPos, cc_cameraPos.xyz, cc_mainLitDir.xyz, cc_planarNDInfo);\n  position = CalculatePlanarShadowClipPos(shadowPos, cc_cameraPos.xyz, cc_matView, cc_matProj, cc_nearFar, cc_shadowWHPBInfo.w);\n  v_dist = shadowPos.w;\n  return position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nuniform lowp vec4 cc_shadowColor;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying float v_dist;\nvec4 frag () {\n  if(v_dist < 0.0)\n    discard;\n  return CCFragOutput(cc_shadowColor);\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}, {"name": "CCShadow", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCMorph", "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 90, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 58}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean"}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_MORPH_TARGET_COUNT", "type": "number", "range": [2, 8]}, {"name": "CC_MORPH_TARGET_HAS_POSITION", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_NORMAL", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_TANGENT", "type": "boolean"}, {"name": "CC_MORPH_PRECOMPUTED", "type": "boolean"}, {"name": "CC_USE_REAL_TIME_JOINT_TEXTURE", "type": "boolean"}]}], [{"name": "opaque", "passes": [{"program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"type": 16, "value": [1, 1, 0, 0]}, "mainColor": {"linear": true, "type": 16, "value": [1, 1, 1, 1]}, "colorScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "color": {"linear": true, "type": 16, "handleInfo": ["mainColor", 0, 16]}, "colorScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}}}, {"phase": "planar-shadow", "propertyIndex": 0, "program": "builtin-unlit|planar-shadow-vs:vert|planar-shadow-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false, "stencilTestFront": true, "stencilFuncFront": 5, "stencilPassOpFront": 2, "stencilRefBack": 128, "stencilRefFront": 128, "stencilReadMaskBack": 128, "stencilReadMaskFront": 128, "stencilWriteMaskBack": 128, "stencilWriteMaskFront": 128}}, {"phase": "deferred-forward", "propertyIndex": 0, "program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag"}]}, {"name": "transparent", "passes": [{"program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"type": 16, "value": [1, 1, 0, 0]}, "mainColor": {"linear": true, "type": 16, "value": [1, 1, 1, 1]}, "colorScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "color": {"linear": true, "type": 16, "handleInfo": ["mainColor", 0, 16]}, "colorScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}}}, {"phase": "planar-shadow", "propertyIndex": 0, "program": "builtin-unlit|planar-shadow-vs:vert|planar-shadow-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false, "stencilTestFront": true, "stencilFuncFront": 5, "stencilPassOpFront": 2, "stencilRefBack": 128, "stencilRefFront": 128, "stencilReadMaskBack": 128, "stencilReadMaskFront": 128, "stencilWriteMaskBack": 128, "stencilWriteMaskFront": 128}}, {"phase": "deferred-forward", "propertyIndex": 0, "program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}}]}, {"name": "add", "passes": [{"program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 1, "blendSrcAlpha": 2, "blendDstAlpha": 1}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"type": 16, "value": [1, 1, 0, 0]}, "mainColor": {"linear": true, "type": 16, "value": [1, 1, 1, 1]}, "colorScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "color": {"linear": true, "type": 16, "handleInfo": ["mainColor", 0, 16]}, "colorScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}}}, {"phase": "deferred-forward", "propertyIndex": 0, "program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 1, "blendSrcAlpha": 2, "blendDstAlpha": 1}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}}]}, {"name": "alpha-blend", "passes": [{"program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendSrcAlpha": 2, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"type": 16, "value": [1, 1, 0, 0]}, "mainColor": {"linear": true, "type": 16, "value": [1, 1, 1, 1]}, "colorScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "color": {"linear": true, "type": 16, "handleInfo": ["mainColor", 0, 16]}, "colorScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}}}, {"phase": "deferred-forward", "propertyIndex": 0, "program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendSrcAlpha": 2, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}}]}]]], 0, 0, [], [], []], [[[1, "default-spine-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true, "CC_USE_EMBEDDED_ALPHA": false, "IS_GRAY": false}]]], 0, 0, [0], [0], [3]], [[[4, "default-physics-material", 0.8, 0.1, 0.1, 0.1]], 0, 0, [], [], []], [[[3, "missing-effect-material", [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_COLOR": true}], [[[{}, "mainColor", 8, [4, 4278255615]]], 11]]], 0, 0, [0], [0], [1]], [[[0, "for2d/builtin-spine", [{"hash": 3152403458, "name": "for2d/builtin-spine|sprite-vs:vert|sprite-fs:frag", "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}, {"name": "a_color", "format": 44, "location": 2, "defines": []}, {"name": "a_color2", "format": 44, "location": 3, "defines": ["TWO_COLORED"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matViewProj;\n#if USE_LOCAL\n  uniform highp mat4 cc_matWorld;\n#endif\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\nvarying vec4 v_light;\nvarying vec2 uv0;\n#if TWO_COLORED\n  attribute vec4 a_color2;\n  varying vec4 v_dark;\n#endif\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  pos = cc_matViewProj * pos;\n  uv0 = a_texCoord;\n  v_light = a_color;\n  #if TWO_COLORED\n    v_dark = a_color2;\n  #endif\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n      uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 v_light;\n#if TWO_COLORED\n  varying vec4 v_dark;\n#endif\nvarying vec2 uv0;\nuniform sampler2D cc_spriteTexture;\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if TWO_COLORED\n    vec4 texColor = vec4(1, 1, 1, 1);\n    texColor *= texture2D(cc_spriteTexture, uv0);\n     o.a = texColor.a * v_light.a;\n    o.rgb = ((texColor.a - 1.0) * v_dark.a + 1.0 - texColor.rgb) * v_dark.rgb + texColor.rgb * v_light.rgb;\n  #else\n    o *= texture2D(cc_spriteTexture, uv0);\n    o *= v_light;\n  #endif\n  ALPHA_TEST(o);\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "defines": []}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 1}}, "defines": [{"name": "USE_LOCAL", "type": "boolean"}, {"name": "TWO_COLORED", "type": "boolean"}, {"name": "USE_ALPHA_TEST", "type": "boolean"}]}], [{"passes": [{"program": "for2d/builtin-spine|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"alphaThreshold": {"type": 13, "value": [0.5]}}}]}]]], 0, 0, [], [], []], [[[3, "missing-material", [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_COLOR": true}], [[[{}, "mainColor", 8, [4, 4294902015]]], 11]]], 0, 0, [0], [0], [1]], [[[1, "ui-sprite-gray-alpha-sep-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true, "CC_USE_EMBEDDED_ALPHA": true, "IS_GRAY": true}]]], 0, 0, [0], [0], [0]], [[[1, "ui-base-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": false}]]], 0, 0, [0], [0], [0]], [[[1, "ui-sprite-gray-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true, "CC_USE_EMBEDDED_ALPHA": false, "IS_GRAY": true}]]], 0, 0, [0], [0], [0]], [[[1, "ui-graphics-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{}]]], 0, 0, [0], [0], [4]], [[[1, "ui-sprite-alpha-sep-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true, "CC_USE_EMBEDDED_ALPHA": true, "IS_GRAY": false}]]], 0, 0, [0], [0], [0]], [[[1, "ui-sprite-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true, "IS_GRAY": false, "CC_USE_EMBEDDED_ALPHA": false}]]], 0, 0, [0], [0], [0]]]]