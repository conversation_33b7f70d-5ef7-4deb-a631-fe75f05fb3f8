{"importBase": "import", "nativeBase": "native", "name": "resources", "deps": [], "uuids": ["13fYR+3ltI4qggglSdZWfK", "14veyZya9Bd4tRg2nFyIEZ", "1bKPuBkyREMJv99t1eFKOa", "23iNFHijZFSLVshxCeBG38", "32+971J7ZLb5SUh6TugJfY", "3cI+fhIJ1Gm46zi2mQFdrI", "3f7uG2qnpEIJ5XJXSSPBrE", "40X9KBGSRLlri9TjvTti9g", "42geXa0AJECJX7y4Qg2N8E", "43ta2q+EhDj4KoN8pMIIl7", "53FGoTQL5ON7yXELl0cfMS", "58l5IjMD9J3pdlhzav/oa9", "5eqUBwrOpAfbr0PRinIIpJ", "67G2mEIX9KYYncfWE+sZ/3", "6aD8i3l2pD1o0+SLo6H1Nt", "6bogzBA/lOdZg5xfGoaZ9t", "6cjstx5a1KBI3dQKciEWuk", "6cqo3Et+BDJ5Xj0uJXZZhE", "73VcPiFbZNZrcMk+FE7PB2", "9c8xbinVxI7oqiox/JRPmi", "a5xQXX5G9F75BFf6f5+2DO", "a6Sguof9xAz5xU/4PYpd9c", "b6bMPE8iFBGJgAMnYQSSRa", "b8HVLtfVFF8Y4Nn+cJz+dW", "d2EHOpQjRAJL9W545DS4Yz", "dfmPqZuZ5KjbHeF8CGfolB", "e02aQ5YshBW72KWsJcaV25", "eaDw7Ge5tP3p5vuXP9eyvF", "eft16D/6xFh7DDLz7f7mw8", "ffRvPw4A9BCLJQtKBadU6k", "06DrjSr6JJA6LGnsj1IO+k", "06qzBy0NVNVqMJdHOnYhY0", "114150d89", "134f2b90b", "139e8406d", "14839f706", "1afead5f2", "1c88747df", "1d4729ac6", "1eefe6a10", "20g1ukYUVPvKWKBRznAKo+", "202rngU7pF45Mh/Par4jS9", "267JIeTZhLpo5YaGw6ZHwV", "29JJigUnBHPKILBSuBYut6", "2fZZMdzeRDhLlVJ5yJJ/PT", "33toE2QOlGC4BIADCqhcXU", "54TknWPwVPqJqeCR+Y/Czo", "5etCCQ0FFBgLCmR7YaRcNR", "69IRAP9k9KobpaiS1N6Due", "76Xin82dBC0LRikaZ0IRMb", "7c/zc+qgxJqpZsqGAAxgiW", "7dj5uJT9FMn6OrOOx83tfK", "84EH3C5JJMgLFSQUcRgKaI", "89jAx46b5OPZhEmp6iHtAS", "8cgxDVTqhKsr2mxeuMl0BG", "92jTKn25xGH5cWbEXpLefN", "95EkngnxZFbYuFpsqVTaFr", "997/+ua69H06OB1l/u9Ys8", "9dzjvEbDpK87s61koG+L+W", "a36IGHHaNCh44+RimT+uTR", "b2CZjnReJGRbf6Md+qFaqq", "b4VSALBfBPL5TCiCvyO4ms", "b55oNsejhKLIEb2nVL3J6X", "bdG8q6vX1KcbFDmXyII4Pk", "beS5Btt6tDs6ro5syd9VGI", "beYYU8pBxPxbvzAg47rUoh", "c1mWWLWIBISoPC5LUdWzqr", "c3fjiK83BBz4TfJv3ZgnSo", "c6NN14JPBGdpPi5UedihkM", "c6y6kVOexCBr1SwNYDAMI3", "d5XnRTPE1KKYYrWHgWvmHR", "d9T1VXk+9BcLEKhrY6P40S", "d9ljw/yv1LYpwdSu/z4Kif", "ddx1Jylw9Goo4GTKoL55YT", "de9lSSk91EQJNNcYDTItAl", "df0VlD6+NC15x/iUha8EeK", "e0dZztyqRLaq3IMhligQgE", "e8W+4l3khBzrKSvuy9Lum5", "f1fiT/mIJDJbvINTt25iUD", "01X22GK6tP7pqgRAqiXy55@f9941", "04XAuTI3NLX4Y1XAPPgJD+@f9941", "05K3iMQPBOYbqgcAEiyAZE@f9941", "06lCogBHRDhZqFKTNm0kc+@f9941", "07G+s3Fn9OrIJtAJ2ox/4U@f9941", "0aHji/vBRKUIsJ1peb9pIr@f9941", "0e0724ac0", "10g0HIeV1If5mv41H3VzLZ", "103JJDgpdPoZcgsVdrB70W@f9941", "114150d89@6c48a", "134f2b90b@6c48a", "139e8406d@6c48a", "14839f706@6c48a", "18Wb+hZ+ZLEqVMIND8n+9G", "19z3Ub2+FNgbZAT/dog2tG@f9941", "1afead5f2@6c48a", "1c88747df@6c48a", "1d4729ac6@6c48a", "1eP8TYjjhIVpdCApe9nsBW@f9941", "1eefe6a10@6c48a", "1fDP8PuklKRohagAxL5Ywd@f9941", "1fHcoFglVNVq8dUehY6Git", "1fwMdDYbJIeL7/sfx1Sfay@f9941", "20g1ukYUVPvKWKBRznAKo+@6c48a", "20g1ukYUVPvKWKBRznAKo+@f9941", "20l0itQshLhJBu0CKoJfMn@f9941", "24ZmqXqyBMspPgXmtFtY/J@f9941", "26lnNhfqpEHKGH6swoo2x2@f9941", "267JIeTZhLpo5YaGw6ZHwV@6c48a", "27U9A+SpVJ3qsiJJfUBvc5@f9941", "28hu2fRkxE3KkfUZWMI3ga@f9941", "28m2XIfmJOuZh7aH63VABL@f9941", "2bjscvGLxPGaczohuBwyAy@f9941", "33EB9Mz7JCnr+S5Wjr/d/C@f9941", "33toE2QOlGC4BIADCqhcXU@6c48a", "34kGxOPpZO9KTR7P3cX23B@f9941", "3dYiQRacxKoLwV5cxkWHG2@f9941", "3e/jR+FjpHQKDaJdhAnx3i@f9941", "4eT65M1zRPebOsQGtSv1kT", "4fUNNDrE5CSqFVxz9EaBWk@f9941", "4fhUj6xtFExrwOnSPXIrII@f9941", "54TknWPwVPqJqeCR+Y/Czo@6c48a", "54TknWPwVPqJqeCR+Y/Czo@f9941", "56iYX5wGxF8Yp5Y5VwmORh@f9941", "5bFJrl4sxOgqNvkqwx6qbI@f9941", "5b36h5fgZJLZXgD6qimva4@f9941", "5dniXTm3VP7b5W4FgShYjY@f9941", "5d2dR0iCJAjbKnK45zGHuR@f9941", "60CjFV7dVA65w7mANLpzRF@f9941", "61pHXvl4ZPhoQNMj2GeGUo@f9941", "63QZnhTLxKvJAqGF+MCq95@f9941", "65iEcFXYNP15dNMf2Z/sKn@f9941", "66Ph/RAopLW6zUGLencyIg@f9941", "67nvPt5uJFUbcJ88bgs0iE@f9941", "69NFCqjVxO1YPjjE2MCrka@f9941", "6bvv5XaDZB/4jw2MkWUzJ0@f9941", "6b1K7/FI1P14xuzNnv3Bms@f9941", "6di2TK091FlK528AANwa+S@f9941", "6feMJpEZNLfIKS9uhBJDR+@f9941", "6f4T/nRzFAn7i9nXUpLX6/@f9941", "71YFDoi61PdqyIRL2MyPWh@f9941", "75X1FQ0I1MJ5ajnl8Nbi9F@f9941", "7a6P8qmJhPqaGmngqx3KjY@f9941", "7b/AejXv5BX6hLfD4+ApTx", "7c9xZ8rwREoYJ8l0nn/mxN@f9941", "7dj5uJT9FMn6OrOOx83tfK@6c48a", "7dj5uJT9FMn6OrOOx83tfK@f9941", "80aZTOb8NHhKhmfHymN7/z@f9941", "81VUed7i1Cm5RvU2rYH3EE@f9941", "8bMgrzradE0oRMi2NLJ4eN@f9941", "90BcD6kTRMVY9KPcFKPkVc@f9941", "91pk+NWw9Ca6U0T1VDYsR+@f9941", "95EkngnxZFbYuFpsqVTaFr@6c48a", "95EkngnxZFbYuFpsqVTaFr@f9941", "96Ep8K9iRGkaVBXosyfKm3@f9941", "96rO37oD1COICoaYtvSbnQ", "981fGpH4VFRpnJOrWJExRO@f9941", "99FKz9iddPO6I8uq3poFID@f9941", "997/+ua69H06OB1l/u9Ys8@6c48a", "9b16tsTYBIdLvE58DLk3xA@f9941", "9cPDPEwC5PTIQWOzjINcm+@f9941", "9dzjvEbDpK87s61koG+L+W@6c48a", "9fpk8AL/VKjZU1o2UMSshm@f9941", "a1Bwc+58RLpqpe4JPX6e3q@f9941", "a43f7jQ61JeLdfgvFYaoCp@f9941", "a4+sHHRP5F5rfnyU1/qq3R@f9941", "a9EZgs59BFyoKXFD0N2RtF@f9941", "abGH5b4FlE0otQazwknODh@f9941", "aea/XbogpCTrIIHGpK6eoD@f9941", "afLXuNl4FFWrfnrjAxpSMe@f9941", "afNMsFBG9EsLS6zQN+FdHR@f9941", "afej/7uHJFdIOT3Q+DKvK6@f9941", "b2OwEoBEtGYJMxooFXBCnD@f9941", "b8c20D+5tK5ps77wHOlWgC@f9941", "bbNhTWxkpHTbEaHEtw1EPD@f9941", "bdG8q6vX1KcbFDmXyII4Pk@6c48a", "bdG8q6vX1KcbFDmXyII4Pk@f9941", "be+qxmVDxO/oNM8HbI4cy+@f9941", "c0Vy2vT51O56JrHDieOc5u@f9941", "c1dlFla1VL06VEpNMlmaFL@f9941", "c3BfboUWRK0KgILe2SqJsM@f9941", "c3UljnWFhLC6QDug71WR/V@f9941", "c4a3P/5e1CBYyK/DPXbxHy@f9941", "c6DcfVR4tFX5lNsz/jprs9@f9941", "c7/fqBNEBNI45Vd2QsbskJ@f9941", "caPSYb9rVO460Sx0SCqY3Q@f9941", "d0+q49D4RIDawVPcFs8hp7@f9941", "d253zp9gxGhaFiqt2Y/luA@f9941", "d4KIW5CD5Iz4l+3pRl1e1h@f9941", "d4qTq0UmBPfYXRXDYVKLeA@f9941", "d4/JtRSYdEALi9omAeYZ0g@f9941", "d5XnRTPE1KKYYrWHgWvmHR@6c48a", "d63IEFa/ZBX435cQc/OCfU@f9941", "d9T1VXk+9BcLEKhrY6P40S@6c48a", "d986xEunBAWq3XaYCqdQvn@f9941", "e04iW4vq1GvJeuOaXdFgYm@f9941", "e44zvQxnlJdLb6708yaEJ+", "eeTNb2yylDWrUyfziW/2wJ@f9941", "f1XhMz+8RMPJ3d1GrZjij+@f9941", "f1bT5EqzFKOL8gkcIsL20+@f9941", "f7yQDYoB5EhZJ0bCDaTauc@f9941", "f9C2CirmpPtbUjLCX6zbrd@f9941", "fdy02ne1pGfpozDPkf6rzp@f9941"], "paths": {"0": ["audio/Shark_PlayOut", 0, 1], "1": ["audio/Shark_Requested", 0, 1], "2": ["audio/Shark_TurnRound", 0, 1], "3": ["audio/Shark_Request", 0, 1], "4": ["audio/Shark_ThrownBomb", 0, 1], "5": ["audio/Shark_GameOver", 0, 1], "6": ["audio/Shark_Click", 0, 1], "7": ["audio/Shark_Pass", 0, 1], "8": ["audio/Shark_Show", 0, 1], "9": ["audio/Shark_Blessing", 0, 1], "10": ["audio/Shark_Inspect", 0, 1], "11": ["audio/Shark_Changed", 0, 1], "12": ["audio/Shark_Clamp_3", 0, 1], "13": ["audio/Shark_YourTurn", 0, 1], "14": ["audio/Shark_Clamp", 0, 1], "15": ["audio/Shark_Bgm", 0, 1], "16": ["audio/silent", 0, 1], "17": ["audio/Shark_GiveCard", 0, 1], "18": ["audio/Shark_ThrowBomb", 0, 1], "19": ["audio/Shark_Change", 0, 1], "20": ["audio/Shark_FireBomb", 0, 1], "21": ["audio/Shark_Curse", 0, 1], "22": ["audio/Shark_DealCards", 0, 1], "23": ["audio/Shark_BeChanged", 0, 1], "24": ["audio/Shark_Escape", 0, 1], "25": ["audio/Shark_Clamp_2", 0, 1], "26": ["audio/Shark_ThrowSuccess", 0, 1], "27": ["audio/Shark_Bite", 0, 1], "28": ["audio/Shark_Resistance", 0, 1], "29": ["audio/Shark_PressTooth", 0, 1], "30": ["prefabs/core/base/root", 1, 1], "31": ["prefabs/scene/game/ui/action/active/common/choose_player/choose_player", 1, 1], "41": ["prefabs/core/base/exportLogEntry", 1, 1], "44": ["prefabs/scene/game/components/time-count/time-count", 1, 1], "47": ["prefabs/scene/game/components/desk/hand_card_list", 1, 1], "48": ["prefabs/core/base/uploadLog", 1, 1], "49": ["prefabs/core/ui/faceImg", 1, 1], "50": ["prefabs/scene/game/ui/action/active/scout/active_scout", 1, 1], "52": ["prefabs/scene/game/ui/finish/ui_player_out", 1, 1], "53": ["prefabs/scene/game/ui/action/active/mine/active_remove_mine", 1, 1], "54": ["prefabs/core/base/toast", 1, 1], "55": ["prefabs/scene/game/components/card/light_card_item", 1, 1], "59": ["prefabs/components/switch/switch", 1, 1], "60": ["prefabs/scene/game/components/card/dark_card_item", 1, 1], "61": ["prefabs/core/base/loading", 1, 1], "64": ["prefabs/scene/game/ui/action/active/draw/draw", 1, 1], "65": ["prefabs/core/base/ui", 1, 1], "66": ["prefabs/scene/game/ui/action/active/clamp/clamp", 1, 1], "67": ["prefabs/scene/game/ui/action/passive/request/passive_request", 1, 1], "68": ["prefabs/scene/game/components/arrow/arrow", 1, 1], "69": ["prefabs/core/base/black-mask", 1, 1], "72": ["prefabs/scene/game/ui/rule/ui_rule", 1, 1], "73": ["prefabs/scene/game/components/draw_mine/draw_mine", 1, 1], "74": ["prefabs/core/ui/modal/ui_modal", 1, 1], "75": ["prefabs/scene/game/components/player/player_item", 1, 1], "76": ["prefabs/core/base/reconnection", 1, 1], "77": ["prefabs/scene/game/components/card/dark_card_list", 1, 1], "78": ["prefabs/core/base/notice", 1, 1]}, "scenes": {}, "packs": {"0e0724ac0": [79, 80, 81, 30, 82, 31, 83, 84, 86, 87, 32, 88, 33, 89, 0, 34, 90, 35, 91, 1, 92, 93, 36, 94, 2, 37, 95, 38, 96, 97, 39, 98, 99, 100, 101, 40, 102, 103, 104, 41, 3, 105, 106, 42, 107, 108, 109, 110, 111, 44, 4, 112, 45, 113, 114, 5, 115, 116, 6, 7, 8, 9, 117, 118, 119, 10, 46, 120, 121, 122, 11, 123, 124, 125, 126, 12, 47, 127, 128, 129, 130, 131, 13, 132, 48, 133, 14, 15, 134, 135, 16, 17, 136, 137, 138, 139, 18, 140, 49, 141, 142, 143, 50, 51, 144, 145, 146, 147, 52, 53, 148, 54, 149, 150, 55, 56, 151, 152, 153, 154, 155, 156, 57, 157, 158, 159, 19, 58, 160, 161, 162, 59, 163, 164, 20, 21, 165, 166, 167, 168, 169, 170, 60, 171, 61, 22, 23, 172, 173, 63, 174, 175, 64, 65, 176, 177, 178, 66, 179, 180, 67, 181, 182, 68, 69, 183, 184, 185, 24, 186, 187, 188, 189, 70, 190, 191, 71, 192, 72, 193, 73, 74, 25, 75, 76, 26, 194, 195, 77, 27, 196, 28, 197, 198, 78, 199, 200, 201, 29]}, "versions": {"import": [85, "232dd", 43, "93752", 62, "bbcda"], "native": [32, "804ca", 33, "12cec", 0, "06dd3", 34, "0c4d1", 35, "93e70", 1, "78c65", 36, "c7e74", 2, "f11d3", 37, "8d214", 38, "b7a40", 39, "64e48", 40, "90cf4", 3, "c2bc3", 42, "d21d7", 4, "b3166", 45, "904d8", 5, "604fd", 6, "ec41c", 7, "d0309", 8, "6b9c7", 9, "e80e2", 10, "dcc8b", 46, "83fcc", 11, "7f338", 12, "6d642", 13, "9388d", 14, "40d85", 15, "ccbc7", 16, "99afb", 17, "cd630", 18, "94684", 51, "cea68", 56, "c06a9", 57, "04d3e", 19, "34fa5", 58, "cacb8", 20, "770f9", 21, "1bea7", 22, "98eef", 23, "00e98", 63, "17df6", 24, "f1ee4", 70, "cf0f2", 71, "0d2f8", 25, "6a17b", 26, "885a6", 27, "368a0", 28, "69a52", 29, "a4edc"]}, "redirect": [], "debug": false, "extensionMap": {".cconb": [43, 62]}, "hasPreloadScript": true, "dependencyRelationships": {}, "types": ["cc.AudioClip", "cc.Prefab"]}