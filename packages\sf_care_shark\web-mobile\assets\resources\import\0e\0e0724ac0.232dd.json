[1, ["1d4729ac6@6c48a", "1c88747df@6c48a", "134f2b90b@6c48a", "1eefe6a10@6c48a", "61pHXvl4ZPhoQNMj2GeGUo@f9941", "14839f706@6c48a", "1afead5f2@6c48a", "26lnNhfqpEHKGH6swoo2x2@f9941", "d0+q49D4RIDawVPcFs8hp7@f9941", "114150d89@6c48a", "fdy02ne1pGfpozDPkf6rzp@f9941", "92jTKn25xGH5cWbEXpLefN", "7dj5uJT9FMn6OrOOx83tfK@f9941", "29JJigUnBHPKILBSuBYut6", "f9C2CirmpPtbUjLCX6zbrd@f9941", "20g1ukYUVPvKWKBRznAKo+@f9941", "2fZZMdzeRDhLlVJ5yJJ/PT", "c6DcfVR4tFX5lNsz/jprs9@f9941", "139e8406d@6c48a", "b2OwEoBEtGYJMxooFXBCnD@f9941", "28m2XIfmJOuZh7aH63VABL@f9941", "202rngU7pF45Mh/Par4jS9", "bdG8q6vX1KcbFDmXyII4Pk@f9941", "5b36h5fgZJLZXgD6qimva4@f9941", "7a6P8qmJhPqaGmngqx3KjY@f9941", "0aHji/vBRKUIsJ1peb9pIr@f9941", "c7/fqBNEBNI45Vd2QsbskJ@f9941", "4fhUj6xtFExrwOnSPXIrII@f9941", "4fUNNDrE5CSqFVxz9EaBWk@f9941", "1fwMdDYbJIeL7/sfx1Sfay@f9941", "5etCCQ0FFBgLCmR7YaRcNR", "b55oNsejhKLIEb2nVL3J6X", "e44zvQxnlJdLb6708yaEJ+", "abGH5b4FlE0otQazwknODh@f9941", "04XAuTI3NLX4Y1XAPPgJD+@f9941", "c6y6kVOexCBr1SwNYDAMI3", "e0dZztyqRLaq3IMhligQgE", "8cgxDVTqhKsr2mxeuMl0BG", "b4VSALBfBPL5TCiCvyO4ms", "f1fiT/mIJDJbvINTt25iUD", "beYYU8pBxPxbvzAg47rUoh", "df0VlD6+NC15x/iUha8EeK", "d9T1VXk+9BcLEKhrY6P40S@6c48a", "10g0HIeV1If5mv41H3VzLZ", "20g1ukYUVPvKWKBRznAKo+@6c48a", "54TknWPwVPqJqeCR+Y/Czo@f9941", "95EkngnxZFbYuFpsqVTaFr@f9941", "69IRAP9k9KobpaiS1N6Due", "80aZTOb8NHhKhmfHymN7/z@f9941", "6di2TK091FlK528AANwa+S@f9941", "1eP8TYjjhIVpdCApe9nsBW@f9941", "997/+ua69H06OB1l/u9Ys8@6c48a", "54TknWPwVPqJqeCR+Y/Czo@6c48a", "d5XnRTPE1KKYYrWHgWvmHR@6c48a", "d63IEFa/ZBX435cQc/OCfU@f9941", "afej/7uHJFdIOT3Q+DKvK6@f9941", "7dj5uJT9FMn6OrOOx83tfK@6c48a", "a43f7jQ61JeLdfgvFYaoCp@f9941", "9fpk8AL/VKjZU1o2UMSshm@f9941", "66Ph/RAopLW6zUGLencyIg@f9941", "d4KIW5CD5Iz4l+3pRl1e1h@f9941", "19z3Ub2+FNgbZAT/dog2tG@f9941", "34kGxOPpZO9KTR7P3cX23B@f9941", "67nvPt5uJFUbcJ88bgs0iE@f9941", "d4/JtRSYdEALi9omAeYZ0g@f9941", "75X1FQ0I1MJ5ajnl8Nbi9F@f9941", "a5xQXX5G9F75BFf6f5+2DO", "56iYX5wGxF8Yp5Y5VwmORh@f9941", "afLXuNl4FFWrfnrjAxpSMe@f9941", "69NFCqjVxO1YPjjE2MCrka@f9941", "3e/jR+FjpHQKDaJdhAnx3i@f9941", "aea/XbogpCTrIIHGpK6eoD@f9941", "28hu2fRkxE3KkfUZWMI3ga@f9941", "e04iW4vq1GvJeuOaXdFgYm@f9941", "d4qTq0UmBPfYXRXDYVKLeA@f9941", "33EB9Mz7JCnr+S5Wjr/d/C@f9941", "1fDP8PuklKRohagAxL5Ywd@f9941", "d253zp9gxGhaFiqt2Y/luA@f9941", "24ZmqXqyBMspPgXmtFtY/J@f9941", "05K3iMQPBOYbqgcAEiyAZE@f9941", "95EkngnxZFbYuFpsqVTaFr@6c48a", "267JIeTZhLpo5YaGw6ZHwV@6c48a", "06lCogBHRDhZqFKTNm0kc+@f9941", "bdG8q6vX1KcbFDmXyII4Pk@6c48a", "7b/AejXv5BX6hLfD4+ApTx", "4eT65M1zRPebOsQGtSv1kT", "63QZnhTLxKvJAqGF+MCq95@f9941", "e8W+4l3khBzrKSvuy9Lum5", "9dzjvEbDpK87s61koG+L+W@6c48a", "c4a3P/5e1CBYyK/DPXbxHy@f9941", "c3UljnWFhLC6QDug71WR/V@f9941", "5dniXTm3VP7b5W4FgShYjY@f9941", "b8c20D+5tK5ps77wHOlWgC@f9941", "71YFDoi61PdqyIRL2MyPWh@f9941", "96Ep8K9iRGkaVBXosyfKm3@f9941", "c3BfboUWRK0KgILe2SqJsM@f9941", "96rO37oD1COICoaYtvSbnQ", "c0Vy2vT51O56JrHDieOc5u@f9941", "2bjscvGLxPGaczohuBwyAy@f9941", "981fGpH4VFRpnJOrWJExRO@f9941", "caPSYb9rVO460Sx0SCqY3Q@f9941", "07G+s3Fn9OrIJtAJ2ox/4U@f9941", "99FKz9iddPO6I8uq3poFID@f9941", "5bFJrl4sxOgqNvkqwx6qbI@f9941", "ddx1Jylw9Goo4GTKoL55YT", "20l0itQshLhJBu0CKoJfMn@f9941", "a1Bwc+58RLpqpe4JPX6e3q@f9941", "be+qxmVDxO/oNM8HbI4cy+@f9941", "81VUed7i1Cm5RvU2rYH3EE@f9941", "65iEcFXYNP15dNMf2Z/sKn@f9941", "6feMJpEZNLfIKS9uhBJDR+@f9941", "76Xin82dBC0LRikaZ0IRMb", "6f4T/nRzFAn7i9nXUpLX6/@f9941", "1fHcoFglVNVq8dUehY6Git", "18Wb+hZ+ZLEqVMIND8n+9G", "33toE2QOlGC4BIADCqhcXU@6c48a", "b2CZjnReJGRbf6Md+qFaqq"], ["node", "_textureSource", "_spriteFrame", "root", "data", "_parent", "asset", "_target", "_skeletonData", "target", "source", "btn_close", "targetInfo", "card_prefab", "btn_confirm", "title", "_defaultClip", "_cameraComponent", "btn_cancel", "card", "_checkMark", "root_mask", "root_log", "root_toast", "root_ui", "reconnection_ui_prefab", "toast_ui_prefab", "loading_ui_prefab", "notice_ui_prefab", "ui_prefab", "exportLogEntry_prefab", "player_prefab", "_effectAsset", "export_log", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "uploadLogPrefab", "time_count", "time_progres", "editBox", "_backgroundImage", "btn_continue", "logo", "rank", "btn_continue_watching", "btn", "warning_node", "run_time", "btn_run", "clip", "slide_label", "fixed_label", "slide_node", "fixed_node", "loadingTween", "light_card_prefab", "hand_card", "request_tips", "btn_give_card", "dark_card_list", "_texture", "tween", "_indicator", "prompt_content_spriteFrame", "prompt_content_str", "default_title", "confirm_spriteFrame", "cancel_spriteFrame", "node_iFrame", "node_frame", "node_face", "wave_node", "sound_effect", "border", "curse", "throw_mine", "draw_mine_node", "watch_node", "leave_node", "out_node", "auto_node", "tag_node", "countdown_node", "select_arrow_node", "card_count_node", "avatar_node", "nick_name_node", "player_node", "plistImg", "_customMaterial", "common_prompt_text", "list"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos", "_lscale"], -2, 4, 9, 1, 2, 5, 5], "cc.ImageAsset", "cc.Texture2D", ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_enableOutline", "_fontFamily", "_outlineWidth", "_overflow", "_enableWrapText", "_isBold", "_horizontalAlign", "_spacingX", "_verticalAlign", "_shadowBlur", "node", "__prefab", "_outlineColor", "_color", "_shadowColor", "_shadowOffset"], -11, 1, 4, 5, 5, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_top", "_right", "_left", "_bottom", "_alignMode", "_enabled", "node", "__prefab", "_target"], -6, 1, 4, 1], ["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_parent", "_lpos", "_children", "_lrot", "_euler", "_lscale"], -1, 12, 4, 1, 5, 2, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_enabled", "_name", "_fillType", "_fillRange", "node", "__prefab", "_spriteFrame", "_color", "_fillCenter"], -4, 1, 4, 6, 5, 5], ["cc.UITransform", ["_name", "node", "__prefab", "_contentSize", "_anchorPoint"], 2, 1, 4, 5, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_isAlign", "_enabled", "node", "__prefab"], -2, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite"], 2, 1, 4, 5, 1, 6, 6, 6, 6], ["cc.Animation", ["_enabled", "playOnLoad", "node", "__prefab", "_clips", "_defaultClip"], 1, 1, 4, 3, 6], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_premultipliedAlpha", "_preCacheMode", "loop", "node", "__prefab", "_skeletonData"], -2, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Camera", ["_projection", "_priority", "_orthoHeight", "_clearFlags", "_visibility", "_near", "_name", "node", "__prefab", "_color"], -4, 1, 4, 5], ["cc.Toggle", ["_interactable", "_isChecked", "node", "__prefab", "_normalColor", "_target", "_checkMark"], 1, 1, 4, 5, 1, 1], ["391bbcqy2FNGoNRz6GA9IQY", ["_enabled", "node", "__prefab"], 2, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 2, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.<PERSON>", ["node", "__prefab", "_cameraComponent"], 3, 1, 4, 1], ["a4eeaB3ILVI5ZHwShhAJmKw", ["node", "__prefab", "root_ui", "root_toast", "root_log", "root_mask", "reconnection_ui_prefab", "toast_ui_prefab", "loading_ui_prefab", "notice_ui_prefab", "ui_prefab", "exportLogEntry_prefab"], 3, 1, 4, 1, 1, 1, 1, 6, 6, 6, 6, 6, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "nestedPrefabInstanceRoots", "root", "asset", "targetOverrides"], 0, 1, 1, 9], ["f2b7dlhPtBCi4X1Mb5KhCpy", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["cc.TargetInfo", ["localID"], 2], ["8f26110B9FFyq1vlNGJhaR0", ["node", "__prefab", "player_prefab"], 3, 1, 4, 6], ["cc.EffectAsset", ["_name", "shaders", "techniques"], 0], ["cc.AudioClip", ["_name", "_native", "_duration"], 0], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3], ["cc.Material", ["_name", "_props", "_states", "_defines"], -1], ["79ec6VXAGVLdZnVVnHRMA9B", ["node", "__prefab", "export_log", "uploadLogPrefab"], 3, 1, 4, 1, 6], ["587e48tp3ZHiaFtheajyiEe", ["node", "__prefab", "time_progres", "time_count"], 3, 1, 4, 1, 1], ["cc.ProgressBar", ["_totalLength", "_progress", "node", "__prefab", "_barSprite"], 1, 1, 4, 1], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["2200c4FeQ9IFKPYae/pDmyM", ["node", "__prefab", "editBox", "btn_confirm", "btn_cancel"], 3, 1, 4, 1, 1, 1], ["cc.EditBox", ["_inputMode", "_maxLength", "node", "__prefab", "_textLabel", "_placeholder<PERSON><PERSON><PERSON>"], 1, 1, 4, 1, 1], ["b8cc44XHuNPg7/1PiaB4Dbq", ["node", "__prefab"], 3, 1, 4], ["cc.TargetOverrideInfo", ["propertyPath", "source", "target", "targetInfo"], 2, 1, 1, 4], ["d067brVOgVCCbVBs2XCJStv", ["node", "__prefab"], 3, 1, 4], ["f8540FmrDVHiIBAF53gPEpM", ["time_count", "node", "__prefab", "title", "shark", "btn_ok", "ok_time_out", "btn_scout", "scout_count", "main"], 2, 1, 4, 1, 1, 1, 1, 1, 1, 1], ["36c45bVK6xGKpwT+dZegFcx", ["node", "__prefab", "btn_continue_watching", "rank", "title", "logo", "btn_continue", "title_spriteFrame", "logo_spriteFrame", "btn_continue_spriteFrame"], 3, 1, 4, 1, 1, 1, 1, 1, 3, 3, 3], ["df541kk9TVH+ZNbKURSvDhs", ["node", "__prefab", "btn"], 3, 1, 4, 1], ["79b1bo3W1JOBIXRe2xOBLc5", ["type", "loop", "node", "__prefab", "btn_run", "run_time", "warning_node", "clip"], 1, 1, 4, 1, 1, 1, 6], ["cc.UIOpacity", ["node", "__prefab"], 3, 1, 4], ["54022HS/G1NFbF0cq5UR5SF", ["node", "__prefab", "fixed_node", "slide_node", "fixed_label", "slide_label"], 3, 1, 4, 1, 1, 1, 1], ["3f518kApRlMzquJtf5T2Sjl", ["node", "__prefab", "card", "card_spriteFrames"], 3, 1, 4, 1, 12], ["cc.ToggleContainer", ["node", "__prefab"], 3, 1, 4], ["5abc1xIeKxAnqeu/dT8rcko", ["selected_scale", "node", "__prefab", "card"], 2, 1, 4, 1], ["27960MiunNM/YnOymX0hNhP", ["node", "__prefab", "title", "loadingTween"], 3, 1, 4, 1, 1], ["4d7834iahBI6LnPqe/bG7jW", ["time_count", "node", "__prefab", "btn_cancel", "overtime", "<PERSON><PERSON><PERSON>", "shark", "btn_confirm", "remaining_count", "boom_count", "desk_card_info", "card", "tips", "shine", "main"], 2, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["4b0b8ljdchPc6C3oh1gDtNZ", ["node", "__prefab", "tween"], 3, 1, 4, 1], ["eebbbAYGlFFTJDO+Hbv40c4", ["node", "__prefab", "scene_mask", "ui_container"], 3, 1, 4, 1, 1], ["f7472V58lJKtJzeBnAdG+6Q", ["time_count", "node", "__prefab", "title", "shark", "btn_confirm_clamp", "main"], 2, 1, 4, 1, 1, 1, 1], ["6e482dzk5BGSKFwxmh4r8+f", ["node", "__prefab", "btn_give_card", "request_tips", "hand_card", "dark_card_list"], 3, 1, 4, 1, 1, 1, 6], ["e5940FDge1EM7VV/Y8ApTqW", ["node", "__prefab", "list"], 3, 1, 4, 1], ["436a189xp9CKLTq/1G2ZJ5Y", ["node", "__prefab"], 3, 1, 4], ["cc.MotionStreak", ["_preview", "_minSeg", "_stroke", "node", "__prefab", "_texture"], 0, 1, 4, 6], ["d8681hRnjJNZ4UWXyT/AM3G", ["node", "__prefab"], 3, 1, 4], ["<PERSON>.<PERSON>", ["_name", "horizontal", "_sizeMode", "node", "__prefab", "_content", "_indicator"], 0, 1, 4, 1, 1], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["0c065WicL5Jzb4wBbtlY/io", ["node", "__prefab", "btn_close"], 3, 1, 4, 1], ["cc.PageViewIndicator", ["spacing", "node", "__prefab", "_cellSize"], 2, 1, 4, 5], ["a3aa5qX19hPdoJGe+PKPHTw", ["node", "__prefab", "title", "prompt_content_str", "prompt_content_spriteFrame", "btn_confirm", "btn_cancel", "btn_close", "default_title", "confirm_spriteFrame", "cancel_spriteFrame"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 6, 6, 6], ["2c08afmBpNGJJIm4yIWKtUs", ["node", "__prefab", "player_node", "nick_name_node", "avatar_node", "card_count_node", "select_arrow_node", "countdown_node", "tag_node", "auto_node", "out_node", "leave_node", "watch_node", "draw_mine_node", "throw_mine", "curse", "border_spriteFrame", "border", "sound_effect", "wave_node", "node_face", "node_frame", "node_iFrame", "plistImg"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 6], ["3c32ccUAPJMGZXtGzvGvxYD", ["node", "__prefab", "spine_slot", "count_throw_mine_node", "count", "spine_throw_mine"], 3, 1, 4, 1, 1, 1, 1], ["2b91eX856pEtaLpeBl3u+ic", ["node", "__prefab", "curse_node", "curse_count", "curse_count_spriteFrame"], 3, 1, 4, 1, 1, 3], ["fc9e86BCa1D/Y+uhIaSh5bW", ["node", "__prefab", "common_prompt_text", "btn_confirm"], 3, 1, 4, 1, 1], ["f7297dPrxVKXKdsUiwmPItn", ["node", "__prefab", "list", "card_prefab"], 3, 1, 4, 1, 6], ["0ca4bTrLM5LP68TdGQk9X2t", ["node", "__prefab", "text", "btn_confirm"], 3, 1, 4, 1, 1]], [[19, 0, 2], [13, 0, 1, 2, 3, 4, 5, 5], [8, 1, 2, 3, 1], [1, 0, 1, 7, 6, 5, 9, 3], [36, 0, 1, 2, 4], [17, 0, 2], [5, 0, 1, 2, 9, 10, 4], [33, 0, 2], [7, 7, 8, 9, 1], [29, 0, 1, 2, 2], [6, 0, 1, 6, 4, 5, 7, 3], [1, 0, 1, 8, 6, 5, 3], [1, 0, 1, 7, 8, 6, 5, 3], [7, 1, 0, 7, 8, 9, 3], [8, 1, 2, 3, 4, 1], [1, 0, 1, 7, 8, 6, 5, 9, 3], [28, 0, 1, 2, 3], [7, 1, 7, 8, 9, 2], [10, 0, 1, 2, 3, 4, 2], [8, 1, 2, 1], [30, 0, 1, 2, 3], [10, 0, 1, 2, 2], [7, 0, 7, 8, 9, 2], [1, 3, 4, 7, 5, 3], [6, 0, 1, 6, 4, 5, 3], [23, 0, 1, 2, 3, 4, 5, 4], [27, 0, 1, 2, 2], [1, 0, 1, 7, 6, 5, 3], [1, 0, 2, 1, 7, 6, 5, 4], [6, 0, 1, 8, 4, 5, 3], [7, 7, 8, 1], [42, 0, 1, 1], [6, 0, 6, 4, 5, 7, 2], [5, 0, 1, 9, 10, 3], [4, 0, 1, 2, 3, 9, 4, 6, 14, 15, 16, 8], [37, 0, 1, 2, 3, 4, 5], [46, 0, 1, 2, 3, 2], [1, 0, 3, 1, 7, 6, 5, 9, 4], [6, 0, 1, 6, 8, 4, 5, 7, 3], [6, 0, 3, 1, 6, 4, 5, 7, 4], [5, 0, 9, 10, 2], [22, 0, 1, 2, 3, 4, 5, 4], [26, 0, 1, 1], [7, 0, 7, 8, 2], [12, 0, 1, 2, 3, 5, 6, 7, 5], [70, 0, 1, 2, 1], [1, 0, 1, 8, 6, 5, 9, 3], [1, 0, 8, 6, 5, 2], [24, 0, 1, 2, 3, 4, 5, 3], [4, 0, 1, 2, 3, 7, 4, 6, 14, 15, 16, 8], [7, 0, 2, 7, 8, 9, 3], [7, 3, 0, 7, 8, 3], [7, 0, 7, 8, 10, 9, 2], [9, 0, 1, 5, 6, 3], [47, 0, 1, 1], [52, 0, 1, 1], [1, 0, 1, 6, 5, 3], [1, 0, 2, 1, 6, 5, 4], [1, 0, 2, 1, 7, 6, 5, 9, 4], [1, 0, 3, 1, 7, 8, 6, 5, 4], [18, 0, 1, 2, 3, 4, 5, 3], [6, 0, 1, 8, 4, 5, 7, 3], [6, 0, 2, 1, 6, 4, 5, 7, 4], [6, 0, 1, 6, 8, 4, 5, 3], [8, 0, 1, 2, 3, 2], [20, 0, 1, 2, 1], [5, 0, 4, 9, 10, 3], [5, 0, 5, 9, 10, 3], [25, 0, 1, 2, 3, 4, 5, 4], [32, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 6, 14, 15, 16, 7], [4, 0, 1, 2, 5, 3, 8, 4, 6, 14, 15, 16, 9], [4, 0, 1, 2, 5, 3, 7, 4, 6, 14, 15, 16, 9], [4, 0, 1, 14, 15, 3], [4, 0, 1, 2, 5, 3, 8, 4, 6, 14, 15, 17, 16, 9], [7, 1, 0, 7, 8, 3], [59, 0, 1, 2, 1], [1, 0, 3, 1, 7, 6, 5, 4], [1, 0, 6, 5, 2], [1, 0, 3, 2, 1, 7, 8, 6, 5, 9, 5], [1, 0, 2, 1, 7, 8, 6, 5, 9, 4], [1, 0, 1, 7, 6, 5, 9, 10, 3], [1, 0, 1, 6, 5, 9, 3], [1, 0, 2, 1, 7, 8, 6, 5, 4], [1, 0, 7, 8, 6, 5, 9, 2], [1, 0, 7, 6, 5, 9, 2], [1, 0, 1, 8, 6, 5, 9, 10, 3], [1, 0, 7, 8, 6, 5, 2], [1, 0, 2, 1, 8, 6, 5, 4], [1, 0, 3, 1, 8, 6, 5, 9, 4], [1, 0, 3, 1, 8, 6, 5, 9, 10, 4], [1, 0, 1, 7, 8, 6, 5, 9, 10, 3], [1, 0, 3, 2, 1, 7, 6, 5, 5], [1, 0, 2, 1, 7, 6, 5, 9, 10, 4], [6, 0, 2, 1, 6, 4, 5, 4], [6, 0, 2, 6, 4, 5, 7, 3], [6, 0, 1, 6, 4, 5, 9, 10, 3], [6, 0, 1, 6, 4, 5, 7, 11, 3], [5, 0, 5, 4, 3, 6, 1, 2, 9, 10, 8], [5, 0, 3, 6, 1, 2, 9, 10, 6], [5, 8, 0, 1, 2, 9, 10, 5], [5, 0, 4, 1, 2, 9, 10, 5], [5, 0, 3, 9, 10, 3], [5, 0, 1, 2, 9, 10, 11, 4], [5, 0, 6, 7, 9, 10, 4], [5, 0, 5, 4, 3, 6, 1, 2, 7, 9, 10, 9], [5, 0, 5, 3, 7, 9, 10, 5], [5, 0, 4, 3, 7, 9, 10, 5], [5, 9, 10, 1], [21, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [13, 0, 1, 3, 2, 4, 5, 5], [31, 0, 1, 2, 2], [14, 0, 1, 2, 5, 3, 4, 7, 8, 9, 7], [14, 6, 0, 1, 2, 3, 4, 7, 8, 9, 7], [34, 0, 1, 2, 1], [4, 0, 1, 2, 7, 8, 14, 15, 17, 6], [4, 0, 1, 2, 5, 3, 4, 14, 15, 16, 7], [4, 0, 10, 1, 2, 7, 8, 14, 15, 7], [4, 0, 10, 1, 2, 7, 8, 14, 15, 17, 7], [4, 0, 1, 2, 5, 3, 14, 15, 6], [4, 0, 1, 2, 5, 3, 7, 14, 15, 7], [4, 0, 1, 2, 5, 3, 8, 9, 4, 6, 14, 15, 16, 10], [4, 0, 1, 2, 5, 3, 9, 14, 15, 7], [4, 0, 12, 1, 2, 5, 3, 8, 11, 4, 6, 14, 15, 17, 16, 11], [4, 0, 1, 2, 5, 3, 7, 11, 9, 4, 6, 13, 14, 15, 16, 18, 19, 12], [35, 0, 1, 2, 4], [38, 0, 1, 2, 3, 5], [39, 0, 1, 2, 3, 1], [7, 4, 0, 7, 8, 3], [7, 1, 5, 0, 6, 2, 7, 8, 11, 6], [7, 0, 2, 7, 8, 3], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 2], [40, 0, 1, 2, 3, 1], [41, 0, 1, 2, 3, 4, 3], [9, 0, 1, 2, 3, 5, 6, 5], [9, 0, 1, 2, 5, 6, 4], [9, 4, 0, 1, 5, 6, 4], [43, 0, 1, 2, 3, 4, 1], [44, 0, 1, 2, 3, 4, 5, 3], [45, 0, 1, 1], [48, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 2], [49, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [50, 0, 1, 2, 1], [51, 0, 1, 2, 3, 4, 5, 6, 7, 3], [53, 0, 1, 2, 3, 4, 5, 1], [54, 0, 1, 2, 3, 1], [55, 0, 1, 1], [15, 0, 2, 3, 4, 5, 6, 2], [15, 0, 1, 2, 3, 4, 5, 6, 3], [56, 0, 1, 2, 3, 2], [57, 0, 1, 2, 3, 1], [11, 2, 3, 4, 5, 1], [11, 0, 2, 3, 4, 5, 2], [11, 1, 2, 3, 4, 5, 2], [16, 1, 2, 1], [16, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 7, 6], [12, 0, 1, 2, 3, 5, 6, 5], [58, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 2], [60, 0, 1, 2, 3, 1], [61, 0, 1, 2, 3, 4, 5, 6, 2], [62, 0, 1, 2, 3, 4, 5, 1], [63, 0, 1, 2, 1], [64, 0, 1, 1], [65, 0, 1, 2, 3, 4, 5, 4], [66, 0, 1, 1], [67, 0, 1, 2, 3, 4, 5, 6, 4], [68, 0, 1, 1], [69, 0, 1, 2, 1], [71, 0, 1, 2, 3, 2], [72, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [73, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 1], [74, 0, 1, 2, 3, 4, 5, 1], [75, 0, 1, 2, 3, 4, 1], [76, 0, 1, 2, 3, 1], [77, 0, 1, 2, 3, 1], [78, 0, 1, 2, 3, 1]], [[[{"name": "bg_card_info", "rect": {"x": 858, "y": 200, "width": 662, "height": 145}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 666, "height": 149}, "rotated": true, "capInsets": [0, 59, 0, 38], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "big", "rect": {"x": 149, "y": 690, "width": 170, "height": 170}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 170, "height": 170}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "16", "rect": {"x": 3, "y": 457, "width": 236, "height": 300}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[[5, "root"], [46, "root", 33554432, [-13, -14, -15, -16], [[2, -4, [0, "2bnRr+Ek1DFaCgw2Reh+zm"], [5, 1080, 1919.9999999999998]], [65, -6, [0, "22lXstQEpGeKNi7laVCSER"], -5], [40, 45, -7, [0, "6ea3zUix9LGKPFTSmhy083"]], [109, -12, [0, "3dHGNg9uZMlYjWycFhRw7t"], -11, -10, -9, -8, 2, 3, 4, 5, 6, 7]], [41, "acxPGEuBVE761HKUz3N4TP", null, null, -3, 0, [-1, -2]], [1, 540, 959.9999999999999, 0]], [12, "root-ui", 33554432, 1, [-20], [[2, -17, [0, "63hm2ZEqpKG6wz7EtokKOd"], [5, 1080, 1919.9999999999998]], [6, 45, 100, 100, -18, [0, "7aIHEJ35NFo7B4WLSmmSUm"]], [42, -19, [0, "c0fIJ3/ARLdoSvjGApihjZ"]]], [1, "03cQkYXXVNtp/0m894w1Kk", null, null, null, 1, 0]], [12, "root_log", 33554432, 1, [-24], [[2, -21, [0, "efkuzTDxRFa6UpBuj9LG32"], [5, 1080, 1919.9999999999998]], [6, 45, 100, 100, -22, [0, "aeX9E8PfZIq7mQOOD90qWN"]], [42, -23, [0, "777FcmPUFEuJfOeg/caD1c"]]], [1, "6dTv7x6EhCCqX6YNXYj0df", null, null, null, 1, 0]], [27, "root_toast", 33554432, 1, [[2, -25, [0, "31vkYyN/pAw5PNDHb48UPk"], [5, 1080, 1919.9999999999998]], [6, 45, 100, 100, -26, [0, "e9bmOSeZ5O3JKzcQ7m9/Kd"]], [42, -27, [0, "777FcmPUFEuJfOeg/caD1c"]]], [1, "2aX092fptMta4uvx8BH2BL", null, null, null, 1, 0]], [23, 0, {}, 2, [25, "c46/YsCPVOJYA4mWEpNYRx", null, null, -32, [26, "f1YO3BCvJMQJFEgr9hwNHy", 1, [[16, "black-mask", ["_name"], -28], [9, ["_lpos"], -29, [1, 0, 0, 0]], [9, ["_lrot"], -30, [3, 0, 0, 0, 1]], [9, ["_euler"], -31, [1, 0, 0, 0]], [20, null, ["_target"], [7, ["ac3XQDNNVBGLOx9lpWvSkl"]]], [111, ["_target"], [7, ["9alnJofXhHQrDX+SnpBIYw"]], 1]]], 0]], [7, ["c46/YsCPVOJYA4mWEpNYRx"]], [7, ["c4VaBoqKdGIrcU0YgUonOh"]], [23, 0, {}, 3, [25, "c4VaBoqKdGIrcU0YgUonOh", null, null, -33, [26, "8aBEMXGdNJl5Viqe0NcNx0", 1, [[16, "exportLogEntry", ["_name"], 7], [9, ["_lpos"], 7, [1, 0, 0, 0]], [9, ["_lrot"], 7, [3, 0, 0, 0, 1]], [9, ["_euler"], 7, [1, 0, 0, 0]]]], 1]], [60, "Camera", 33554432, 1, [-34], [1, "4blFXbiQhOiJr5TvmtH7pG", null, null, null, 1, 0], [1, 0, 0, 1000]], [112, 0, 65535, 959.9999999999999, 0, 6, 1108344832, 9, [0, "9clM26P5NJQI178j2l3vy2"], [4, 4278190080]]], 0, [0, -1, 8, 0, -2, 5, 0, 3, 1, 0, 0, 1, 0, 17, 10, 0, 0, 1, 0, 0, 1, 0, 21, 5, 0, 22, 3, 0, 23, 4, 0, 24, 2, 0, 0, 1, 0, -1, 9, 0, -2, 2, 0, -3, 4, 0, -4, 3, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 8, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 12, 6, 0, 12, 6, 0, 12, 6, 0, 12, 6, 0, 3, 5, 0, 3, 8, 0, -1, 10, 0, 4, 1, 34], [0, 0, 0, 0, 0, 0, 0, 0], [6, 6, 25, 26, 27, 28, 29, 30], [35, 21, 36, 37, 38, 39, 40, 21]], [[{"name": "loading", "rect": {"x": 714, "y": 660, "width": 138, "height": 138}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 138, "height": 138}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[[5, "choose_player"], [11, "choose_player", 32768, [-5], [[2, -2, [0, "2dwwy3LAZES7m2W8Muz+0W"], [5, 1080, 1920]], [6, 45, 100, 100, -3, [0, "1arlxr7wRLeKMsQ/rN1hct"]], [114, -4, [0, "79DhKja7RBf44+NgvdUe4G"], 0]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [3, "Label", 32768, 1, [[2, -6, [0, "beGRkCbm1PkYhD6RMzp8bt"], [5, 276, 67.44]], [70, "选择一位玩家", 44, 44, 0, true, 6, -7, [0, "11QL7tPZpFHLwytdAsPM29"], [4, 4291385356]]], [1, "9a1Vpy+iJEZ7a25RXGK3IN", null, null, null, 1, 0], [1, 0, 72.476, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 4, 1, 7], [0], [31], [41]], [[{"name": "mask", "rect": {"x": 840, "y": 200, "width": 121, "height": 121}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 121, "height": 121}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "title_out", "rect": {"x": 3, "y": 911, "width": 328, "height": 108}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 328, "height": 108}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[[125, "../res/shader/CircleAvatar", [{"hash": 774022156, "name": "../res/shader/CircleAvatar|sprite-vs:vert|sprite-fs:frag", "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}, {"name": "ARGS", "stageFlags": 16, "binding": 1, "members": [{"name": "radius", "type": 13, "count": 1}, {"name": "blur", "type": 13, "count": 1}, {"name": "center", "type": 14, "count": 1}, {"name": "wh_ratio", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}, {"name": "a_color", "format": 44, "location": 2, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["USE_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}, {"name": "ARGS", "stageFlags": 16, "binding": 1, "members": [{"name": "radius", "type": 13, "count": 1}, {"name": "blur", "type": 13, "count": 1}, {"name": "center", "type": 14, "count": 1}, {"name": "wh_ratio", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n  uniform highp mat4 cc_matViewProj;\n#if USE_LOCAL\n  uniform highp mat4 cc_matWorld;\n#endif\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\nvarying vec4 v_color;\nvarying vec2 v_uv0;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  #if USE_PIXEL_ALIGNMENT\n    pos = cc_matView * pos;\n    pos.xyz = floor(pos.xyz);\n    pos = cc_matProj * pos;\n  #else\n    pos = cc_matViewProj * pos;\n  #endif\n  v_color = a_color;\n  v_uv0 = a_texCoord;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 CCSampleWithAlphaSeparated(sampler2D tex, vec2 uv) {\n#if CC_USE_EMBEDDED_ALPHA\n  return vec4(texture2D(tex, uv).rgb, texture2D(tex, uv + vec2(0.0, 0.5)).r);\n#else\n  return texture2D(tex, uv);\n#endif\n}\n#if USE_ALPHA_TEST\n      uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 v_color;\n#if USE_TEXTURE\n  varying vec2 v_uv0;\n  uniform sampler2D cc_spriteTexture;\n#endif\n   uniform float radius;\n   uniform float blur;\n   uniform vec2 center;\n   uniform float wh_ratio;\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  o *= CCSampleWithAlphaSeparated(cc_spriteTexture, v_uv0);\n  o *= v_color;\n  ALPHA_TEST(o);\n  float circle = radius * radius;\n  float rx = (v_uv0.x - center.x) * wh_ratio;\n  float ry = v_uv0.y - center.y;\n  float dis = rx * rx + ry * ry;\n  o.a = smoothstep(circle, circle - blur, dis) * o.a;\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "defines": ["USE_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 5}}, "defines": [{"name": "USE_LOCAL", "type": "boolean"}, {"name": "USE_PIXEL_ALIGNMENT", "type": "boolean"}, {"name": "CC_USE_EMBEDDED_ALPHA", "type": "boolean"}, {"name": "USE_ALPHA_TEST", "type": "boolean"}, {"name": "USE_TEXTURE", "type": "boolean"}]}], [{"passes": [{"program": "../res/shader/CircleAvatar|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"alphaThreshold": {"type": 13, "value": [0.5]}, "wh_ratio": {"type": 13, "value": [1]}, "blur": {"type": 13, "value": [0.01]}, "radius": {"type": 13, "value": [0.5]}, "center": {"type": 14, "value": [0.5, 0.5]}}}]}]]], 0, 0, [], [], []], [[{"name": "dashboard", "rect": {"x": 3, "y": 835, "width": 565, "height": 68}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 565, "height": 68}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"fmt": "0", "w": 934, "h": 1023}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["114150d89"]}], [3], 0, [], [], []], [[{"fmt": "0", "w": 1018, "h": 1014}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["134f2b90b"]}], [3], 0, [], [], []], [[[4, "Shark_PlayOut", ".mp3", 0.862041], -1], 0, 0, [], [], []], [[{"fmt": "0", "w": 242, "h": 918}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["139e8406d"]}], [3], 0, [], [], []], [[{"fmt": "0", "w": 982, "h": 1001}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["14839f706"]}], [3], 0, [], [], []], [[[4, "Shark_Requested", ".mp3", 6.112653], -1], 0, 0, [], [], []], [[[35, "shua<PERSON><PERSON>", "\nshuaiguo.png\nsize: 418,375\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nsg\n  rotate: true\n  xy: 2, 2\n  size: 371, 355\n  orig: 464, 477\n  offset: 39, 62\n  index: -1\nsp_0\n  rotate: true\n  xy: 359, 7\n  size: 44, 32\n  orig: 74, 58\n  offset: 16, 14\n  index: -1\nsp_1\n  rotate: false\n  xy: 359, 53\n  size: 53, 40\n  orig: 74, 58\n  offset: 10, 9\n  index: -1\nsp_2\n  rotate: true\n  xy: 359, 95\n  size: 61, 48\n  orig: 74, 58\n  offset: 5, 6\n  index: -1\nsp_3\n  rotate: true\n  xy: 359, 158\n  size: 70, 55\n  orig: 74, 58\n  offset: 3, 1\n  index: -1\nsp_4\n  rotate: true\n  xy: 359, 230\n  size: 69, 56\n  orig: 74, 58\n  offset: 1, 1\n  index: -1\nsp_5\n  rotate: true\n  xy: 359, 301\n  size: 72, 57\n  orig: 74, 58\n  offset: 0, 1\n  index: -1\n", ["shuaiguo.png"], {"skeleton": {"hash": "lBoeiWOGKb/92H2RaP7f/bgxa3A", "spine": "3.8.99", "x": 476.63, "y": -732.98, "width": 561.37, "height": 551.28, "images": "./images/", "audio": "E:/XM/商业策划部/拆雷鸭动效/16抛雷"}, "bones": [{"name": "root"}, {"name": "a", "parent": "root"}, {"name": "sg", "parent": "a", "rotation": -101.73, "x": 734.78, "y": -471.03, "scaleX": -1}, {"name": "sg2", "parent": "sg", "length": 283.98, "rotation": -48.11, "x": -78.23, "y": 89.98}, {"name": "sp_0", "parent": "root", "scaleX": 5.1645, "scaleY": 5.1645}], "slots": [{"name": "sp_0", "bone": "sp_0"}, {"name": "sg", "bone": "sg2", "attachment": "sg"}], "skins": [{"name": "default", "attachments": {"sg": {"sg": {"x": 116.88, "y": 24.42, "rotation": 48.11, "width": 464, "height": 477}}, "sp_0": {"sp_0": {"width": 74, "height": 58}, "sp_1": {"width": 74, "height": 58}, "sp_2": {"width": 74, "height": 58}, "sp_3": {"width": 74, "height": 58}, "sp_4": {"width": 74, "height": 58}, "sp_5": {"width": 74, "height": 58}}}}], "animations": {"shuaiguo": {"slots": {"sg": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}, "sp_0": {"attachment": [{"time": 1.1667, "name": "sp_2"}, {"time": 1.2, "name": "sp_3"}, {"time": 1.2333, "name": "sp_4"}, {"time": 1.2667, "name": "sp_5"}, {"time": 1.3, "name": null}, {"time": 1.3667, "name": "sp_2"}, {"time": 1.4, "name": "sp_3"}, {"time": 1.4333, "name": "sp_4"}, {"time": 1.4667, "name": "sp_5"}, {"time": 1.5, "name": null}]}}, "bones": {"sg2": {"rotate": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 50.25, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 41.71, "curve": 0.25, "c3": 0.75}, {"time": 1.3667}], "shear": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "y": -14.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -14.8, "curve": 0.25, "c3": 0.75}, {"time": 1.3667}]}, "sp_0": {"translate": [{"time": 1.1667, "x": -37.31, "y": -45.42, "curve": "stepped"}, {"time": 1.3, "x": -37.31, "y": -45.42, "curve": "stepped"}, {"time": 1.3667, "x": -47.04, "y": -53.53}], "scale": [{"time": 1.1667, "x": 1.188, "y": 1.188, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 1.445, "y": 1.445, "curve": "stepped"}, {"time": 1.3667, "x": 1.351, "y": 1.351, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.445, "y": 1.445}]}, "sg": {"rotate": [{"angle": 111.61}, {"time": 0.0333, "angle": 154.42}, {"time": 0.0667, "angle": -162.77}, {"time": 0.1, "angle": -112.03}, {"time": 0.1333, "angle": -61.28}, {"time": 0.1667, "angle": -11.25}, {"time": 0.2, "angle": 38.78}, {"time": 0.2333, "angle": 75.19}, {"time": 0.2667, "angle": 111.61}, {"time": 0.3, "angle": 154.42}, {"time": 0.3333, "angle": -162.77}, {"time": 0.3667, "angle": -61.28}, {"time": 0.4, "angle": -11.25}, {"time": 0.4333, "angle": 38.78}, {"time": 0.4667, "angle": 111.61}, {"time": 0.5, "angle": 154.42}, {"time": 0.5333, "angle": -162.77}, {"time": 0.5667, "angle": -61.28}, {"time": 0.6, "angle": -11.25}, {"time": 0.6333, "angle": 38.78}, {"time": 0.6667, "angle": 111.61}], "translate": [{}, {"time": 0.0333, "x": -2.73, "y": 237.45}, {"time": 0.0667, "x": -53.46, "y": 391.49}, {"time": 0.1, "x": -127.88, "y": 506.9}, {"time": 0.1333, "x": -201.08, "y": 576.63}, {"time": 0.1667, "x": -282.42, "y": 624.1}, {"time": 0.2, "x": -352.42, "y": 645.47}, {"time": 0.2333, "x": -424.07, "y": 650.93}, {"time": 0.2667, "x": -477.94, "y": 644.57}, {"time": 0.3, "x": -530.69, "y": 629.6}, {"time": 0.3333, "x": -570.47, "y": 612.43}, {"time": 0.3667, "x": -606.86, "y": 592.11}, {"time": 0.4, "x": -638.51, "y": 570.68}, {"time": 0.4333, "x": -660.69, "y": 553.47}, {"time": 0.4667, "x": -682.79, "y": 534.46}, {"time": 0.5, "x": -698.57, "y": 519.69}, {"time": 0.5333, "x": -713.91, "y": 504.35}, {"time": 0.5667, "x": -725.04, "y": 492.59}, {"time": 0.6, "x": -734.39, "y": 482.29}, {"time": 0.6333, "x": -739.63, "y": 476.36}, {"time": 0.6667, "x": -743.03, "y": 472.43}], "scale": [{"x": 0.507, "y": 0.507}, {"time": 0.0333, "x": 0.581, "y": 0.581}, {"time": 0.0667, "x": 0.655, "y": 0.655}, {"time": 0.1, "x": 0.729, "y": 0.729}, {"time": 0.1333, "x": 0.803, "y": 0.803}, {"time": 0.1667, "x": 0.876, "y": 0.876}, {"time": 0.2, "x": 0.95, "y": 0.95}, {"time": 0.2333, "x": 1.024, "y": 1.024}, {"time": 0.2667, "x": 1.098, "y": 1.098}, {"time": 0.3, "x": 1.065, "y": 1.065}, {"time": 0.3333, "x": 1.032, "y": 1.032}, {"time": 0.3667, "x": 0.998, "y": 0.998}, {"time": 0.4, "x": 0.965, "y": 0.965}, {"time": 0.4333, "x": 0.932, "y": 0.932}, {"time": 0.4667, "x": 0.899, "y": 0.899}, {"time": 0.5, "x": 0.865, "y": 0.865}, {"time": 0.5333, "x": 0.832, "y": 0.832}, {"time": 0.5667, "x": 0.799, "y": 0.799}, {"time": 0.6, "x": 0.766, "y": 0.766}, {"time": 0.6333, "x": 0.732, "y": 0.732}, {"time": 0.6667, "x": 0.699, "y": 0.699}, {"time": 0.7, "x": 0.912, "y": 0.912}, {"time": 0.7333, "x": 1.125, "y": 1.125}, {"time": 0.8}]}, "a": {"scale": [{"time": 1.6667}, {"time": 1.8333, "x": 0.597, "y": 0.597}]}}}, "shuaiguo2": {"slots": {"sg": {"color": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00"}]}, "sp_0": {"attachment": [{"time": 0.1, "name": "sp_2"}, {"time": 0.1333, "name": "sp_3"}, {"time": 0.1667, "name": "sp_4"}, {"time": 0.2, "name": "sp_5"}, {"time": 0.2333, "name": null}]}}, "bones": {"sg2": {"rotate": [{"angle": 90.22, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.05}, {"time": 0.2333, "angle": -1.86}], "translate": [{"x": -58.12, "y": 61.86}, {"time": 0.1, "x": 1.34, "y": -8.43}, {"time": 0.1667, "x": -0.75, "y": 4.71}, {"time": 0.2333}], "shear": [{"y": -14.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1}]}, "sp_0": {"translate": [{"x": -17.5, "y": 12.76}], "scale": [{"x": 0.791, "y": 0.791, "curve": "stepped"}, {"time": 0.1, "x": 0.791, "y": 0.791, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.839, "y": 0.839}]}, "sg": {"rotate": [{"angle": 110.73}], "translate": [{"x": -730.92, "y": 519.45}], "scale": [{"x": 0.624, "y": 0.624, "curve": "stepped"}, {"time": 0.3333, "x": 0.624, "y": 0.624}, {"time": 0.5, "x": 0.362, "y": 0.362}]}}}}}, [0]]], 0, 0, [0], [-1], [42]], [[{"name": "btn_confirm", "rect": {"x": 337, "y": 911, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"fmt": "0", "w": 947, "h": 923}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["1afead5f2"]}], [3], 0, [], [], []], [[[4, "Shark_TurnRound", ".mp3", 1.880816], -1], 0, 0, [], [], []], [[{"fmt": "0", "w": 1023, "h": 1024}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["1c88747df"]}], [3], 0, [], [], []], [[{"fmt": "0", "w": 1024, "h": 1024}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["1d4729ac6"]}], [3], 0, [], [], []], [[{"name": "time_progress_top", "rect": {"x": 985, "y": 3, "width": 206, "height": 34}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 206, "height": 36}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"fmt": "0", "w": 1021, "h": 966}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["1eefe6a10"]}], [3], 0, [], [], []], [[{"name": "13", "rect": {"x": 309, "y": 481, "width": 236, "height": 300}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[[126, "CircleAvatar", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true}]]], 0, 0, [0], [32], [43]], [[{"name": "btn_confirm", "rect": {"x": 634, "y": 911, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [2], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["20g1ukYUVPvKWKBRznAKo+"]}], [3], 0, [], [], []], [[{"name": "default_btn_normal", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [44]], [[{"name": "label_x", "rect": {"x": 707, "y": 835, "width": 28, "height": 29}, "offset": {"x": 1, "y": -0.5}, "originalSize": {"width": 32, "height": 34}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[[5, "exportLogEntry"], [11, "exportLogEntry", 33554432, [-6], [[2, -2, [0, "d8R3KCuwVGupwN7oxtSEQY"], [5, 1080, 1920]], [127, -4, [0, "60euiL3WBGvY+t6AVAG/I/"], -3, 5], [6, 45, 1080, 1920, -5, [0, "45vm4VG4tG0J710+V1pW1E"]]], [1, "c4VaBoqKdGIrcU0YgUonOh", null, null, null, -1, 0]], [15, "<PERSON><PERSON>", 33554432, 1, [-12], [[2, -7, [0, "53QstonBxK251QG+uS6Cpt"], [5, 100, 40]], [13, 1, 0, -8, [0, "20nv/qiTZBwJWm/jpst/4v"], 0], [131, 2, -10, [0, "c5j9RfIkdOibbOtwhTrk9F"], [4, 4292269782], -9, 1, 2, 3, 4], [66, 34, 49.38300000000004, -11, [0, "d1xACi0ZJGPqKGjKRqeeRX"]]], [1, "48Wm6q74JA4JhSJLxI0Y9J", null, null, null, 1, 0], [1, 440.61699999999996, 0, 1000]], [77, "Label", 512, 33554432, 2, [[2, -13, [0, "b5AEbjlvNIoZ8IcSC8w7L0"], [5, 100, 40]], [115, "导出日志", 20, 20, 1, false, -14, [0, "5aWB7CnDtNgJLnEsYjotDS"], [4, 4278190080]]], [1, "90EUrW9pdGSZk4xbEN0eLw", null, null, null, 1, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 33, 2, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 7, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, 0, 3, 0, 0, 3, 0, 4, 1, 14], [0, 0, 0, 0, 0, 0], [2, 34, 35, 36, 37, 38], [15, 15, 15, 45, 46, 47]], [[[4, "Shark_Request", ".mp3", 0.992625], -1], 0, 0, [], [], []], [[{"name": "15", "rect": {"x": 615, "y": 485, "width": 236, "height": 300}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[{"name": "common_title", "rect": {"x": 840, "y": 710, "width": 195, "height": 108}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 195, "height": 108}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [2], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["267JIeTZhLpo5YaGw6ZHwV"]}], [3], 0, [], [], []], [[{"name": "btn_draw_2", "rect": {"x": 3, "y": 835, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[{"name": "8", "rect": {"x": 718, "y": 3, "width": 235, "height": 300}, "offset": {"x": -0.5, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[{"name": "avatar_border", "rect": {"x": 840, "y": 60, "width": 134, "height": 134}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 134, "height": 134}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "state_out", "rect": {"x": 881, "y": 974, "width": 76, "height": 47}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 76, "height": 47}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[[5, "time-count"], [61, "time-count", 33554432, [-8, -9, -10], [[[2, -2, [0, "61GXN1+XxLUrl7pLiD+chz"], [5, 218, 46]], [17, 1, -3, [0, "c1CL8b7SxNGLRVP0QYMsqN"], 1], -4, [132, -7, [0, "66xhH67ntA1av+sEAh+gzK"], -6, -5]], 4, 4, 1, 4], [1, "57lc+k+ixJpJCfphQW9Jwl", null, null, null, -1, 0], [1, 0, -328, 0]], [10, "Bar", 33554432, 1, [[[14, -11, [0, "b6F6NH8CxPV5TLx67n3k5A"], [5, 206, 34], [0, 0, 0.5]], -12, [67, 8, 5.222999999999999, -13, [0, "44O0Y5KKpMjqNWUHkw2N0Z"]]], 4, 1, 4], [1, "e1mZeOYG1IXKGy3HAnfC01", null, null, null, 1, 0], [1, -103.777, 0, 0]], [3, "clock", 33554432, 1, [[2, -14, [0, "57cU2RGuhBzYD7csHlWKFJ"], [5, 69, 66]], [8, -15, [0, "5adrA30eBJm7aasZnGDKBY"], 0]], [1, "5555PXCehPaZ4yc9gKf5QV", null, null, null, 1, 0], [1, -97.937, 0, 0]], [24, "count", 33554432, 1, [[[2, -16, [0, "edIuwAo0VHN6YfJkmprsH9"], [5, 33.380859375, 39.28]], -17], 4, 1], [1, "52EYBhBddOjY4dvY20phS1", null, null, null, 1, 0]], [75, 2, 0, 2, [0, "c5NAwKcWVCXKHaoaXrVq+P"]], [116, "0s", 28, 28, "Source Han Sans CN", 0, true, 4, [0, "0fmqrVyUVJhZbt8rl9PcXF"], [4, 4284573709]], [133, 206, 1, 1, [0, "f1nYAMlWZPi4IiB04e1Bgw"], 5]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -3, 7, 0, 39, 6, 0, 40, 7, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, 0, 2, 0, -2, 5, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, -2, 6, 0, 4, 1, 17], [0, 0, 5], [2, 2, 2], [48, 49, 50]], [[[4, "Shark_ThrownBomb", ".mp3", 0.313469], -1], 0, 0, [], [], []], [[{"name": "12", "rect": {"x": 3, "y": 699, "width": 236, "height": 300}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["33toE2QOlGC4BIADCqhcXU"]}], [3], 0, [], [], []], [[{"name": "dangerous_border", "rect": {"x": 259, "y": 3, "width": 216, "height": 207}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 216, "height": 207}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[[4, "Shark_GameOver", ".mp3", 2.45551], -1], 0, 0, [], [], []], [[{"name": "btn_play", "rect": {"x": 300, "y": 835, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[{"name": "5", "rect": {"x": 309, "y": 723, "width": 236, "height": 300}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[[4, "<PERSON>_<PERSON><PERSON>", ".mp3", 0.20898], -1], 0, 0, [], [], []], [[[4, "Shark_Pass", ".mp3", 1.645714], -1], 0, 0, [], [], []], [[[4, "Shark_Show", ".mp3", 0.783673], -1], 0, 0, [], [], []], [[[4, "Shark_Blessing", ".mp3", 3.709388], -1], 0, 0, [], [], []], [[[35, "09-suoqu", "\n09-suoqu.png\nsize: 934,291\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\ns1\n  rotate: true\n  xy: 224, 64\n  size: 67, 103\n  orig: 70, 105\n  offset: 1, 1\n  index: -1\ns10\n  rotate: true\n  xy: 746, 194\n  size: 95, 93\n  orig: 97, 95\n  offset: 1, 1\n  index: -1\ns12\n  rotate: true\n  xy: 152, 2\n  size: 60, 61\n  orig: 62, 63\n  offset: 1, 1\n  index: -1\ns13\n  rotate: true\n  xy: 677, 4\n  size: 69, 76\n  orig: 71, 78\n  offset: 1, 1\n  index: -1\ns14\n  rotate: true\n  xy: 2, 64\n  size: 225, 220\n  orig: 228, 222\n  offset: 1, 1\n  index: -1\ns15\n  rotate: false\n  xy: 560, 70\n  size: 99, 104\n  orig: 99, 104\n  offset: 0, 0\n  index: -1\ns16\n  rotate: true\n  xy: 224, 133\n  size: 156, 148\n  orig: 158, 150\n  offset: 1, 1\n  index: -1\ns17\n  rotate: false\n  xy: 400, 66\n  size: 64, 65\n  orig: 67, 68\n  offset: 1, 2\n  index: -1\ns18\n  rotate: true\n  xy: 374, 133\n  size: 38, 41\n  orig: 42, 43\n  offset: 2, 1\n  index: -1\ns19\n  rotate: false\n  xy: 2, 2\n  size: 75, 60\n  orig: 77, 63\n  offset: 1, 2\n  index: -1\ns2\n  rotate: false\n  xy: 823, 100\n  size: 52, 54\n  orig: 55, 57\n  offset: 2, 1\n  index: -1\ns3\n  rotate: true\n  xy: 466, 66\n  size: 69, 92\n  orig: 72, 94\n  offset: 1, 1\n  index: -1\ns4\n  rotate: true\n  xy: 362, 3\n  size: 61, 104\n  orig: 64, 107\n  offset: 1, 2\n  index: -1\ns5\n  rotate: false\n  xy: 468, 8\n  size: 54, 56\n  orig: 57, 59\n  offset: 1, 2\n  index: -1\ns6\n  rotate: false\n  xy: 661, 75\n  size: 71, 100\n  orig: 73, 102\n  offset: 1, 1\n  index: -1\ns7\n  rotate: true\n  xy: 841, 211\n  size: 78, 91\n  orig: 81, 94\n  offset: 1, 2\n  index: -1\ns8\n  rotate: true\n  xy: 877, 102\n  size: 52, 54\n  orig: 56, 57\n  offset: 2, 1\n  index: -1\ns9\n  rotate: false\n  xy: 734, 79\n  size: 87, 97\n  orig: 90, 100\n  offset: 1, 2\n  index: -1\nsd/gx_0\n  rotate: false\n  xy: 589, 177\n  size: 112, 112\n  orig: 116, 116\n  offset: 2, 2\n  index: -1\nsd/sd0\n  rotate: false\n  xy: 486, 175\n  size: 30, 114\n  orig: 51, 116\n  offset: 13, 0\n  index: -1\nsd/sd1\n  rotate: false\n  xy: 412, 174\n  size: 35, 115\n  orig: 51, 116\n  offset: 11, 0\n  index: -1\nsd/sd2\n  rotate: true\n  xy: 417, 137\n  size: 35, 115\n  orig: 51, 116\n  offset: 3, 0\n  index: -1\nsd/sd2_0\n  rotate: true\n  xy: 79, 2\n  size: 60, 71\n  orig: 79, 75\n  offset: 4, 3\n  index: -1\nsd/sd2_1\n  rotate: false\n  xy: 812, 9\n  size: 78, 68\n  orig: 79, 75\n  offset: 1, 3\n  index: -1\nsd/sd2_2\n  rotate: false\n  xy: 601, 4\n  size: 74, 64\n  orig: 79, 75\n  offset: 1, 4\n  index: -1\nsd/sd2_3\n  rotate: false\n  xy: 841, 156\n  size: 77, 53\n  orig: 79, 75\n  offset: 1, 7\n  index: -1\nsd/sd2_4\n  rotate: true\n  xy: 755, 3\n  size: 74, 55\n  orig: 79, 75\n  offset: 4, 8\n  index: -1\nsd/sd2_5\n  rotate: false\n  xy: 215, 3\n  size: 76, 59\n  orig: 79, 75\n  offset: 2, 11\n  index: -1\nsd/sd2_6\n  rotate: false\n  xy: 524, 9\n  size: 75, 55\n  orig: 79, 75\n  offset: 1, 15\n  index: -1\nsd/sd2_7\n  rotate: false\n  xy: 293, 5\n  size: 67, 57\n  orig: 79, 75\n  offset: 2, 14\n  index: -1\nsd/sd2_8\n  rotate: true\n  xy: 329, 66\n  size: 65, 69\n  orig: 79, 75\n  offset: 4, 1\n  index: -1\nsd/sd3\n  rotate: false\n  xy: 374, 173\n  size: 36, 116\n  orig: 51, 116\n  offset: 9, 0\n  index: -1\nsd/sd4\n  rotate: false\n  xy: 547, 176\n  size: 40, 113\n  orig: 51, 116\n  offset: 9, 3\n  index: -1\nsd/sd5\n  rotate: false\n  xy: 449, 175\n  size: 35, 114\n  orig: 51, 116\n  offset: 11, 2\n  index: -1\nsd/sd6\n  rotate: false\n  xy: 703, 178\n  size: 41, 111\n  orig: 51, 116\n  offset: 2, 4\n  index: -1\nsd/sd7\n  rotate: false\n  xy: 518, 175\n  size: 27, 114\n  orig: 51, 116\n  offset: 12, 2\n  index: -1\n", ["09-suoqu.png"], {"skeleton": {"hash": "srxyuT1EmJnLY/FXmgv8nTTjz1g", "spine": "3.8.99", "x": -283.25, "y": 578.52, "width": 608.85, "height": 686.74, "images": "./images/", "audio": "E:/XM/商业策划部/拆雷鸭动效/11给牌"}, "bones": [{"name": "root"}, {"name": "a", "parent": "root", "rotation": 10.71, "x": -1.49, "y": 1200.6, "scaleX": 1.3764, "scaleY": 1.3764}, {"name": "s16", "parent": "a", "length": 75.47, "rotation": -101.65, "x": -1.45, "y": 2.75}, {"name": "s15", "parent": "s16", "length": 47.23, "rotation": 1.43, "x": 75.03, "y": -1.65}, {"name": "s14", "parent": "s15", "length": 158.86, "rotation": -4.4, "x": 44.85, "y": 0.34}, {"name": "s18", "parent": "s14", "length": 37.09, "rotation": -54.49, "x": 140.3, "y": -84.55}, {"name": "s20", "parent": "s18", "x": 44.34, "y": -1.67}, {"name": "s19", "parent": "s20", "length": 49.2, "rotation": -1.67, "x": 1.07, "y": -0.55}, {"name": "s9", "parent": "s14", "length": 52.89, "rotation": -27.69, "x": 162.73, "y": -54.03}, {"name": "s8", "parent": "s9", "x": 61.33, "y": 1.71}, {"name": "s7", "parent": "s8", "length": 60.85, "rotation": 3.31, "x": 11.18, "y": -0.01}, {"name": "s6", "parent": "s14", "length": 55.25, "rotation": 0.4, "x": 165.72, "y": 10.48}, {"name": "s5", "parent": "s6", "x": 63.07, "y": 0.51}, {"name": "s4", "parent": "s5", "length": 68.68, "rotation": -0.43, "x": 10.28, "y": 0.25}, {"name": "s3", "parent": "s14", "length": 48.3, "rotation": 25.16, "x": 154.64, "y": 71.8}, {"name": "s2", "parent": "s3", "x": 57.62, "y": -0.28}, {"name": "s1", "parent": "s2", "length": 74.14, "rotation": 0.34, "x": 9.5, "y": 0.3}, {"name": "s13", "parent": "s14", "length": 34.69, "rotation": 84.5, "x": 113.96, "y": 107.44}, {"name": "s12", "parent": "s13", "x": 39.21, "y": -0.73}, {"name": "s10", "parent": "s12", "length": 80.82, "rotation": -28.55, "x": 12.24, "y": -4.37}, {"name": "sd/sd0", "parent": "root", "x": -15.53, "y": 1046.89, "scaleX": 9.3279, "scaleY": 6.2461}, {"name": "sd/sd2_0", "parent": "root", "rotation": -51.88, "x": -4.93, "y": 1048.63, "scaleX": 2.9326, "scaleY": 2.9326}, {"name": "sd/gx_0", "parent": "root", "x": -3.29, "y": 1023.35, "scaleX": 1.6576, "scaleY": 1.0359}], "slots": [{"name": "s19", "bone": "s19", "attachment": "s19"}, {"name": "s18", "bone": "s20", "attachment": "s18"}, {"name": "s17", "bone": "s18", "attachment": "s17"}, {"name": "s16", "bone": "s16", "attachment": "s16"}, {"name": "s15", "bone": "s15", "attachment": "s15"}, {"name": "sd/gx_0", "bone": "sd/gx_0", "attachment": "sd/gx_0", "blend": "additive"}, {"name": "sd/sd0", "bone": "sd/sd0", "blend": "additive"}, {"name": "s14", "bone": "s14", "attachment": "s14"}, {"name": "s13", "bone": "s13", "attachment": "s13"}, {"name": "s12", "bone": "s12", "attachment": "s12"}, {"name": "s10", "bone": "s10", "attachment": "s10"}, {"name": "s9", "bone": "s9", "attachment": "s9"}, {"name": "s8", "bone": "s8", "attachment": "s8"}, {"name": "s7", "bone": "s7", "attachment": "s7"}, {"name": "s6", "bone": "s6", "attachment": "s6"}, {"name": "s5", "bone": "s5", "attachment": "s5"}, {"name": "s4", "bone": "s4", "attachment": "s4"}, {"name": "s3", "bone": "s3", "attachment": "s3"}, {"name": "s2", "bone": "s2", "attachment": "s2"}, {"name": "s1", "bone": "s1", "attachment": "s1"}, {"name": "sd/sd2_0", "bone": "sd/sd2_0", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"s1": {"s1": {"x": 43.78, "y": 3.43, "rotation": 73.03, "width": 70, "height": 105}}, "s2": {"s2": {"x": 7.05, "y": -0.47, "rotation": 73.37, "width": 55, "height": 57}}, "s3": {"s3": {"x": 29.28, "y": -2.18, "rotation": 73.37, "width": 72, "height": 94}}, "s4": {"s4": {"x": 39.24, "y": 3.06, "rotation": 98.56, "width": 64, "height": 107}}, "s5": {"s5": {"x": 7.05, "y": 3.52, "rotation": 98.13, "width": 57, "height": 59}}, "s6": {"s6": {"x": 34.41, "y": 2.97, "rotation": 98.13, "width": 73, "height": 102}}, "s7": {"s7": {"x": 37.09, "y": 3.81, "rotation": 122.91, "width": 81, "height": 94}}, "s8": {"s8": {"x": 5.89, "y": 6.72, "rotation": 126.22, "width": 56, "height": 57}}, "s9": {"s9": {"x": 32.41, "y": 7.72, "rotation": 126.22, "width": 90, "height": 100}}, "s10": {"s10": {"x": 48.97, "y": 2.63, "rotation": 42.59, "width": 97, "height": 95}}, "s12": {"s12": {"x": 7.04, "y": -6.91, "rotation": 14.04, "width": 62, "height": 63}}, "s13": {"s13": {"x": 17.51, "y": -6.07, "rotation": 14.04, "width": 71, "height": 78}}, "s14": {"s14": {"x": 92.01, "y": 3.71, "rotation": 101.9, "width": 228, "height": 222}}, "s15": {"s15": {"type": "mesh", "hull": 4, "width": 99, "height": 104, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [73.76, 60.2, 86.93, -37.92, -16.14, -51.76, -29.32, 46.36], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "s16": {"s16": {"x": 43.82, "y": 1.14, "rotation": 101.65, "width": 158, "height": 150}}, "s17": {"s17": {"x": 16.29, "y": 7.36, "rotation": 153.02, "width": 67, "height": 68}}, "s18": {"s18": {"x": -1.45, "y": 7.27, "rotation": 153.02, "width": 42, "height": 43}}, "s19": {"s19": {"x": 33.95, "y": 6.98, "rotation": 154.69, "width": 77, "height": 63}}, "sd/gx_0": {"sd/gx_0": {"width": 116, "height": 116}}, "sd/sd0": {"sd/sd0": {"x": -0.83, "y": -56.35, "width": 51, "height": 116}, "sd/sd1": {"x": -0.83, "y": -56.35, "width": 51, "height": 116}, "sd/sd2": {"x": -0.83, "y": -56.35, "width": 51, "height": 116}, "sd/sd3": {"x": -0.83, "y": -56.35, "width": 51, "height": 116}, "sd/sd4": {"x": -0.83, "y": -56.35, "width": 51, "height": 116}, "sd/sd5": {"x": -0.83, "y": -56.35, "width": 51, "height": 116}, "sd/sd6": {"x": -0.83, "y": -56.35, "width": 51, "height": 116}, "sd/sd7": {"x": -0.83, "y": -56.35, "width": 51, "height": 116}}, "sd/sd2_0": {"sd/sd2_0": {"x": 0.5, "y": 0.5, "width": 79, "height": 75}, "sd/sd2_1": {"x": 0.5, "y": 0.5, "width": 79, "height": 75}, "sd/sd2_2": {"x": 0.5, "y": 0.5, "width": 79, "height": 75}, "sd/sd2_3": {"x": 0.5, "y": 0.5, "width": 79, "height": 75}, "sd/sd2_4": {"x": 0.5, "y": 0.5, "width": 79, "height": 75}, "sd/sd2_5": {"x": 0.5, "y": 0.5, "width": 79, "height": 75}, "sd/sd2_6": {"x": 0.5, "y": 0.5, "width": 79, "height": 75}, "sd/sd2_7": {"x": 0.5, "y": 0.5, "width": 79, "height": 75}, "sd/sd2_8": {"x": 0.5, "y": 0.5, "width": 79, "height": 75}}}}], "animations": {"idle": {"slots": {"sd/gx_0": {"color": [{"time": 2.8667, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 1.0667, "name": "sd/gx_0"}]}, "sd/sd0": {"attachment": [{"time": 1.0667, "name": "sd/sd0"}, {"time": 1.1333, "name": "sd/sd1"}, {"time": 1.2, "name": "sd/sd2"}, {"time": 1.2667, "name": "sd/sd3"}, {"time": 1.3333, "name": "sd/sd4"}, {"time": 1.4, "name": "sd/sd5"}, {"time": 1.4667, "name": "sd/sd6"}, {"time": 1.5333, "name": "sd/sd7"}, {"time": 1.6, "name": "sd/sd0"}, {"time": 1.6667, "name": "sd/sd1"}, {"time": 1.7333, "name": "sd/sd2"}, {"time": 1.8, "name": "sd/sd3"}, {"time": 1.8667, "name": "sd/sd4"}, {"time": 1.9333, "name": "sd/sd5"}, {"time": 2, "name": "sd/sd6"}, {"time": 2.0667, "name": "sd/sd7"}, {"time": 2.1333, "name": "sd/sd0"}, {"time": 2.2, "name": "sd/sd1"}, {"time": 2.2667, "name": "sd/sd2"}, {"time": 2.3333, "name": "sd/sd3"}, {"time": 2.4, "name": "sd/sd4"}, {"time": 2.4667, "name": "sd/sd5"}, {"time": 2.5333, "name": "sd/sd6"}, {"time": 2.6, "name": "sd/sd7"}, {"time": 2.6667, "name": "sd/sd0"}, {"time": 2.7333, "name": "sd/sd1"}, {"time": 2.8, "name": "sd/sd2"}, {"time": 2.8667, "name": "sd/sd3"}, {"time": 2.9333, "name": "sd/sd4"}, {"time": 3, "name": "sd/sd5"}, {"time": 3.0667, "name": "sd/sd6"}, {"time": 3.1333, "name": "sd/sd7"}]}, "sd/sd2_0": {"color": [{"time": 3, "color": "ffffffff", "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 3.2, "color": "ffffff00"}], "attachment": [{"time": 1.0667, "name": "sd/sd2_0"}, {"time": 1.1333, "name": "sd/sd2_1"}, {"time": 1.2, "name": "sd/sd2_2"}, {"time": 1.2667, "name": "sd/sd2_3"}, {"time": 1.3333, "name": "sd/sd2_4"}, {"time": 1.3667, "name": "sd/sd2_5"}, {"time": 1.4333, "name": "sd/sd2_6"}, {"time": 1.5, "name": "sd/sd2_7"}, {"time": 1.5667, "name": "sd/sd2_0"}, {"time": 1.6333, "name": "sd/sd2_1"}, {"time": 1.7, "name": "sd/sd2_2"}, {"time": 1.7667, "name": "sd/sd2_3"}, {"time": 1.8333, "name": "sd/sd2_4"}, {"time": 1.8667, "name": "sd/sd2_5"}, {"time": 1.9333, "name": "sd/sd2_6"}, {"time": 2, "name": "sd/sd2_7"}, {"time": 2.0667, "name": "sd/sd2_0"}, {"time": 2.1333, "name": "sd/sd2_1"}, {"time": 2.2, "name": "sd/sd2_2"}, {"time": 2.2667, "name": "sd/sd2_3"}, {"time": 2.3333, "name": "sd/sd2_4"}, {"time": 2.3667, "name": "sd/sd2_5"}, {"time": 2.4333, "name": "sd/sd2_6"}, {"time": 2.5, "name": "sd/sd2_7"}, {"time": 2.5667, "name": "sd/sd2_0"}, {"time": 2.6333, "name": "sd/sd2_1"}, {"time": 2.7, "name": "sd/sd2_2"}, {"time": 2.7667, "name": "sd/sd2_3"}, {"time": 2.8333, "name": "sd/sd2_4"}, {"time": 2.8667, "name": "sd/sd2_5"}, {"time": 2.9333, "name": "sd/sd2_6"}, {"time": 3, "name": "sd/sd2_7"}, {"time": 3.0667, "name": "sd/sd2_0"}, {"time": 3.1333, "name": "sd/sd2_1"}, {"time": 3.2, "name": "sd/sd2_2"}]}}, "bones": {"root": {"rotate": [{"time": 0.3333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.9}]}, "a": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "translate": [{"y": 1037.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": -18.75, "curve": "stepped"}, {"time": 1.0667, "y": -18.75, "curve": "stepped"}, {"time": 3, "y": -18.75, "curve": "stepped"}, {"time": 3.2, "y": -18.75}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s18": {"rotate": [{}, {"time": 0.2667, "angle": 8.02, "curve": "stepped"}, {"time": 0.4, "angle": 8.02, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.9333, "angle": 8.02, "curve": "stepped"}, {"time": 1.0667, "angle": 8.02, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 63.01, "curve": "stepped"}, {"time": 2.3667, "angle": 63.01, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 8.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 28.58}, {"time": 3, "angle": 33.7, "curve": "stepped"}, {"time": 3.2, "angle": 33.7}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -0.27, "y": 3.98, "curve": "stepped"}, {"time": 2.3667, "x": -0.27, "y": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s20": {"rotate": [{}, {"time": 0.2667, "angle": 10.35, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.9333, "angle": 10.35, "curve": "stepped"}, {"time": 1.0667, "angle": 10.35, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 24.07, "curve": "stepped"}, {"time": 2.3667, "angle": 24.07, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 10.35, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 23.17, "curve": "stepped"}, {"time": 3, "angle": 23.17, "curve": "stepped"}, {"time": 3.2, "angle": 23.17}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s19": {"rotate": [{}, {"time": 0.2667, "angle": 14.42, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.0667, "angle": 14.42, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 146.82, "curve": "stepped"}, {"time": 2.3667, "angle": 146.82, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 14.42, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 23.17, "curve": "stepped"}, {"time": 3, "angle": 23.17, "curve": "stepped"}, {"time": 3.2, "angle": 23.17}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s9": {"rotate": [{}, {"time": 0.2667, "angle": 8.02, "curve": "stepped"}, {"time": 0.4, "angle": 8.02, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.9333, "angle": 8.02, "curve": "stepped"}, {"time": 1.0667, "angle": 8.02, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 29.3, "curve": "stepped"}, {"time": 2.3667, "angle": 29.3, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 8.02, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 18.5}, {"time": 3, "angle": 23.62, "curve": "stepped"}, {"time": 3.2, "angle": 23.62}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s8": {"rotate": [{}, {"time": 0.2667, "angle": 10.35, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.9333, "angle": 10.35, "curve": "stepped"}, {"time": 1.0667, "angle": 10.35, "curve": "stepped"}, {"time": 1.3667, "angle": 10.35, "curve": "stepped"}, {"time": 2.3667, "angle": 10.35, "curve": "stepped"}, {"time": 2.5, "angle": 10.35, "curve": "stepped"}, {"time": 2.6333, "angle": 12.44, "curve": "stepped"}, {"time": 3, "angle": 12.44, "curve": "stepped"}, {"time": 3.2, "angle": 12.44}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s7": {"rotate": [{}, {"time": 0.2667, "angle": 14.42, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.0667, "angle": 14.42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": -13.07, "curve": "stepped"}, {"time": 1.3667, "angle": -13.07, "curve": "stepped"}, {"time": 2.3667, "angle": -13.07, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 5.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6333, "angle": 16.71, "curve": "stepped"}, {"time": 3, "angle": 16.71, "curve": "stepped"}, {"time": 3.2, "angle": 16.71}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667}, {"time": 1.2, "x": -0.769, "curve": "stepped"}, {"time": 1.3667, "x": -0.769, "curve": "stepped"}, {"time": 2.3667, "x": -0.769, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s3": {"rotate": [{}, {"time": 0.2667, "angle": -4.55, "curve": "stepped"}, {"time": 0.4667, "angle": -4.55, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1, "angle": -4.55, "curve": "stepped"}, {"time": 1.0667, "angle": -4.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -21.71, "curve": "stepped"}, {"time": 2.3667, "angle": -21.71, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -4.55, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -15.94}, {"time": 3, "angle": -17.18, "curve": "stepped"}, {"time": 3.2, "angle": -17.18}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 1.5667, "x": 1.035, "y": 1.035, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 1.9667, "x": 1.035, "y": 1.035, "curve": "stepped"}, {"time": 2.1667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s2": {"rotate": [{}, {"time": 0.2667, "angle": -8.15, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.9333, "angle": -8.15, "curve": "stepped"}, {"time": 1.0667, "angle": -8.15, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -6.11, "curve": "stepped"}, {"time": 2.3667, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -8.15, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -15.94, "curve": "stepped"}, {"time": 3, "angle": -15.94, "curve": "stepped"}, {"time": 3.2, "angle": -15.94}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s1": {"rotate": [{}, {"time": 0.2667, "angle": -13.87, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.0667, "angle": -13.87, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 4.33, "curve": "stepped"}, {"time": 2.3667, "angle": 4.33, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -13.36, "curve": "stepped"}, {"time": 3, "angle": -13.36, "curve": "stepped"}, {"time": 3.2, "angle": -13.36}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s13": {"rotate": [{}, {"time": 0.2667, "angle": -4.55, "curve": "stepped"}, {"time": 0.4667, "angle": -4.55, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1, "angle": -4.55, "curve": "stepped"}, {"time": 1.0667, "angle": -4.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -53.94, "curve": "stepped"}, {"time": 2.3667, "angle": -53.94, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -4.55, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -29.03}, {"time": 3, "angle": -32.63, "curve": "stepped"}, {"time": 3.2, "angle": -32.63}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -11.38, "y": -8.77, "curve": "stepped"}, {"time": 2.3667, "x": -11.38, "y": -8.77, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": -6.86, "y": -9.77, "curve": "stepped"}, {"time": 3, "x": -6.86, "y": -9.77, "curve": "stepped"}, {"time": 3.2, "x": -6.86, "y": -9.77}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s12": {"rotate": [{}, {"time": 0.2667, "angle": -8.15, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.9333, "angle": -8.15, "curve": "stepped"}, {"time": 1.0667, "angle": -8.15, "curve": "stepped"}, {"time": 1.3667, "angle": -8.15, "curve": "stepped"}, {"time": 2.3667, "angle": -8.15, "curve": "stepped"}, {"time": 2.5, "angle": -8.15, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -40.01, "curve": "stepped"}, {"time": 3, "angle": -40.01, "curve": "stepped"}, {"time": 3.2, "angle": -40.01}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s10": {"rotate": [{}, {"time": 0.2667, "angle": -13.87, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.0667, "angle": -13.87, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -36.01, "curve": "stepped"}, {"time": 2.3667, "angle": -36.01, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -13.87, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -2.91, "curve": "stepped"}, {"time": 3, "angle": -2.91, "curve": "stepped"}, {"time": 3.2, "angle": -2.91}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": 0.868, "y": 0.891, "curve": "stepped"}, {"time": 3, "x": 0.868, "y": 0.891, "curve": "stepped"}, {"time": 3.2, "x": 0.868, "y": 0.891}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s6": {"rotate": [{}, {"time": 0.2667, "angle": -2.82, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.8, "angle": -2.82, "curve": "stepped"}, {"time": 1.0667, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 2.38, "curve": "stepped"}, {"time": 2.3667, "angle": 2.38, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -2.29, "curve": "stepped"}, {"time": 3, "angle": -2.29, "curve": "stepped"}, {"time": 3.2, "angle": -2.29}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5}, {"time": 2.6333, "x": 0.956, "curve": "stepped"}, {"time": 3, "x": 0.956, "curve": "stepped"}, {"time": 3.2, "x": 0.956}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s5": {"rotate": [{}, {"time": 0.2667, "angle": -4.61, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.9333, "angle": -4.61, "curve": "stepped"}, {"time": 1.0667, "angle": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 12.17, "curve": "stepped"}, {"time": 2.3667, "angle": 12.17, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -4.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 0.51, "curve": "stepped"}, {"time": 3, "angle": 0.51, "curve": "stepped"}, {"time": 3.2, "angle": 0.51}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s4": {"rotate": [{}, {"time": 0.2667, "angle": -6.03, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.0667, "angle": -6.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "angle": -4.77, "curve": "stepped"}, {"time": 1.3667, "angle": -4.77, "curve": "stepped"}, {"time": 2.3667, "angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 5.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6333, "angle": -6.59, "curve": "stepped"}, {"time": 3, "angle": -6.59, "curve": "stepped"}, {"time": 3.2, "angle": -6.59}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5}, {"time": 2.6333, "x": -2.51, "y": -0.67, "curve": "stepped"}, {"time": 3, "x": -2.51, "y": -0.67, "curve": "stepped"}, {"time": 3.2, "x": -2.51, "y": -0.67}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667}, {"time": 1.2, "x": -0.769, "curve": "stepped"}, {"time": 1.3667, "x": -0.769, "curve": "stepped"}, {"time": 2.3667, "x": -0.769, "curve": 0.25, "c3": 0.75}, {"time": 2.5}, {"time": 2.6333, "x": 0.888, "curve": "stepped"}, {"time": 3, "x": 0.888, "curve": "stepped"}, {"time": 3.2, "x": 0.888}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s16": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s15": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "s14": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667}, {"time": 1.3667, "angle": -13.26, "curve": "stepped"}, {"time": 1.5667, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 7.53, "curve": "stepped"}, {"time": 2.1, "angle": 7.53}, {"time": 2.3667, "angle": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 4.18, "y": -0.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 4.18, "y": -0.04, "curve": "stepped"}, {"time": 1.0667, "x": 4.18, "y": -0.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": 412.69, "y": -3.49, "curve": "stepped"}, {"time": 1.5667, "x": 412.69, "y": -3.49, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 412.82, "y": 11.92, "curve": "stepped"}, {"time": 2.1, "x": 412.82, "y": 11.92, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 412.69, "y": -3.49, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 504.23, "y": -4.26, "curve": "stepped"}, {"time": 2.6333, "x": 504.23, "y": -4.26}, {"time": 2.7667, "x": 532.14, "y": -4.49}, {"time": 3, "x": -3.59, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": 0.29}, {"time": 3.2, "x": -1.37, "y": 0.01}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2.5, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 3.2}]}, "sd/sd0": {"scale": [{"x": 0.556, "y": 0.126, "curve": "stepped"}, {"time": 1.0667, "x": 0.556, "y": 0.126, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "y": 0.927, "curve": "stepped"}, {"time": 2.3667, "y": 0.927, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": 1.068, "curve": "stepped"}, {"time": 2.7667, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.432, "y": 0.185, "curve": "stepped"}, {"time": 3.2, "x": 0.432, "y": 0.185}]}, "sd/sd2_0": {"scale": [{"time": 3, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 3.2, "x": 1.48, "y": 1.48}]}, "sd/gx_0": {"scale": [{"x": 0.279, "y": 0.279, "curve": "stepped"}, {"time": 1.0667, "x": 0.279, "y": 0.279, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.796, "y": 0.796, "curve": "stepped"}, {"time": 2.8667, "x": 0.796, "y": 0.796}, {"time": 3, "x": 0.434, "y": 0.434}]}}, "drawOrder": [{"time": 1.2, "offsets": [{"slot": "s7", "offset": -2}, {"slot": "s4", "offset": -4}]}, {"time": 2.4333}]}}}, [0]]], 0, 0, [0], [-1], [51]], [[{"name": "toast", "rect": {"x": 3, "y": 419, "width": 841, "height": 65}, "offset": {"x": 2.5, "y": 0}, "originalSize": {"width": 850, "height": 65}, "rotated": false, "capInsets": [343, 0, 344, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "bg_warn", "rect": {"x": 920, "y": 3, "width": 95, "height": 341}, "offset": {"x": -4.5, "y": 0}, "originalSize": {"width": 104, "height": 341}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[[4, "Shark_Inspect", ".mp3", 1.123265], -1], 0, 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [2], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["54TknWPwVPqJqeCR+Y/Czo"]}], [3], 0, [], [], []], [[{"name": "default_btn_pressed", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [52]], [[{"name": "1", "rect": {"x": 615, "y": 727, "width": 236, "height": 300}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[[4, "Shark_Changed", ".mp3", 0.287347], -1], 0, 0, [], [], []], [[{"name": "bg_nick_name", "rect": {"x": 3, "y": 868, "width": 144, "height": 35}, "offset": {"x": 0, "y": -2}, "originalSize": {"width": 144, "height": 39}, "rotated": true, "capInsets": [21, 0, 33, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "btn_yellow_bg", "rect": {"x": 840, "y": 3, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [5]], [[{"name": "rule_3", "rect": {"x": 3, "y": 3, "width": 831, "height": 826}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 831, "height": 826}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "stage", "rect": {"x": 3, "y": 525, "width": 537, "height": 306}, "offset": {"x": 0, "y": 1}, "originalSize": {"width": 541, "height": 308}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [9]], [[[4, "Shark_Clamp_3", ".mp3", 0.504], -1], 0, 0, [], [], []], [[[5, "hand_card_list"], [78, "hand_card_list", [[2, -2, [0, "8aQaZa8hVGgZhhsRTLSmTa"], [5, 80, 295]], [134, 1, 1, -80, true, -3, [0, "700FYOHUpE05ofRLRxISjP"]], [98, 5, 420, 420, 32.5, 32.5, 240, 100, -4, [0, "20mBsDCohC3rXCzDlc7tbA"]]], [1, "cdE8mOophJm6wT0UhPe3Op", null, null, null, -1, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 4, 1, 4], [], [], []], [[{"name": "btn_effect_on", "rect": {"x": 597, "y": 835, "width": 85, "height": 85}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 85, "height": 85}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[{"name": "btn_close", "rect": {"x": 94, "y": 835, "width": 77, "height": 85}, "offset": {"x": 0, "y": 0.5}, "originalSize": {"width": 77, "height": 86}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [6]], [[{"name": "btn_confirm", "rect": {"x": 840, "y": 300, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [5]], [[{"name": "+3", "rect": {"x": 954, "y": 419, "width": 67, "height": 49}, "offset": {"x": -0.5, "y": 0}, "originalSize": {"width": 70, "height": 49}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "title_win", "rect": {"x": 840, "y": 742, "width": 242, "height": 99}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 242, "height": 99}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[[4, "Shark_YourTurn", ".mp3", 1.201633], -1], 0, 0, [], [], []], [[{"name": "dangerous", "rect": {"x": 858, "y": 3, "width": 162, "height": 151}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 162, "height": 151}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[[5, "uploadLog"], [11, "uploadLog", 33554432, [-9], [[2, -2, [0, "6eUW35Su9LBYNmZQCu1iHw"], [5, 1080, 1919.9999999999998]], [6, 45, 100, 100, -3, [0, "98pZuMTQBEOpiKtbCJRaF/"]], [31, -4, [0, "18a1CgMb5Ez5JNrIfL+nIy"]], [137, -8, [0, "92L1EdU0dOhL92lbnXeHi7"], -7, -6, -5]], [110, "c46/YsCPVOJYA4mWEpNYRx", null, null, [], -1, 0]], [11, "main", 33554432, [-12, -13, -14, -15, -16], [[2, -10, [0, "e8ahYqtXNJWadkGiXnr/OX"], [5, 785, 516]], [22, 0, -11, [0, "74ctsN2XpBy5txX20zBRs+"], 4]], [1, "dadkok6m9LtZkXdljuKQHx", null, null, null, 1, 0]], [38, "EditBox", 33554432, 2, [-20, -21], [[[2, -17, [0, "8bMOcOGSVIL6L+36rIxsX3"], [5, 378.159, 94.53975]], [13, 1, 0, -18, [0, "72YFCB199NcJ0dGQ6T8rjn"], 3], -19], 4, 4, 1], [1, "f8OhXQCMpHaJqZZRsyE6+F", null, null, null, 1, 0], [1, 10.489000000000033, 3.8010000000001583, 0]], [10, "btn_close", 33554432, 2, [[[2, -22, [0, "baC4lv9TNGS72SM17Wl0he"], [5, 77, 86]], [13, 1, 0, -23, [0, "1eLx9BepJCcr82JJRYKs8E"], 2], -24], 4, 4, 1], [1, "17mQCOnBRES64FZVMz5mp4", null, null, null, 1, 0], [1, 307.443, 219.025, 0]], [12, "ui_modal", 33554432, 1, [2], [[2, -25, [0, "e7FLsoEkNHJKn0vzw5GrM5"], [5, 1080, 1919.9999999999998]], [6, 45, 100, 100, -26, [0, "fcNUKh2KNBYJqZ0ubylbCM"]]], [1, "95InSLr5VFIYcSRkge9/44", null, null, null, 1, 0]], [10, "btn_confirm", 33554432, 2, [[[2, -27, [0, "b4+FshBtFKwrGHs+G3ljNf"], [5, 291, 104]], [13, 1, 0, -28, [0, "13veipj2tDqJmg+qvkJusx"], 1], -29], 4, 4, 1], [1, "69cM+xERtCKrc+Tu2+I179", null, null, null, 1, 0], [1, 2.384, -134.607, 0]], [3, "title", 33554432, 2, [[2, -30, [0, "e5cLbHUuREz5VixiMPO/Me"], [5, 195, 108]], [22, 0, -31, [0, "07qTVSw0ZEqbD0lXCAeHUe"], 0]], [1, "b5kRBzHYlDGI93/Amklj6k", null, null, null, 1, 0], [1, -0.014, 241.822, 0]], [3, "prompt_content_str", 33554432, 2, [[2, -32, [0, "2fVkqTf2tPAItS9a8sEBa5"], [5, 624.955875, 85.6]], [49, "请输入日志名称", 47, 47, 60, 3, true, 5, -33, [0, "54oh+9R4dFc737N/nBJ7/U"], [4, 4290741029]]], [1, "3dv5b69WlPxIkiPUZRmIQn", null, null, null, 1, 0], [1, 9.663000000000011, 105.09300000000019, 0]], [62, "TEXT_LABEL", false, 33554432, 3, [[[14, -34, [0, "dcV3hNoaJLhou1c82n4C++"], [5, 376.159, 94.53975], [0, 0, 1]], -35], 4, 1], [1, "3a9S0ERqdGOba+Ns9ZH1mD", null, null, null, 1, 0], [1, -187.0795, 47.269875, 0]], [10, "PLACEHOLDER_LABEL", 33554432, 3, [[[14, -36, [0, "ddeyEINixHPJCT9COPSiaH"], [5, 376.159, 94.53975], [0, 0, 1]], -37], 4, 1], [1, "abjlQIrq1Cj5kF8ueawwf3", null, null, null, 1, 0], [1, -187.0795, 47.269875, 0]], [21, 3, 6, [0, "edGeoOtQZD/rU0qZKboChv"]], [18, 3, 4, [0, "5ef5a3ra9HPZx4gYvGz3mE"], [4, 4292269782], 4], [117, "", 0, 40, 20, 1, false, 9, [0, "72elgXrsNPBJfeGPI6AX/Y"]], [118, "请输入日志名称", 0, 32, 32, 1, false, 10, [0, "dao+LHtQ5HyadTZ8ehhNMe"], [4, 4290493371]], [138, 6, 8, 3, [0, "73bZlcWKtGaYjE1svvTTiL"], 13, 14]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 18, 12, 0, 14, 11, 0, 41, 15, 0, 0, 1, 0, -1, 5, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, -2, 8, 0, -3, 6, 0, -4, 4, 0, -5, 3, 0, 0, 3, 0, 0, 3, 0, -3, 15, 0, -1, 9, 0, -2, 10, 0, 0, 4, 0, 0, 4, 0, -3, 12, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, -3, 11, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 13, 0, 0, 10, 0, -2, 14, 0, 4, 1, 2, 5, 5, 37], [0, 0, 0, 0, 0, 15], [2, 2, 2, 2, 2, 42], [7, 10, 4, 22, 8, 22]], [[{"name": "4", "rect": {"x": 546, "y": 784, "width": 236, "height": 300}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [9]], [[[4, "Shark_Clamp", ".mp3", 1.097143], -1], 0, 0, [], [], []], [[[4, "Shark_Bgm", ".mp3", 52.871837], -1], 0, 0, [], [], []], [[{"name": "loading_progress_bottom", "rect": {"x": 840, "y": 3, "width": 733, "height": 43}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 733, "height": 43}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[{"name": "btn_music_on", "rect": {"x": 688, "y": 835, "width": 85, "height": 85}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 85, "height": 85}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[[4, "silent", ".mp3", 1.044898], -1], 0, 0, [], [], []], [[[4, "Shark_GiveCard", ".mp3", 0.2432], -1], 0, 0, [], [], []], [[{"name": "time_progress_bot", "rect": {"x": 945, "y": 731, "width": 218, "height": 46}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 222, "height": 52}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[{"name": "+4", "rect": {"x": 574, "y": 835, "width": 68, "height": 47}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 70, "height": 49}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "countdown", "rect": {"x": 481, "y": 3, "width": 231, "height": 231}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 233, "height": 233}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[{"name": "indicator_on", "rect": {"x": 707, "y": 870, "width": 26, "height": 26}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 26, "height": 26}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[[4, "Shark_ThrowBomb", ".mp3", 0.705306], -1], 0, 0, [], [], []], [[{"name": "btn_bg_red", "rect": {"x": 840, "y": 597, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [5]], [[[5, "faceImg"], [56, "faceImg", 33554432, [[19, -2, [0, "1eFc6X4rFLPKe6fOb4SCKc"]], [30, -3, [0, "14M9oEU+JNCaHCtFnE8F0q"]], [139, -4, [0, "44aN7CWZ1AfInJvdHtguwp"]]], [1, "dee14Jc8xDxIF3t5qlA9DR", null, null, null, -1, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 4, 1, 4], [], [], []], [[{"name": "btn_continue", "rect": {"x": 3, "y": 894, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [5]], [[[35, "13-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "\n13-zhanshiyupai.png\nsize: 1008,608\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\na00\n  rotate: false\n  xy: 2, 2\n  size: 605, 604\n  orig: 609, 607\n  offset: 0, 2\n  index: -1\nxx\n  rotate: true\n  xy: 609, 181\n  size: 425, 397\n  orig: 427, 399\n  offset: 1, 1\n  index: -1\n", ["13-zhanshiyupai.png"], {"skeleton": {"hash": "WGBq4MKarB2YvDXOVfYO/Oz2kNM", "spine": "3.8.99", "x": -304.5, "y": -303.5, "width": 609, "height": 607, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "a", "parent": "root"}, {"name": "a2", "parent": "a"}, {"name": "xx", "parent": "root"}], "slots": [{"name": "a2", "bone": "a2", "color": "ffffffa7", "attachment": "a00", "blend": "additive"}, {"name": "xx", "bone": "xx", "attachment": "xx"}], "skins": [{"name": "default", "attachments": {"a2": {"a00": {"width": 609, "height": 607}}, "xx": {"xx": {"x": 0.5, "y": 0.5, "width": 427, "height": 399}}}}], "animations": {"idle": {"bones": {"a2": {"rotate": [{}, {"time": 1.5, "angle": 90}, {"time": 3, "angle": 180}, {"time": 4.5, "angle": -90}, {"time": 6}]}, "xx": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.95, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.95, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 6}]}}}}}, [0]]], 0, 0, [0], [-1], [53]], [[{"name": "btn_setting", "rect": {"x": 597, "y": 926, "width": 85, "height": 85}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 85, "height": 85}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[[5, "active_scout"], [29, "active_scout", 131072, [-8], [[[2, -5, [0, "59IZfWwKBAzagxOvuiSpWF"], [5, 1080, 1919.9999999999998]], [6, 45, 100, 100, -6, [0, "6c5gTc121Nobuq0YQ8hrXN"]], -7], 4, 4, 1], [48, "c46/YsCPVOJYA4mWEpNYRx", null, -4, 0, [[36, ["time_count"], -3, -2, [7, ["66xhH67ntA1av+sEAh+gzK"]]]], [-1]]], [12, "main", 131072, 1, [-12, -13, -14, -15, -16], [[2, -9, [0, "bcdLRcu6VJ44zBFOjWVwo8"], [5, 1080, 1325]], [33, 40, 100, -10, [0, "07u6P6YqdFCau1SuA/jpPw"]], [54, -11, [0, "e1JkGPPnpN1J1IPYFQCdhY"]]], [1, "8eJMYUyvVHPKBnFoZPtCzh", null, null, null, 1, 0]], [79, "btn_scout", 512, false, 131072, 2, [-20, -21], [[2, -17, [0, "2eoaKgsmhEaLNOXInRZLCP"], [5, 291, 104]], [17, 1, -18, [0, "7e7N32K1RACpUqCpyQbjai"], 2], [21, 3, -19, [0, "30RC6z5DRETJrU2SdrT2og"]]], [1, "52WiVQ035Eu4T/Ys0W3eff", null, null, null, 1, 0], [1, 0, -480.614, 0]], [80, "btn_ok", false, 131072, 2, [-26], [[2, -22, [0, "c7i6AaCA1Jopx7QouxS09T"], [5, 291, 104]], [17, 1, -23, [0, "e8qCyxlrxKG7PnDo88XvAO"], 4], [18, 3, -25, [0, "dfdNwRlVBJyYfZewIhW+18"], [4, 4292269782], -24]], [1, "7eytaKiK5L07kTymNQzBJs", null, null, null, 1, 0], [1, 0, -479.815, 0]], [7, ["57lc+k+ixJpJCfphQW9Jwl"]], [12, "Node", 131072, 4, [-29, -30], [[2, -27, [0, "abxSJNZX9I+73uqcE1MzDt"], [5, 194.12060546875, 100]], [53, 1, 1, -28, [0, "88ev5p6RBFR6maCiBVKP97"]]], [1, "81a8Wqt/VGzZGpg/LF7Cjr", null, null, null, 1, 0]], [3, "title", 131072, 2, [[2, -31, [0, "71i4MUaMNFMYYYoC+zbb+0"], [5, 324, 77.52]], [34, "选择两颗牙齿", 52, 52, 0, true, true, 6, -32, [0, "c79aoABtpCdZB266fn8wJH"], [4, 4291385356]]], [1, "50OVhoHmZAjZMHcT1sHrd/", null, null, null, 1, 0], [1, 0, 402.1700000000001, 0]], [23, 0, {}, 2, [25, "57lc+k+ixJpJCfphQW9Jwl", null, null, -33, [26, "8fbN7RvxJJ5KHfmnUJmtIk", 1, [[16, "time", ["_name"], 5], [9, ["_lpos"], 5, [1, 4, -350.293, 0]], [9, ["_lrot"], 5, [3, 0, 0, 0, 1]], [9, ["_euler"], 5, [1, 0, 0, 0]], [16, 131072, ["_layer"], 5], [20, 131072, ["_layer"], [7, ["e1mZeOYG1IXKGy3HAnfC01"]]], [20, 131072, ["_layer"], [7, ["5555PXCehPaZ4yc9gKf5QV"]]], [20, 131072, ["_layer"], [7, ["52EYBhBddOjY4dvY20phS1"]]]]], 0]], [3, "shark", 131072, 2, [[19, -34, [0, "21hWgN5rJCP6tudUgaJabS"]]], [1, "deDrGxfShBvY4ER2EtLEUo", null, null, null, 1, 0], [1, -180, 0, 0]], [37, "label", 512, 131072, 3, [[2, -35, [0, "e7WDfg0OhCMrEoqQYa5AMB"], [5, 96, 51]], [8, -36, [0, "f8t2TNL0lNN56FUgqk7szO"], 1]], [1, "d84gN67nBLzZ9eCK4yB/pn", null, null, null, 1, 0], [1, -43.302, 0, 0]], [39, "count", 512, 131072, 3, [[[2, -37, [0, "ec+pGVTf1MnrCuX22Y4RTb"], [5, 91.52783203125, 64.18]], -38], 4, 1], [1, "d1qwQnv4FONbpV26o67ZWO", null, null, null, 1, 0], [1, 58.936, 0, 0]], [3, "ok", 131072, 6, [[2, -39, [0, "88yjJDrCVBkpk7eQwDpQqN"], [5, 139, 51]], [8, -40, [0, "f0jYp0r1JB4YwbbiZR3S3T"], 3]], [1, "7c0rWJs19P5JpgPOITG0O1", null, null, null, 1, 0], [1, -27.560302734375, 0, 0]], [39, "count", 512, 131072, 6, [[[2, -41, [0, "97OUCUwRBK8rUD7vW/FZcL"], [5, 55.12060546875, 64.18]], -42], 4, 1], [1, "fe7vEScxFPCK4z4GPnX5AU", null, null, null, 1, 0], [1, 69.5, -2.4359999999999786, 0]], [71, "0/2 ", 43, 43, "Microsoft YaHei", 0, false, true, 5, 11, [0, "275l28+kJLkq9KOx9NVWgz"], [4, 4279729869]], [71, "2s", 43, 43, "Microsoft YaHei", 0, false, true, 5, 13, [0, "ca1+Q+WbJAd69ZgJ9BMLw7"], [4, 4279729869]], [140, null, 1, [0, "bdNFQ8pE1HW53AP14NJDJU"], 7, 9, 4, 15, 3, 14, 2]], 0, [0, -1, 8, 0, 9, 8, 0, 10, 16, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -3, 16, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, -2, 9, 0, -3, 8, 0, -4, 3, 0, -5, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 10, 0, -2, 11, 0, 0, 4, 0, 0, 4, 0, 7, 4, 0, 0, 4, 0, -1, 6, 0, 0, 6, 0, 0, 6, 0, -1, 12, 0, -2, 13, 0, 0, 7, 0, 0, 7, 0, 3, 8, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -2, 14, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -2, 15, 0, 4, 1, 42], [0, 0, 0, 0, 0, 16], [6, 2, 2, 2, 2, 13], [16, 54, 23, 55, 23, 11]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [2], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["7dj5uJT9FMn6OrOOx83tfK"]}], [3], 0, [], [], []], [[{"name": "default_sprite_splash", "rect": {"x": 0, "y": 0, "width": 2, "height": 2}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 2, "height": 2}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-1, -1, 0, 1, -1, 0, -1, 1, 0, 1, 1, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2, 2, 2, 0, 0, 2, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -1, "y": -1, "z": 0}, "maxPos": {"x": 1, "y": 1, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [56]], [[{"name": "clock", "rect": {"x": 954, "y": 527, "width": 69, "height": 66}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 69, "height": 66}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "+2", "rect": {"x": 954, "y": 474, "width": 67, "height": 47}, "offset": {"x": -0.5, "y": 0}, "originalSize": {"width": 70, "height": 49}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[[5, "ui_player_out"], [11, "ui_player_out", 32768, [-10], [[2, -2, [0, "4cHhmeky1B/Juq103WDIAb"], [5, 1080, 1920]], [99, 45, -4.04121180963557e-14, 4.04121180963557e-14, 100, 100, -3, [0, "bdvgsesn1LBYHqQb15OG1r"]], [141, -9, [0, "cdDPp2MUhF6LZVO7mJihjS"], -8, -7, -6, -5, -4, [5, 6], [7, 8], [9, 10]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [15, "spriteFrame", 32768, 1, [-13, -14, -15, -16, -17, -18], [[2, -11, [0, "72GuoY2JRKAZwX9NRmU/5G"], [5, 785, 543]], [13, 1, 0, -12, [0, "6bC4u5bORLwbszCI8q+gjJ"], 4]], [1, "dbyEHSL3hBF6qwap3JXKPR", null, null, null, 1, 0], [1, 0, -82.468, 0]], [62, "btn_close", false, 32768, 2, [[[2, -19, [0, "89/kSd7wBGY76ipJ5M8Bi9"], [5, 77, 86]], [17, 1, -20, [0, "4f0iiLkLBHl6IUQqpsd3t3"], 2], [142, -22, [0, "77ypxbY/FOoJ4xAFLNIk01"], -21], -23], 4, 4, 4, 1], [1, "aaPnllcKNIrLhJvrxlrJBC", null, null, null, 1, 0], [1, 312.097, 238.245, 0]], [10, "btn_continue", 32768, 2, [[[2, -24, [0, "54f3xq0mpHK7VZmPE7kdVE"], [5, 291, 104]], [13, 1, 2, -25, [0, "3aV/j/YPFGKLTxdROc2KP0"], 3], -26], 4, 4, 1], [1, "603kHQvTZDLYFFdyEth9CT", null, null, null, 1, 0], [1, 0, -152.10400000000004, 0]], [15, "rank", 32768, 2, [-29], [[2, -27, [0, "f3dwnBFpZGD74yTYzQ/G+D"], [5, 509, 164]], [8, -28, [0, "cepdzHbBtD/K7XdBKv90oU"], 1]], [1, "c2iA5dcgNOM55JAvF3B6E1", null, null, null, 1, 0], [1, 0, 33.289, 0]], [10, "logo", 32768, 2, [[[2, -30, [0, "44SpE1X1pKPKYOVChFFTn6"], [5, 385, 253]], -31], 4, 1], [1, "52zikd59JKtr3q409uAXF+", null, null, null, 1, 0], [1, 0, 386.096, 0]], [3, "label", 32768, 2, [[2, -32, [0, "eeYzpa1nRCf7MYuim2nBpY"], [5, 427, 41]], [22, 0, -33, [0, "cekRgZ5iVLopmNEapHclq7"], 0]], [1, "cbBU6i1v9MDb+ZlJcIt3J9", null, null, null, 1, 0], [1, 0, 150.865, 0]], [24, "Label", 32768, 5, [[[2, -34, [0, "3dA0Q6oFZDUp9WP6iPULcN"], [5, 195, 81.9]], -35], 4, 1], [1, "1841eCMGBEmLGx29iyiqQn", null, null, null, 1, 0]], [10, "title", 32768, 2, [[[2, -36, [0, "c5J7cOjL1M97GE9apQfoUG"], [5, 328, 108]], -37], 4, 1], [1, "86Y509ey1BLrcc7N7F5rOi", null, null, null, 1, 0], [1, 0, 249.943, 0]], [30, 6, [0, "87Dd6iAmlJkoPnBNnljfxo"]], [119, "第三名", 65, 65, "Source Han Sans CN", 0, 8, [0, "deGx/ruI9DF5u//NGwm8X+"]], [30, 9, [0, "92jF44Qt5BXI/901vxRlU4"]], [21, 3, 3, [0, "18KjMuUANGkJjT4PJOVHJg"]], [21, 3, 4, [0, "bbU2Tgtg1GiY4JbM3o0PeN"]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 43, 14, 0, 44, 10, 0, 15, 12, 0, 45, 11, 0, 46, 4, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 5, 0, -4, 9, 0, -5, 3, 0, -6, 4, 0, 0, 3, 0, 0, 3, 0, 47, 13, 0, 0, 3, 0, -4, 13, 0, 0, 4, 0, 0, 4, 0, -3, 14, 0, 0, 5, 0, 0, 5, 0, -1, 8, 0, 0, 6, 0, -2, 10, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -2, 11, 0, 0, 9, 0, -2, 12, 0, 4, 1, 37], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 12], [2, 2, 2, 2, 2, -1, -2, -1, -2, -1, -2, 2, 2], [57, 58, 4, 24, 8, 59, 25, 60, 26, 24, 61, 26, 25]], [[[5, "active_remove_mine"], [11, "active_remove_mine", 32768, [-8], [[2, -2, [0, "f8gTd+x6FCVo1OcTu/6F8H"], [5, 1080, 1919.9999999999998]], [6, 45, 100, 100, -3, [0, "6eAPwO3ONMCIZWB9MMuyTl"]], [143, 1, true, -7, [0, "7aLFcwOVdDPJ2VhEYm9Ksi"], -6, -5, -4, 6]], [1, "8eM+e0+xlFRKBDzrPK/3Ov", null, null, null, -1, 0]], [12, "adapt", 32768, 1, [-11, -12, -13, -14], [[2, -9, [0, "edBnFYTD9Fa6P8QSKxwzMG"], [5, 1080, 1919.9999999999998]], [6, 45, 100, 1325, -10, [0, "b3vdZnsNtKzYREvAByVON4"]]], [1, "b5lsMRuGBJX7Gx+nLV9AuM", null, null, null, 1, 0]], [15, "Node-001", 32768, 2, [-18, -19], [[2, -15, [0, "87pneb6xRK+qZLyvE+Vjub"], [5, 1080, 2400]], [100, false, 45, 100, 100, -16, [0, "23st1V6KhDmY+s1I+cXDM7"]], [55, -17, [0, "84nY9VZcNHzIFLO+kZ9QKj"]]], [1, "8feO82mKlEBrFUw8Uu2uZc", null, null, null, 1, 0], [1, 0, -222.0429999999999, 0]], [38, "btn_run", 32768, 2, [-23], [[[2, -20, [0, "d8jm0a7JFDlo4e/MyUukja"], [5, 291, 104]], [17, 1, -21, [0, "08hZrSzotGgabaRXAVNPcG"], 5], -22], 4, 4, 1], [1, "92cFfz161JQKXykH7SQqJM", null, null, null, 1, 0], [1, 0, -488.128, 0]], [12, "Node", 32768, 4, [-26, -27], [[2, -24, [0, "5cTgSQughP050GV1cFdfkG"], [5, 113, 100]], [135, 1, 1, 7, -25, [0, "e28mi34+dIS7+pLGvRCzzG"]]], [1, "45wphDIX1I9YbrtpqpcpmF", null, null, null, 1, 0]], [3, "left", 32768, 3, [[2, -28, [0, "14mzEApE5PTK+XgjksilSc"], [5, 104, 2400]], [101, 13, 490, 100, 1325, -29, [0, "a1uXh6EQZOSL15dWDyDNG4"]], [13, 2, 0, -30, [0, "48REV+48RF1oULvwYNx7HI"], 0]], [1, "c2pCwQb7pABKqY6MPa67Qm", null, null, null, 1, 0], [1, -488, 0, 0]], [81, "right", 32768, 3, [[2, -31, [0, "25mieOIGlH9qk7eS1jj2hl"], [5, 104, 2400]], [6, 37, 100, 1325, -32, [0, "79U3abx9lDF4a9kHHr9ZKI"]], [13, 2, 0, -33, [0, "aa0YWe+/lH2pk7ivr9Dt/j"], 1]], [1, "54kCigyUxNPZtgIB8zJNHj", null, null, null, 1, 0], [1, 488, 0, 0], [1, -1, 1, 1]], [82, "dangerous_border", 32768, [[2, -34, [0, "7cs6qcJqdMLJeSE4jzs18m"], [5, 216, 207]], [8, -35, [0, "37c6U1G9VM57xxF4MIm3W3"], 2], [102, 1, 18, -36, [0, "77r2YEUmdJQaECpD1WVptD"]]], [1, "bfjlOjWQJJW43UYg6OFVQT", null, null, null, 1, 0], [1, 0, 541, 0]], [15, "Node", 32768, 2, [8], [[2, -37, [0, "a3eMJUEsFPnYY5FiROJzsL"], [5, 1080, 1325]]], [1, "a4a5O4ffNPuKaPmR23BN3x", null, null, null, 1, 0], [1, 0, 53.597000000000094, 0]], [3, "dangerous", 32768, 2, [[2, -38, [0, "ee9jKoN6dJ5oHpNALVltBU"], [5, 162, 151]], [8, -39, [0, "a0V+aFGLFDQ5QJKECe1Yy3"], 3]], [1, "d59wBF4X9Bh4Wis35erMYf", null, null, null, 1, 0], [1, 0, 587.0860000000001, 0]], [3, "label_run", 32768, 5, [[2, -40, [0, "08Xl+/yyhKYLgoK+UY6t+6"], [5, 96, 51]], [8, -41, [0, "98YP0oXJhMhZhWj9qoDXEZ"], 4]], [1, "9605YHdmJDgYM2r8ABj2kR", null, null, null, 1, 0], [1, -8.5, 0, 0]], [10, "time", 32768, 5, [[[2, -42, [0, "c64gSfP3dOSJzK3E/NqPUm"], [5, 10, 64.18]], -43], 4, 1], [1, "5bZd583PFC0YafsGIgzaIb", null, null, null, 1, 0], [1, 51.5, 0, 0]], [70, "", 43, 43, 0, true, 5, 12, [0, "4bLr7ciWBK6ri2OrOyIFm8"], [4, 4280558268]], [21, 3, 4, [0, "d9IwzcC05PA7Qd0K/tw5IG"]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 48, 3, 0, 49, 13, 0, 50, 14, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 9, 0, -3, 10, 0, -4, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 6, 0, -2, 7, 0, 0, 4, 0, 0, 4, 0, -3, 14, 0, -1, 5, 0, 0, 5, 0, 0, 5, 0, -1, 11, 0, -2, 12, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -2, 13, 0, 4, 1, 8, 5, 9, 43], [0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, 51], [27, 27, 62, 63, 64, 65, 66]], [[{"name": "watch", "rect": {"x": 920, "y": 350, "width": 283, "height": 93}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 283, "height": 93}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[[5, "toast"], [11, "toast", 33554432, [-9, -10], [[2, -2, [0, "1bLMoeVdpH1oZdMxZ5Fh5H"], [5, 810, 90]], [144, -7, [0, "084wfmMmtBgLAVgIP6fNZx"], -6, -5, -4, -3], [55, -8, [0, "d7dkpIJJFGGbQhVAorLO02"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [12, "slide_node", 33554432, 1, [-13], [[2, -11, [0, "c5ieXMtq5GKIUNwTEATjpd"], [5, 850, 65]], [13, 1, 0, -12, [0, "caZ4Kkqy5MjJ+7Ohb9S/uY"], 0]], [1, "a7dWA3o2BAooU+rDRpkLJs", null, null, null, 1, 0]], [83, "fixed_node", false, 33554432, 1, [-16], [[2, -14, [0, "fbC3umLiJCsaZedO2P/Gww"], [5, 850, 65]], [13, 1, 0, -15, [0, "fbkz0gZxdOK7DQA9KtThyy"], 1]], [1, "5aNzQkI19H3rjrxXuyt2mQ", null, null, null, 1, 0]], [24, "Label", 33554432, 2, [[[2, -17, [0, "18QaqQ3KtAuYtSIC91jAle"], [5, 800, 40.76]], -18], 4, 1], [1, "90y4m7DbtJPIhA2ikYLqGh", null, null, null, 1, 0]], [24, "Label", 33554432, 3, [[[2, -19, [0, "84ZyhH17dK1p1WNoEisu9S"], [5, 720, 40.76]], -20], 4, 1], [1, "cfd5Q6jEtNIY80UI3aL/9t", null, null, null, 1, 0]], [72, "", 26, 26, "Microsoft YaHei", 0, 3, true, 4, 4, [0, "c7UbrZaUxOFbwndpfcJiOp"], [4, 4283715864]], [72, "", 26, 26, "Microsoft YaHei", 0, 3, true, 4, 5, [0, "f8OrigpV5DELqXNE0bHox6"], [4, 4283715864]]], 0, [0, 3, 1, 0, 0, 1, 0, 52, 6, 0, 53, 7, 0, 54, 2, 0, 55, 3, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, 0, 3, 0, -1, 5, 0, 0, 4, 0, -2, 6, 0, 0, 5, 0, -2, 7, 0, 4, 1, 20], [0, 0], [2, 2], [28, 28]], [[{"name": "loading", "rect": {"x": 858, "y": 160, "width": 156, "height": 34}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 156, "height": 34}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "btn_setting_bg", "rect": {"x": 714, "y": 868, "width": 100, "height": 303}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 100, "height": 303}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[[5, "light_card_item"], [11, "light_card_item", 16384, [-5], [[2, -2, [0, "a7BTaj1IFOf5azJX8Fkiy/"], [5, 240, 300]], [145, -4, [0, "33kEkWeFlJ5JBz+A83wda6"], -3, [[1, 2, null, 3, 4, 5, null, 6, 7, 8, 9, null, 10, 11, 12, 13, 14], 6, 6, 0, 6, 6, 6, 0, 6, 6, 6, 6, 0, 6, 6, 6, 6, 6]]], [1, "f4R0zErbdFz62N9N7osow+", null, null, null, -1, 0]], [3, "card", 16384, 1, [[14, -6, [0, "c97fsXAbJJzZTwGssiVq6X"], [5, 235, 300], [0, 0.5, 0]], [50, 0, false, -7, [0, "97NeVsFZ1Ny7E0vbNTZ2Fr"], 0], [40, 4, -8, [0, "c6tv5wcphFJLdHkauLTPgK"]]], [1, "b5RAiYRYtFKbwakFIbJ+FG", null, null, null, 1, 0], [1, 0, -150, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 19, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 4, 1, 8], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, -1, -2, -4, -5, -6, -8, -9, -10, -11, -13, -14, -15, -16, -17], [17, 17, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [2], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["95EkngnxZFbYuFpsqVTaFr"]}], [3], 0, [], [], []], [[{"name": "default_btn_disabled", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [80]], [[{"name": "warning", "rect": {"x": 840, "y": 327, "width": 40, "height": 117}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 117}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[[35, "skeleton", "\nskeleton.png\nsize: 174, 174\nformat: RGBA8888\nfilter: Linear, Linear\nrepeat: none\n声波_00054\n  rotate: false\n  xy: 0, 0\n  size: 174, 174\n  orig: 180, 180\n  offset: 3, 3\n  index: -1\n", ["skeleton.png"], {"skeleton": {"hash": "cMFZMgtp7YM", "spine": "3.8-from-4.0-from-4.1-from-4.2.24", "x": -89.45, "y": 5.82, "width": 180, "height": 180, "images": "./image/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 0.04, "y": 93.37}, {"name": "bone2", "parent": "root", "x": 0.04, "y": 93.37}, {"name": "bone3", "parent": "root", "x": 0.04, "y": 93.37}], "slots": [{"name": "声波_00054", "bone": "bone", "attachment": "声波_00054"}, {"name": "声波_00055", "bone": "bone2", "attachment": "声波_00054"}, {"name": "声波_00056", "bone": "bone3", "attachment": "声波_00054"}], "skins": [{"name": "default", "attachments": {"声波_00054": {"声波_00054": {"x": 0.51, "y": 2.45, "width": 180, "height": 180}}, "声波_00055": {"声波_00054": {"x": 0.51, "y": 2.45, "width": 180, "height": 180}}, "声波_00056": {"声波_00054": {"x": 0.51, "y": 2.45, "width": 180, "height": 180}}}}], "animations": {"animation": {"slots": {"声波_00054": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}]}, "声波_00055": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4333, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}]}, "声波_00056": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}}, "bones": {"bone": {"scale": [{"x": 0.823, "y": 0.823}, {"time": 1.1667, "x": 1.172, "y": 1.172}]}, "bone2": {"scale": [{"x": 0.823, "y": 0.823, "curve": "stepped"}, {"time": 0.4333, "x": 0.823, "y": 0.823}, {"time": 1.6667, "x": 1.172, "y": 1.172}]}, "bone3": {"scale": [{"x": 0.823, "y": 0.823, "curve": "stepped"}, {"time": 0.7, "x": 0.823, "y": 0.823}, {"time": 1.8333, "x": 1.172, "y": 1.172}]}}}}}, [0]]], 0, 0, [0], [-1], [81]], [[{"name": "status_leave", "rect": {"x": 798, "y": 974, "width": 77, "height": 47}, "offset": {"x": 0, "y": 0.5}, "originalSize": {"width": 81, "height": 50}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "bg_card_count", "rect": {"x": 714, "y": 804, "width": 40, "height": 53}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 53}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["997/+ua69H06OB1l/u9Ys8"]}], [3], 0, [], [], []], [[{"name": "icon_your_turn", "rect": {"x": 333, "y": 660, "width": 357, "height": 101}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 357, "height": 101}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "btn_draw_4", "rect": {"x": 300, "y": 894, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [5]], [[[4, "Shark_Change", ".mp3", 1.044898], -1], 0, 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [2], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["9dzjvEbDpK87s61koG+L+W"]}], [3], 0, [], [], []], [[{"name": "bg_rank", "rect": {"x": 333, "y": 490, "width": 509, "height": 164}, "offset": {"x": -1, "y": 1.5}, "originalSize": {"width": 517, "height": 171}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "label_turn", "rect": {"x": 627, "y": 835, "width": 74, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 80, "height": 44}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[[5, "switch"], [47, "switch", [-5, -6], [[2, -2, [0, "138qulXcVBzqc6V0iPlpyA"], [5, 138, 51]], [43, 0, -3, [0, "3b1h60ez5OFoSOh2dUyk1X"]], [146, -4, [0, "d776xIJg9PEZ54LQSGg0d3"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [15, "Toggle1", 33554432, 1, [-13], [[2, -7, [0, "52KNfQuwVCm5nXd0wUSknH"], [5, 58, 39]], [51, false, 0, -8, [0, "adYopmylRG9Z4uMWRUPZ70"]], [147, false, -11, [0, "763sZs/xJNSYs+js++VTq1"], [4, 4292269782], -10, -9], [67, 8, 10, -12, [0, "d2ia8qLXxI0rpXX2K600x1"]]], [1, "bdDlzWT6hIdZekjm+2A2mO", null, null, null, 1, 0], [1, -30, 0, 0]], [15, "Toggle2", 33554432, 1, [-20], [[2, -14, [0, "d3s+eur4dJCLps5HZ6cWNq"], [5, 58, 39]], [51, false, 0, -15, [0, "4d9JlLd8FJ9q2uctcls6/V"]], [148, false, false, -18, [0, "1b4rWBQSROeZxgg3ah/gW5"], [4, 4292269782], -17, -16], [66, 32, 10, -19, [0, "35gaMVPoBL8aAmNjd739Y7"]]], [1, "41RrxEcdFPqoUfejFDwLGT", null, null, null, 1, 0], [1, 30, 0, 0]], [24, "Checkmark", 33554432, 2, [[[2, -21, [0, "19RPBL+z9GS7D/x0jeaQC8"], [5, 58, 39]], -22], 4, 1], [1, "9bzooVn5JHB5Wf+QFEroHz", null, null, null, 1, 0]], [94, "Checkmark", false, 33554432, 3, [[[64, "Checkmark<UITransformComponent>", -23, [0, "ebNMH1QghChYDNiXOWxZOV"], [5, 58, 39]], -24], 4, 1], [1, "34YroSJdlN+L2aFQvOGXPG", null, null, null, 1, 0]], [43, 0, 4, [0, "dfwp31d7NABI0ASbNS3FyR"]], [128, "Checkmark<SpriteComponent>", 2, 5, [0, "04W7OsSAxPbJ8ltUQNVP3W"]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, 20, 6, 0, 7, 2, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, 0, 3, 0, 20, 7, 0, 7, 3, 0, 0, 3, 0, 0, 3, 0, -1, 5, 0, 0, 4, 0, -2, 6, 0, 0, 5, 0, -2, 7, 0, 4, 1, 24], [], [], []], [[{"name": "label", "rect": {"x": 954, "y": 602, "width": 419, "height": 41}, "offset": {"x": 1, "y": 0}, "originalSize": {"width": 427, "height": 41}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "btn_draw", "rect": {"x": 597, "y": 894, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [5]], [[[4, "Shark_FireBomb", ".mp3", 1.567347], -1], 0, 0, [], [], []], [[[4, "Shark_Curse", ".mp3", 3.94449], -1], 0, 0, [], [], []], [[{"name": "btn_rule", "rect": {"x": 688, "y": 926, "width": 85, "height": 85}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 85, "height": 85}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[{"name": "+1", "rect": {"x": 963, "y": 974, "width": 57, "height": 47}, "offset": {"x": -5.5, "y": 0}, "originalSize": {"width": 70, "height": 49}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "2", "rect": {"x": 3, "y": 216, "width": 235, "height": 300}, "offset": {"x": -0.5, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[{"name": "3", "rect": {"x": 3, "y": 3, "width": 236, "height": 300}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [18]], [[{"name": "btn_effect_off", "rect": {"x": 894, "y": 894, "width": 85, "height": 85}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 85, "height": 85}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [5]], [[{"name": "ok", "rect": {"x": 840, "y": 3, "width": 139, "height": 51}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 139, "height": 51}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[[5, "dark_card_item"], [11, "dark_card_item", 33554432, [-5], [[2, -2, [0, "a7BTaj1IFOf5azJX8Fkiy/"], [5, 240, 300]], [149, 1.2, -4, [0, "ee1E9DUH5MOYJuv0merdmB"], -3]], [1, "f4R0zErbdFz62N9N7osow+", null, null, null, -1, 0]], [3, "card", 33554432, 1, [[14, -6, [0, "c97fsXAbJJzZTwGssiVq6X"], [5, 236, 300], [0, 0.5, 0]], [50, 0, false, -7, [0, "97NeVsFZ1Ny7E0vbNTZ2Fr"], 0], [40, 4, -8, [0, "c6tv5wcphFJLdHkauLTPgK"]]], [1, "b5RAiYRYtFKbwakFIbJ+FG", null, null, null, 1, 0], [1, 0, -150, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 19, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 4, 1, 8], [0], [2], [17]], [[{"name": "btn_cancel", "rect": {"x": 840, "y": 3, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [6]], [[[5, "loading"], [11, "loading", 33554432, [-7, -8, -9], [[2, -2, [0, "17MJgdyUJNDpHD71zTge8w"], [5, 1080, 1920]], [150, -5, [0, "8arMfhFHtOm6yEFoOXsAtR"], -4, -3], [31, -6, [0, "66jwf72yBGb6dkNUDggJO+"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [27, "loading", 33554432, 1, [[2, -10, [0, "f28y9GzkNNU6VhMziLIPfr"], [5, 138, 138]], [8, -11, [0, "efqvLVtftKZI/5mH5t/iNn"], 1]], [1, "f8ke7Wa0ZIxbIT80DV7djB", null, null, null, 1, 0]], [28, "black", false, 33554432, 1, [[2, -12, [0, "1cV3X5EY9L+bGF0pTwKCKs"], [5, 1080, 1920]], [52, 0, -13, [0, "109gc11PRD5IaHxuDdQrzd"], [4, 3019898880], 0]], [1, "behI+vBRJDSYBXcCXC6WqD", null, null, null, 1, 0]], [10, "title", 33554432, 1, [[[2, -14, [0, "19SyeMIsNAVpYCbYvS31ER"], [5, 120, 50.4]], -15], 4, 1], [1, "42HoQwYP5LdZDc0JWg7VO5", null, null, null, 1, 0], [1, 0, -140, 0]], [73, "加载中", 40, 4, [0, "cck4gdLCdOQZh8FJ9O6mia"]]], 0, [0, 3, 1, 0, 0, 1, 0, 56, 2, 0, 15, 5, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, -3, 4, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, -2, 5, 0, 4, 1, 15], [0, 0], [2, 2], [12, 82]], [[[4, "Shark_DealCards", ".mp3", 0.835918], -1], 0, 0, [], [], []], [[[4, "Shark_BeChanged", ".mp3", 0.992653], -1], 0, 0, [], [], []], [[{"name": "rule_5", "rect": {"x": 3, "y": 3, "width": 831, "height": 826}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 831, "height": 826}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[{"name": "btn_draw_3", "rect": {"x": 840, "y": 300, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [6]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [2], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["bdG8q6vX1KcbFDmXyII4Pk"]}], [3], 0, [], [], []], [[{"name": "default_editbox_bg", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [83]], [[[5, "draw"], [29, "draw", 131072, [-8], [[[2, -5, [0, "0buD2j869IZY/krntQ5wnJ"], [5, 1080, 1919.9999999999998]], [6, 45, 100, 100, -6, [0, "1cttrxU/pE/YImFxkTZfVr"]], -7], 4, 4, 1], [48, "c46/YsCPVOJYA4mWEpNYRx", null, -4, 0, [[36, ["time_count"], -3, -2, [7, ["66xhH67ntA1av+sEAh+gzK"]]]], [-1]]], [12, "main", 131072, 1, [-11, -12, -13, -14, -15, -16, -17, -18], [[2, -9, [0, "1fdxKJIklOOIVOOHQVLD8x"], [5, 1080, 1325]], [54, -10, [0, "c7DeRXa4pHG5gymYGYetyB"]]], [1, "26+d93UxxFypaEAh/ADUAh", null, null, null, 1, 0]], [57, "overtime", false, 131072, [[2, -19, [0, "f6aAAl1wBIJ5rSaIYSuLF1"], [5, 480, 158.89999999999998]], [34, "超时未操作\n系统将自动帮你按牙", 52, 52, 65, true, true, 6, -20, [0, "78ulH5BiBDEI3rnudtp7BG"], [4, 4291385356]], [151, -21, [0, "77lptoAq9GLbZPALo6hift"], [0], 1], [154, -22, [0, "205B+xcqVBOoSnPv0i62Kw"]]], [1, "31anSwC8FHhpM2druUNHlQ", null, null, null, 1, 0]], [57, "<PERSON><PERSON><PERSON>", false, 131072, [[2, -23, [0, "dbE6vPc05KybHAqyyQdt2z"], [5, 428, 158.89999999999998]], [34, "祈祷生效\n正在自动帮你按牙", 52, 52, 65, true, true, 6, -24, [0, "45K6Xr99JAja0IPUJmtHPy"], [4, 4291385356]], [152, false, -25, [0, "e3W/j42vZNtLWd7njLYWLr"], [2], 3], [155, false, -26, [0, "a1ysZcOW9DYqWzy+O361sv"]]], [1, "63zcYnRKhOpqZrGtAl5ece", null, null, null, 1, 0]], [15, "desk_card_info", 131072, 2, [-29, -30], [[2, -27, [0, "fa6ZOdPMxKeo4QZzP7VvVw"], [5, 515.5859375, 50.4]], [73, "剩余       颗，其中       颗危险", 40, -28, [0, "a80borfZBC4YOK9TPosfun"]]], [1, "44JASjXq5GhpWN/yvubwrC", null, null, null, 1, 0], [1, 0, -300.722, 0]], [15, "Node", 131072, 2, [-32, 3, 4], [[19, -31, [0, "70xFtYxNVHTazKSKaAwz+z"]]], [1, "1dqppUPg5EXJP4BCeExrJF", null, null, null, 1, 0], [1, -7.065, 458.123, 0]], [7, ["57lc+k+ixJpJCfphQW9Jwl"]], [10, "btn_cancel", 131072, 2, [[[2, -33, [0, "acfSZOceZDV578kuEvUyL5"], [5, 291, 104]], [17, 1, -34, [0, "6d56C4lMdB65cSqUp/mwaH"], 5], -35], 4, 4, 1], [1, "b0vmlPRZtF0puA6BTWmlB5", null, null, null, 1, 0], [1, -165, -540, 0]], [10, "btn_confirm", 131072, 2, [[[2, -36, [0, "9djs4FV3JG6JPmslqLq3fu"], [5, 291, 104]], [17, 1, -37, [0, "d2eiyyoaNMwbS3jDtiKaiL"], 6], -38], 4, 4, 1], [1, "90KpdtM8xHo5n05nzYl9gx", null, null, null, 1, 0], [1, 189.583, -540, 0]], [28, "tips", false, 131072, 6, [[2, -39, [0, "bdSlTHdYRLFalWz7vxxDTr"], [5, 324, 77.52]], [34, "选择一颗牙齿", 52, 52, 0, true, true, 6, -40, [0, "c8dno2b+lLdYSJbjKBT/hh"], [4, 4291385356]]], [1, "61IL6AtDtLQpVt2+GWNSHu", null, null, null, 1, 0]], [23, 0, {}, 2, [25, "57lc+k+ixJpJCfphQW9Jwl", null, null, -41, [26, "0bsXHozdlNNbsKD/j3/qDO", 1, [[16, "time", ["_name"], 7], [9, ["_lpos"], 7, [1, 0, -408, 0]], [9, ["_lrot"], 7, [3, 0, 0, 0, 1]], [9, ["_euler"], 7, [1, 0, 0, 0]], [16, 131072, ["_layer"], 7], [20, 131072, ["_layer"], [7, ["e1mZeOYG1IXKGy3HAnfC01"]]], [20, 131072, ["_layer"], [7, ["5555PXCehPaZ4yc9gKf5QV"]]], [20, 131072, ["_layer"], [7, ["52EYBhBddOjY4dvY20phS1"]]]]], 4]], [58, "shine", false, 131072, 2, [[2, -42, [0, "1cEwVXmZVLaJddKDg/wbHX"], [5, 609, 607]], [44, "default", "idle", false, 0, -43, [0, "acobI8W1tPmrfIqhFccamF"], 7]], [1, "50Ien7IQJMD7EEeuQ+5pA6", null, null, null, 1, 0], [1, 0, 421.208, 0]], [3, "shark", 131072, 2, [[19, -44, [0, "e03P1uPFdNhaU02Z7xvEn9"]]], [1, "9ehZprYC5M24K8ZvhKdYd8", null, null, null, 1, 0], [1, -75, 0, 0]], [10, "remaining_count", 131072, 5, [[[2, -45, [0, "5c6vikTutDFIZCf7Oeh3+Y"], [5, 36.38916015625, 66.7]], -46], 4, 1], [1, "2bcon+OUtN4pp+u59XAkH5", null, null, null, 1, 0], [1, -138.607, 0, 0]], [10, "boom_count", 131072, 5, [[[2, -47, [0, "ddD6iIxelFAp+XAeFsp8no"], [5, 36.38916015625, 66.7]], -48], 4, 1], [1, "b6EnGrdiJOp5d7nEiQAtxf", null, null, null, 1, 0], [1, 97.644, 0, 0]], [3, "draw_card", 131072, 2, [[19, -49, [0, "8dvG7Mbt9IzZMcR/KklIv/"]]], [1, "67Qj+ogmpGe57WUUhgcPpi", null, null, null, 1, 0], [1, 0, 421.208, 0]], [74, "0", 45, 45, "Microsoft YaHei", 0, false, true, 5, 14, [0, "49oqXYhMNIYKOGudbv+ldh"], [4, 4278251775], [4, 4285098013]], [74, "0", 45, 45, "Microsoft YaHei", 0, false, true, 5, 15, [0, "2bfcr4qY9EEYYKfAlz437A"], [4, 4278251775], [4, 4285098013]], [18, 3, 8, [0, "17xqOb0ppI9JcLIB0zRaS9"], [4, 4292269782], 8], [18, 3, 9, [0, "289l0sABtPMLpDH3R6Bs11"], [4, 4292269782], 9], [158, null, 1, [0, "9frYSvDNJLl5D9p7kpwAyo"], 19, 3, 4, 13, 20, 17, 18, 5, 16, 10, 12, 2]], 0, [0, -1, 11, 0, 9, 11, 0, 10, 21, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -3, 21, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 13, 0, -3, 5, 0, -4, 11, 0, -5, 8, 0, -6, 9, 0, -7, 12, 0, -8, 16, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 14, 0, -2, 15, 0, 0, 6, 0, -1, 10, 0, 0, 8, 0, 0, 8, 0, -3, 19, 0, 0, 9, 0, 0, 9, 0, -3, 20, 0, 0, 10, 0, 0, 10, 0, 3, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, -2, 17, 0, 0, 15, 0, -2, 18, 0, 0, 16, 0, 4, 1, 3, 5, 6, 4, 5, 6, 49], [0, 0, 0, 0, 0, 0, 0, 0, 21], [-1, 16, -1, 16, 6, 2, 2, 8, 57], [13, 13, 13, 13, 16, 19, 29, 84, 11]], [[[5, "ui"], [61, "ui", 32768, [-9, -10], [[[2, -4, [0, "adOWUq3hVKablfO6oj1lOj"], [5, 1080, 1920]], [6, 45, 100, 100, -5, [0, "b5xRZjx4pDba9RsObR5VMj"]], -6, [65, -8, [0, "85X7qRy0FNTo8PGNdfNGJp"], -7]], 4, 4, 1, 4], [68, "c46/YsCPVOJYA4mWEpNYRx", null, null, -3, 0, [[36, ["scene_mask"], -2, -1, [7, ["ecjlfrIdJMZLCOEdjBtXFf"]]]]], [1, 540, 959.9999999999999, 0]], [29, "scene-ui-mask", 32768, [-15], [[[2, -11, [0, "22Ca1PRSNM4ZFa0kfprS1C"], [5, 1080, 1920]], [31, -12, [0, "f162yNpUBC9IO8rN74PPoA"]], -13, [6, 45, 1080, 1920, -14, [0, "6buRsu74BE7o9kSTxkAxX7"]]], 4, 4, 1, 4], [1, "a9pOSaqz9I4pW9UfcFm3l9", null, null, null, 1, 0]], [12, "ui_container", 32768, 1, [2], [[2, -16, [0, "9cuZCiazRHyJSJUWtWUC7K"], [5, 1080, 1920]], [6, 45, 100, 100, -17, [0, "cfBpiYqD5GkoxtqyN1YqqN"]], [42, -18, [0, "87KzkMIf9OX5gim2RZDil/"]]], [1, "9f+6bveDtGL5XA0Ul7IYKA", null, null, null, 1, 0]], [12, "tween", 32768, 2, [-21], [[2, -19, [0, "79qJ0NNVBIgJktObT9g4dl"], [5, 1080, 1920]], [6, 45, 100, 100, -20, [0, "66ZJLeLhhG2rohKw6uIvkQ"]]], [1, "bdJQfBFp1PTbej3N+LGlkJ", null, null, null, 1, 0]], [27, "SpriteSplash", 32768, 4, [[2, -22, [0, "f2f9QlgkRB2YB6NNccNDfU"], [5, 1080, 1920]], [52, 0, -23, [0, "438EX5sVNLR5uRvSF7wuRT"], [4, 3758096384], 0], [103, 45, 100, 100, -24, [0, "1bwwEE0fNIZ43liCoHWoEA"], 1]], [1, "b5bMebdkJAc7KcuRc6Y9u0", null, null, null, 1, 0]], [60, "Camera", 32768, 1, [-25], [1, "5bi7t12rJN7YcPWT07C0Gt", null, null, null, 1, 0], [1, 0, 0, 1000]], [113, "Camera<CameraComponent>", 0, 99, 960, 6, 32768, 6, [0, "d4OHdeLiNDELC1McYXtBHF"], [4, 4286073907]], [76, 2, [0, "67jPCxRJRNF5GTama5uRoS"], 4], [159, 1, [0, "43qezTGKhMrZjkBK4wl7nd"], 8, 3]], 0, [0, 9, 2, 0, 10, 9, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -3, 9, 0, 17, 7, 0, 0, 1, 0, -1, 6, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, -3, 8, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 7, 0, 4, 1, 2, 5, 3, 25], [0], [2], [12]], [[{"name": "curse_shark", "rect": {"x": 920, "y": 639, "width": 86, "height": 87}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 86, "height": 87}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[{"name": "state_auto", "rect": {"x": 714, "y": 974, "width": 78, "height": 47}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 78, "height": 47}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "play_zone", "rect": {"x": 3, "y": 3, "width": 190, "height": 250}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 190, "height": 250}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[[5, "clamp"], [29, "clamp", 131072, [-8], [[[2, -5, [0, "59IZfWwKBAzagxOvuiSpWF"], [5, 1080, 1920]], [6, 45, 100, 100, -6, [0, "6c5gTc121Nobuq0YQ8hrXN"]], -7], 4, 4, 1], [48, "c46/YsCPVOJYA4mWEpNYRx", null, -4, 0, [[36, ["time_count"], -3, -2, [7, ["66xhH67ntA1av+sEAh+gzK"]]]], [-1]]], [59, "main", 512, 131072, 1, [-12, -13, -14, -15], [[2, -9, [0, "bcdLRcu6VJ44zBFOjWVwo8"], [5, 1080, 1325]], [33, 40, 100, -10, [0, "07u6P6YqdFCau1SuA/jpPw"]], [54, -11, [0, "e1JkGPPnpN1J1IPYFQCdhY"]]], [1, "8eJMYUyvVHPKBnFoZPtCzh", null, null, null, 1, 0]], [7, ["57lc+k+ixJpJCfphQW9Jwl"]], [37, "btn_scout", 512, 131072, 2, [[2, -16, [0, "2eoaKgsmhEaLNOXInRZLCP"], [5, 291, 104]], [17, 1, -17, [0, "7e7N32K1RACpUqCpyQbjai"], 1], [21, 3, -18, [0, "30RC6z5DRETJrU2SdrT2og"]]], [1, "52WiVQ035Eu4T/Ys0W3eff", null, null, null, 1, 0], [1, 0, -480.614, 0]], [37, "title", 512, 131072, 2, [[2, -19, [0, "71i4MUaMNFMYYYoC+zbb+0"], [5, 324, 77.52]], [34, "选择一颗牙齿", 52, 52, 0, true, true, 6, -20, [0, "c79aoABtpCdZB266fn8wJH"], [4, 4291385356]]], [1, "50OVhoHmZAjZMHcT1sHrd/", null, null, null, 1, 0], [1, 0, 402.1700000000001, 0]], [23, 512, {}, 2, [25, "57lc+k+ixJpJCfphQW9Jwl", null, null, -21, [26, "8fbN7RvxJJ5KHfmnUJmtIk", 1, [[16, "time", ["_name"], 3], [9, ["_lpos"], 3, [1, 4, -350.293, 0]], [9, ["_lrot"], 3, [3, 0, 0, 0, 1]], [9, ["_euler"], 3, [1, 0, 0, 0]], [16, 131072, ["_layer"], 3], [20, 131072, ["_layer"], [7, ["e1mZeOYG1IXKGy3HAnfC01"]]], [20, 131072, ["_layer"], [7, ["5555PXCehPaZ4yc9gKf5QV"]]], [20, 131072, ["_layer"], [7, ["52EYBhBddOjY4dvY20phS1"]]]]], 0]], [37, "shark", 512, 131072, 2, [[19, -22, [0, "21hWgN5rJCP6tudUgaJabS"]]], [1, "deDrGxfShBvY4ER2EtLEUo", null, null, null, 1, 0], [1, -180, 0, 0]], [160, null, 1, [0, "348NGRYdZJfZOONyngk0YY"], 5, 7, 4, 2]], 0, [0, -1, 6, 0, 9, 6, 0, 10, 8, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -3, 8, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 7, 0, -3, 6, 0, -4, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 3, 6, 0, 0, 7, 0, 4, 1, 22], [0, 0, 8], [6, 2, 13], [16, 29, 11]], [[{"name": "warning_bg", "rect": {"x": 149, "y": 490, "width": 194, "height": 176}, "offset": {"x": 0, "y": -0.5}, "originalSize": {"width": 194, "height": 177}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "rule_2", "rect": {"x": 3, "y": 3, "width": 831, "height": 826}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 831, "height": 826}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [5]], [[[5, "passive_request"], [11, "passive_request", 32768, [-9], [[2, -3, [0, "afsQr3xDBOHqmbzpFtrDlx"], [5, 1080, 1920]], [6, 45, 100, 100, -4, [0, "84ZWLffohMTKzyMxLU4q2x"]], [161, -8, [0, "12yK4DaolJT5v+hnFKmdDp"], -7, -6, -5, 3]], [41, "c46/YsCPVOJYA4mWEpNYRx", null, null, -2, 0, [-1]]], [12, "main", 32768, 1, [-11, -12, -13, -14], [[2, -10, [0, "7007qlb/RFMonBB1Y9KHuW"], [5, 1080, 1325]]], [1, "40XejXrQBBjrlEOuUU+HTd", null, null, null, 1, 0]], [3, "btn_give_card", 32768, 2, [[2, -15, [0, "5ft0of+GdC+4M3nLeRTdzv"], [5, 291, 104]], [13, 1, 2, -16, [0, "b0jKLFHmBJNL5pCpU3aWUQ"], 1], [21, 3, -17, [0, "4dbOUJs11I7YuW1sP+v2Br"]]], [1, "13Qa2DPGZDRLtkz6ifo38M", null, null, null, 1, 0], [1, 0, -151.729, 0]], [38, "hand_card", 32768, 2, [-21], [[[2, -18, [0, "abR/bGN9tHz4E4FiXc9q9G"], [5, 1080, 360]], [33, 40, 100, -19, [0, "04Uri8Qj1KT7w4OObdPVQd"]], -20], 4, 4, 1], [1, "b3edSWayJJiaiZN3okiJvN", null, null, null, 1, 0], [1, 0, -488.5, 0]], [7, ["cdE8mOophJm6wT0UhPe3Op"]], [23, 0, {}, 4, [25, "cdE8mOophJm6wT0UhPe3Op", null, null, -22, [26, "58IWM/LFpJOJv0N82PW4kA", 1, [[16, "list", ["_name"], 5], [9, ["_lpos"], 5, [1, 0, 0, 0]], [9, ["_lrot"], 5, [3, 0, 0, 0, 1]], [9, ["_euler"], 5, [1, 0, 0, 0]], [69, ["_contentSize"], [7, ["8aQaZa8hVGgZhhsRTLSmTa"]], [5, 80, 295]], [16, 32768, ["_layer"], 5]]], 2]], [10, "request_tips", 32768, 2, [[[2, -23, [0, "c9v0v0ryhHHoZU3FLhf+am"], [5, 761.670140625, 55.44]], -24], 4, 1], [1, "e69dECFd1D/ZCedNsWa/Y0", null, null, null, 1, 0], [1, 0, -2.674, 0]], [3, "icon_request_hand", 32768, 2, [[14, -25, [0, "6dR3fDX/5FN7BgS2j/i5hH"], [5, 608.8499755859375, 686.739990234375], [0, 0.*****************, 0.8424149281503309]], [44, "default", "idle", false, 0, -26, [0, "d9WtvCSqtMX7jA8mcjluVH"], 0]], [1, "9e7YcrbGtO6ZjRiF+lnG5e", null, null, null, 1, 0], [1, 0, 164.789, 0]], [120, "向你索要,请选择一张牌", 44, 44, "Source Han Sans CN", 0, 3, 7, [0, "74OceUF/dJ9bWC2mkV3jiF"]], [162, 4, [0, "97ntzbe2xLx4W6yx8XrKoz"], 6]], 0, [0, -1, 6, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 58, 10, 0, 59, 9, 0, 60, 3, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 7, 0, -2, 8, 0, -3, 3, 0, -4, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -3, 10, 0, -1, 6, 0, 3, 6, 0, 0, 7, 0, -2, 9, 0, 0, 8, 0, 0, 8, 0, 4, 1, 26], [0, 0, 0, 0, 10], [8, 2, 6, 61, 13], [85, 86, 30, 87, 11]], [[{"name": "rule_1", "rect": {"x": 3, "y": 3, "width": 831, "height": 826}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 831, "height": 826}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [6]], [[{"name": "0", "rect": {"x": 3, "y": 309, "width": 236, "height": 300}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [18]], [[[5, "arrow"], [56, "arrow", 16384, [[14, -2, [0, "79jOvr4kVP0YFBYEXPnw8I"], [5, 0, 0], [0, 0, 0.5]], [163, -3, [0, "e0Sv5VbvFBu6z0EPX71rp+"]], [164, true, 200, 88, -4, [0, "e1Q3Y6/b5FJJOLZX9VXKp5"], 0]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 4, 1, 4], [0], [62], [88]], [[[5, "black-mask"], [11, "black-mask", 33554432, [-7], [[2, -2, [0, "34brNIssJH/4UtYAhMOROC"], [5, 1080, 1920]], [31, -3, [0, "a1OBzo2iNF/YuPlXIr2wM8"]], [76, -5, [0, "ecjlfrIdJMZLCOEdjBtXFf"], -4], [6, 45, 1080, 1920, -6, [0, "ac3XQDNNVBGLOx9lpWvSkl"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [12, "tween", 33554432, 1, [-10], [[2, -8, [0, "9dKbk0IEFB8JN42so88S6p"], [5, 1080, 1920]], [6, 45, 100, 100, -9, [0, "3dPsTLgC5HD4PSxLX3vwGO"]]], [1, "7aXlbp48BK8o/d//eXtAjC", null, null, null, 1, 0]], [27, "SpriteSplash", 33554432, 2, [[2, -11, [0, "acdob6JghMKI/wUiPr5FM1"], [5, 1080, 1920]], [52, 0, -12, [0, "91+AYMcSlNsJ7z6nDO9oxE"], [4, 3019898880], 0], [6, 45, 100, 100, -13, [0, "9alnJofXhHQrDX+SnpBIYw"]]], [1, "6e8egoNWFEop/D513AMbr6", null, null, null, 1, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 63, 2, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 4, 1, 13], [0], [2], [12]], [[{"name": "shark_cry", "rect": {"x": 546, "y": 525, "width": 385, "height": 253}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 385, "height": 253}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [9]], [[{"name": "status_watch", "rect": {"x": 760, "y": 804, "width": 62, "height": 50}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 64, "height": 54}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "modal_bg", "rect": {"x": 3, "y": 3, "width": 785, "height": 516}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 785, "height": 516}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [9]], [[[4, "Shark_Escape", ".mp3", 1.906939], -1], 0, 0, [], [], []], [[{"name": "14", "rect": {"x": 3, "y": 615, "width": 236, "height": 300}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [18]], [[{"name": "shark_smile", "rect": {"x": 440, "y": 660, "width": 354, "height": 268}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 354, "height": 268}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "7", "rect": {"x": 309, "y": 240, "width": 235, "height": 300}, "offset": {"x": -0.5, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[{"name": "label_run", "rect": {"x": 967, "y": 215, "width": 96, "height": 51}, "offset": {"x": 0, "y": -0.5}, "originalSize": {"width": 100, "height": 54}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["d5XnRTPE1KKYYrWHgWvmHR"]}], [3], 0, [], [], []], [[{"name": "scout", "rect": {"x": 967, "y": 317, "width": 96, "height": 51}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 100, "height": 55}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [2], 0, [], [], []], [[{"base": "2,2,0,0,0,0", "mipmaps": ["d9T1VXk+9BcLEKhrY6P40S"]}], [3], 0, [], [], []], [[[5, "ui_rule"], [47, "ui_rule", [-5, -6], [[2, -2, [0, "a8ECeZZyBOcrs5wO3lPvmn"], [5, 1080, 1920]], [6, 45, 100, 100, -3, [0, "38msxsIg1PkYJU+gr8XiLC"]], [165, -4, [0, "4eW6tgbzdFirnuTcNiSRhk"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [46, "content", 33554432, [-10, -11, -12, -13], [[14, -7, [0, "89CFaizQFIvaEKSSsBQtUg"], [5, 4320, 1278], [0, 0, 0.5]], [53, 1, 1, -8, [0, "26eCxxCWNF06tOdCOYx2So"]], [40, 8, -9, [0, "4eppdwpdpJfrD8HQnBUQm6"]]], [1, "74SrCfha5HC4oYMrKC4qgZ", null, null, null, 1, 0], [1, -540, 0, 0]], [84, "<PERSON><PERSON><PERSON><PERSON>", 1, [-18, -19], [[64, "pageView-horizontal<UITransform>", -14, [0, "18+wLFbsBKvYQrZCHXV5mH"], [5, 1080, 1278]], [166, "pageView-horizontal<PageView>", false, 1, -16, [0, "f71pegkrlAO6QaKQV4qV4F"], 2, -15], [33, 40, 790, -17, [0, "23ctHaggJKsJMQRXKFjICM"]]], [1, "04IoO7Ic5PuaujP1ncYwxs", null, null, null, 1, 0], [1, 0, -61.051, 0]], [12, "view", 33554432, 3, [2], [[2, -20, [0, "55dOwldYNMt5qjmmnZSpxE"], [5, 1080, 1278]], [167, -21, [0, "0crvabpJVLaa/ax0rEzjgP"]], [168, -22, [0, "2d384qH7NBcoehcqjqvN3T"], [4, 16777215]], [6, 45, 792, 1008, -23, [0, "7008q51P9LeY8jMQLg4Zd1"]]], [1, "d4ypkqbM9F9r7Tl04avzjk", null, null, null, 1, 0]], [11, "Node", 33554432, [-26, -27], [[2, -24, [0, "353hZKfIZHSJXp9lUp24Op"], [5, 831, 826]], [8, -25, [0, "8dHYBfQ8hCgb0sxEOh8CAj"], 2]], [1, "6fJn+JE5ZKVYF48n1z2qS6", null, null, null, 1, 0]], [10, "btn_close", 33554432, 5, [[[2, -28, [0, "54knAzQHhF7Z5s3LweVsFF"], [5, 77, 86]], [13, 1, 0, -29, [0, "f2jy5Hk2VJYY2IBjnzHb5y"], 1], -30], 4, 4, 1], [1, "63zuzCQING6oLpMvb9fGGk", null, null, null, 1, 0], [1, 341.121, 382.7629999999999, 0]], [11, "Node", 33554432, [-33, -34], [[2, -31, [0, "4cP3otx35D8ZdjT32I8Pmf"], [5, 831, 826]], [8, -32, [0, "54a8EIQ/VFv48RihXxL+VG"], 5]], [1, "52XRs9ieZBj5rzXAP3i7lj", null, null, null, 1, 0]], [10, "btn_close", 33554432, 7, [[[2, -35, [0, "72tOvAcnhMg6dIoVe+VqdD"], [5, 77, 86]], [13, 1, 0, -36, [0, "fdRuLw28RDO7CnJ7Np4wD4"], 4], -37], 4, 4, 1], [1, "33j4eBtCJFlo5Opkiv4ZG8", null, null, null, 1, 0], [1, 341.121, 382.7629999999999, 0]], [11, "Node", 33554432, [-40, -41], [[2, -38, [0, "67bElyr6RIj52uBttrTfes"], [5, 831, 826]], [8, -39, [0, "55F9hv/85NdLGsv6Y8rW5x"], 8]], [1, "20Nq8GyANJoqQuhAchkmvi", null, null, null, 1, 0]], [10, "btn_close", 33554432, 9, [[[2, -42, [0, "f8m3NioHVE67DZP+R9Etg8"], [5, 77, 86]], [13, 1, 0, -43, [0, "aeGDV1lOxFzqmKlM4Pm0Xz"], 7], -44], 4, 4, 1], [1, "baEH/0dcZImb+HjtodU+aB", null, null, null, 1, 0], [1, 341.121, 382.7629999999999, 0]], [11, "Node", 33554432, [-47, -48], [[2, -45, [0, "9d50Lt5zNNWKsARqK9Esq0"], [5, 831, 826]], [8, -46, [0, "44gQf4QcZJhZMnhBQchu9P"], 11]], [1, "1ffFJT5u9HJro0FPh4wRtx", null, null, null, 1, 0]], [10, "btn_close", 33554432, 11, [[[2, -49, [0, "e42ow+myBFULMuoYnooeaB"], [5, 77, 86]], [13, 1, 0, -50, [0, "480CiE/nxAiJNP0TI6FbTX"], 10], -51], 4, 4, 1], [1, "5a6uS9LE5GzoEvPEbbgGWd", null, null, null, 1, 0], [1, 341.121, 382.7629999999999, 0]], [85, "btn_confrim", 1, [[2, -52, [0, "71ZLL7CtpIo40rD1J1Cd8G"], [5, 437, 122]], [75, 1, 0, -53, [0, "11gyTEwUVPAYIwKKcA9kqm"]], [18, 3, -55, [0, "abWpbIARpMy5LUaRPLwPmI"], [4, 4292269782], -54]], [1, "b0KMariTRBnY6n9j9KMdnF", null, null, null, 1, 0], [1, 0, -693.903, 0]], [15, "rule_1", 33554432, 2, [5], [[2, -56, [0, "01yxXwviFOzo6RdJyZABRP"], [5, 1080, 826]], [45, -58, [0, "61hzvH7V1LVp1o5PC7Pv4a"], -57]], [1, "3aT78rDsFDdKnsD80S1Vou", null, null, null, 1, 0], [1, 540, 0, 0]], [15, "rule_2", 33554432, 2, [7], [[2, -59, [0, "6bREv8urBEco3bmaVAYifE"], [5, 1080, 826]], [45, -61, [0, "1aFpGV7Z1LzYZtzvZiPp5B"], -60]], [1, "2bVKorrBZE1ILXFge3CA1c", null, null, null, 1, 0], [1, 1620, 0, 0]], [15, "rule_3", 33554432, 2, [9], [[2, -62, [0, "3ekEGCUfdAIoOgXJI9uptB"], [5, 1080, 826]], [45, -64, [0, "b5qWY6Z7pH5L2K5YvD4FjG"], -63]], [1, "72CWF2QaFG6KjONY3sFGss", null, null, null, 1, 0], [1, 2700, 0, 0]], [15, "rule_4", 33554432, 2, [11], [[2, -65, [0, "0aro6GjdVDZKosN4iXS1ZA"], [5, 1080, 826]], [45, -67, [0, "01Q12TxhZDuo8CQDGg7ibU"], -66]], [1, "f40+NmULlJ+pZv7yOKG4+9", null, null, null, 1, 0], [1, 3780, 0, 0]], [3, "title", 33554432, 5, [[2, -68, [0, "efi8TS5PxDO54Q/53PmVp8"], [5, 331, 108]], [8, -69, [0, "20JpW85jlLd5GGkJNlloag"], 0]], [1, "06qbtSdntLxKlUB8kVQ9SS", null, null, null, 1, 0], [1, 0, 389.65200000000004, 0]], [3, "title", 33554432, 7, [[2, -70, [0, "6ergRGo0BI/qDwqNXLpP4j"], [5, 331, 108]], [8, -71, [0, "15Fu1ToG5JPpqusXHxr6Ye"], 3]], [1, "01kP0S5x1Gj7EDVH+Xr/3f", null, null, null, 1, 0], [1, 0, 389.65200000000004, 0]], [3, "title", 33554432, 9, [[2, -72, [0, "28NOR8mb1FUI14ws0CPCRx"], [5, 331, 108]], [8, -73, [0, "cetP7sshBH76kGkvP1HH6m"], 6]], [1, "fa/NKbeiVECoPgaZ4fkUHv", null, null, null, 1, 0], [1, 0, 389.65200000000004, 0]], [3, "title", 33554432, 11, [[2, -74, [0, "37cpaMjjZC0KW6MnunBlyN"], [5, 331, 108]], [8, -75, [0, "03sCYMGHNJCaZCJZg8mX2U"], 9]], [1, "56mKLvxOJB961nNCh1NK1H", null, null, null, 1, 0], [1, 0, 389.65200000000004, 0]], [39, "indicator", 512, 33554432, 3, [[[2, -76, [0, "10Fd0IWpdO4YE55tYLGegF"], [5, 500, 60]], -77], 4, 1], [1, "1bvz7MIEdBgpOkAYn1s2kp", null, null, null, 1, 0], [1, 0, -448.89699999999993, 0]], [18, 3, 6, [0, "17VGpPYuhKELqtkRx87pB9"], [4, 4292269782], 6], [18, 3, 8, [0, "12LI7RAitAWY4bgY6DY2Z0"], [4, 4292269782], 8], [18, 3, 10, [0, "a4Gnq0LKVJZoLWNkLMpE/u"], [4, 4292269782], 10], [18, 3, 12, [0, "a2TAyo8yRMyadEEEt9DxUw"], [4, 4292269782], 12], [169, 36, 22, [0, "23pDH3Mh1Dkq5Pds5c7nZE"], [5, 24, 24]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 13, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, -4, 17, 0, 0, 3, 0, 64, 27, 0, 0, 3, 0, 0, 3, 0, -1, 4, 0, -2, 22, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 18, 0, -2, 6, 0, 0, 6, 0, 0, 6, 0, -3, 23, 0, 0, 7, 0, 0, 7, 0, -1, 19, 0, -2, 8, 0, 0, 8, 0, 0, 8, 0, -3, 24, 0, 0, 9, 0, 0, 9, 0, -1, 20, 0, -2, 10, 0, 0, 10, 0, 0, 10, 0, -3, 25, 0, 0, 11, 0, 0, 11, 0, -1, 21, 0, -2, 12, 0, 0, 12, 0, 0, 12, 0, -3, 26, 0, 0, 13, 0, 0, 13, 0, 7, 13, 0, 0, 13, 0, 0, 14, 0, 11, 23, 0, 0, 14, 0, 0, 15, 0, 11, 24, 0, 0, 15, 0, 0, 16, 0, 11, 25, 0, 0, 16, 0, 0, 17, 0, 11, 26, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, -2, 27, 0, 4, 1, 2, 5, 4, 5, 5, 14, 7, 5, 15, 9, 5, 16, 11, 5, 17, 77], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [14, 4, 89, 14, 4, 90, 14, 4, 91, 14, 4, 92, 93]], [[{"name": "logo", "rect": {"x": 3, "y": 3, "width": 849, "height": 410}, "offset": {"x": -0.5, "y": -1}, "originalSize": {"width": 856, "height": 414}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[[5, "draw_mine"], [86, "draw_mine", 16384, [-4], [[14, -2, [0, "7a5Vy0bmZBMrP0vC54ms6p"], [5, 194, 177], [0, 0.5005631424167134, 0.49754295197944187]], [8, -3, [0, "e7Le+NQLJEiYkBCSbJvGJ+"], 3]], [1, "873KEkFjVHAIgSZMub22XD", null, null, null, -1, 0], [1, 0, 7.524, 0], [1, 0.6, 0.6, 1]], [3, "dangerous", 16384, 1, [[2, -5, [0, "8ddAHE6J1NRItc1VVMoYc7"], [5, 40, 117]], [8, -6, [0, "2eEscCcWtBbKcsXAHaktWx"], 0], [153, true, -7, [0, "0b+1cFRbBN9KJdVqlxRRwG"], [1], 2], [55, -8, [0, "2dyWEe1TZJd5+H2x3RO0ab"]]], [1, "2b1vX17udBo6er2njlLqiB", null, null, null, 1, 0], [1, 0, -7, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 4, 1, 8], [0, 0, 0, 0], [2, -1, 16, 2], [94, 31, 31, 95]], [[[5, "ui_modal"], [47, "ui_modal", [-11], [[2, -2, [0, "7746RC/BdELbHZ1J4Wy7O8"], [5, 1080, 1920]], [6, 45, 100, 100, -3, [0, "8333dwNwJNdZufVVq4hR0U"]], [170, -10, [0, "8cX2ySrUhMZ7ZWvdZn1FJN"], -9, -8, -7, -6, -5, -4, 4, 5, 6]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [87, "main", 1, [-14, -15, -16, -17, -18, -19], [[2, -12, [0, "d8SJV9ObJPaIhq0KBikWpQ"], [5, 785, 516]], [22, 0, -13, [0, "d5iL8ljzNK06RSz8RHP0zP"], 3]], [1, "e9qqgiCWRAj5UwRfk+RPS6", null, null, null, 1, 0]], [32, "btn_close", 2, [[[2, -20, [0, "566xb4DK1MLoEzYk8bQyqZ"], [5, 77, 86]], [17, 1, -21, [0, "ecB/zCIyxFZ7phZfIFNY1m"], 2], -22], 4, 4, 1], [1, "45H1/vqGVDQIZio8GiqXYB", null, null, null, 1, 0], [1, 307.443, 219.025, 0]], [32, "btn_confirm", 2, [[[2, -23, [0, "bfBWjTCwpCqZ2JQr4ihu/E"], [5, 291, 104]], [13, 1, 0, -24, [0, "e8aK/XlBxJnIAvSMXL5BjX"], 0], -25], 4, 4, 1], [1, "41ga10ZZxGPL4TlnuwZ6Q9", null, null, null, 1, 0], [1, 2.384, -134.607, 0]], [95, "btn_cancel", false, 2, [[[2, -26, [0, "69J9BfOWROMbHnb/otjd8+"], [5, 291, 104]], [17, 1, -27, [0, "c7fDW+odFNs7JG8BRt5Bne"], 1], -28], 4, 4, 1], [1, "17xwM6QfBAjrx04wMQwGKj", null, null, null, 1, 0], [1, 3.08, -134.734, 0]], [32, "title", 2, [[[2, -29, [0, "83C5H6iqlJ74v7yLJOfCL5"], [5, 195, 108]], -30], 4, 1], [1, "5apNT9xj5I35zXBjcLN9RT", null, null, null, 1, 0], [1, -0.014, 241.822, 0]], [32, "prompt_content_str", 2, [[[2, -31, [0, "09mFn9mXtEHoAl7nsV4+Nl"], [5, 624.955875, 145.6]], -32], 4, 1], [1, "f9aipwwvVIB4cGoVDYCiY3", null, null, null, 1, 0], [1, 9.663, 40.065, 0]], [32, "prompt_content_spriteFrame", 2, [[[2, -33, [0, "3fg0pICL1MKqZPhGc0mO50"], [5, 42.255859375, 50.4]], -34], 4, 1], [1, "03e1OMQiNNMaqd2qH6aahR", null, null, null, 1, 0], [1, 0, 50, 0]], [30, 6, [0, "76u7V9/HNDNZ5KPqkDuByx"]], [49, "有玩家未加入游戏\n请返回重试", 47, 47, 60, 3, true, 5, 7, [0, "f6M0VDx1JFAYnKeneOjWFW"], [4, 4290741029]], [30, 8, [0, "b3i06lsEdJ96zCfKZdJvVi"]], [21, 3, 4, [0, "91wDAIycpIL7tBeUI+pPMF"]], [21, 3, 5, [0, "329WahzU5EY6nQdP7GSfkx"]], [18, 3, 3, [0, "4cNFusCBtPgqyvnR6+jryZ"], [4, 4292269782], 3]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 11, 14, 0, 18, 13, 0, 14, 12, 0, 65, 11, 0, 66, 10, 0, 15, 9, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 4, 0, -5, 5, 0, -6, 3, 0, 0, 3, 0, 0, 3, 0, -3, 14, 0, 0, 4, 0, 0, 4, 0, -3, 12, 0, 0, 5, 0, 0, 5, 0, -3, 13, 0, 0, 6, 0, -2, 9, 0, 0, 7, 0, -2, 10, 0, 0, 8, 0, -2, 11, 0, 4, 1, 34], [0, 0, 0, 0, 0, 0, 0, 9], [2, 2, 2, 2, 67, 68, 69, 2], [10, 19, 4, 8, 7, 10, 19, 7]], [[[4, "Shark_Clamp_2", ".mp3", 1.750204], -1], 0, 0, [], [], []], [[[5, "player_item"], [11, "player_item", 16384, [-25, -26, -27, -28], [[2, -3, [0, "18Hrqxz1dKqI9m+9zo3y3D"], [5, 144, 144]], [171, -24, [0, "deG9lVFWVLBZzu0lbF2tS2"], -23, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, [19, 20], -9, -8, -7, -6, -5, -4, 21]], [41, "0aikPZyhpB54CvRuoSwMiF", null, null, -2, 0, [-1]]], [38, "border", 16384, 1, [-31, -32, -33, -34, -35, -36], [[[2, -29, [0, "83mcFHUdxCeZFvak1q90tN"], [5, 170, 170]], -30], 4, 1], [1, "63fk6N3GRJVYwJrRvaSIUe", null, null, null, 1, 0], [1, 0, -10.5125, 0]], [88, "tag", false, 16384, [-40, -41, -42, -43], [[2, -37, [0, "90Q0k5RXdIKrIPIAiAmymP"], [5, 121, 121]], [8, -38, [0, "17fpuY9WVE07KRVETyRcCa"], 6], [6, 45, 121, 121, -39, [0, "26ocEsyMFH8KJfkT0o5IiE"]]], [1, "c5kJCPzr5NEZySWllF5EY/", null, null, null, 1, 0]], [89, "label", 512, 16384, [-46, -47, -48], [[14, -44, [0, "32k+trqYVK+5QhgX5kTKIm"], [5, 139.90771484375, 0], [0, 0.5005631424167134, 0.49754295197944187]], [53, 1, 1, -45, [0, "4fAlESB21NPLpCXFspx2FS"]]], [1, "deWfoYD71PO43I/QtBxT2I", null, null, null, 1, 0], [1, 0, -99.92700000000002, 0]], [15, "nick_name", 16384, 2, [-52, -53], [[2, -49, [0, "7bEsfL50RAsK5S+mrSj2sl"], [5, 200, 35]], [13, 1, 0, -50, [0, "c1VmrTCpRH76whs6XjDESa"], 10], [104, 4, -43.708, 1, -51, [0, "84p7rmseZJVKsHQNoGicCl"]]], [1, "48E2+uC+VOVKwGkfQ0mVKY", null, null, null, 1, 0], [1, 0, -111.208, 0]], [90, "curse_node", 512, 16384, [-56, -57], [[14, -54, [0, "03rGSLeb5OTafGbMtxGmTB"], [5, 143, 200], [0, 0.5005631424167134, 0.49754295197944187]], [136, false, 1, 1, -55, [0, "36bxy2zcNOi6tfP9odeMXh"]]], [1, "96qzxoSd1JD7s/sDteFGRc", null, null, null, 1, 0], [1, 92.995, 55.98000000000002, 0], [1, 0.9, 0.9, 0.9]], [63, "player", 16384, 2, [-60, -61], [[[2, -58, [0, "e8brTWM6ZLv7mVP1olb7yg"], [5, 134, 134]], -59], 4, 1], [1, "2aAEZlbWpJM7VVJfR3NRBR", null, null, null, 1, 0]], [11, "state", 16384, [3, -64], [[2, -62, [0, "5dWnYLvv9CnIJeiZyXKBi4"], [5, 121, 121]], [105, 45, 7, 7, 7, 7, 121, 121, 1, -63, [0, "a3rO+xO8RIpbPyqK4S/ObU"]]], [1, "47WTizsDhA6oEMrAa/uKxT", null, null, null, 1, 0]], [91, "card_count", 16384, 7, [-68], [[2, -65, [0, "c9Sk6OXrxA1YNS6L5WGeO2"], [5, 40, 53]], [22, 2, -66, [0, "bdJ++BJV5N54IMi/2JZFHZ"], 7], [106, 9, -6.219615384615384, 15.62038461538462, 1, -67, [0, "97X3pdvq5Bf6mwJ79OFkmB"]]], [1, "2173ax+zJBj4uTD4jhM0HC", null, null, null, 1, 0], [1, -57.834999999999994, 30.994999999999994, 0], [1, 0.7692307692307692, 0.7692307692307692, 1]], [7, ["873KEkFjVHAIgSZMub22XD"]], [29, "throw_mine", 16384, [-71, 4], [[[19, -69, [0, "e37HBKXAFEq5QXVHl0iSet"]], -70], 4, 1], [1, "66bxCy26JJpoE6Ey+FSsLk", null, null, null, 1, 0]], [58, "sound_effect", false, 16384, 2, [[2, -72, [0, "54ah8C9NVDl6LU15tTujRO"], [5, 180, 180]], [156, "default", "animation", false, 0, false, -73, [0, "a6yh+0ZEtNqpUJoo4w2Wrq"], 0]], [1, "f8bILd/IlHy6SVRvr5+mFQ", null, null, null, 1, 0], [1, 0, -90, 0]], [27, "frameNode", 16384, 2, [[2, -74, [0, "3ewMxwRC1NiZoBXxN2k7LK"], [5, 180, 180]]], [1, "7cZWtS7UJFQJh+Y+ynCEYQ", null, null, null, 1, 0]], [12, "Node", 16384, 7, [-76, 8], [[2, -75, [0, "d6rflivcdLTJ8Nb6LTHpnf"], [5, 135, 135]]], [1, "65+1jC/GRPu7SdDRsqD0Lm", null, null, null, 1, 0]], [12, "avatar_border", 16384, 14, [-79], [[2, -77, [0, "aa++mdGfFPXJvV7TA8Qf6e"], [5, 134, 134]], [50, 2, false, -78, [0, "41PS2o8KlOEqrdkqPA3b59"], 1]], [1, "b23mfc831LQ4SB0dAfxMqr", null, null, null, 1, 0]], [28, "auto", false, 16384, 3, [[2, -80, [0, "abH4sdAfxNgLSFzayzikHn"], [5, 78, 47]], [8, -81, [0, "71uqzep91BGIj/XYkWFuIF"], 2]], [1, "00qufRE4pGiKgYqkYgHu4w", null, null, null, 1, 0]], [92, "out", 512, false, 16384, 3, [[2, -82, [0, "a5e3j2cGpCoYfK/cLy4g4F"], [5, 76, 47]], [8, -83, [0, "09nRrqFfJGxo/uJEpWejEt"], 3]], [1, "c6YtMJfd5Ip7MpnfockQ/F", null, null, null, 1, 0]], [28, "leave", false, 16384, 3, [[2, -84, [0, "a4enQTRTRKP5urYSqiO+oZ"], [5, 77, 47]], [8, -85, [0, "7dlhENYaNN27MUUz2S5uZI"], 4]], [1, "94+d+q171Jn4Rmgok84C1Z", null, null, null, 1, 0]], [28, "watch", false, 16384, 3, [[2, -86, [0, "2c5pOYlo1IBrEEhWjz+J+W"], [5, 64, 54]], [22, 2, -87, [0, "dcFfEuD6dOZ4vUnOETGCPd"], 5]], [1, "b3JO6/wlNGe56w7kkj83Mh", null, null, null, 1, 0]], [3, "team_tag", 16384, 8, [[2, -88, [0, "421tRTsjRM8bkmVDqpOP1v"], [5, 81, 41]], [43, 0, -89, [0, "3ek9MOcy9HPIgOjy5NN1oM"]], [107, 33, -20.801000000000002, -22.331000000000003, 1, -90, [0, "81X4s0VgxBFa4/72wFvBMo"]]], [1, "99E9sQ+UVLnIzb8EoKWWVs", null, null, null, 1, 0], [1, 40.801, 62.331, 0]], [15, "select", 16384, 5, [-92, -93], [[2, -91, [0, "ac+54PH1pN+4/RfnvD8l84"], [5, 53, 41]]], [1, "f8tpMW9nFLD43mKWG0bus3", null, null, null, 1, 0], [1, 0, 124.884, 0]], [93, "arrow", false, 16384, 21, [[14, -94, [0, "32WCn1OpVCC7vhKuQhJURf"], [5, 208.52999877929688, 208.52999877929688], [0, 0.5005514691624356, 0.5004555757984906]], [44, "default", "idle1", false, 0, -95, [0, "a9tThFGvlDbbo7GRP8B9Dy"], 8]], [1, "c4YH/CgXxOmaTIGhVDeWM0", null, null, null, 1, 0], [1, 0, -95.665, 0], [1, 1, -1, 1]], [23, 0, {}, 1, [25, "873KEkFjVHAIgSZMub22XD", null, null, -96, [26, "ccu8RW4oRD2oykLAOpIvk1", 1, [[16, "draw_mine", ["_name"], 10], [9, ["_lpos"], 10, [1, 0, 0, 0]], [9, ["_lrot"], 10, [3, 0, 0, 0, 1]], [9, ["_euler"], 10, [1, 0, 0, 0]], [20, true, ["playOnLoad"], [7, ["0b+1cFRbBN9KJdVqlxRRwG"]]], [16, false, ["_active"], 10]]], 11]], [59, "tween", 512, 16384, 1, [11, -98], [[19, -97, [0, "18mOqmqm1LeptGQVX2egUI"]]], [1, "50py+8qmxKSojMyULzZ48/", null, null, null, 1, 0]], [12, "spine_slot", 16384, 11, [-100], [[19, -99, [0, "87CB8reIhI1IMNqwti5a8g"]]], [1, "0aipoLb+tD2YMOlrIt3aI7", null, null, null, 1, 0]], [63, "curse", 16384, 24, [6], [[[19, -101, [0, "5cggxT+VpIf5QBMKdTIJdw"]], -102], 4, 1], [1, "95uLqb2TxBFZrg2/YXPfZn", null, null, null, 1, 0]], [27, "waveNode", 16384, 2, [[2, -103, [0, "2a+BIlDfBEApplKHSCSsgf"], [5, 180, 180]]], [1, "08E7bbuWNP2ZmSf2TMaaDi", null, null, null, 1, 0]], [96, "countdown", 16384, 2, [[[2, -104, [0, "42RrOtZJBKM5+/ortn/CBk"], [5, 233, 233]], -105], 4, 1], [1, "16eGKpHwtKsYalRwFMcY8s", null, null, null, 1, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [24, "avatar", 16384, 15, [[[2, -106, [0, "78wq6tF4pDiZkbEV5Vlu80"], [5, 125, 125]], -107], 4, 1], [1, "b0WR9nno9O2YuW8Bc6yNQI", null, null, null, 1, 0]], [24, "Label", 16384, 9, [[[2, -108, [0, "0fjPN4s55NRoLFvzDc4XbK"], [5, 25.734375, 46.32]], -109], 4, 1], [1, "eccCzj72xHBbAaqhsqkW8q", null, null, null, 1, 0]], [10, "Label", 16384, 5, [[[2, -110, [0, "450FGjAgBGCYFGWVlajYcL"], [5, 0, 28.98]], -111], 4, 1], [1, "75/lENjGRMvLZ0mOahI3gz", null, null, null, 1, 0], [1, 0, 3.01, 0]], [28, "selected", false, 16384, 21, [[14, -112, [0, "53tkRsCGtP0KHTYHTfza8e"], [5, 208.52999877929688, 208.52999877929688], [0, 0.5005514691624356, 0.5004555757984906]], [44, "default", "idle2", false, 0, -113, [0, "b0v4sz5phPaI2D9Frk8aJ3"], 9]], [1, "72FSZxaRpPJJFFbTfihvrI", null, null, null, 1, 0]], [24, "Node", 16384, 25, [[[19, -114, [0, "36hzlG1idG4r95VcECwKkX"]], -115], 4, 1], [1, "624itQ2wtDmYJ6KMJiTFc8", null, null, null, 1, 0]], [3, "label_x", 16384, 4, [[14, -116, [0, "67uvm4RHRHV7JVgYxq/5Qs"], [5, 28, 29], [0, 0.5, 0]], [8, -117, [0, "b50e9DeJZJ2r8Vtx0btTmX"], 12]], [1, "d7iUAGFJJJt4I/cOjSwx4r", null, null, null, 1, 0], [1, -56.032645390528955, 0, 0]], [39, "count", 512, 16384, 4, [[[14, -118, [0, "54UztOek1Ew6sYyIVNkwje"], [5, 37.90771484375, 72.26], [0, 0.5, 0]], -119], 4, 1], [1, "f5rgaNFsJE77yXPhbXASMz", null, null, null, 1, 0], [1, -23.078787968653955, -13.128, 0]], [3, "label_turn", 16384, 4, [[14, -120, [0, "50FoKsxsxDY601+MGfsOId"], [5, 74, 40], [0, 0.5, 0]], [8, -121, [0, "15s5OWjzxO2KP2lU4FzllQ"], 13]], [1, "2c9xGM1DlMz7mznI4s6mJT", null, null, null, 1, 0], [1, 32.875069453221045, 0, 0]], [3, "curse_shark", 16384, 6, [[2, -122, [0, "a228HAXWNDUJmxAIxlcS0s"], [5, 86, 87]], [8, -123, [0, "17zom6mlFIWIa4rNkhYq+s"], 14]], [1, "1bTD+ksqpByJThTuXDXTT5", null, null, null, 1, 0], [1, -28.58052936559001, -11.524, 0]], [97, "curse_count", 16384, 6, [[[2, -124, [0, "9bFDqy7fJFm5grJ17cYxVC"], [5, 57, 47]], -125], 4, 1], [1, "19bTt9BLRItLyu4tFmLbxL", null, null, null, 1, 0], [1, 16.652, 0, 0], [1, 1.1, 1.1, 1.1]], [3, "faceNode", 16384, 1, [[2, -126, [0, "e0g0JFIrVJ0LlUDSDZnmTw"], [5, 180, 180]]], [1, "ddD++QrvhBhbTqmLZLPNsU", null, null, null, 1, 0], [1, 0, -8.782, 0]], [129, 3, 2, 2, 1, false, 28, [0, "67rN9RaX5CGLHofdQ+yeWu"], [0, 0.5, 0.5]], [43, 0, 29, [0, "89yarII6NCw5p4Z1eKNly9"]], [121, "0", 32, 32, "Nowar Rounded TW", 0, false, true, true, 3, 30, [0, "9akDT4MHpME5Sp5wEOp8rq"], [4, 4279322518]], [130, 2, false, 7, [0, "21CVHs5zBN/5ZM0ViVjV6A"]], [122, "", 23, 23, "Source Han Sans CN", 0, true, 31, [0, "6aADI8H6JF1qlLsDqpa/jL"]], [51, false, 2, 2, [0, "b68o26U5tA14HorhuYFHDt"]], [157, "default", "shuaiguo2", false, 0, 33, [0, "b05amT/GNOJKB4lQKFlG2M"]], [123, "0", 2, 51, 51, "Microsoft YaHei", 0, false, -10, true, 4, 35, [0, "fbOCh3zpdKxI3qrYQ0gQmJ"], [4, 4278775039], [4, 4278927531]], [172, 11, [0, "6ceQap9HZLaZ2CnOlv/uRX"], 25, 4, 47, 46], [30, 38, [0, "70JmiGJAtAeLoYM9iS4Esz"]], [173, 26, [0, "9dJdGtyS1OxZRnBjTjRxDw"], 6, 49, [15, 16, 17, 18]]], 0, [0, -1, 23, 0, 3, 1, 0, 0, 1, 0, 70, 13, 0, 71, 13, 0, 72, 39, 0, 73, 27, 0, 74, 12, 0, 75, 45, 0, 76, 50, 0, 77, 48, 0, 78, 23, 0, 79, 19, 0, 80, 18, 0, 81, 17, 0, 82, 16, 0, 83, 3, 0, 84, 40, 0, 85, 22, 0, 86, 42, 0, 87, 41, 0, 88, 44, 0, 89, 43, 0, 0, 1, 0, -1, 2, 0, -2, 23, 0, -3, 24, 0, -4, 39, 0, 0, 2, 0, -2, 45, 0, -1, 12, 0, -2, 27, 0, -3, 13, 0, -4, 28, 0, -5, 7, 0, -6, 5, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, 0, 4, 0, 0, 4, 0, -1, 34, 0, -2, 35, 0, -3, 36, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 31, 0, -2, 21, 0, 0, 6, 0, 0, 6, 0, -1, 37, 0, -2, 38, 0, 0, 7, 0, -2, 43, 0, -1, 14, 0, -2, 9, 0, 0, 8, 0, 0, 8, 0, -2, 20, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 30, 0, 0, 11, 0, -2, 48, 0, -1, 25, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, -1, 15, 0, 0, 15, 0, 0, 15, 0, -1, 29, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, -1, 22, 0, -2, 32, 0, 0, 22, 0, 0, 22, 0, 3, 23, 0, 0, 24, 0, -2, 26, 0, 0, 25, 0, -1, 33, 0, 0, 26, 0, -2, 50, 0, 0, 27, 0, 0, 28, 0, -2, 40, 0, 0, 29, 0, -2, 41, 0, 0, 30, 0, -2, 42, 0, 0, 31, 0, -2, 44, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, -2, 46, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, -2, 47, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, -2, 49, 0, 0, 39, 0, 4, 1, 3, 5, 8, 4, 5, 11, 6, 5, 26, 8, 5, 14, 11, 5, 24, 126], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 41, 41, 43, 45, 46, 49], [8, 2, 2, 2, 2, 2, 2, 2, 8, 8, 2, 6, 2, 2, 2, -1, -2, -3, -4, -1, -2, 90, 2, 91, 2, 2, 2, 8, 2], [96, 20, 97, 98, 99, 100, 101, 102, 32, 32, 103, 104, 105, 106, 107, 33, 108, 109, 110, 20, 34, 111, 112, 113, 12, 20, 34, 114, 33]], [[[5, "reconnection"], [11, "reconnection", 33554432, [-8], [[2, -2, [0, "6eUW35Su9LBYNmZQCu1iHw"], [5, 1080, 1919.9999999999998]], [6, 45, 100, 100, -3, [0, "98pZuMTQBEOpiKtbCJRaF/"]], [174, -6, [0, "ee3gCczXRLl4ouavoQrAZJ"], -5, -4], [31, -7, [0, "bfJQRTUBJBwJ/GQwUT6dE7"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [12, "notice", 33554432, 1, [-11, -12, -13], [[2, -9, [0, "83z03924hNyZcMAhrSwpxD"], [5, 785, 516]], [8, -10, [0, "7bLUUJ6AFG4bAL9iuJC97T"], 2]], [1, "acn/sqOJtMJYemaeqRHj+z", null, null, null, 1, 0]], [10, "btn_confirm", 33554432, 2, [[[2, -14, [0, "3eqJ5OV0NObrQzOUpcJTBx"], [5, 77, 86]], [17, 1, -15, [0, "3duHP9LBhFu5O0/6qdW8az"], 1], -16, [108, -17, [0, "07dpuWUAtEJoA91mKGruQL"]]], 4, 4, 1, 4], [1, "34DMQlQyRFSIrhS76UQ6nK", null, null, null, 1, 0], [1, 308.024, 220.462, 0]], [10, "common_prompt_text", 33554432, 2, [[[2, -18, [0, "e4PmeHHdZO4aSaVcA24Ii0"], [5, 631.0957421874999, 69.22]], -19], 4, 1], [1, "04pp92sFdMBpTMDI5U13iv", null, null, null, 1, 0], [1, 0, 24.297, 0]], [3, "modal_title_tips", 33554432, 2, [[2, -20, [0, "325fH1WxtAJKg1tcAsh7O7"], [5, 195, 108]], [22, 0, -21, [0, "28xJTR9E1PbqlSRmZCdnuA"], 0]], [1, "c7MWUY7MJEib7ePn/5VSbk", null, null, null, 1, 0], [1, 0, 240.231, 0]], [124, "重连失败,请检查网络重连失败", 47, 47, "Source Han Sans CN", 0, 3, -10, true, true, 5, 0, 4, [0, "ae5B3Hf+VH16FwIItLGtpa"], [4, 4290741029], [4, 4293340537], [0, 4, 4]], [18, 3, 3, [0, "b8KkAyNsZGWqfWdZlw5KW0"], [4, 4292269782], 3]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 14, 7, 0, 92, 6, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 3, 0, 0, 3, 0, 0, 3, 0, -3, 7, 0, 0, 3, 0, 0, 4, 0, -2, 6, 0, 0, 5, 0, 0, 5, 0, 4, 1, 21], [0, 0, 0], [2, 2, 2], [7, 4, 8]], [[[4, "Shark_ThrowSuccess", ".mp3", 0.626939], -1], 0, 0, [], [], []], [[{"name": "9", "rect": {"x": 615, "y": 244, "width": 235, "height": 300}, "offset": {"x": -0.5, "y": 0}, "originalSize": {"width": 236, "height": 300}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [3]], [[[35, "12-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "\n12-zhidinduixiang.png\nsize: 622,420\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n1\n  rotate: false\n  xy: 414, 275\n  size: 102, 143\n  orig: 106, 148\n  offset: 2, 3\n  index: -1\n2\n  rotate: false\n  xy: 414, 130\n  size: 102, 143\n  orig: 106, 148\n  offset: 2, 2\n  index: -1\n3\n  rotate: true\n  xy: 518, 275\n  size: 143, 102\n  orig: 148, 106\n  offset: 2, 2\n  index: -1\n4\n  rotate: true\n  xy: 518, 130\n  size: 143, 102\n  orig: 148, 106\n  offset: 3, 2\n  index: -1\n5\n  rotate: false\n  xy: 2, 8\n  size: 410, 410\n  orig: 416, 414\n  offset: 3, 2\n  index: -1\nxingxing\n  rotate: true\n  xy: 414, 2\n  size: 126, 127\n  orig: 150, 150\n  offset: 14, 12\n  index: -1\n", ["12-zhidinduixiang.png"], {"skeleton": {"hash": "/O0mTZhPhJdo5IZnzRgBzFllf2Y", "spine": "3.8.99", "x": -104.38, "y": -104.36, "width": 208.53, "height": 208.53, "images": "./images/", "audio": "E:/XM/商业策划部/小心鲨手/12-指定对象"}, "bones": [{"name": "root"}, {"name": "a", "parent": "root", "scaleX": 0.3949, "scaleY": 0.3949}, {"name": "0", "parent": "root"}, {"name": "1", "parent": "0"}, {"name": "xingxing", "parent": "1", "scaleX": 0.2956, "scaleY": 0.2956}, {"name": "2", "parent": "0", "rotation": -22.16, "scaleX": 0.5763, "scaleY": 0.5763}, {"name": "xingxing2", "parent": "2", "scaleX": 0.2956, "scaleY": 0.2956}, {"name": "3", "parent": "0", "rotation": 39.61, "scaleX": 0.8354, "scaleY": 0.8354}, {"name": "xingxing3", "parent": "3", "scaleX": 0.2956, "scaleY": 0.2956}, {"name": "4", "parent": "root", "rotation": -97.43, "scaleX": 0.8572, "scaleY": 0.8572}, {"name": "5", "parent": "4"}, {"name": "xingxing4", "parent": "5", "scaleX": 0.2956, "scaleY": 0.2956}, {"name": "6", "parent": "4", "rotation": -22.16, "scaleX": 0.5763, "scaleY": 0.5763}, {"name": "xingxing5", "parent": "6", "scaleX": 0.2956, "scaleY": 0.2956}, {"name": "7", "parent": "4", "rotation": 39.61, "scaleX": 0.8354, "scaleY": 0.8354}, {"name": "xingxing6", "parent": "7", "scaleX": 0.2956, "scaleY": 0.2956}, {"name": "8", "parent": "root", "rotation": 128.92}, {"name": "9", "parent": "8"}, {"name": "xingxing7", "parent": "9", "scaleX": 0.2956, "scaleY": 0.2956}, {"name": "10", "parent": "8", "rotation": -22.16, "scaleX": 0.5763, "scaleY": 0.5763}, {"name": "xingxing8", "parent": "10", "scaleX": 0.2956, "scaleY": 0.2956}, {"name": "11", "parent": "8", "rotation": 39.61, "scaleX": 0.8354, "scaleY": 0.8354}, {"name": "xingxing9", "parent": "11", "scaleX": 0.2956, "scaleY": 0.2956}], "slots": [{"name": "5", "bone": "a", "attachment": "5"}, {"name": "4", "bone": "a", "attachment": "4"}, {"name": "3", "bone": "a", "attachment": "3"}, {"name": "2", "bone": "a", "attachment": "2"}, {"name": "1", "bone": "a", "attachment": "1"}, {"name": "xingxing", "bone": "xingxing"}, {"name": "xingxing7", "bone": "xingxing7"}, {"name": "xingxing4", "bone": "xingxing4"}, {"name": "xingxing3", "bone": "xingxing3"}, {"name": "xingxing9", "bone": "xingxing9"}, {"name": "xingxing6", "bone": "xingxing6"}, {"name": "xingxing2", "bone": "xingxing2"}, {"name": "xingxing8", "bone": "xingxing8"}, {"name": "xingxing5", "bone": "xingxing5"}], "skins": [{"name": "default", "attachments": {"1": {"1": {"x": -0.29, "y": 189.75, "width": 106, "height": 148}}, "2": {"2": {"x": -0.29, "y": -190.25, "width": 106, "height": 148}}, "3": {"3": {"x": -190.29, "y": -0.25, "width": 148, "height": 106}}, "4": {"4": {"x": 189.71, "y": -0.25, "width": 148, "height": 106}}, "5": {"5": {"width": 416, "height": 414}}, "xingxing": {"xingxing": {"x": -0.67, "y": -0.92, "width": 150, "height": 150}}, "xingxing2": {"xingxing": {"x": -0.67, "y": -0.92, "width": 150, "height": 150}}, "xingxing3": {"xingxing": {"x": -0.67, "y": -0.92, "width": 150, "height": 150}}, "xingxing4": {"xingxing": {"x": -0.67, "y": -0.92, "width": 150, "height": 150}}, "xingxing5": {"xingxing": {"x": -0.67, "y": -0.92, "width": 150, "height": 150}}, "xingxing6": {"xingxing": {"x": -0.67, "y": -0.92, "width": 150, "height": 150}}, "xingxing7": {"xingxing": {"x": -0.67, "y": -0.92, "width": 150, "height": 150}}, "xingxing8": {"xingxing": {"x": -0.67, "y": -0.92, "width": 150, "height": 150}}, "xingxing9": {"xingxing": {"x": -0.67, "y": -0.92, "width": 150, "height": 150}}}}], "animations": {"idle1": {"slots": {"2": {"attachment": [{"name": null}, {"time": 0.5333, "name": null}]}, "3": {"attachment": [{"name": null}, {"time": 0.5333, "name": null}]}, "4": {"attachment": [{"name": null}, {"time": 0.5333, "name": null}]}, "5": {"attachment": [{"name": null}, {"time": 0.5333, "name": null}]}}, "bones": {"a": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333}], "translate": [{"y": 25.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 25.96}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333}]}}}, "idle2": {"bones": {"a": {"scale": [{"x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 1.179, "y": 1.179}]}}}, "idle3": {"slots": {"xingxing": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "xingxing"}]}, "xingxing2": {"color": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}], "attachment": [{"name": "xingxing"}]}, "xingxing3": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "xingxing"}]}, "xingxing4": {"color": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}], "attachment": [{"time": 0.3333, "name": "xingxing"}]}, "xingxing5": {"color": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}], "attachment": [{"name": "xingxing"}]}, "xingxing6": {"color": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}], "attachment": [{"time": 0.5, "name": "xingxing"}]}, "xingxing7": {"color": [{"color": "ffffff7f"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff7f"}], "attachment": [{"time": 0.1, "name": "xingxing"}]}, "xingxing8": {"color": [{"color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}], "attachment": [{"time": 0.5, "name": "xingxing"}]}, "xingxing9": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "xingxing"}]}}, "bones": {"a": {"rotate": [{}], "scale": [{"x": 1.179, "y": 1.179, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.179, "y": 1.179}]}, "xingxing": {"rotate": [{}, {"time": 0.6333, "angle": 161.65, "curve": "stepped"}, {"time": 0.6667}], "translate": [{}, {"time": 0.6333, "x": -219.95, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"x": 0.193, "y": 0.193}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4667}, {"time": 0.6667, "x": 0.193, "y": 0.193}]}, "xingxing2": {"rotate": [{"angle": 76.57}, {"time": 0.3333, "angle": 161.65, "curve": "stepped"}, {"time": 0.3667}, {"time": 0.6667, "angle": 76.57}], "translate": [{"x": -104.19}, {"time": 0.3333, "x": -219.95, "curve": "stepped"}, {"time": 0.3667}, {"time": 0.6667, "x": -104.19}], "scale": [{"curve": "stepped"}, {"time": 0.1667}, {"time": 0.3667, "x": 0.193, "y": 0.193}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6667}]}, "xingxing3": {"rotate": [{"angle": 34.03}, {"time": 0.5, "angle": 161.65, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.6667, "angle": 34.03}], "translate": [{"x": -46.31}, {"time": 0.5, "x": -219.95, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.6667, "x": -46.31}], "scale": [{"x": 0.731, "y": 0.731}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3333}, {"time": 0.5333, "x": 0.193, "y": 0.193}, {"time": 0.6667, "x": 0.731, "y": 0.731}]}, "xingxing4": {"rotate": [{"angle": 85.08}, {"time": 0.3, "angle": 161.65, "curve": "stepped"}, {"time": 0.3333}, {"time": 0.6667, "angle": 85.08}], "translate": [{"x": -115.76}, {"time": 0.3, "x": -219.95, "curve": "stepped"}, {"time": 0.3333}, {"time": 0.6667, "x": -115.76}], "scale": [{"curve": "stepped"}, {"time": 0.1333}, {"time": 0.3333, "x": 0.193, "y": 0.193}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6667}]}, "xingxing5": {"rotate": [{"angle": 76.57}, {"time": 0.3333, "angle": 161.65, "curve": "stepped"}, {"time": 0.3667}, {"time": 0.6667, "angle": 76.57}], "translate": [{"x": -104.19}, {"time": 0.3333, "x": -219.95, "curve": "stepped"}, {"time": 0.3667}, {"time": 0.6667, "x": -104.19}], "scale": [{"curve": "stepped"}, {"time": 0.1667}, {"time": 0.3667, "x": 0.193, "y": 0.193}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6667}]}, "xingxing6": {"rotate": [{"angle": 76.57}, {"time": 0.3333, "angle": 161.65, "curve": "stepped"}, {"time": 0.3667}, {"time": 0.5, "angle": 34.03}, {"time": 0.6667, "angle": 76.57}], "translate": [{"x": -104.19}, {"time": 0.3333, "x": -219.95, "curve": "stepped"}, {"time": 0.3667}, {"time": 0.5, "x": -46.31}, {"time": 0.6667, "x": -104.19}], "scale": [{"curve": "stepped"}, {"time": 0.1667}, {"time": 0.3667, "x": 0.193, "y": 0.193}, {"time": 0.5, "x": 0.731, "y": 0.731}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6667}]}, "xingxing7": {"rotate": [{"angle": 144.64}, {"time": 0.0667, "angle": 161.65, "curve": "stepped"}, {"time": 0.1}, {"time": 0.6667, "angle": 144.64}], "translate": [{"x": -196.8}, {"time": 0.0667, "x": -219.95, "curve": "stepped"}, {"time": 0.1}, {"time": 0.6667, "x": -196.8}], "scale": [{"x": 0.596, "y": 0.596}, {"time": 0.1, "x": 0.193, "y": 0.193}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5667}, {"time": 0.6667, "x": 0.596, "y": 0.596}]}, "xingxing8": {"rotate": [{"angle": 119.11}, {"time": 0.1667, "angle": 161.65, "curve": "stepped"}, {"time": 0.2}, {"time": 0.5, "angle": 76.57}, {"time": 0.6667, "angle": 119.11}], "translate": [{"x": -162.07}, {"time": 0.1667, "x": -219.95, "curve": "stepped"}, {"time": 0.2}, {"time": 0.5, "x": -104.19}, {"time": 0.6667, "x": -162.07}], "scale": [{}, {"time": 0.2, "x": 0.193, "y": 0.193}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667}]}, "xingxing9": {"rotate": [{"angle": 34.03}, {"time": 0.5, "angle": 161.65, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.6667, "angle": 34.03}], "translate": [{"x": -46.31}, {"time": 0.5, "x": -219.95, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.6667, "x": -46.31}], "scale": [{"x": 0.731, "y": 0.731}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3333}, {"time": 0.5333, "x": 0.193, "y": 0.193}, {"time": 0.6667, "x": 0.731, "y": 0.731}]}}}}}, [0]]], 0, 0, [0], [-1], [115]], [[[5, "dark_card_list"], [46, "dark_card_list", 32768, [-7], [[2, -3, [0, "c5m4M4JIFPRZ8EAJtje7hk"], [5, 1080, 360]], [33, 40, 100, -4, [0, "b74woIW4RAIYYqYO5AagKv"]], [175, -6, [0, "abUpkSgY9Iho11CTMt63ns"], -5, 1]], [41, "86wbzKodtKGYlQbUgtm4RK", null, null, -2, 0, [-1]], [1, 0, -488.5, 0]], [7, ["cdE8mOophJm6wT0UhPe3Op"]], [23, 0, {}, 1, [25, "cdE8mOophJm6wT0UhPe3Op", null, null, -8, [26, "646aJ0lKRKvb+4ZvRjKRCe", 1, [[16, "list", ["_name"], 2], [9, ["_lpos"], 2, [1, 0, 0, 0]], [9, ["_lrot"], 2, [3, 0, 0, 0, 1]], [9, ["_euler"], 2, [1, 0, 0, 0]], [69, ["_contentSize"], [7, ["8aQaZa8hVGgZhhsRTLSmTa"]], [5, 80, 295]], [16, 32768, ["_layer"], 2]]], 0]]], 0, [0, -1, 3, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 93, 3, 0, 0, 1, 0, -1, 3, 0, 3, 3, 0, 4, 1, 8], [0, 0], [6, 13], [30, 116]], [[[4, "Shark_Bite", ".mp3", 0.7181], -1], 0, 0, [], [], []], [[{"name": "desk_state_no_card", "rect": {"x": 3, "y": 490, "width": 369, "height": 140}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 369, "height": 140}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[[4, "Shark_Resistance", ".mp3", 6.243265], -1], 0, 0, [], [], []], [[{"name": "desk_state_out_watch", "rect": {"x": 44, "y": 868, "width": 283, "height": 140}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 283, "height": 140}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [1]], [[{"name": "icon_loading_bar", "rect": {"x": 889, "y": 3, "width": 717, "height": 25}, "offset": {"x": -1, "y": 2}, "originalSize": {"width": 733, "height": 43}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [2]], [[[5, "notice"], [29, "notice", 33554432, [-8], [[[2, -4, [0, "6eUW35Su9LBYNmZQCu1iHw"], [5, 1080, 1920]], [6, 45, 100, 100, -5, [0, "98pZuMTQBEOpiKtbCJRaF/"]], -6, [31, -7, [0, "18a1CgMb5Ez5JNrIfL+nIy"]]], 4, 4, 1, 4], [68, "c46/YsCPVOJYA4mWEpNYRx", null, null, -3, 0, [[36, ["text"], -2, -1, [7, ["f6M0VDx1JFAYnKeneOjWFW"]]]]]], [11, "main", 33554432, [-11, -12, -13, -14], [[2, -9, [0, "e8ahYqtXNJWadkGiXnr/OX"], [5, 785, 516]], [22, 0, -10, [0, "74ctsN2XpBy5txX20zBRs+"], 3]], [1, "dadkok6m9LtZkXdljuKQHx", null, null, null, 1, 0]], [12, "ui_modal", 33554432, 1, [2], [[2, -15, [0, "e7FLsoEkNHJKn0vzw5GrM5"], [5, 1080, 1920]], [6, 45, 100, 100, -16, [0, "fcNUKh2KNBYJqZ0ubylbCM"]]], [1, "95InSLr5VFIYcSRkge9/44", null, null, null, 1, 0]], [3, "btn_close", 33554432, 2, [[2, -17, [0, "baC4lv9TNGS72SM17Wl0he"], [5, 77, 86]], [17, 1, -18, [0, "1eLx9BepJCcr82JJRYKs8E"], 2], [18, 3, -20, [0, "5ef5a3ra9HPZx4gYvGz3mE"], [4, 4292269782], -19]], [1, "17mQCOnBRES64FZVMz5mp4", null, null, null, 1, 0], [1, 307.443, 219.025, 0]], [10, "btn_confirm", 33554432, 2, [[[2, -21, [0, "b4+FshBtFKwrGHs+G3ljNf"], [5, 291, 104]], [13, 1, 0, -22, [0, "13veipj2tDqJmg+qvkJusx"], 1], -23], 4, 4, 1], [1, "69cM+xERtCKrc+Tu2+I179", null, null, null, 1, 0], [1, 2.384, -134.607, 0]], [3, "title", 33554432, 2, [[2, -24, [0, "e5cLbHUuREz5VixiMPO/Me"], [5, 195, 108]], [22, 0, -25, [0, "07qTVSw0ZEqbD0lXCAeHUe"], 0]], [1, "b5kRBzHYlDGI93/Amklj6k", null, null, null, 1, 0], [1, -0.014, 241.822, 0]], [10, "prompt_content_str", 33554432, 2, [[[2, -26, [0, "2fVkqTf2tPAItS9a8sEBa5"], [5, 624.955875, 145.6]], -27], 4, 1], [1, "3dv5b69WlPxIkiPUZRmIQn", null, null, null, 1, 0], [1, 9.663, 40.065, 0]], [49, "有玩家未加入游戏\n请返回重试", 47, 47, 60, 3, true, 5, 7, [0, "54oh+9R4dFc737N/nBJ7/U"], [4, 4290741029]], [21, 3, 5, [0, "edGeoOtQZD/rU0qZKboChv"]], [176, 1, [0, "73HH+Wt1BLno52J7Sj1JKE"], 8, 9]], 0, [0, 9, 3, 0, 10, 10, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -3, 10, 0, 0, 1, 0, -1, 3, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 5, 0, -4, 4, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 7, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -3, 9, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -2, 8, 0, 4, 1, 2, 5, 3, 27], [0, 0, 0, 0], [2, 2, 2, 2], [7, 10, 4, 8]], [[{"name": "btn_music_off", "rect": {"x": 3, "y": 835, "width": 85, "height": 85}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 85, "height": 85}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [6]], [[{"name": "title", "rect": {"x": 840, "y": 373, "width": 331, "height": 108}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 331, "height": 108}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "btn_confirm", "rect": {"x": 840, "y": 597, "width": 291, "height": 104}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 291, "height": 104}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [6]], [[[4, "Shark_PressT<PERSON>h", ".mp3", 0.165571], -1], 0, 0, [], [], []]]]