/**
 * 通知服务端最新包地址的脚本
 *
 * 使用方法:
 * node scripts/notify-server.js [upload-result.json的路径]
 *
 * 环境变量:
 * NOTIFY_API_URL - 通知服务端的API URL
 * NOTIFY_API_TOKEN - 通知服务端的API Token (可选)
 */

const fs = require('fs')
const path = require('path')
const http = require('http')
const https = require('https')
const { URL } = require('url')
// 使用 crypto-js 替代 crypto-es，因为它是 CommonJS 兼容的
const crypto = require('crypto')

// 从环境变量获取API配置
const apiUrl = process.env.NOTIFY_API_URL
const apiToken = process.env.NOTIFY_API_TOKEN || ''

// 检查必要的环境变量
if (!apiUrl) {
    console.error('错误: 缺少必要的环境变量 (NOTIFY_API_URL)')
    process.exit(1)
}

// 获取上传结果文件路径
const resultFilePath =
    process.argv[2] || path.join(process.cwd(), 'upload-result.json')

// 检查上传结果文件是否存在
if (!fs.existsSync(resultFilePath)) {
    console.error(`错误: 上传结果文件 "${resultFilePath}" 不存在`)
    process.exit(1)
}

// 读取上传结果
let uploadResult
try {
    uploadResult = JSON.parse(fs.readFileSync(resultFilePath, 'utf8'))
} catch (err) {
    console.error('错误: 无法解析上传结果文件:', err)
    process.exit(1)
}

// 检查上传结果是否包含必要的字段
if (!uploadResult.baseUrl || !uploadResult.gameCode) {
    console.error('错误: 上传结果文件缺少必要的字段 (baseUrl, gameCode)')
    process.exit(1)
}

// 构建通知数据
const notifyData = {
    gameCode: uploadResult.gameCode,
    packageUrl: uploadResult.baseUrl,
    // 可以添加更多需要的字段
}

// 构建请求头
const headers = {
    'Content-Type': 'application/json',
}

// 如果有API Token，添加到请求头
if (apiToken) {
    headers['Authorization'] = `${apiToken}`
}

// 发送通知
function notifyServer() {
    console.log('正在通知服务端...')
    console.log('API URL:', apiUrl)
    console.log('通知数据:', JSON.stringify(notifyData, null, 2))

    // 解析URL以确定使用http还是https
    const url = new URL(apiUrl)
    const requestModule = url.protocol === 'https:' ? https : http

    // 准备请求数据
    const postData = JSON.stringify(notifyData)

    // 设置请求选项
    const options = {
        hostname: url.hostname,
        port: url.port || (url.protocol === 'https:' ? 443 : 80),
        path:
            url.pathname +
            crypto
                .createHash('md5')
                .update(notifyData.packageUrl)
                .digest('hex') +
            url.search,
        method: 'POST',
        headers: {
            ...headers,
            'Content-Length': Buffer.byteLength(postData),
        },
        timeout: 30000, // 30秒超时
    }
    console.log(
        '请求接口：',
        url.pathname +
            crypto
                .createHash('md5')
                .update(notifyData.packageUrl)
                .digest('hex') +
            url.search
    )
    console.log('请求参数： ', options)

    // 创建请求
    const req = requestModule.request(options, (res) => {
        let responseData = ''

        // 接收数据
        res.on('data', (chunk) => {
            responseData += chunk
        })

        // 请求完成
        res.on('end', () => {
            if (
                res.statusCode >= 200 &&
                res.statusCode < 300 &&
                JSON.parse(responseData)?.code === 200
            ) {
                console.log('通知成功!')
                try {
                    const response = JSON.parse(responseData)
                    console.log(
                        '服务端响应:',
                        JSON.stringify(response, null, 2)
                    )
                } catch (e) {
                    console.log('服务端响应:', responseData)
                }
                process.exit(0)
            } else {
                console.error(`通知服务端失败: HTTP 状态码 ${res.statusCode}`)
                console.error('服务端错误响应:', responseData)
                process.exit(1)
            }
        })
    })

    // 错误处理
    req.on('error', (err) => {
        console.error('通知服务端失败:', err.message)
        process.exit(1)
    })

    // 超时处理
    req.on('timeout', () => {
        console.error('请求超时')
        req.destroy()
        process.exit(1)
    })

    // 发送数据
    req.write(postData)
    req.end()
}

notifyServer()
