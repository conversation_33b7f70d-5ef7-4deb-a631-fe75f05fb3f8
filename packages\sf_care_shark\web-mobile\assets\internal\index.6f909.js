System.register("chunks:///_virtual/builtin-pipeline-settings.ts",["./rollupPluginModLoBabelHelpers.js","cc","./builtin-pipeline-types.ts"],(function(t){var e,o,i,n,r,s,a,p,l,g,c,y,u,d,b,m,h;return{setters:[function(t){e=t.applyDecoratedDescriptor,o=t.inheritsLoose,i=t.initializerDefineProperty,n=t.assertThisInitialized,r=t.createClass},function(t){s=t.cclegacy,a=t._decorator,p=t.Camera,l=t.CCBoolean,g=t.CCInteger,c=t.CCFloat,y=t.Material,u=t.Texture2D,d=t.rendering,b=t.Component},function(t){m=t.fillRequiredPipelineSettings,h=t.makePipelineSettings}],execute:function(){var f,_,P,M,S,O,w,E,G,k,D,C,A,j,v,x,F,B,R,I,T,L,X,z,H,q,Y,N;s._RF.push({},"de1c2EHcMhAIYRZY5nyTQHG","builtin-pipeline-settings",void 0);var Q=a.ccclass,Z=a.disallowMultiple,J=a.executeInEditMode,K=a.menu,U=a.property,V=a.requireComponent;a.type,t("BuiltinPipelineSettings",(f=Q("BuiltinPipelineSettings"),_=K("Rendering/BuiltinPipelineSettings"),P=V(p),M=U(l),S=U({displayName:"Editor Preview (Experimental)",type:l}),O=U({group:{id:"MSAA",name:"Multisample Anti-Aliasing"},type:l}),w=U({group:{id:"MSAA",name:"Multisample Anti-Aliasing",style:"section"},type:g,range:[2,4,2]}),E=U({group:{id:"ShadingScale",name:"ShadingScale",style:"section"},type:l}),G=U({tooltip:"i18n:postprocess.shadingScale",group:{id:"ShadingScale",name:"ShadingScale"},type:c,range:[.01,4,.01],slide:!0}),k=U({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:l}),D=U({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:y}),C=U({tooltip:"i18n:bloom.enableAlphaMask",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:l}),A=U({tooltip:"i18n:bloom.iterations",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:g,range:[1,6,1],slide:!0}),j=U({tooltip:"i18n:bloom.threshold",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:c,min:0}),v=U({group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:l}),x=U({group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:y}),F=U({tooltip:"i18n:color_grading.contribute",group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:c,range:[0,1,.01],slide:!0}),B=U({tooltip:"i18n:color_grading.originalMap",group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:u}),R=U({group:{id:"FXAA",name:"Fast Approximate Anti-Aliasing (PostProcessing)",style:"section"},type:l}),I=U({group:{id:"FXAA",name:"Fast Approximate Anti-Aliasing (PostProcessing)",style:"section"},type:y}),T=U({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:l}),L=U({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:y}),X=U({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:c,range:[0,1,.01],slide:!0}),z=U({group:{id:"ToneMapping",name:"ToneMapping",style:"section"},type:y}),f(H=_(H=P(H=Z(H=J((Y=e((q=function(t){function e(){for(var e,o=arguments.length,r=new Array(o),s=0;s<o;s++)r[s]=arguments[s];return e=t.call.apply(t,[this].concat(r))||this,i(e,"_settings",Y,n(e)),i(e,"_editorPreview",N,n(e)),e}o(e,t);var s=e.prototype;return s.getPipelineSettings=function(){return this._settings},s.onEnable=function(){m(this._settings),this.getComponent(p).camera.pipelineSettings=this._settings},s.onDisable=function(){this.getComponent(p).camera.pipelineSettings=null},s._tryEnableEditorPreview=function(){void 0!==d&&(this._editorPreview?d.setEditorPipelineSettings(this._settings):this._disableEditorPreview())},s._disableEditorPreview=function(){void 0!==d&&(d.getEditorPipelineSettings()===this._settings&&d.setEditorPipelineSettings(null))},r(e,[{key:"editorPreview",get:function(){return this._editorPreview},set:function(t){this._editorPreview=t}},{key:"MsaaEnable",get:function(){return this._settings.msaa.enabled},set:function(t){this._settings.msaa.enabled=t}},{key:"msaaSampleCount",get:function(){return this._settings.msaa.sampleCount},set:function(t){t=Math.pow(2,Math.ceil(Math.log2(Math.max(t,2)))),t=Math.min(t,4),this._settings.msaa.sampleCount=t}},{key:"shadingScaleEnable",get:function(){return this._settings.enableShadingScale},set:function(t){this._settings.enableShadingScale=t}},{key:"shadingScale",get:function(){return this._settings.shadingScale},set:function(t){this._settings.shadingScale=t}},{key:"bloomEnable",get:function(){return this._settings.bloom.enabled},set:function(t){this._settings.bloom.enabled=t}},{key:"bloomMaterial",get:function(){return this._settings.bloom.material},set:function(t){this._settings.bloom.material!==t&&(this._settings.bloom.material=t)}},{key:"bloomEnableAlphaMask",get:function(){return this._settings.bloom.enableAlphaMask},set:function(t){this._settings.bloom.enableAlphaMask=t}},{key:"bloomIterations",get:function(){return this._settings.bloom.iterations},set:function(t){this._settings.bloom.iterations=t}},{key:"bloomThreshold",get:function(){return this._settings.bloom.threshold},set:function(t){this._settings.bloom.threshold=t}},{key:"bloomIntensity",get:function(){return this._settings.bloom.intensity},set:function(t){this._settings.bloom.intensity=t}},{key:"colorGradingEnable",get:function(){return this._settings.colorGrading.enabled},set:function(t){this._settings.colorGrading.enabled=t}},{key:"colorGradingMaterial",get:function(){return this._settings.colorGrading.material},set:function(t){this._settings.colorGrading.material!==t&&(this._settings.colorGrading.material=t)}},{key:"colorGradingContribute",get:function(){return this._settings.colorGrading.contribute},set:function(t){this._settings.colorGrading.contribute=t}},{key:"colorGradingMap",get:function(){return this._settings.colorGrading.colorGradingMap},set:function(t){this._settings.colorGrading.colorGradingMap=t}},{key:"fxaaEnable",get:function(){return this._settings.fxaa.enabled},set:function(t){this._settings.fxaa.enabled=t}},{key:"fxaaMaterial",get:function(){return this._settings.fxaa.material},set:function(t){this._settings.fxaa.material!==t&&(this._settings.fxaa.material=t)}},{key:"fsrEnable",get:function(){return this._settings.fsr.enabled},set:function(t){this._settings.fsr.enabled=t}},{key:"fsrMaterial",get:function(){return this._settings.fsr.material},set:function(t){this._settings.fsr.material!==t&&(this._settings.fsr.material=t)}},{key:"fsrSharpness",get:function(){return this._settings.fsr.sharpness},set:function(t){this._settings.fsr.sharpness=t}},{key:"toneMappingMaterial",get:function(){return this._settings.toneMapping.material},set:function(t){this._settings.toneMapping.material!==t&&(this._settings.toneMapping.material=t)}}]),e}(b)).prototype,"_settings",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return h()}}),N=e(q.prototype,"_editorPreview",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),e(q.prototype,"editorPreview",[S],Object.getOwnPropertyDescriptor(q.prototype,"editorPreview"),q.prototype),e(q.prototype,"MsaaEnable",[O],Object.getOwnPropertyDescriptor(q.prototype,"MsaaEnable"),q.prototype),e(q.prototype,"msaaSampleCount",[w],Object.getOwnPropertyDescriptor(q.prototype,"msaaSampleCount"),q.prototype),e(q.prototype,"shadingScaleEnable",[E],Object.getOwnPropertyDescriptor(q.prototype,"shadingScaleEnable"),q.prototype),e(q.prototype,"shadingScale",[G],Object.getOwnPropertyDescriptor(q.prototype,"shadingScale"),q.prototype),e(q.prototype,"bloomEnable",[k],Object.getOwnPropertyDescriptor(q.prototype,"bloomEnable"),q.prototype),e(q.prototype,"bloomMaterial",[D],Object.getOwnPropertyDescriptor(q.prototype,"bloomMaterial"),q.prototype),e(q.prototype,"bloomEnableAlphaMask",[C],Object.getOwnPropertyDescriptor(q.prototype,"bloomEnableAlphaMask"),q.prototype),e(q.prototype,"bloomIterations",[A],Object.getOwnPropertyDescriptor(q.prototype,"bloomIterations"),q.prototype),e(q.prototype,"bloomThreshold",[j],Object.getOwnPropertyDescriptor(q.prototype,"bloomThreshold"),q.prototype),e(q.prototype,"colorGradingEnable",[v],Object.getOwnPropertyDescriptor(q.prototype,"colorGradingEnable"),q.prototype),e(q.prototype,"colorGradingMaterial",[x],Object.getOwnPropertyDescriptor(q.prototype,"colorGradingMaterial"),q.prototype),e(q.prototype,"colorGradingContribute",[F],Object.getOwnPropertyDescriptor(q.prototype,"colorGradingContribute"),q.prototype),e(q.prototype,"colorGradingMap",[B],Object.getOwnPropertyDescriptor(q.prototype,"colorGradingMap"),q.prototype),e(q.prototype,"fxaaEnable",[R],Object.getOwnPropertyDescriptor(q.prototype,"fxaaEnable"),q.prototype),e(q.prototype,"fxaaMaterial",[I],Object.getOwnPropertyDescriptor(q.prototype,"fxaaMaterial"),q.prototype),e(q.prototype,"fsrEnable",[T],Object.getOwnPropertyDescriptor(q.prototype,"fsrEnable"),q.prototype),e(q.prototype,"fsrMaterial",[L],Object.getOwnPropertyDescriptor(q.prototype,"fsrMaterial"),q.prototype),e(q.prototype,"fsrSharpness",[X],Object.getOwnPropertyDescriptor(q.prototype,"fsrSharpness"),q.prototype),e(q.prototype,"toneMappingMaterial",[z],Object.getOwnPropertyDescriptor(q.prototype,"toneMappingMaterial"),q.prototype),H=q))||H)||H)||H)||H)||H));s._RF.pop()}}}));

System.register("chunks:///_virtual/builtin-pipeline-types.ts",["cc"],(function(e){var a,n;return{setters:[function(e){a=e.cclegacy,n=e.gfx}],execute:function(){e({fillRequiredBloom:o,fillRequiredColorGrading:u,fillRequiredFSR:c,fillRequiredFXAA:m,fillRequiredHBAO:function(e){void 0===e.enabled&&(e.enabled=!1);void 0===e.radiusScale&&(e.radiusScale=1);void 0===e.angleBiasDegree&&(e.angleBiasDegree=10);void 0===e.blurSharpness&&(e.blurSharpness=3);void 0===e.aoSaturation&&(e.aoSaturation=1);void 0===e.needBlur&&(e.needBlur=!1)},fillRequiredMSAA:r,fillRequiredPipelineSettings:function(e){e.msaa?r(e.msaa):e.msaa=i();void 0===e.enableShadingScale&&(e.enableShadingScale=!1);void 0===e.shadingScale&&(e.shadingScale=.5);e.bloom?o(e.bloom):e.bloom={enabled:!1,material:null,enableAlphaMask:!1,iterations:3,threshold:.8,intensity:2.3};e.toneMapping?f(e.toneMapping):e.toneMapping={material:null};e.colorGrading?u(e.colorGrading):e.colorGrading={enabled:!1,material:null,contribute:1,colorGradingMap:null};e.fsr?c(e.fsr):e.fsr={enabled:!1,material:null,sharpness:.8};e.fxaa?m(e.fxaa):e.fxaa={enabled:!1,material:null}},fillRequiredToneMapping:f,makeBloom:t,makeColorGrading:d,makeFSR:s,makeFXAA:b,makeHBAO:function(){return{enabled:!1,radiusScale:1,angleBiasDegree:10,blurSharpness:3,aoSaturation:1,needBlur:!1}},makeMSAA:i,makePipelineSettings:function(){return{msaa:i(),enableShadingScale:!1,shadingScale:.5,bloom:{enabled:!1,material:null,enableAlphaMask:!1,iterations:3,threshold:.8,intensity:2.3},toneMapping:{material:null},colorGrading:{enabled:!1,material:null,contribute:1,colorGradingMap:null},fsr:{enabled:!1,material:null,sharpness:.8},fxaa:{enabled:!1,material:null}}},makeToneMapping:p}),a._RF.push({},"cbf30kCUX9A3K+QpVC6wnzx","builtin-pipeline-types",void 0);var l=n.SampleCount;function i(){return{enabled:!1,sampleCount:l.X4}}function r(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.sampleCount&&(e.sampleCount=l.X4)}function t(){return{enabled:!1,material:null,enableAlphaMask:!1,iterations:3,threshold:.8,intensity:2.3}}function o(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.material&&(e.material=null),void 0===e.enableAlphaMask&&(e.enableAlphaMask=!1),void 0===e.iterations&&(e.iterations=3),void 0===e.threshold&&(e.threshold=.8),void 0===e.intensity&&(e.intensity=2.3)}function d(){return{enabled:!1,material:null,contribute:1,colorGradingMap:null}}function u(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.material&&(e.material=null),void 0===e.contribute&&(e.contribute=1),void 0===e.colorGradingMap&&(e.colorGradingMap=null)}function s(){return{enabled:!1,material:null,sharpness:.8}}function c(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.material&&(e.material=null),void 0===e.sharpness&&(e.sharpness=.8)}function b(){return{enabled:!1,material:null}}function m(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.material&&(e.material=null)}function p(){return{material:null}}function f(e){void 0===e.material&&(e.material=null)}a._RF.pop()}}}));

System.register("chunks:///_virtual/builtin-pipeline.ts",["./rollupPluginModLoBabelHelpers.js","cc","./env","./builtin-pipeline-types.ts"],(function(e){var a,t,i,n,r,s,o,d,l,h,c,u,p,g,m,S,f,_;return{setters:[function(e){a=e.createForOfIteratorHelperLoose},function(e){t=e.cclegacy,i=e.geometry,n=e.gfx,r=e.renderer,s=e.Vec2,o=e.Vec4,d=e.rendering,l=e.assert,h=e.clamp,c=e.Vec3,u=e.Material,p=e.Layers,g=e.PipelineEventType,m=e.sys,S=e.pipeline},function(e){f=e.DEBUG},function(e){_=e.makePipelineSettings}],execute:function(){e("getPingPongRenderTarget",G),t._RF.push({},"ff9b0GZzgRM/obMbHGfCNbk","builtin-pipeline",void 0);var w=i.AABB,b=i.Sphere,P=i.intersect,R=n.ClearFlagBit,T=n.Color,M=n.Format,E=n.FormatFeatureBit,v=n.LoadOp,C=n.StoreOp,A=n.TextureType,L=n.Viewport,x=r.scene,D=x.CameraUsage,F=x.CSMLevel,N=x.LightType;function O(e){return!!(e.clearFlag&(R.COLOR|R.STENCIL<<1))}function B(e,a,t,i,n,r){e.shadowFixedArea||e.csmLevel===F.LEVEL_1?(n.left=0,n.top=0,n.width=Math.trunc(a),n.height=Math.trunc(t)):(n.left=Math.trunc(i%2*.5*a),n.top=r>0?Math.trunc(.5*(1-Math.floor(i/2))*t):Math.trunc(.5*Math.floor(i/2)*t),n.width=Math.trunc(.5*a),n.height=Math.trunc(.5*t)),n.left=Math.max(0,n.left),n.top=Math.max(0,n.top),n.width=Math.max(1,n.width),n.height=Math.max(1,n.height)}var Q=e("PipelineConfigs",(function(){this.isWeb=!1,this.isWebGL1=!1,this.isWebGPU=!1,this.isMobile=!1,this.isHDR=!1,this.useFloatOutput=!1,this.toneMappingType=0,this.shadowEnabled=!1,this.shadowMapFormat=M.R32F,this.shadowMapSize=new s(1,1),this.usePlanarShadow=!1,this.screenSpaceSignY=1,this.supportDepthSample=!1,this.mobileMaxSpotLightShadowMaps=1,this.platform=new o(0,0,0,0)}));function y(e,a){var t=E.SAMPLED_TEXTURE|E.LINEAR_FILTER,i=e.device;a.isWeb=!m.isNative,a.isWebGL1=i.gfxAPI===n.API.WEBGL,a.isWebGPU=i.gfxAPI===n.API.WEBGPU,a.isMobile=m.isMobile,a.isHDR=e.pipelineSceneData.isHDR,a.useFloatOutput=e.getMacroBool("CC_USE_FLOAT_OUTPUT"),a.toneMappingType=e.pipelineSceneData.postSettings.toneMappingType;var s=e.pipelineSceneData.shadows;a.shadowEnabled=s.enabled,a.shadowMapFormat=S.supportsR32FloatTexture(e.device)?M.R32F:M.RGBA8,a.shadowMapSize.set(s.size),a.usePlanarShadow=s.enabled&&s.type===r.scene.ShadowType.Planar,a.screenSpaceSignY=e.device.capabilities.screenSpaceSignY,a.supportDepthSample=(e.device.getFormatFeatures(M.DEPTH_STENCIL)&t)===t;var o=i.capabilities.screenSpaceSignY;a.platform.x=a.isMobile?1:0,a.platform.w=.5*o+.5<<1|.5*i.capabilities.clipSpaceSignY+.5}var H=_(),z=e("CameraConfigs",(function(){this.settings=H,this.isMainGameWindow=!1,this.renderWindowId=0,this.colorName="",this.depthStencilName="",this.enableFullPipeline=!1,this.enableProfiler=!1,this.remainingPasses=0,this.enableShadingScale=!1,this.shadingScale=1,this.nativeWidth=1,this.nativeHeight=1,this.width=1,this.height=1,this.enableHDR=!1,this.radianceFormat=n.Format.RGBA8,this.copyAndTonemapMaterial=null,this.enableStoreSceneDepth=!1})),I=new T(0,0,0,0);function W(e,a,t,i){l(!!t.copyAndTonemapMaterial);var n=e.addRenderPass(t.nativeWidth,t.nativeHeight,"cc-tone-mapping");return n.addRenderTarget(t.colorName,v.CLEAR,C.STORE,I),n.addTexture(i,"inputTexture"),n.setVec4("g_platform",a.platform),n.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(t.copyAndTonemapMaterial,1),n}function G(e,a,t){return e.startsWith(a)?""+a+(1-Number(e.charAt(a.length)))+"_"+t:a+"0_"+t}var U=function(){function e(){this.lights=[],this.shadowEnabledSpotLights=[],this._sphere=b.create(0,0,0,1),this._boundingBox=new w,this._rangedDirLightBoundingBox=new w(0,0,0,.5,.5,.5)}var t=e.prototype;return t.cullLights=function(e,t,i){this.lights.length=0,this.shadowEnabledSpotLights.length=0;for(var n,r=a(e.spotLights);!(n=r()).done;){var s=n.value;s.baked||(b.set(this._sphere,s.position.x,s.position.y,s.position.z,s.range),P.sphereFrustum(this._sphere,t)&&(s.shadowEnabled?this.shadowEnabledSpotLights.push(s):this.lights.push(s)))}for(var o,d=a(e.sphereLights);!(o=d()).done;){var l=o.value;l.baked||(b.set(this._sphere,l.position.x,l.position.y,l.position.z,l.range),P.sphereFrustum(this._sphere,t)&&this.lights.push(l))}for(var h,u=a(e.pointLights);!(h=u()).done;){var p=h.value;p.baked||(b.set(this._sphere,p.position.x,p.position.y,p.position.z,p.range),P.sphereFrustum(this._sphere,t)&&this.lights.push(p))}for(var g,m=a(e.rangedDirLights);!(g=m()).done;){var S=g.value;w.transform(this._boundingBox,this._rangedDirLightBoundingBox,S.node.getWorldMatrix()),P.aabbFrustum(this._boundingBox,t)&&this.lights.push(S)}i&&this.shadowEnabledSpotLights.sort((function(e,a){return c.squaredDistance(i,e.position)-c.squaredDistance(i,a.position)}))},t._addLightQueues=function(e,t){for(var i,n=a(this.lights);!(i=n()).done;){var r=i.value,s=t.addQueue(d.QueueHint.BLEND,"forward-add");switch(r.type){case N.SPHERE:s.name="sphere-light";break;case N.SPOT:s.name="spot-light";break;case N.POINT:s.name="point-light";break;case N.RANGED_DIRECTIONAL:s.name="ranged-directional-light";break;default:s.name="unknown-light"}s.addScene(e,d.SceneFlags.BLEND,r)}},t.addSpotlightShadowPasses=function(e,t,i){for(var n,r=0,s=a(this.shadowEnabledSpotLights);!(n=s()).done;){var o=n.value,l=e.pipelineSceneData.shadows.size,h=e.addRenderPass(l.x,l.y,"default");if(h.name="SpotLightShadowPass"+r,h.addRenderTarget("SpotShadowMap"+r,v.CLEAR,C.STORE,new T(1,1,1,1)),h.addDepthStencil("SpotShadowDepth"+r,v.CLEAR,C.DISCARD),h.addQueue(d.QueueHint.NONE,"shadow-caster").addScene(t,d.SceneFlags.OPAQUE|d.SceneFlags.MASK|d.SceneFlags.SHADOW_CASTER).useLightFrustum(o),++r>=i)break}},t.addLightQueues=function(e,t,i){this._addLightQueues(t,e);for(var n,r=0,s=a(this.shadowEnabledSpotLights);!(n=s()).done;){var o=n.value;if(e.addTexture("SpotShadowMap"+r,"cc_spotShadowMap"),e.addQueue(d.QueueHint.BLEND,"forward-add").addScene(t,d.SceneFlags.BLEND,o),++r>=i)break}},t.addLightPasses=function(e,t,i,n,r,s,o,l,h,c){this._addLightQueues(o,c);for(var u,p=0,g=h.pipelineSceneData.shadows.size,m=a(this.shadowEnabledSpotLights);!(u=m()).done;){var S=u.value,f=h.addRenderPass(g.x,g.y,"default");f.name="SpotlightShadowPass",f.addRenderTarget("ShadowMap"+n,v.CLEAR,C.STORE,new T(1,1,1,1)),f.addDepthStencil("ShadowDepth"+n,v.CLEAR,C.DISCARD),f.addQueue(d.QueueHint.NONE,"shadow-caster").addScene(o,d.SceneFlags.OPAQUE|d.SceneFlags.MASK|d.SceneFlags.SHADOW_CASTER).useLightFrustum(S);var _=++p===this.shadowEnabledSpotLights.length?i:C.STORE;(c=h.addRenderPass(r,s,"default")).name="SpotlightWithShadowMap",c.setViewport(l),c.addRenderTarget(e,v.LOAD),c.addDepthStencil(t,v.LOAD,_),c.addTexture("ShadowMap"+n,"cc_spotShadowMap"),c.addQueue(d.QueueHint.BLEND,"forward-add").addScene(o,d.SceneFlags.BLEND,S)}return c},t.isMultipleLightPassesNeeded=function(){return this.shadowEnabledSpotLights.length>0},e}(),V=e("BuiltinForwardPassBuilder",function(){function e(){this.forwardLighting=new U,this._viewport=new L,this._clearColor=new T(0,0,0,1),this._reflectionProbeClearColor=new c(0,0,0)}var i=e.prototype;return i.getConfigOrder=function(){return e.ConfigOrder},i.getRenderOrder=function(){return e.RenderOrder},i.configCamera=function(e,a,t){t.enableMainLightShadowMap=a.shadowEnabled&&!a.usePlanarShadow&&!!e.scene&&!!e.scene.mainLight&&e.scene.mainLight.shadowEnabled,t.enableMainLightPlanarShadowMap=a.shadowEnabled&&a.usePlanarShadow&&!!e.scene&&!!e.scene.mainLight&&e.scene.mainLight.shadowEnabled,t.enablePlanarReflectionProbe=t.isMainGameWindow||e.cameraUsage===D.SCENE_VIEW,t.enableMSAA=t.settings.msaa.enabled&&!t.enableStoreSceneDepth&&!a.isWeb&&!a.isWebGL1,t.enableSingleForwardPass=a.isMobile||t.enableMSAA,++t.remainingPasses},i.windowResize=function(e,a,t,i,n,r,s){var o=d.ResourceFlags,l=d.ResourceResidency,h=i.renderWindowId,c=t.settings,u=t.enableShadingScale?Math.max(Math.floor(r*t.shadingScale),1):r,p=t.enableShadingScale?Math.max(Math.floor(s*t.shadingScale),1):s;if(t.enableMSAA&&(t.enableHDR?e.addTexture("MsaaRadiance"+h,A.TEX2D,t.radianceFormat,u,p,1,1,1,c.msaa.sampleCount,o.COLOR_ATTACHMENT,l.MEMORYLESS):e.addTexture("MsaaRadiance"+h,A.TEX2D,M.RGBA8,u,p,1,1,1,c.msaa.sampleCount,o.COLOR_ATTACHMENT,l.MEMORYLESS),e.addTexture("MsaaDepthStencil"+h,A.TEX2D,M.DEPTH_STENCIL,u,p,1,1,1,c.msaa.sampleCount,o.DEPTH_STENCIL_ATTACHMENT,l.MEMORYLESS)),e.addRenderTarget("ShadowMap"+h,a.shadowMapFormat,a.shadowMapSize.x,a.shadowMapSize.y),e.addDepthStencil("ShadowDepth"+h,M.DEPTH_STENCIL,a.shadowMapSize.x,a.shadowMapSize.y),t.enableSingleForwardPass)for(var g=a.mobileMaxSpotLightShadowMaps,m=0;m!==g;++m)e.addRenderTarget("SpotShadowMap"+m,a.shadowMapFormat,a.shadowMapSize.x,a.shadowMapSize.y),e.addDepthStencil("SpotShadowDepth"+m,M.DEPTH_STENCIL,a.shadowMapSize.x,a.shadowMapSize.y)},i.setup=function(e,a,t,i,n){var r=i.window.renderWindowId,s=i.scene,o=s.mainLight;--t.remainingPasses,l(t.remainingPasses>=0),this.forwardLighting.cullLights(s,i.frustum),t.enableMainLightShadowMap&&(l(!!o),this._addCascadedShadowMapPass(e,a,r,o,i)),t.enableSingleForwardPass&&this.forwardLighting.addSpotlightShadowPasses(e,i,a.mobileMaxSpotLightShadowMaps),this._tryAddReflectionProbePasses(e,t,r,o,i.scene),t.remainingPasses>0||t.enableShadingScale?(n.colorName=t.enableShadingScale?"ScaledRadiance0_"+r:"Radiance0_"+r,n.depthStencilName=t.enableShadingScale?"ScaledSceneDepth_"+r:"SceneDepth_"+r):(n.colorName=t.colorName,n.depthStencilName=t.depthStencilName);var d=this._addForwardRadiancePasses(e,a,t,r,i,t.width,t.height,o,n.colorName,n.depthStencilName,!t.enableMSAA,t.enableStoreSceneDepth?C.STORE:C.DISCARD);return t.enableStoreSceneDepth||(n.depthStencilName=""),0===t.remainingPasses&&t.enableShadingScale?W(e,a,t,n.colorName):d},i._addCascadedShadowMapPass=function(e,a,t,i,n){var r=d.QueueHint,s=d.SceneFlags,o=e.pipelineSceneData.shadows.size,l=o.x,h=o.y,c=this._viewport;c.left=c.top=0,c.width=l,c.height=h;var u=e.addRenderPass(l,h,"default");u.name="CascadedShadowMap",u.addRenderTarget("ShadowMap"+t,v.CLEAR,C.STORE,new T(1,1,1,1)),u.addDepthStencil("ShadowDepth"+t,v.CLEAR,C.DISCARD);for(var p=e.pipelineSceneData.csmSupported?i.csmLevel:1,g=0;g!==p;++g){B(i,l,h,g,this._viewport,a.screenSpaceSignY);var m=u.addQueue(r.NONE,"shadow-caster");a.isWebGPU||m.setViewport(this._viewport),m.addScene(n,s.OPAQUE|s.MASK|s.SHADOW_CASTER).useLightFrustum(i,g)}},i._tryAddReflectionProbePasses=function(e,i,s,o,l){var h=t.internal.reflectionProbeManager;if(h)for(var c,u=d.ResourceResidency,p=h.getProbes(),g=0,m=a(p);!(c=m()).done;){var S=c.value;if(S.needRender){var f=S.renderArea(),_=Math.max(Math.floor(f.x),1),w=Math.max(Math.floor(f.y),1);if(S.probeType===r.scene.ProbeType.PLANAR){if(!i.enablePlanarReflectionProbe)continue;var b=S.realtimePlanarTexture.window,P="PlanarProbeRT"+g,R="PlanarProbeDS"+g;e.addRenderWindow(P,i.radianceFormat,_,w,b),e.addDepthStencil(R,n.Format.DEPTH_STENCIL,_,w,u.MEMORYLESS);var T=e.addRenderPass(_,w,"default");T.name="PlanarReflectionProbe"+g,this._buildReflectionProbePass(T,i,s,S.camera,P,R,o,l)}if(4===++g)break}}},i._buildReflectionProbePass=function(e,a,t,i,n,r,s,o){void 0===o&&(o=null);var l=d.QueueHint,h=d.SceneFlags,c=a.enableMSAA?C.DISCARD:C.STORE;if(O(i)){this._reflectionProbeClearColor.x=i.clearColor.x,this._reflectionProbeClearColor.y=i.clearColor.y,this._reflectionProbeClearColor.z=i.clearColor.z;var u=d.packRGBE(this._reflectionProbeClearColor);this._clearColor.x=u.x,this._clearColor.y=u.y,this._clearColor.z=u.z,this._clearColor.w=u.w,e.addRenderTarget(n,v.CLEAR,c,this._clearColor)}else e.addRenderTarget(n,v.LOAD,c);i.clearFlag&R.DEPTH_STENCIL?e.addDepthStencil(r,v.CLEAR,C.DISCARD,i.clearDepth,i.clearStencil,i.clearFlag&R.DEPTH_STENCIL):e.addDepthStencil(r,v.LOAD,C.DISCARD),a.enableMainLightShadowMap&&e.addTexture("ShadowMap"+t,"cc_shadowMap"),e.addQueue(l.NONE,"reflect-map").addScene(i,h.OPAQUE|h.MASK|h.REFLECTION_PROBE,s||void 0,o||void 0)},i._addForwardRadiancePasses=function(e,a,t,i,n,r,s,o,h,c,u,p){void 0===u&&(u=!1),void 0===p&&(p=C.DISCARD);var g=d.QueueHint,m=d.SceneFlags,S=n.clearColor;this._clearColor.x=S.x,this._clearColor.y=S.y,this._clearColor.z=S.z,this._clearColor.w=S.w;var f=n.viewport;this._viewport.left=Math.round(f.x*r),this._viewport.top=Math.round(f.y*s),this._viewport.width=Math.max(Math.round(f.width*r),1),this._viewport.height=Math.max(Math.round(f.height*s),1);var _=!u&&t.enableMSAA;l(!_||t.enableSingleForwardPass);var w=t.enableSingleForwardPass?this._addForwardSingleRadiancePass(e,a,t,i,n,_,r,s,o,h,c,p):this._addForwardMultipleRadiancePasses(e,t,i,n,r,s,o,h,c,p);t.enableMainLightPlanarShadowMap&&this._addPlanarShadowQueue(n,o,w);var b=m.BLEND|(n.geometryRenderer?m.GEOMETRY:m.NONE);return w.addQueue(g.BLEND).addScene(n,b,o||void 0),w},i._addForwardSingleRadiancePass=function(e,a,t,i,n,r,s,o,d,h,c,u){var p;if(l(t.enableSingleForwardPass),r){var g="MsaaRadiance"+i,m="MsaaDepthStencil"+i,S=t.settings.msaa.sampleCount,f=e.addMultisampleRenderPass(s,o,S,0,"default");f.name="MsaaForwardPass",this._buildForwardMainLightPass(f,t,i,n,g,m,C.DISCARD,d),f.resolveRenderTarget(g,h),p=f}else(p=e.addRenderPass(s,o,"default")).name="ForwardPass",this._buildForwardMainLightPass(p,t,i,n,h,c,u,d);return l(void 0!==p),this.forwardLighting.addLightQueues(p,n,a.mobileMaxSpotLightShadowMaps),p},i._addForwardMultipleRadiancePasses=function(e,a,t,i,n,r,s,o,d,h){l(!a.enableSingleForwardPass);var c=e.addRenderPass(n,r,"default");c.name="ForwardPass";var u=this.forwardLighting.isMultipleLightPassesNeeded()?C.STORE:h;return this._buildForwardMainLightPass(c,a,t,i,o,d,u,s),c=this.forwardLighting.addLightPasses(o,d,h,t,n,r,i,this._viewport,e,c)},i._buildForwardMainLightPass=function(e,a,t,i,n,r,s,o,l){void 0===l&&(l=null);var h=d.QueueHint,c=d.SceneFlags;e.setViewport(this._viewport);var u=a.enableMSAA?C.DISCARD:C.STORE;O(i)?e.addRenderTarget(n,v.CLEAR,u,this._clearColor):e.addRenderTarget(n,v.LOAD,u),i.clearFlag&R.DEPTH_STENCIL?e.addDepthStencil(r,v.CLEAR,s,i.clearDepth,i.clearStencil,i.clearFlag&R.DEPTH_STENCIL):e.addDepthStencil(r,v.LOAD,s),a.enableMainLightShadowMap&&e.addTexture("ShadowMap"+t,"cc_shadowMap"),e.addQueue(h.NONE).addScene(i,c.OPAQUE|c.MASK,o||void 0,l||void 0)},i._addPlanarShadowQueue=function(e,a,t){var i=d.QueueHint,n=d.SceneFlags;t.addQueue(i.BLEND,"planar-shadow").addScene(e,n.SHADOW_CASTER|n.PLANAR_SHADOW|n.BLEND,a||void 0)},e}());V.ConfigOrder=100,V.RenderOrder=100;var k=e("BuiltinBloomPassBuilder",function(){function e(){this._clearColorTransparentBlack=new T(0,0,0,0),this._bloomParams=new o(0,0,0,0),this._bloomTexSize=new o(0,0,0,0),this._bloomWidths=[],this._bloomHeights=[],this._bloomTexNames=[]}var a=e.prototype;return a.getConfigOrder=function(){return 0},a.getRenderOrder=function(){return 200},a.configCamera=function(e,a,t){t.enableBloom=t.settings.bloom.enabled&&!!t.settings.bloom.material,t.enableBloom&&++t.remainingPasses},a.windowResize=function(e,a,t,i){if(t.enableBloom)for(var n=i.renderWindowId,r=t.width,s=t.height,o=0;o!==t.settings.bloom.iterations+1;++o)r=Math.max(Math.floor(r/2),1),s=Math.max(Math.floor(s/2),1),e.addRenderTarget("BloomTex"+n+"_"+o,t.radianceFormat,r,s)},a.setup=function(e,a,t,i,n,r){if(!t.enableBloom)return r;--t.remainingPasses,l(t.remainingPasses>=0);var s=i.window.renderWindowId;return l(!!t.settings.bloom.material),this._addKawaseDualFilterBloomPasses(e,a,t,t.settings,t.settings.bloom.material,s,t.width,t.height,n.colorName)},a._addKawaseDualFilterBloomPasses=function(e,a,t,i,n,r,s,o,l){var h=d.QueueHint,c=i.bloom.iterations,u=c+1;this._bloomWidths.length=u,this._bloomHeights.length=u,this._bloomWidths[0]=Math.max(Math.floor(s/2),1),this._bloomHeights[0]=Math.max(Math.floor(o/2),1);for(var p=1;p!==u;++p)this._bloomWidths[p]=Math.max(Math.floor(this._bloomWidths[p-1]/2),1),this._bloomHeights[p]=Math.max(Math.floor(this._bloomHeights[p-1]/2),1);this._bloomTexNames.length=u;for(var g=0;g!==u;++g)this._bloomTexNames[g]="BloomTex"+r+"_"+g;this._bloomParams.x=a.useFloatOutput?1:0,this._bloomParams.x=0,this._bloomParams.z=i.bloom.threshold,this._bloomParams.w=i.bloom.enableAlphaMask?1:0;var m=e.addRenderPass(this._bloomWidths[0],this._bloomHeights[0],"cc-bloom-prefilter");m.addRenderTarget(this._bloomTexNames[0],v.CLEAR,C.STORE,this._clearColorTransparentBlack),m.addTexture(l,"inputTexture"),m.setVec4("g_platform",a.platform),m.setVec4("bloomParams",this._bloomParams),m.addQueue(h.OPAQUE).addFullscreenQuad(n,0);for(var S=1;S!==u;++S){var f=e.addRenderPass(this._bloomWidths[S],this._bloomHeights[S],"cc-bloom-downsample");f.addRenderTarget(this._bloomTexNames[S],v.CLEAR,C.STORE,this._clearColorTransparentBlack),f.addTexture(this._bloomTexNames[S-1],"bloomTexture"),this._bloomTexSize.x=this._bloomWidths[S-1],this._bloomTexSize.y=this._bloomHeights[S-1],f.setVec4("g_platform",a.platform),f.setVec4("bloomTexSize",this._bloomTexSize),f.addQueue(h.OPAQUE).addFullscreenQuad(n,1)}for(var _=c;_-- >0;){var w=e.addRenderPass(this._bloomWidths[_],this._bloomHeights[_],"cc-bloom-upsample");w.addRenderTarget(this._bloomTexNames[_],v.CLEAR,C.STORE,this._clearColorTransparentBlack),w.addTexture(this._bloomTexNames[_+1],"bloomTexture"),this._bloomTexSize.x=this._bloomWidths[_+1],this._bloomTexSize.y=this._bloomHeights[_+1],w.setVec4("g_platform",a.platform),w.setVec4("bloomTexSize",this._bloomTexSize),w.addQueue(h.OPAQUE).addFullscreenQuad(n,2)}var b=e.addRenderPass(s,o,"cc-bloom-combine");return b.addRenderTarget(l,v.LOAD,C.STORE),b.addTexture(this._bloomTexNames[0],"bloomTexture"),b.setVec4("g_platform",a.platform),b.setVec4("bloomParams",this._bloomParams),b.addQueue(h.BLEND).addFullscreenQuad(n,3),0===t.remainingPasses?W(e,a,t,l):b},e}()),Y=e("BuiltinToneMappingPassBuilder",function(){function e(){this._colorGradingTexSize=new s(0,0)}var a=e.prototype;return a.getConfigOrder=function(){return 0},a.getRenderOrder=function(){return 300},a.configCamera=function(e,a,t){var i=t.settings;t.enableColorGrading=i.colorGrading.enabled&&!!i.colorGrading.material&&!!i.colorGrading.colorGradingMap,t.enableToneMapping=t.enableHDR||t.enableColorGrading,t.enableToneMapping&&++t.remainingPasses},a.windowResize=function(e,a,t){t.enableColorGrading&&(l(!!t.settings.colorGrading.material),t.settings.colorGrading.material.setProperty("colorGradingMap",t.settings.colorGrading.colorGradingMap))},a.setup=function(e,a,t,i,n,r){if(!t.enableToneMapping)return r;if(--t.remainingPasses,l(t.remainingPasses>=0),0===t.remainingPasses)return this._addCopyAndTonemapPass(e,a,t,t.nativeWidth,t.nativeHeight,n.colorName,t.colorName);var s=t.renderWindowId,o=t.enableShadingScale?"ScaledLdrColor":"LdrColor",d=G(n.colorName,o,s),h=n.colorName;return n.colorName=d,this._addCopyAndTonemapPass(e,a,t,t.width,t.height,h,d)},a._addCopyAndTonemapPass=function(e,a,t,i,n,r,s){var o,h=t.settings;if(t.enableColorGrading){l(!!h.colorGrading.material),l(!!h.colorGrading.colorGradingMap);var c=h.colorGrading.colorGradingMap;this._colorGradingTexSize.x=c.width,this._colorGradingTexSize.y=c.height;var u=c.width===c.height;(o=u?e.addRenderPass(i,n,"cc-color-grading-8x8"):e.addRenderPass(i,n,"cc-color-grading-nx1")).addRenderTarget(s,v.CLEAR,C.STORE,I),o.addTexture(r,"sceneColorMap"),o.setVec4("g_platform",a.platform),o.setVec2("lutTextureSize",this._colorGradingTexSize),o.setFloat("contribute",h.colorGrading.contribute),o.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(h.colorGrading.material,u?1:0)}else(o=e.addRenderPass(i,n,"cc-tone-mapping")).addRenderTarget(s,v.CLEAR,C.STORE,I),o.addTexture(r,"inputTexture"),o.setVec4("g_platform",a.platform),h.toneMapping.material?o.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(h.toneMapping.material,0):(l(!!t.copyAndTonemapMaterial),o.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(t.copyAndTonemapMaterial,0));return o},e}()),X=e("BuiltinFXAAPassBuilder",function(){function e(){this._fxaaParams=new o(0,0,0,0)}var a=e.prototype;return a.getConfigOrder=function(){return 0},a.getRenderOrder=function(){return 400},a.configCamera=function(e,a,t){t.enableFXAA=t.settings.fxaa.enabled&&!!t.settings.fxaa.material,t.enableFXAA&&++t.remainingPasses},a.setup=function(e,a,t,i,n,r){if(!t.enableFXAA)return r;--t.remainingPasses,l(t.remainingPasses>=0);var s=t.renderWindowId,o=t.enableShadingScale?"ScaledLdrColor":"LdrColor",d=G(n.colorName,o,s);if(l(!!t.settings.fxaa.material),0===t.remainingPasses)return t.enableShadingScale?(this._addFxaaPass(e,a,t.settings.fxaa.material,t.width,t.height,n.colorName,d),W(e,a,t,d)):(l(t.width===t.nativeWidth),l(t.height===t.nativeHeight),this._addFxaaPass(e,a,t.settings.fxaa.material,t.width,t.height,n.colorName,t.colorName));var h=n.colorName;return n.colorName=d,this._addFxaaPass(e,a,t.settings.fxaa.material,t.width,t.height,h,d)},a._addFxaaPass=function(e,a,t,i,n,r,s){this._fxaaParams.x=i,this._fxaaParams.y=n,this._fxaaParams.z=1/i,this._fxaaParams.w=1/n;var o=e.addRenderPass(i,n,"cc-fxaa");return o.addRenderTarget(s,v.CLEAR,C.STORE,I),o.addTexture(r,"sceneColorMap"),o.setVec4("g_platform",a.platform),o.setVec4("texSize",this._fxaaParams),o.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(t,0),o},e}()),K=e("BuiltinFsrPassBuilder",function(){function e(){this._fsrParams=new o(0,0,0,0),this._fsrTexSize=new o(0,0,0,0)}var a=e.prototype;return a.getConfigOrder=function(){return 0},a.getRenderOrder=function(){return 500},a.configCamera=function(e,a,t){t.enableFSR=t.settings.fsr.enabled&&!!t.settings.fsr.material&&t.enableShadingScale&&t.shadingScale<1,t.enableFSR&&++t.remainingPasses},a.setup=function(e,a,t,i,n,r){if(!t.enableFSR)return r;--t.remainingPasses;var s=n.colorName,o=0===t.remainingPasses?t.colorName:G(n.colorName,"UiColor",t.renderWindowId);return n.colorName=o,l(!!t.settings.fsr.material),this._addFsrPass(e,a,t,t.settings,t.settings.fsr.material,t.renderWindowId,t.width,t.height,s,t.nativeWidth,t.nativeHeight,o)},a._addFsrPass=function(e,a,t,i,n,r,s,o,l,c,u,p){this._fsrTexSize.x=s,this._fsrTexSize.y=o,this._fsrTexSize.z=c,this._fsrTexSize.w=u,this._fsrParams.x=h(1-i.fsr.sharpness,.02,.98);var g=G(p,"UiColor",r),m=e.addRenderPass(c,u,"cc-fsr-easu");m.addRenderTarget(g,v.CLEAR,C.STORE,I),m.addTexture(l,"outputResultMap"),m.setVec4("g_platform",a.platform),m.setVec4("fsrTexSize",this._fsrTexSize),m.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(n,0);var S=e.addRenderPass(c,u,"cc-fsr-rcas");return S.addRenderTarget(p,v.CLEAR,C.STORE,I),S.addTexture(g,"outputResultMap"),S.setVec4("g_platform",a.platform),S.setVec4("fsrTexSize",this._fsrTexSize),S.setVec4("fsrParams",this._fsrParams),S.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(n,1),S},e}()),q=e("BuiltinUiPassBuilder",function(){function e(){}var a=e.prototype;return a.getConfigOrder=function(){return 0},a.getRenderOrder=function(){return 1e3},a.setup=function(e,a,t,i,n,r){l(!!r);var s=d.SceneFlags.UI;return t.enableProfiler&&(s|=d.SceneFlags.PROFILER,r.showStatistics=!0),r.addQueue(d.QueueHint.BLEND,"default","default").addScene(i,s),r},e}());if(d){var j=d.QueueHint,Z=d.SceneFlags,J=function(){function e(){this._pipelineEvent=t.director.root.pipelineEvent,this._forwardPass=new V,this._bloomPass=new k,this._toneMappingPass=new Y,this._fxaaPass=new X,this._fsrPass=new K,this._uiPass=new q,this._clearColor=new T(0,0,0,1),this._viewport=new L,this._configs=new Q,this._cameraConfigs=new z,this._copyAndTonemapMaterial=new u,this._initialized=!1,this._passBuilders=[]}var i=e.prototype;return i._setupPipelinePreview=function(e,a){if(e.cameraUsage===D.SCENE_VIEW||e.cameraUsage===D.PREVIEW){var t=d.getEditorPipelineSettings();a.settings=t||H}else e.pipelineSettings?a.settings=e.pipelineSettings:a.settings=H},i._preparePipelinePasses=function(e){var t=this._passBuilders;t.length=0;var i=e.settings;if(i._passes){for(var n,r=a(i._passes);!(n=r()).done;){var s=n.value;t.push(s)}l(t.length===i._passes.length)}t.push(this._forwardPass),i.bloom.enabled&&t.push(this._bloomPass),t.push(this._toneMappingPass),i.fxaa.enabled&&t.push(this._fxaaPass),i.fsr.enabled&&t.push(this._fsrPass),t.push(this._uiPass)},i._setupBuiltinCameraConfigs=function(e,a,t){var i=e.window,r=e.cameraUsage===D.GAME&&!!i.swapchain;t.isMainGameWindow=r,t.renderWindowId=i.renderWindowId,t.colorName=i.colorName,t.depthStencilName=i.depthStencilName,t.enableFullPipeline=0!=(e.visibility&p.Enum.DEFAULT),t.enableProfiler=f,t.remainingPasses=0,t.shadingScale=t.settings.shadingScale,t.enableShadingScale=t.settings.enableShadingScale&&1!==t.shadingScale,t.nativeWidth=Math.max(Math.floor(i.width),1),t.nativeHeight=Math.max(Math.floor(i.height),1),t.width=t.enableShadingScale?Math.max(Math.floor(t.nativeWidth*t.shadingScale),1):t.nativeWidth,t.height=t.enableShadingScale?Math.max(Math.floor(t.nativeHeight*t.shadingScale),1):t.nativeHeight,t.enableHDR=t.enableFullPipeline&&a.useFloatOutput,t.radianceFormat=t.enableHDR?n.Format.RGBA16F:n.Format.RGBA8,t.copyAndTonemapMaterial=this._copyAndTonemapMaterial,t.enableStoreSceneDepth=!1},i._setupCameraConfigs=function(e,t,i){this._setupPipelinePreview(e,i),this._preparePipelinePasses(i),this._passBuilders.sort((function(e,a){return e.getConfigOrder()-a.getConfigOrder()})),this._setupBuiltinCameraConfigs(e,t,i);for(var n,r=a(this._passBuilders);!(n=r()).done;){var s=n.value;s.configCamera&&s.configCamera(e,t,i)}},i.windowResize=function(e,t,i,n,r){y(e,this._configs),this._setupCameraConfigs(i,this._configs,this._cameraConfigs);var s=t.renderWindowId;e.addRenderWindow(this._cameraConfigs.colorName,M.RGBA8,n,r,t,this._cameraConfigs.depthStencilName);var o=this._cameraConfigs.width,d=this._cameraConfigs.height;this._cameraConfigs.enableShadingScale?(e.addDepthStencil("ScaledSceneDepth_"+s,M.DEPTH_STENCIL,o,d),e.addRenderTarget("ScaledRadiance0_"+s,this._cameraConfigs.radianceFormat,o,d),e.addRenderTarget("ScaledRadiance1_"+s,this._cameraConfigs.radianceFormat,o,d),e.addRenderTarget("ScaledLdrColor0_"+s,M.RGBA8,o,d),e.addRenderTarget("ScaledLdrColor1_"+s,M.RGBA8,o,d)):(e.addDepthStencil("SceneDepth_"+s,M.DEPTH_STENCIL,o,d),e.addRenderTarget("Radiance0_"+s,this._cameraConfigs.radianceFormat,o,d),e.addRenderTarget("Radiance1_"+s,this._cameraConfigs.radianceFormat,o,d),e.addRenderTarget("LdrColor0_"+s,M.RGBA8,o,d),e.addRenderTarget("LdrColor1_"+s,M.RGBA8,o,d)),e.addRenderTarget("UiColor0_"+s,M.RGBA8,n,r),e.addRenderTarget("UiColor1_"+s,M.RGBA8,n,r);for(var l,h=a(this._passBuilders);!(l=h()).done;){var c=l.value;c.windowResize&&c.windowResize(e,this._configs,this._cameraConfigs,t,i,n,r)}},i.setup=function(e,t){if(!this._initMaterials(t))for(var i,n=a(e);!(i=n()).done;){var r=i.value;r.scene&&r.window&&(this._setupCameraConfigs(r,this._configs,this._cameraConfigs),this._pipelineEvent.emit(g.RENDER_CAMERA_BEGIN,r),this._cameraConfigs.enableFullPipeline?this._buildForwardPipeline(t,r,r.scene,this._passBuilders):this._buildSimplePipeline(t,r),this._pipelineEvent.emit(g.RENDER_CAMERA_END,r))}},i._buildSimplePipeline=function(e,a){var t=Math.max(Math.floor(a.window.width),1),i=Math.max(Math.floor(a.window.height),1),n=this._cameraConfigs.colorName,r=this._cameraConfigs.depthStencilName,s=a.viewport;this._viewport.left=Math.round(s.x*t),this._viewport.top=Math.round(s.y*i),this._viewport.width=Math.max(Math.round(s.width*t),1),this._viewport.height=Math.max(Math.round(s.height*i),1);var o=a.clearColor;this._clearColor.x=o.x,this._clearColor.y=o.y,this._clearColor.z=o.z,this._clearColor.w=o.w;var d=e.addRenderPass(t,i,"default");O(a)?d.addRenderTarget(n,v.CLEAR,C.STORE,this._clearColor):d.addRenderTarget(n,v.LOAD,C.STORE),a.clearFlag&R.DEPTH_STENCIL?d.addDepthStencil(r,v.CLEAR,C.DISCARD,a.clearDepth,a.clearStencil,a.clearFlag&R.DEPTH_STENCIL):d.addDepthStencil(r,v.LOAD,C.DISCARD),d.setViewport(this._viewport),d.addQueue(j.OPAQUE).addScene(a,Z.OPAQUE);var l=Z.BLEND|Z.UI;this._cameraConfigs.enableProfiler&&(l|=Z.PROFILER,d.showStatistics=!0),d.addQueue(j.BLEND).addScene(a,l)},i._buildForwardPipeline=function(e,t,i,n){!function(e){e.sort((function(e,a){return e.getRenderOrder()-a.getRenderOrder()}))}(n);for(var r,s={colorName:"",depthStencilName:""},o=void 0,d=a(n);!(r=d()).done;){var h=r.value;h.setup&&(o=h.setup(e,this._configs,this._cameraConfigs,t,s,o))}l(0===this._cameraConfigs.remainingPasses)},i._initMaterials=function(e){return this._initialized?0:(y(e,this._configs),this._copyAndTonemapMaterial._uuid="builtin-pipeline-tone-mapping-material",this._copyAndTonemapMaterial.initialize({effectName:"pipeline/post-process/tone-mapping"}),this._copyAndTonemapMaterial.effectAsset&&(this._initialized=!0),this._initialized?0:1)},e}();d.setCustomPipeline("Builtin",new J)}t._RF.pop()}}}));

System.register("chunks:///_virtual/internal",["./builtin-pipeline-settings.ts","./builtin-pipeline-types.ts","./builtin-pipeline.ts"],(function(){return{setters:[null,null,null],execute:function(){}}}));

(function(r) {
  r('virtual:///prerequisite-imports/internal', 'chunks:///_virtual/internal'); 
})(function(mid, cid) {
    System.register(mid, [cid], function (_export, _context) {
    return {
        setters: [function(_m) {
            var _exportObj = {};

            for (var _key in _m) {
              if (_key !== "default" && _key !== "__esModule") _exportObj[_key] = _m[_key];
            }
      
            _export(_exportObj);
        }],
        execute: function () { }
    };
    });
});